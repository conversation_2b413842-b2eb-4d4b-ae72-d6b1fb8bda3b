import Link from "next/link";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import axios from "axios";
import config from "@/store/config.json";
import styles from "./home.module.css";
import handleAccessTokenExpiration from "./handle-access-token-expiration";
import { getGoogleToken } from "@/actions/get-google-token";

type FileResult = {
  id: string;
  name: string;
  mimeType: string;
};

const PlayBookFolders = () => {
  const router = useRouter();
  const targetFolderId =
    typeof router.query.fid !== "undefined" ? router.query.fid : config.directory.target_folder;
  const teamDriveId = config.directory.team_drive;
  const corpora = teamDriveId ? "teamDrive" : "allDrives";
  const [results, setResults] = useState<FileResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<{ message: string } | null>(null);

  // Using the custom hook to get the access token

  
  useEffect(() => {
    const getFiles = async () => {
      setLoading(true);
      setError(null);
      setResults([]);

      try {
        const { accessToken } = await getGoogleToken();
        if (!accessToken) {
          throw new Error("Unable to get a valid access token.");
        }

        // Now, fetch files from Google Drive
        const res = await axios.get("https://www.googleapis.com/drive/v3/files", {
          headers: { Authorization: `Bearer ${accessToken}` },
          params: {
            source: "PlayBookFolders",
            corpora: corpora,
            includeTeamDriveItems: true,
            supportsAllDrives: true,
            teamDriveId: teamDriveId,
            q: `mimeType='application/vnd.google-apps.folder' and trashed = false and parents in '${targetFolderId}'`,
          },
        });

        setResults(res.data.files);
      } catch (err: any) {
        if (err.response && err.response.status === 401) {
          // Handle access token expiration if API responds with 401
          handleAccessTokenExpiration();
        } else {
          setError({ message: err.message });
        }
      }

      setLoading(false);
    };

    getFiles();
  }, [targetFolderId]);

  return (
    <div style={{ width: "100%", textAlign: "left" }}>
      {loading && <div>Loading...</div>}
      {error && <div>{error.message}</div>}
      <div className={styles.grid}>
        {results.map((result) => (
          <Link
            href={{
              pathname: `/list/[fid]`,
              query: { fid: result.id },
            }}
            as={`/list/${result.id}`}
            key={result.id}
          >
            <div
              className={styles.card}
              onClick={() => {
                const container = document.querySelector(".searchContainer");
                if (container) {
                  container.innerHTML = "";
                }
              }}
            >
              <h3>{result.name}</h3>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default PlayBookFolders;


import { FaCaretRight, FaCaretDown, FaCaretUp } from "react-icons/fa";
import { cn } from "@/lib/utils";

import { Card, CardHeader, CardDescription, CardTitle } from "@/components/ui/card"

interface AnalyticsCardProps {
  title: string;
  value: number;
  variant: "up" | "down" | "flat";
  increaseValue: number;
  className?: string;
}
export const AnalyticsCard = ({ 
  title, 
  value, 
  variant, 
  increaseValue, 
  className 
 }: AnalyticsCardProps) => {
    const iconColor = variant === "flat" ? "text-blue-500" : variant === "up" ? "text-red-500" : "text-green-500";
    const increaseValueColor = variant === "flat" ? "text-blue-500" : variant === "up" ? "text-red-500" : "text-green-500";
    const Icon = variant === "flat" ? FaCaretRight : variant === "up" ? FaCaretUp : FaCaretDown;

  return (
    <Card className="w-full shadow-none">
      <CardHeader className="flex p-4 py-4">
        <div className="flex items-center gap-x-2.5">
          <CardDescription className="flex items-center gap-x-2 font-medium overflow-hidden">
            <span className="truncate text-sm">{title}</span>
          </CardDescription>
        </div>
        
        <CardTitle className="flex items-center text-base font-bold gap-x-6">
          <div className="">{value}</div>
          <div className="flex items-center">
            <Icon className={cn(iconColor, "size-4")} />
            <span className={cn(increaseValueColor, "truncate text-sm")}>{increaseValue}</span>
          </div>
        </CardTitle>
      </CardHeader>
    </Card>
  )
}
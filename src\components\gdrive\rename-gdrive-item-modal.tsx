"use client"

import { useState } from "react";
import { GoogleDriveItem } from "./columns"
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader } from "lucide-react";
import { ResponsiveModal } from "@/components/responsive-modal";
import { useUpdateItem } from './use-update-item';

interface RenameModalProps {
  isOpen: boolean;
  onClose: () => void;
  item:  GoogleDriveItem;
  currentFolder: GoogleDriveItem | null;
  setCurrentFolder: (item: GoogleDriveItem | null) => void;
}

export const RenameGdriveItemModal = ({
  isOpen,
  onClose,
  item,
  currentFolder,
  setCurrentFolder,
}: RenameModalProps) => {
  const [newName, setNewName] = useState(item.name);
  const { mutate: updateItemMutate, isPending: isUpdateItemLoading } = useUpdateItem();


  const handleRename = async () => {
    if (!item || !newName.trim()) return;
  
    try {
      const values = {
        fileId: item.id,
        newName: newName.trim(),
      };
  
      updateItemMutate(
        { values },
        {
          onSuccess: () => {
            if (currentFolder) {
              const updatedChildren =
                currentFolder.children?.map((child: any) =>
                  child.id === item.id
                    ? { ...child, name: newName.trim() }
                    : child
                ) || [];

              const updatedFolder = {
                ...currentFolder,
                children: updatedChildren,
              };

              setCurrentFolder(updatedFolder);
            }
            close();
          },
        }
      );
    } catch (error) {
      console.error("Error renaming item:", error);
    }
  };
  

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleRename();
    }
  };

  return (
    <ResponsiveModal
      open={isOpen}
      onOpenChange={onClose}
    >
      <div className="p-6 space-y-6">
        <h2 className="text-xl font-semibold">Rename Item</h2>
        <div className="space-y-4">
          <Input
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter new name"
            autoFocus
          />
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isUpdateItemLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRename}
              disabled={isUpdateItemLoading || !newName.trim() || newName.trim() === item?.name}
            >
              {isUpdateItemLoading && <Loader className="h-4 w-4 animate-spin" />}
              Save
            </Button>
          </div>
        </div>
      </div>
    </ResponsiveModal>
  );
};

"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { Prisma } from "@prisma/client";
import { startOfMonth, endOfMonth, setHours, setMinutes, setSeconds, setMilliseconds } from 'date-fns';

const defaultCategoryBudgets = {
  Housing: 2000,
  Food: 10000,
  Transportation: 2000,
  Utilities: 300,
  Entertainment: 2000,
  Healthcare: 300,
  Other: 200,
};

const categoryDescriptions = {
  Housing: "Rent, mortgage, and maintenance",
  Food: "Groceries and dining",
  Transportation: "Car, fuel, and public transit",
  Utilities: "Electricity, water, and internet",
  Entertainment: "Movies, games, and hobbies",
  Healthcare: "Medical and wellness expenses",
  Other: "Miscellaneous expenses",
} as const;

export async function createDefaultBudget() {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized: No user found");
    }

    const now = new Date();
    const { start, end } = await getMonthDateRange(now);

    console.log('Start:', start.toISOString());
    console.log('End:', end.toISOString());

    // Prepare category operations
    const categoryOperations = Object.entries(defaultCategoryBudgets).map(([name, amount]) => ({
      where: { name_userId: { name, userId } },
      create: {
        name,
        icon: name,
        userId,
        description: categoryDescriptions[name as keyof typeof categoryDescriptions],
      },
      update: {},
    }));

    // Execute all category upserts in parallel
    const categories = await prisma.$transaction(
      categoryOperations.map(operation => 
        prisma.category.upsert(operation)
      )
    );

    // Prepare budget operations
    const budgetOperations = categories.map((category, index) => ({
      where: { id: `default-${userId}` },
      create: {
        userId,
        amount: new Prisma.Decimal(Object.values(defaultCategoryBudgets)[index]),
        startDate: start,
        endDate: end,
        categories: {
          create: {
            amount: new Prisma.Decimal(Object.values(defaultCategoryBudgets)[index]),
            category: { connect: { id: category.id } },
          },
        },
      },
      update: {
        categories: {
          upsert: {
            where: {
              budgetId_categoryId: {
                budgetId: `default-${userId}`,
                categoryId: category.id,
              },
            },
            create: {
              amount: new Prisma.Decimal(Object.values(defaultCategoryBudgets)[index]),
              category: { connect: { id: category.id } },
            },
            update: {
              amount: new Prisma.Decimal(Object.values(defaultCategoryBudgets)[index]),
            },
          },
        },
      },
    }));

    // Execute all budget upserts in parallel
    await prisma.$transaction(
      budgetOperations.map(operation => 
        prisma.budget.upsert(operation)
      )
    );

    revalidatePath("/categories");
    return { success: true };
  } catch (error) {
    console.error("Error in createDefaultBudget:", error);
    if (error instanceof Error) {
      return { success: false, error: error.message };
    }
    return {
      success: false,
      error: "An unexpected error occurred while creating the default budget",
    };
  }
}
export async function getMonthDateRange(date: Date) {
  const start = startOfMonth(date);
  const end = endOfMonth(date);
  
  // Ensure start is at 00:00:00.000
  const adjustedStart = setMilliseconds(setSeconds(setMinutes(setHours(start, 0), 0), 0), 0);
  
  // Ensure end is at 23:59:59.999
  const adjustedEnd = setMilliseconds(setSeconds(setMinutes(setHours(end, 23), 59), 59), 999);

  return { start: adjustedStart, end: adjustedEnd };
}
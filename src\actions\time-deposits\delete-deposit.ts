'use server';

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { z } from "zod";
import { revalidatePath } from "next/cache";

const deleteTimeDepositSchema = z.object({
  timeDepositId: z.string().min(1),
});

export async function deleteTimeDeposit(input: z.infer<typeof deleteTimeDepositSchema>) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false,
      };
    }

    const validatedFields = deleteTimeDepositSchema.safeParse(input);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false,
      };
    }

    const { timeDepositId } = validatedFields.data;

    // Fetch the time deposit to ensure it exists and get its details
    const timeDeposit = await prisma.timeDeposit.findUnique({
      where: { id: timeDepositId },
      include: {
        bankAccount: true,
      },
    });

    if (!timeDeposit) {
      return {
        error: "Time deposit not found.",
        success: false,
      };
    }

    // Safely handle potentially null amount
    const depositAmount = timeDeposit.amount ?? 0;

    // Determine balance adjustment based on time deposit type and amount
    const balanceAdjustment = timeDeposit.type === "AVAILABLE" 
      ? -depositAmount 
      : depositAmount;

    // Perform transaction to delete time deposit and update balance
    const result = await prisma.$transaction(async (tx) => {
      // Delete the time deposit
      const deletedTimeDeposit = await tx.timeDeposit.delete({
        where: { id: timeDepositId },
      });

      // Get the latest balance for the account
      const latestBalance = await tx.balance.findFirst({
        where: { accountId: timeDeposit.accountId },
        orderBy: { date: 'desc' },
      });

      // Calculate new balance
      const currentAmount = latestBalance?.amount ?? 0;
      const newBalanceAmount = currentAmount + balanceAdjustment;

      // Create a new balance record
      await tx.balance.create({
        data: {
          amount: newBalanceAmount,
          date: new Date(),
          bankAccount: {
            connect: { id: timeDeposit.accountId! },
          },
          currency: {
            connect: { iso: timeDeposit.currencyIso || "TWD" },
          },
        },
      });

      return deletedTimeDeposit;
    });

    // Revalidate relevant paths
    revalidatePath(`/banking`);
    revalidatePath(`/time-deposits`);

    return {
      success: true,
      data: result,
      message: "Time deposit deleted successfully.",
    };
  } catch (error) {
    console.error("Error deleting time deposit:", error);
    return {
      error: "Failed to delete time deposit.",
      success: false,
    };
  }
}
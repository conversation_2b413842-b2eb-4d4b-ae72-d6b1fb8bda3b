import { google } from "googleapis";
import { NextResponse } from "next/server";
import { authenticateGoogleOAuth2 } from "../authenticate";

const deleteItemFromDrive = async (fileId: string) => {
  const auth = await authenticateGoogleOAuth2();
  if (!auth) {
    throw new Error('Authentication failed');
  }

  const drive = google.drive({ version: "v3", auth });

  try {
    await drive.files.delete({
      fileId: fileId,
      supportsAllDrives: true,
    });

    return { success: true };
  } catch (error) {
    console.error('Error deleting item:', error);
    return NextResponse.json(
      { error: 'Error deleting item' },
      { status: 400 }
    );
  }
};

export async function POST(req: Request) {
  try {
    const res = await req.json();
    const { fileId } = res;

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      );
    }

    const deletedItem = await deleteItemFromDrive(fileId);
    
    return NextResponse.json(
      { data: deletedItem,
        success: true },
      { status: 200 }
    );
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Failed to delete item' },
      { status: 500 }
    );
  }
}
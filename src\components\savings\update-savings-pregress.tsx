"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Loader2, Wand } from "lucide-react";
import { updateSavingProgress } from "@/actions/savings/update-saving-progress";

type GoalProps = {
  goal: {
    id: string;
    name: string;
    current: number;
    target: number;
  };
};

const progressUpdateSchema = z.object({
  goalId: z.string().min(1, "Goal ID is required"),
  amount: z.number().positive("Amount must be a positive number"),
});

type ProgressUpdateFormValues = z.infer<typeof progressUpdateSchema>;

export function UpdateSavingsProgress({ goal }: GoalProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  console.log("goal", goal)

  const form = useForm<ProgressUpdateFormValues>({
    resolver: zodResolver(progressUpdateSchema),
    defaultValues: {
      goalId: goal.id,
      amount: 0, // Default progress to 0
    },
  });

  async function onSubmit(data: ProgressUpdateFormValues) {
    try {
      setIsLoading(true);

      const result = await updateSavingProgress(data);

      if (!result.success) {
        if (Array.isArray(result.error)) {
          result.error.forEach((error) => {
            form.setError(error.path[0] as any, {
              message: error.message,
            });
          });
          toast.error("Please check the form for errors");
          return;
        }
        throw new Error(result.error);
      }

      toast.success("Savings progress updated successfully!");
      setOpen(false);
    } catch (error) {
      toast.error("Failed to update savings progress");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={"ghost"} size={"sm"} className="hover:bg-transparent">
          <Wand size={12} />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        {isLoading && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}
        <DialogHeader>
          <DialogTitle>Update Savings Progress</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Progress Amount</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter progress amount"
                      {...field}
                      value={field.value ?? ""} // Ensure a controlled component
                      onChange={(e) => field.onChange(Number(e.target.value))} // Parse to number
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end pt-4">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save Progress"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { UpdateMeetingFormWrapper } from "./update-meeting-form-wrapper";
import { useUpdateMeetingModal } from "@/hooks/use-update-meeting-modal";

export const UpdateMeetingModal = () => {
  const { meetingId, close } = useUpdateMeetingModal()
  // meetingId can be null, so we need to check for it before using it.
  // If it's null, we don't render the UpdateMeetingForm.  This prevents the error.
  const meetingIdToUse = meetingId ?? undefined;

  return (
    <ResponsiveModal open={!!meetingIdToUse} onOpenChange={close}>
      {meetingIdToUse && <UpdateMeetingFormWrapper onCancel={close} id={meetingIdToUse} />}
    </ResponsiveModal>
  )
}

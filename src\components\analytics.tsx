
"use client"

import { useGetProjectAnalytics } from "@/components/projects/use-get-project-analytics";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { AnalyticsCard } from "./projects/analytics-card";

interface AnalyticsProps {
  data: {
    taskCount: number,
    taskDifference: number,
    projectCount?: number,
    projectDifference?: number,
    incompleteTaskCount?: number,
    incompleteTaskDifference?: number,    
    assignedTaskCount: number,
    assignedTaskDifference: number,
    completedTaskCount: number,
    completedTaskDifference: number,
    overdueTaskCount: number,
    overdueTaskDifference: number,
  };
}
export const Analytics = ({ data }: AnalyticsProps) => {

  if (!data) return null;

  return (
    <ScrollArea className="rouned-lg w-full shrink-0">
      <div className="w-full flex flex-row gap-x-1">
        <div className="flex items-center flex-1">
          <AnalyticsCard 
            title="Total tasks"
            value={data.taskCount}
            variant={data.taskDifference === 0 ? "flat" : data.taskDifference > 0 ? "up" : "down"}
            increaseValue={data.taskDifference}
          />
          <div className="border-r-2 border-cyan-100"></div>
        </div>
        <div className="flex items-center flex-1">
          <AnalyticsCard 
            title="Assigned"
            value={data.assignedTaskCount}
            variant={data.assignedTaskDifference === 0 ? "flat" : data.assignedTaskDifference > 0 ? "up" : "down"}
            increaseValue={data.assignedTaskDifference}
          />
        </div>
        <div className="flex items-center flex-1">
          <AnalyticsCard 
            title="Completed"
            value={data.completedTaskCount}
            variant={data.completedTaskDifference === 0 ? "flat" : data.completedTaskDifference > 0 ? "up" : "down"}
            increaseValue={data.completedTaskDifference}
          />
        </div>
        <div className="flex items-center flex-1">
          <AnalyticsCard 
            title="Overdue"
            value={data.overdueTaskCount}
            variant={data.overdueTaskDifference === 0 ? "flat" : data.overdueTaskDifference > 0 ? "up" : "down"}
            increaseValue={data.overdueTaskDifference}
          />
        </div>
        <div className="flex items-center flex-1">
          <AnalyticsCard 
            title="Incompleted"
            value={data?.incompleteTaskCount!}
            variant={data.incompleteTaskDifference === 0 ? "flat" : data?.incompleteTaskDifference! > 0 ? "up" : "down"}
            increaseValue={data?.incompleteTaskDifference!}
          />
        </div>
      </div>
      <ScrollBar orientation="horizontal"/>
    </ScrollArea>
  )
}
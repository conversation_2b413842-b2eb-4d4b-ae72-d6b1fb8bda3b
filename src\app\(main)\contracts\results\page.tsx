"use client";

import ContractAnalysisResults from "../_components/analysis/contract-analysis-results";
import EmptyState from "../_components/analysis/empty-state";
import { useContractStore } from "@/store/zustand";

export default function ContractResultsPage() {
  const analysisResults = useContractStore((state) => state.analysisrResults);
  console.log("analysisResults", analysisResults)

  if (!analysisResults) {
    return <EmptyState title="No Analysis" description="Please try again" />;
  }

  return (
    <ContractAnalysisResults
      contractId={analysisResults.id}
      isActive={true}
      analysisResults={analysisResults}
    />
  );
}
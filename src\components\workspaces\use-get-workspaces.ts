import { Workspace, Member } from "@prisma/client";
import { useQuery } from "@tanstack/react-query";
import { getWorkspaces } from "@/actions/workspaces/get-workspaces";

type Workspaces = { id: string; name: string; members: Member[] };

export const useGetWorkspaces = () => {
  const { data: workspaces, isLoading, error } = useQuery<Workspaces[]>({
    queryKey: ["workspaces"],
    queryFn: async () => {
      const response = await getWorkspaces();
      if (!response.success || !response.data) throw new Error("Failed to fetch workspaces");
      return response?.data;
    },
  })

  return { workspaces, isLoading, error }
}
import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { nanoid } from 'nanoid';
import { encrypt } from "@/lib/encryption";
import { rateLimit } from "@/lib/rate-limit";
import { validateCsrfToken } from "@/lib/csrf";
import axios from "axios";
import config from "@/lib/config/env";

// Configure rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500
});

async function handleOAuthCallback(req: NextRequest, code: string, state: string) {
  try {
    // Log the incoming state and stored state for debugging
    const cookieStore_ = await cookies();
    const storedState_ = cookieStore_.get("oauth_state")?.value;
    console.log('Incoming state:', state);
    console.log('Stored state:', storedState_);

    // Validate state parameter
    if (!storedState_ || state !== storedState_) {
      console.log('State validation failed');
      return NextResponse.json(
        { error: "Invalid state parameter", debug: { storedState_, receivedState: state } },
        { status: 400 }
      );
    }

    // 1. Rate limiting check
    const clientIp = req.headers.get("x-forwarded-for") || "0.0.0.0";
    try {
      await limiter.check(5, clientIp); // 5 requests per minute per IP
    } catch {
      return NextResponse.json(
        { error: "Too many requests" },
        { status: 429 }
      );
    }

    // 2. CSRF protection for POST requests only
    if (req.method === 'POST') {
      const csrfToken = req.headers.get("x-csrf-token");
      if (!validateCsrfToken(csrfToken)) {
        return NextResponse.json(
          { error: "Invalid request" },
          { status: 403 }
        );
      }
    }

    // 3. Validate state parameter (prevents CSRF attacks in OAuth flow)
    const cookie_store = await cookies();
    const storedState = cookie_store.get("oauth_state")?.value;
    if (!storedState || state !== storedState) {
      return NextResponse.json(
        { error: "Invalid state parameter" },
        { status: 400 }
      );
    }

    // 4. Exchange authorization code for tokens
    const { clientId, clientSecret, redirectUri } = config.env.google;
    const tokenResponse = await axios.post(
      "https://oauth2.googleapis.com/token",
      new URLSearchParams({
        code,
        client_id: clientId,
        client_secret: clientSecret,
        redirect_uri: redirectUri,
        grant_type: "authorization_code",
      }).toString(),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    const { access_token: accessToken, refresh_token: refreshToken, expires_in } = tokenResponse.data;

    // 5. Encrypt sensitive data
    const encryptedRefreshToken = encrypt(refreshToken);

    // 6. Set cookies
    const cookieStore = await cookies();
    
    // Set access token
    cookieStore.set({
      name: 'access_token',
      value: accessToken,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'lax',
      path: '/',
      maxAge: expires_in
    });

    // Set refresh token
    cookieStore.set({
      name: 'refresh_token',
      value: encryptedRefreshToken,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'lax',
      path: '/',
      maxAge: 30 * 24 * 60 * 60 // 30 days
    });

    // Set session ID
    cookieStore.set({
      name: 'session_id',
      value: nanoid(),
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: 'lax',
      path: '/'
    });

    // 7. Redirect to dashboard on success
    const dashboardUrl = new URL('/dashboard', req.url);
    return NextResponse.redirect(dashboardUrl);

  } catch (error: any) {
    console.error("Google OAuth Error:", error);
    
    // Redirect to error page
    const errorUrl = new URL('/auth/error', req.url);
    return NextResponse.redirect(errorUrl);
  }
}


export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');

    if (!code || !state) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Get the stored state
    const cookieStore = await cookies();
    const storedState = cookieStore.get('oauth_state')?.value;

    console.log('Debug state check:', {
      receivedState: state,
      storedState: storedState,
      allCookies: cookieStore.getAll()
    });

    if (!storedState || state !== storedState) {
      return NextResponse.json(
        { 
          error: "Invalid state parameter",
          debug: { 
            storedState,
            receivedState: state 
          }
        },
        { status: 400 }
      );
    }

    // Clear the state cookie as it's no longer needed
    cookieStore.delete('oauth_state');

    // Continue with the OAuth flow...
    return handleOAuthCallback(req, code, state);
  } catch (error) {
    console.error('Callback error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { code, state } = await req.json();

    if (!code || !state) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    return handleOAuthCallback(req, code, state);
  } catch (error) {
    return NextResponse.json(
      { error: "Invalid request body" },
      { status: 400 }
    );
  }
}
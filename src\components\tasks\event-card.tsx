import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { Project, TaskStatus } from "@prisma/client";
import { MemberAvatar } from "../workspaces/member-avatar";
import { ProjectAvatar } from "../projects/project-avatar";
import React from "react";

interface EventCardProps {
  id: string;
  title: string;
  project: Project | null;
  assignee: any;
  status: TaskStatus;
  workspaceId: string;
}

const statusColorMap: Record<TaskStatus, string> = {
  [TaskStatus.BACKLOG]: "border-l-pink-500",
  [TaskStatus.TODO]: "border-l-red-500",
  [TaskStatus.IN_PROGRESS]: "border-l-yellow-500",
  [TaskStatus.IN_REVIEW]: "border-l-blue-500",
  [TaskStatus.DONE]: "border-l-emerald-500",
}

export const EventCard = ({
  id,
  title,
  project,
  assignee,
  status,
  workspaceId,
}: EventCardProps) => {
  const router = useRouter();

  const onClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    router.push(`/workspaces/${workspaceId}/tasks/${id}`);
  }

  return (
    <div className="px-2">
      <div 
        onClick={onClick}
        className={
          cn("p-1 text-xs text-primary border border-t-0 border-e-0 border-b-indigo-700 rounded-md border-b-4 border-l-4 flex flex-col gap-y-1.5 cursor-pointer hover:opacity-75 transition",
          statusColorMap[status]
      )}>
        <p>{title}</p>
        <div className="flex items-center gap-x-1" >
          <MemberAvatar 
            name={assignee.user.name}
          />
          <div className="size-1 rounded-full bg-neutral-300" />
          <ProjectAvatar 
            name={project?.name!}
            image={project?.imageUrl!}
          />
        </div>

      </div>
    </div>
  )
}
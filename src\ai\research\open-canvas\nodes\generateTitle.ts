import { LangGraphRunnableConfig } from "@langchain/langgraph";
//import { Client } from "@langchain/langgraph-sdk";
import { prisma } from "@/lib/db";
import { OpenCanvasGraphAnnotation } from "../state";
import { ToolMessage } from "@langchain/core/messages";
import { graph } from '../../thread-title/index'

export const generateTitleNode = async (
  state: typeof OpenCanvasGraphAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  if (state.messages.length > 2) {
    // Skip if it's not first human ai conversation. Should never occur in practice
    // due to the conditional edge which is called before this node.
    return {};
  }

  try {
    /*const langGraphClient = new Client({
      apiUrl: `http://localhost:${process.env.PORT}`,
    });*/

    let modelMessages = [];
    for (let i = state.messages.length - 1; i >= 0; i--) {
      modelMessages.push(state.messages[i]);
      if (modelMessages.length >= 5) {
        if (!ToolMessage.isInstance(modelMessages[modelMessages.length - 1])) {
          break;
        }
      }
    }
    modelMessages.reverse();

    const titleInput = {
      messages: modelMessages,
      artifact: state.artifact,
    };
    const titleConfig = {
      configurable: {
        open_canvas_thread_id: config.configurable?.thread_id,
      },
    };

    /*const newThread = await prisma.thread.create({
      data: {
        status: "idle", // Provide a valid ThreadStatus value
        values: {},
        metadata: {},
      },
    });*/

    // Create a new run record linked to the thread.
    // IMPORTANT: Ensure you pass a valid assistant_id, as it is required by the model.
    const newRun = await prisma.run.create({
      data: {
        thread_id: config.configurable?.thread_id,//newThread.thread_id,
        // Provide an existing assistant_id or a default value.
        assistant_id: config.configurable?.assistant_id!, 
        status: "pending", // Starting status for the run
        multitask_strategy: "enqueue", // According to your enum
      },
    });
    const titleOutput = await graph.invoke(titleInput, titleConfig)

    const jsonResult = JSON.parse(JSON.stringify(titleOutput));

    // Optionally, update the run record with the results and change its status.
    await prisma.run.update({
      where: { run_id: newRun.run_id },
      data: {
        status: "success",
        metadata: {
          ...((typeof newRun.metadata === "object" && newRun.metadata !== null)
            ? (newRun.metadata as Record<string, unknown>)
            : {}),
          result: jsonResult,
        },
      },
    });
    
    // Create a new thread for title generation
    /*const newThread = await langGraphClient.threads.create();

    // Create a new title generation run in the background
    await langGraphClient.runs.create(newThread.thread_id, "thread_title", {
      input: titleInput,
      config: titleConfig,
      multitaskStrategy: "enqueue",
      afterSeconds: 0,
    });*/
  } catch (e) {
    console.error("Failed to call generate title graph\n\n", e);
  }

  return {};
};

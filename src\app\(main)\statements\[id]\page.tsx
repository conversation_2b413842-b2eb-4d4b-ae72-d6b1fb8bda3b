"use client";

import { getAccountDetails } from "@/actions/statements/get-account-details";
import { notFound } from "next/navigation";
import AccountTransactions from "@/components/statements/account-transactions";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select";
import { formatDistanceToNow } from "date-fns";
import { BalanceTooltip } from "@/components/transactions/balance-tooltip";
import { useEffect, useState, use } from "react";
import { BarChartCard } from "@/components/bar-chart-statements";
import { PieChartCard } from "@/components/pie-chart-statements";
import { format, subYears } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { DateRange } from "react-day-picker";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { CalendarIcon, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";

// Define interfaces and time ranges
interface BankingDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

interface Transaction {
  id: string;
  amount: number;
  description: string;
  date: Date;
  categoryId: string;
  accountId: string;
  type: string, //"CREDIT" | "DEBIT";
  categoryValidated: boolean;
  suggestedCategoryId?: string | null | undefined;
  category?: {
    id: string;
    name: string;
    icon: string;
  };
}

interface ProcessedMonthData {
  monthYear: string;    // e.g. "Jan 2024" for display
  sortKey: string;      // e.g. "2024-01" for sorting
  income: number;
  expense: number;
}

const chartConfig = {
  income: {
    label: "Income",
    color: "hsl(var(--chart-2))",
  },
  expense: {
    label: "Expense",
    color: "hsl(var(--chart-1))",
  },
};

type TimeRange = {
  label: string;
  duration: Date;
}
type CustomDateRange = {
  from: Date;
  to?: Date;
}

type TimeRangeKey = 'custom' | 'latest_5y' | 'latest_4y' | 'latest_3y' | 'latest_2y' | 'this_year';

const timeRanges: Record<TimeRangeKey, { label: string; duration: Date | null }> = {
  "custom": { label: "Custom Range", duration: null },
  "latest_5y": { label: "Latest 5 years", duration: new Date(new Date().getFullYear() - 4, 0, 1) },
  "latest_4y": { label: "Latest 4 years", duration: new Date(new Date().getFullYear() - 3, 0, 1) },
  "latest_3y": { label: "Latest 3 years", duration: new Date(new Date().getFullYear() - 2, 0, 1) },
  "latest_2y": { label: "Latest 2 years", duration: new Date(new Date().getFullYear() - 1, 0, 1) },
  "this_year": { label: "This year", duration: new Date(new Date().getFullYear(), 0, 1) },
};
  
export default function BankingDetailsPage(props: BankingDetailsPageProps) {
  const params = use(props.params);
  const [timeRange, setTimeRange] = useState<TimeRangeKey>("latest_5y");
  const [lastFetchedStartDate, setLastFetchedStartDate] = useState<Date | null>(null);
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [customDateRange, setCustomDateRange] = useState<DateRange | undefined>(undefined);

  const fetchData = async (startDate: Date, endDate?: Date) => {
    try {
      const result = await getAccountDetails(params.id, startDate, endDate);
      if (!result.success || !result.data) {
        notFound();
      }
      setData(result.data);
    } catch (error) {
      console.error("Failed to fetch account details", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let startDate: Date;
    let endDate: Date | undefined;
    
    if (timeRange === 'custom' && customDateRange?.from) {
      startDate = customDateRange.from;
      endDate = customDateRange.to;
    } else {
      startDate = timeRanges[timeRange]?.duration || subYears(new Date(), 1);
    }

    // Always fetch when timeRange changes or when we need earlier data
    fetchData(startDate, endDate).then(() => {
      setLastFetchedStartDate(startDate);
    });
  }, [params.id, timeRange, customDateRange]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      let startDate: Date;
      let endDate: Date | undefined;

      if (timeRange === 'custom' && customDateRange?.from) {
        startDate = customDateRange.from;
        endDate = customDateRange.to;
      } else {
        startDate = timeRanges[timeRange]?.duration || subYears(new Date(), 1);
      }

      await fetchData(startDate, endDate);
    } finally {
      setRefreshing(false);
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center">Loading...</div>;
  }

  const { account, transactions, incomes, expenses } = data;
  const chartData = processTransactionData(transactions)

  if (!data) {
    return null;
  }

  const handleTimeRangeChange = (value: TimeRangeKey) => {
    setTimeRange(value);
    if (value !== 'custom') {
      setCustomDateRange(undefined);
    }
  };

  const handleCustomDateChange = (range: DateRange | undefined) => {
    if (!range) {
      setCustomDateRange(undefined);
      return;
    }

    if (range.from) {
      // Set the start date to beginning of day
      const from = new Date(range.from);
      from.setHours(0, 0, 0, 0);

      // Set the end date to end of day if 'to' is provided
      let to = range.to;
      if (to) {
        to = new Date(to);
        to.setHours(23, 59, 59, 999);
      }

      setCustomDateRange({ from, to });
    }
  };

  const getCurrentRange = (): CustomDateRange | TimeRange => {
    if (timeRange === 'custom' && customDateRange?.from) {
      return {
        label: customDateRange.to 
          ? `${format(customDateRange.from, "MMM d, yyyy")} - ${format(customDateRange.to, "MMM d, yyyy")}`
          : format(customDateRange.from, "MMM d, yyyy"),
        from: customDateRange.from,
        to: customDateRange.to,
      };
    }
    
    return timeRanges[timeRange].duration 
      ? { label: timeRanges[timeRange].label, duration: timeRanges[timeRange].duration! }
      : { label: "Last year", duration: subYears(new Date(), 1) };
  };

  return (
    <div className="flex flex-1 flex-col gap-2 p-2 pt-0">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">{account.name}</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="h-9"
          >
            <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
          </Button>

          {timeRange === "custom" && (
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal h-9",
                    !customDateRange && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {customDateRange?.from ? (
                    customDateRange.to ? (
                      <>
                        {format(customDateRange.from, "LLL dd, y")} -{" "}
                        {format(customDateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(customDateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>Pick a date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={customDateRange?.from}
                  selected={customDateRange}
                  onSelect={handleCustomDateChange}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          )}

          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger
              className="h-9 w-[160px] rounded-lg"
              aria-label="Select time range"
            >
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              {Object.entries(timeRanges).map(([key, range]) => (
                <SelectItem key={key} value={key} className="rounded-lg">
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Balance</CardTitle>
            <BalanceTooltip
              balance={account.balance}
              availableBalance={account.balance}
              pendingBalance={0}
              lastUpdated={account.lastUpdated}
            />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {account.balance.toLocaleString("zh-TW", {
                style: "currency",
                currency: "TWD",
              })}
            </div>
            <p className="text-xs text-muted-foreground">
              Last updated{" "}
              {formatDistanceToNow(account.lastUpdated, { addSuffix: true })}
            </p>
          </CardContent>
        </Card>
        <BarChartCard
          title="Statements"
          description="Income vs Expense"
          data={chartData}
          xAxisKey="month"
          chartConfig={chartConfig}
        />
        <PieChartCard
          title="Statements"
          description="Income vs Expense"
          data={chartData}
          xAxisKey="month"
          chartConfig={chartConfig}
        />
      </div>

      <AccountTransactions
        dataRange={
          (() => {
            if (timeRange === "custom" && customDateRange?.from) {
              // If 'to' is missing, use 'from' as both start and end but set to end of day
              const from = customDateRange.from;
              let to = customDateRange.to ?? customDateRange.from;
              if (to && !customDateRange.to) {
                // If we're using 'from' as 'to', set it to end of day
                to = new Date(to);
                to.setHours(23, 59, 59, 999);
              }
              return { from, to };
            }
            // For predefined ranges, use duration as 'from' and today as 'to'
            const currentRange = getCurrentRange();
            if ("duration" in currentRange) {
              const endOfDay = new Date();
              endOfDay.setHours(23, 59, 59, 999);
              return { from: currentRange.duration, to: endOfDay };
            }
            // Fallback: use today for both
            const endOfDay = new Date();
            endOfDay.setHours(23, 59, 59, 999);
            return { from: new Date(), to: endOfDay };
          })()
        }
        transactions={transactions}
        onTransactionUpdate={async () => {
          if (timeRange === "custom" && customDateRange?.from) {
            await fetchData(customDateRange.from, customDateRange.to);
          } else {
            const currentRange = getCurrentRange();
            if ("duration" in currentRange && currentRange?.duration) {
              await fetchData(currentRange.duration);
            }
          }
        }}
      />
    </div>
  );
}

export function processTransactionData(transactions: Transaction[]) {
  // Group by year-month and calculate totals
  const monthlyData = transactions.reduce((acc: Record<string, ProcessedMonthData>, transaction) => {
    const date = new Date(transaction.date);
    const sortKey = format(date, 'yyyy-MM');
    const monthYear = format(date, 'MMM yyyy');
    const amount = Number(transaction.amount);

    if (!acc[sortKey]) {
      acc[sortKey] = {
        monthYear,
        sortKey,
        income: 0,
        expense: 0
      };
    }

    if (transaction.type === 'CREDIT') {
      acc[sortKey].income += amount;
    } else if (transaction.type === 'DEBIT') {
      acc[sortKey].expense += amount;
    }

    return acc;
  }, {});

  // Convert to array and sort by date
  const sortedMonths = Object.values(monthlyData)
    .sort((a, b) => a.sortKey.localeCompare(b.sortKey));

  // Step 1: Get the last 6 months and previous 6 months
  const last6Months = sortedMonths.slice(-6);
  const previous6Months = sortedMonths.slice(-12, -6);

  // Step 2: Calculate the net income for both periods
  const currentNet = last6Months.reduce((acc, month) => acc + (month.income - month.expense), 0);
  const previousNet = previous6Months.reduce((acc, month) => acc + (month.income - month.expense), 0);

  // Step 3: Calculate trending percentage and direction
  let trendingPercentage = 0;
  let trendDirection: 'up' | 'down' = 'up';

  if (previousNet !== 0) {
    const percentageChange = ((currentNet - previousNet) / Math.abs(previousNet)) * 100;
    trendingPercentage = Math.abs(percentageChange);
    trendDirection = percentageChange >= 0 ? 'up' : 'down';
  }

  // Logging for debugging purposes
  console.log("_Sorted months:", sortedMonths);
  console.log("_Last 6 months:", last6Months);
  console.log("_Previous 6 months:", previous6Months);
  console.log("_Current net income:", currentNet);
  console.log("_Previous net income:", previousNet);
  console.log("_Trending percentage:", trendingPercentage);
  console.log("_Trend direction:", trendDirection);

  return {
    data: sortedMonths,
    trending: {
      percentage: trendingPercentage.toFixed(1),
      direction: trendDirection
    }
  };
}
"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { startOfMonth, endOfMonth } from "date-fns";
import { ensureDefaultCategories } from "./manage-categories";

interface MonthlySpending {
  date: string;
  [category: string]: string | number;
}

interface CategoryOverview {
  totalSpending: number;
  categoryBreakdown: {
    name: string;
    icon: string;
    amount: number;
    budget: number;
    color: string;
    percentage: number;
    progress: number;
  }[];
  recentTransactions: {
    id: string;
    name: string;
    category: string;
    amount: number;
    date: Date;
    icon: string;
    color: string;
  }[];
  monthlySpending: MonthlySpending[];
}

export async function getCategoriesOverview(
  startDate?: Date,
  endDate?: Date
): Promise<{
  success: boolean;
  data?: CategoryOverview & { hasBudget: boolean };
  error?: string;
}> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    // Ensure we have default categories
    await ensureDefaultCategories(userId);

    // Get the current budget period
    const now = new Date();
    const periodStart = startDate || startOfMonth(now);
    const periodEnd = endDate || endOfMonth(now);

    // Get all categories with their transactions and budgets
    const categories = await prisma.category.findMany({
      where: { userId },
      include: {
        transactions: {
          where: {
            date: {
              gte: periodStart,
              lte: periodEnd,
            },
          },
        },
        budgets: {
          where: {
            budget: {
              userId,
              startDate: {
                lte: periodEnd,
              },
              endDate: {
                gte: periodStart,
              },
            },
          },
          include: {
            budget: true,
          },
        },
      },
    });

    const existingBudget = await prisma.budget.findFirst({
      where: {
        userId,
        startDate: {
          lte: periodEnd,
        },
        endDate: {
          gte: periodStart,
        },
      },
    });

    let totalSpending = 0;
    const categoryBreakdown: CategoryOverview["categoryBreakdown"] = [];
    const recentTransactions: CategoryOverview["recentTransactions"] = [];

    // Process each category
    for (const category of categories) {
      if (Array.isArray(category.transactions)) {
        const categoryAmount = category.transactions.reduce(
          (sum, tx) => sum + tx.amount.toNumber(),
          0
        );
        const categoryBudgetAmount = category.budgets[0]?.budget
          ? category.budgets[0]?.amount?.toNumber() || 0
          : 0;

        totalSpending += categoryAmount;

        const percentage = totalSpending > 0 ? (categoryAmount / totalSpending) * 100 : 0;
        const progress = categoryBudgetAmount > 0 ? (categoryAmount / categoryBudgetAmount) * 100 : 0;

        categoryBreakdown.push({
          name: category.name,
          icon: category.icon,
          amount: categoryAmount,
          budget: categoryBudgetAmount,
          color: category.color || "hsl(var(--chart-10))",
          percentage,
          progress,
        });

        // Get recent transactions for this category
        const categoryRecentTransactions = category.transactions
          .map((tx) => ({
            id: tx.id,
            name: tx.description,
            category: category.name,
            amount: tx.amount.toNumber(),
            date: tx.date,
            icon: category.icon,
            color: category.color || "hsl(var(--chart-10))",
          }));

        recentTransactions.push(...categoryRecentTransactions);
      }
    }

    // Calculate monthly spending
    const monthlySpending: MonthlySpending[] = await calculateMonthlySpending(categories);

    // Consolidate the data
    return {
      success: true,
      data: {
        totalSpending,
        categoryBreakdown,
        recentTransactions: recentTransactions.sort((a, b) => b.date.getTime() - a.date.getTime()).slice(0, 5),
        monthlySpending,
        hasBudget: !!existingBudget,
      },
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      error: "Failed to fetch categories overview",
    };
  }
}

async function calculateMonthlySpending(categories: any[]) {
  const monthlyData: Record<string, Record<string, number>> = {};

  for (const category of categories) {
    if (Array.isArray(category.transactions)) {
      for (const tx of category.transactions) {
        const date = tx.date.toISOString().split("T")[0].slice(0, 7); // Extract YYYY-MM

        if (!monthlyData[date]) {
          monthlyData[date] = {}; // Initialize a new month entry
        }

        const categoryName = category.name || "Other";
        monthlyData[date][categoryName] =
          (monthlyData[date][categoryName] || 0) + tx.amount.toNumber();
      }
    }
  }

  return Object.entries(monthlyData)
    .map(([date, categories]) => ({
      date,
      ...categories,
    }))
    .sort((a, b) => a.date.localeCompare(b.date));
}

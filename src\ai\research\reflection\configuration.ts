import { Annotation, LangGraphRunnableConfig } from "@langchain/langgraph";
import { CustomModelConfig } from "@badget/shared/types";
import { v4 as uuidv4 } from 'uuid';

export const ConfigurationAnnotation = Annotation.Root({
  open_canvas_assistant_id: Annotation<string>(),
  thread_id: Annotation<string>(),
});

export type Configuration = typeof ConfigurationAnnotation.State;


export function ensureConfiguration(config?: LangGraphRunnableConfig): Configuration {
  const configurable = config?.configurable || {};
  
  return {
    open_canvas_assistant_id: configurable?.assistant_id || uuidv4(),
    thread_id: configurable?.thread_id || uuidv4(),
  };
}
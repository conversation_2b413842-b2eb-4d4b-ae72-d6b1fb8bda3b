"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

type Income = {
  id: string;
  accountId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  categoryId: string;
  date: Date;
  description?: string | null;
  amount: number;
  type: string;
  categoryValidated: boolean;
  category?: {
    id: string;
    name: string;
    icon: string;
  };
};

type Expense = {
  id: string;
  accountId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  categoryId: string;
  date: Date;
  description?: string | null;
  amount: number;
  type: string;
  categoryValidated: boolean;
  category?: {
    id: string;
    name: string;
    icon: string;
  };
};

interface AccountDetails {
  account: {
    id: string;
    name: string;
    type: string;
    balance: number;
    accountNumber?: string;
    lastUpdated: Date;
  };
  transactions: Income[];
  incomes: Income[];
  expenses: Expense[];
}

export async function getAccountDetails(
  accountId: string,
  startDate: Date,
  endDate?: Date
): Promise<{ success: boolean; data?: AccountDetails; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const account = await prisma.bankAccount.findFirst({
      where: {
        id: accountId,
        userId,
      },
      include: {
        Income: {
          where: {
            date: {
              gte: startDate,
              ...(endDate && { lte: endDate }),
            },
          },
          orderBy: {
            date: "desc",
          },
          include: {
            category: {
              select: {
                id: true,
                name: true,
                icon: true,
              },
            },
          },
        },
        Expense: {
          where: {
            date: {
              gte: startDate,
              ...(endDate && { lte: endDate }),
            },
          },
          orderBy: { date: "desc" },
          include: {
            category: {
              select: {
                id: true,
                name: true,
                icon: true,
              },
            },
          },
        },
      },
    });

    if (!account) {
      throw new Error("Account not found");
    }

    // Calculate total income
    const totalIncome = account.Income.reduce((sum, income) => sum + Number(income.amount), 0);

    // Calculate total expenses
    const totalExpenses = account.Expense.reduce((sum, expense) => sum + Number(expense.amount), 0);

    // Calculate balance (total income - total expenses)
    const balance = totalIncome - totalExpenses;

    const incomes = account.Income.map((income) => ({
      ...income,
      amount: Number(income.amount),
    }));
    const expenses = account.Expense.map((expense) => ({
      ...expense,
      amount: Number(expense.amount),
    }));
    const transactions = [...incomes, ...expenses];


    return {
      success: true,
      data: {
        account: {
          id: account.id,
          name: account.name,
          type: account.accountType,
          balance: balance, // Updated to calculated balance
          accountNumber: account.originalId || undefined,
          lastUpdated: account.Income[0]?.date || account.updatedAt,
        },
        transactions,
        incomes,
        expenses,
      },
    };
  } catch (error) {
    console.error("Error in getAccountDetails:", error);
    return {
      success: false,
      error: "Failed to fetch account details",
    };
  }
}

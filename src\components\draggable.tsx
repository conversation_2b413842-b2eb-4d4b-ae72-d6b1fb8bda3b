import { GripVertical } from "lucide-react"; // or your preferred icon
import { useDraggable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";

export   const Draggable = ({ id, children }: { id: string; children: React.ReactNode }) => {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id,
  });

  const style = transform ? {
    transform: CSS.Transform.toString(transform),
  } : undefined;

  return (
    <div 
      ref={setNodeRef} 
      style={style}
      className="relative group"
    >
      <div className="flex items-center gap-2 p-2 border rounded cursor-pointer">
        <div
          {...listeners}
          {...attributes}
          className="cursor-grab active:cursor-grabbing"
        >
          <GripVertical className="h-4 w-4" />
        </div>
        <div>{children}</div>
      </div>
    </div>
  );
};
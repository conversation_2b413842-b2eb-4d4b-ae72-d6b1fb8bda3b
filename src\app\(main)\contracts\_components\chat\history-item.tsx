'use client'

import React, {Dispatch, SetStateAction } from 'react'
import { Chat } from '@prisma/client'
import { cn } from '@/lib/utils'

type HistoryItemProps = {
  chat: Chat
  chatId: string
  setChatId: Dispatch<SetStateAction<string>>;
  filePath?: string
  path: string
}

const formatDateWithTime = (date: Date | string) => {
  const parsedDate = new Date(date)
  const now = new Date()
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)

  const formatTime = (date: Date) => {
    return date.toLocaleString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  if (
    parsedDate.getDate() === now.getDate() &&
    parsedDate.getMonth() === now.getMonth() &&
    parsedDate.getFullYear() === now.getFullYear()
  ) {
    return `Today, ${formatTime(parsedDate)}`
  } else if (
    parsedDate.getDate() === yesterday.getDate() &&
    parsedDate.getMonth() === yesterday.getMonth() &&
    parsedDate.getFullYear() === yesterday.getFullYear()
  ) {
    return `Yesterday, ${formatTime(parsedDate)}`
  } else {
    return parsedDate.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }
}

const HistoryItem: React.FC<HistoryItemProps> = ({ chat, chatId, setChatId, filePath, path }) => {
  const isActive = chatId === chat.id

  const storageKey = filePath ? `chat_${chat.contractId}_${filePath}` : `chat_${chat.contractId}`;

  return (
    <div
      onClick={()=> {
        localStorage.setItem(storageKey, chat.id);
        setChatId(chat.id);}
      }
      className={cn(
        'w-full max-w-[16rem] flex flex-col hover:bg-muted cursor-pointer p-2 rounded border',
        isActive ? 'bg-muted/70 border-border/10' : 'border-transparent'
      )}
    >
      <div className="text-xs font-medium truncate select-none">
        {chat.title}
      </div>
      <div className="text-xs text-muted-foreground">
        {formatDateWithTime(chat.createdAt)}
      </div>
    </div>
  )
}

export default HistoryItem
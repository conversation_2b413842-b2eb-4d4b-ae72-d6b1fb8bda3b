import { create } from 'zustand';
import { Member, Workspace } from '@prisma/client';
import { saveToLocalStorage, getFromLocalStorage } from '@/utils/encryption-utils';

interface WorkspaceWithMembers {
  id: string;
  name: string;
  members: Member[];
}

interface WorkspaceStore {
  workspaces: WorkspaceWithMembers[];
  currentWorkspaceId: string | null;
  setWorkspaces: (workspaces: WorkspaceWithMembers[]) => void;
  setWorkspaceId: (workspaceId: string) => void;
  getCurrentWorkspace: () => WorkspaceWithMembers | null;
  getUserWorkspaces: (userId: string) => WorkspaceWithMembers[];
  getLastVisitedWorkspaceId: () => string | null;
  hydrateOnClient: () => void;
  isHydrated: boolean;
  setHydrated: (state: boolean) => void;
}

export const useWorkspaces = create<WorkspaceStore>((set, get) => ({
  workspaces: [],
  currentWorkspaceId: null,
  isHydrated: false,
  setHydrated: (state) => set({ isHydrated: state }),
  
  hydrateOnClient: () => {
    if (typeof window !== 'undefined' && !get().isHydrated) {
      const workspacesFromLocalStorage = getFromLocalStorage('workspaces');
      const currentWorkspaceIdFromLocalStorage = getFromLocalStorage('currentWorkspaceId');
      
      console.log("Hydrating from localStorage:", { 
        workspaces: workspacesFromLocalStorage,
        currentId: currentWorkspaceIdFromLocalStorage 
      });

      set({
        workspaces: workspacesFromLocalStorage || [],
        currentWorkspaceId: currentWorkspaceIdFromLocalStorage || null,
        isHydrated: true
      });
    }
  },

  setWorkspaces: (workspaces) => {
    console.log("Setting workspaces:", workspaces);
    if (typeof window !== 'undefined') {
      saveToLocalStorage('workspaces', workspaces);
    }
    set({ workspaces });
  },

  setWorkspaceId: (workspaceId: string) => {
    const current = get().currentWorkspaceId;
    if (current !== workspaceId) {
      const cleanWorkspaceId = workspaceId.replace(/^["']|["']$/g, '');
      
      if (typeof window !== 'undefined') {
        saveToLocalStorage('currentWorkspaceId', cleanWorkspaceId);
      }
      set({ currentWorkspaceId: cleanWorkspaceId });
    }
  },

  getLastVisitedWorkspaceId: () => {
    if (typeof window === 'undefined') return null;
    
    // First try to get from store
    const { currentWorkspaceId } = get();
    if (currentWorkspaceId) return currentWorkspaceId;
    
    // If not in store, try to get from localStorage
    const storedWorkspaceId = getFromLocalStorage('currentWorkspaceId');
    if (storedWorkspaceId) {
      // Update store with stored ID
      const cleanWorkspaceId = storedWorkspaceId.replace(/^["']|["']$/g, '');
      set({ currentWorkspaceId: cleanWorkspaceId });
      return cleanWorkspaceId;
    }
    
    // If no stored ID, try to get first workspace
    const { workspaces } = get();
    if (workspaces.length > 0) {
      const defaultWorkspaceId = workspaces[0].id;
      set({ currentWorkspaceId: defaultWorkspaceId });
      saveToLocalStorage('currentWorkspaceId', defaultWorkspaceId);
      return defaultWorkspaceId;
    }
    
    return null;
  },
  
  getCurrentWorkspace: () => {
    const { workspaces, currentWorkspaceId } = get();
    console.log("currentWorkspaceId__", currentWorkspaceId)
    return workspaces.find((workspace) => workspace.id === currentWorkspaceId) || null;
  },

  getUserWorkspaces: (userId) => {
    const { workspaces } = get();
    return workspaces.filter((workspace) =>
      workspace.members.some((member) => member.id === userId)
    );
  },
}));

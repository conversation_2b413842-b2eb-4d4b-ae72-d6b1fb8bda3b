'use client'

import SkeletonWrapper from "@/components/skeleton-wrapper";
import { useQuery } from "@tanstack/react-query";

import { DateToUTCDate } from "@/lib/helpers";
import { Card } from "@/components/ui/card";
import { GetActivityResponseType } from "@/app/api/credits/activity/route"
import React, { useState } from "react";

import './styles.css';



type Props = {
  from: Date;
  to: Date;
  uId?: string | null;
}

export const ActivityTable = ({
  from, to, uId, 
}: Props) => {
  const [value, setValue] = useState("");

  const activityQuery = useQuery<GetActivityResponseType>({
    queryKey: ["activity", "history", from, to, uId].filter(Boolean),
    queryFn: () =>
      fetch(
        `/api/credits/activity?uId=${uId}&from=${DateToUTCDate(from)}&to=${DateToUTCDate(to)}`
      ).then((res) => res.json()),
    enabled: !!uId,
  });

  const currency = 'USD'

  //console.log("activityQuery.data", activityQuery?.data)
  const transactionData = activityQuery.data?.transactions;
  const totals = activityQuery.data?.totals;
  const dataAvailable = transactionData && !!totals && transactionData.length > 0;

  return (
    <SkeletonWrapper isLoading={activityQuery.isFetching}>
      {dataAvailable ? (
        <div className="gap-y-2 overflow-auto">
          <div className="flex flex-rows items-center justify-center">
            {/*<UsagePieChart totals={totals} />*/}
          </div>

          <div className="stacked-container">
            {/*<APIRequestChart transactions={transactionData} />
            <TokenChart transactions={transactionData} />
            <AmountChart transactions={transactionData} currency={currency} />*/}
          </div>          
        </div>
      ) : (
        <Card className="flex h-[300px] flex-col items-center justify-center border-0 bg-background/20 p-4">
          No data for the selected period
          <p className="text-sm text-muted-foreground">
            Try selecting a different period
          </p>
        </Card>
      )}
    </SkeletonWrapper>
  );
};
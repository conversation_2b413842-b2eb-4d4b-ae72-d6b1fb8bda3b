"use server";

import { prisma } from "@/lib/db";

// Function to fetch budgets and calculate accumulated totals
export async function getBudgetData(userId: string, monthsRange: number) {
  try {
    const now = new Date();
    const startDateCurrent = new Date();
    startDateCurrent.setMonth(startDateCurrent.getMonth() - monthsRange);
    
    const startDatePrevious = new Date(startDateCurrent);
    startDatePrevious.setMonth(startDatePrevious.getMonth() - monthsRange);

    // Parallel data fetching
    const [currentBudgets, previousBudgets, expenses] = await Promise.all([
      prisma.budget.findMany({
        where: {
          userId,
          startDate: { 
            gte: startDateCurrent,
            lte: now 
          }
        },
        include: { 
          categories: { 
            include: { 
              category: true 
            } 
          } 
        },
      }),
      prisma.budget.findMany({
        where: {
          userId,
          startDate: { 
            gte: startDatePrevious, 
            lt: startDateCurrent 
          }
        },
        include: { 
          categories: { 
            include: { 
              category: true 
            } 
          } 
        },
      }),
      // Optional: Fetch actual expenses for comparison
      prisma.expense.findMany({
        where: {
          userId,
          date: {
            gte: startDateCurrent,
            lte: now
          }
        },
        include: {
          category: true
        }
      })
    ]);

    // Calculation logic remains the same
    const calculateTotal = (budgets: typeof currentBudgets) => {
      return budgets.reduce((sum, budget) => {
        const categoryTotal = budget.categories.reduce(
          (catSum, catBudget) => catSum + Number(catBudget.amount || 0),
          0
        );
        return sum + categoryTotal;
      }, 0);
    };

    const currentTotal = calculateTotal(currentBudgets);
    const previousTotal = calculateTotal(previousBudgets);

    // Aggregate budgets data
    const aggregatedData: Record<string, any> = {};
    
    currentBudgets.forEach((budget) => {
      const startDate = budget.startDate 
        ? budget.startDate.toISOString().split("T")[0] 
        : "N/A";
      
      if (!aggregatedData[startDate]) {
        aggregatedData[startDate] = { date: startDate };
      }

      budget.categories.forEach((categoryBudget) => {
        const categoryName = categoryBudget.category.name || "Unknown";
        const amount = Number(categoryBudget.amount || 0);

        aggregatedData[startDate][categoryName] = 
          (aggregatedData[startDate][categoryName] || 0) + amount;
      });
    });

    // Optional: Aggregate actual expenses
    const expenseData = expenses.reduce((acc, expense) => {
      const date = expense.date.toISOString().split("T")[0];
      const categoryName = expense.category?.name || "Unknown";

      if (!acc[date]) {
        acc[date] = { date };
      }

      acc[date][categoryName] = 
        (acc[date][categoryName] || 0) + Number(expense.amount);

      return acc;
    }, {} as Record<string, any>);

    const rawChartData = Object.values(aggregatedData);

    return {
      success: true,
      data: rawChartData,
      expenses: Object.values(expenseData), // Optional expense data
      totals: {
        currentTotal,
        previousTotal,
        difference: currentTotal - previousTotal,
      },
    };
  } catch (error) {
    console.error("Error fetching budget data:", error);
    return { success: false, error: "Failed to fetch budget data" };
  }
}
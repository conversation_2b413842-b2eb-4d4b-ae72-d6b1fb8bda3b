import CryptoJS from 'crypto-js';

// Secret key for encryption (use environment variables for production)
const SECRET_KEY = process.env.NEXT_PUBLIC_SECRET_KEY || 'your-secret-key';

// Encrypt data
export function encryptData(data: any): string {
  return CryptoJS.AES.encrypt(JSON.stringify(data), SECRET_KEY).toString();
}

// Decrypt data
export function decryptData(cipherText: string): any {
  const bytes = CryptoJS.AES.decrypt(cipherText, SECRET_KEY);
  return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
}

// Save to local storage securely
export const saveToLocalStorage = (key: string, value: any) => {
  try {
    // Remove any extra quotes if the value is already a string
    const cleanValue = typeof value === 'string' ? value : JSON.stringify(value);
    const encrypted = encryptData(cleanValue);
    localStorage.setItem(key, encrypted);
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

// Retrieve from local storage securely
export const getFromLocalStorage = (key: string) => {
  try {
    const encrypted = localStorage.getItem(key);
    if (!encrypted) return null;
    
    const decrypted = decryptData(encrypted);
    // Parse only if it's not already a string
    try {
      return JSON.parse(decrypted);
    } catch {
      // If parsing fails, return the raw decrypted string
      return decrypted;
    }
  } catch (error) {
    console.error('Error getting from localStorage:', error);
    return null;
  }
};


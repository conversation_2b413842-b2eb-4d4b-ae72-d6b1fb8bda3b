import { useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { useSpeechRecognition } from "@lobehub/tts/react";
import { Mic, StopCircle } from "lucide-react";
import { useAILanguage } from "@/hooks/useAILanguage";

type SpeechRecognitionProps = {
  input: string; // Pass the current input text from the Textarea
  setInput: (value: string) => void; // Update input in Textarea
  setStopTranscript: React.Dispatch<React.SetStateAction<(() => void) | null>>;
  className: string;
};

export const LOBETTS = ({ input, setInput, setStopTranscript, className }: SpeechRecognitionProps) => {
  const [selectedLanguage] = useAILanguage();
  const language = selectedLanguage?.value === "en-US" ? "en-US" : "zh-TW";

  const { text: transcript, start, stop, isLoading } = useSpeechRecognition(language, { autoStop: false });

  // References to manage accumulated text and the last transcript length
  const accumulatedText = useRef<string>(input); // Initialize with current input
  const lastTranscriptLength = useRef<number>(0); // Tracks the length of the last transcript

  // Update accumulated text with only the new part of the transcript
  useEffect(() => {
    if (!transcript) return;

    // Identify and append only the new portion of the transcript
    const newPart = transcript.slice(lastTranscriptLength.current);
    console.log("newPart", newPart)
    if (newPart) {
      accumulatedText.current += newPart; // Append new part
      setInput(accumulatedText.current);   // Update input with accumulated text
      lastTranscriptLength.current = transcript.length; // Track the new length
    }
  }, [transcript, setInput]);

  // Reset tracking and save the current input on stop
  useEffect(() => {
    setStopTranscript(() => () => {
      stop();
      lastTranscriptLength.current = 0; // Reset length tracking
    });
    accumulatedText.current = input; // Save current input as base
    //console.log("accumulatedText.current", accumulatedText.current)
  }, [stop, setStopTranscript, input]);
  
  //useEffect(() => {
  //  console.log("input", input)
  //  console.log("transcript", transcript)
  //  console.log("accumulatedText", accumulatedText)
  //  console.log("lastTranscriptLength", lastTranscriptLength)

  //}, [transcript, input, accumulatedText, lastTranscriptLength])
  
  return (
    <>
      {isLoading ? (
        <button
          type="button"
          className={cn(
            "absolute right-[0.45rem] items-center bg-amber-600 hover:bg-amber-700 text-white p-[0.4rem] rounded-full",
            className
          )}
          onClick={stop}
        >
          <StopCircle className="h-[1.35rem] w-[1.35rem] z-10 animate-pulse" />
        </button>
      ) : (
        <button
          type="button"
          className={cn(
            "absolute right-[0.45rem] items-center bg-amber-600 hover:bg-amber-700 text-white p-[0.4rem] rounded-full",
            className
          )}
          onClick={start}
        >
          <Mic className="size-5 z-10" />
        </button>
      )}
    </>
  );
};

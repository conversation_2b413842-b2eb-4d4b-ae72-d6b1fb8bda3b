import * as z from 'zod';
import { TaskStatus, ReminderBasis } from '@prisma/client';


export const createTaskSchema = z.object({
  name: z.string().trim().min(1, "Task name is required"),
  status: z.nativeEnum(TaskStatus, { required_error: "TaskStatus is required" }),
  workspaceId: z.string().trim().min(1, "Workspace ID is required"),
  projectId: z.string().trim().min(1, "Project ID is required"),
  startDate: z.coerce.date().optional(),
  dueDate: z.coerce.date(),
  assigneeId: z.string().trim().min(1, "Assignee ID is required"),
  description: z.string().optional(),
  reminders: z
    .array(
      z.object({
        id: z.string().optional(),
        enabled: z.boolean().default(false),
        basis: z.nativeEnum(ReminderBasis, { required_error: "Reminder basis is required" }),
        daysBefore: z.number().min(0, "Days before must be non-negative").optional(),
        customDate: z.coerce.date().nullable().optional(),
      })
    )
    .optional(),
});


export const updateTaskSchema = z.object({
  id: z.string(),
  name: z.string().trim().min(1, "Must be 1 or more characters").optional(),
  status: z.nativeEnum(TaskStatus, { required_error: "TaskStatus is required" }),
  projectId: z.string().trim().min(1, "Project ID is required"),
  startDate: z.coerce.date().optional(),
  dueDate: z.coerce.date(),
  assigneeId: z.string().trim().min(1, "Assignee ID is required"),
  description: z.string().optional(),
  reminders: z
    .array(
      z.object({
        id: z.string().optional(),
        enabled: z.boolean().default(false),
        basis: z.nativeEnum(ReminderBasis, { required_error: "Reminder basis is required" }),
        daysBefore: z.number().min(0, "Days before must be non-negative").optional(),
        customDate: z.coerce.date().nullable().optional(),
      })
    )
    .optional(),
});

export const bulkUpdateTasksSchema = z.object({
  tasks: z.array(
    z.object({
      id: z.string(), 
      status: z.nativeEnum(TaskStatus), 
      position: z.number().int().positive().min(1000).max(1_000_000)
    }))
})

export const calendarEventSchema = z.object({
  id: z.string().optional(),
  summary: z.string(),
  status: z.string().optional(),
  description: z.string(),
  start: z.object({
    dateTime: z.string(),
    timeZone: z.string(),
  }),
  end: z.object({
    dateTime: z.string(),
    timeZone: z.string(),
  }),
  allDay: z.boolean().optional(),
})


import { useState } from "react";
import axios from "axios";
import handleAccessTokenExpiration from "./handle-access-token-expiration";
import { getGoogleToken } from "@/actions/get-google-token";


const handleGoogleDriveShortcutLink = async (event: any) => {
  const [error, setError] = useState(null);
  const link = event.target;
  const mimeType = link.getAttribute("data-mime-type");
  const fileId = link.getAttribute("data-file-id");

  try {
    const { accessToken } = await getGoogleToken();
    if (!accessToken) {
      throw new Error("Unable to get a valid access token.");
    }

    if (mimeType === "application/vnd.google-apps.shortcut") {
      event.preventDefault();

      try {
        const driveRes = await axios.get(
          `https://www.googleapis.com/drive/v3/files/${fileId}`,
          {
            headers: { Authorization: `Bearer ${accessToken}` },
            params: {
              supportsAllDrives: true,
              fields: "shortcutDetails/targetId",
            },
          }
        );

        const targetId = driveRes.data.shortcutDetails.targetId;
        window.open(
          `https://docs.google.com/document/d/${targetId}/edit`,
          "_blank"
        );
      } catch (err: any) {
        if (err.response && err.response.status === 401) {
          // Handle expired token by refreshing it
          await handleAccessTokenExpiration();
        } else {
          setError(err);
        }
      }
    }
  } catch (err: any) {
    console.error("Error verifying token:", err);
    setError(err);
  }
};

export default handleGoogleDriveShortcutLink;

"use client"

import { useState, useEffect, useCallback } from "react"
import { ResponsiveModal } from "@/components/responsive-modal"
import { useGetFolders } from "./use-get-folders"
import { useCreateFolder } from "./use-create-folder"
import { useUploadFile } from "./use-upload-file"
import { GoogleDriveMenu } from "./google-drive-menu"
import { GoogleDriveAuthStatus } from "./google-drive-auth-status"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Loading } from "@/components/ui/loader"
import { DataTable } from "./data-table"
import { createColumns, type GoogleDriveItem } from "./columns"
import { toast } from "sonner";

interface GoogleDriveComponentProps {
  isOpen: boolean;
  onClose: () => void;
  selectedFiles: GoogleDriveItem[];
  onFileSelect?: (item: GoogleDriveItem) => void;
}

export default function GoogleDriveComponent({
  isOpen,
  onClose,
  selectedFiles,
  onFileSelect,
}: GoogleDriveComponentProps) {
  const [items, setItems] = useState<GoogleDriveItem[]>([]);
  const [currentFolder, setCurrentFolder] = useState<GoogleDriveItem | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<GoogleDriveItem[]>([])

  const { data, isLoading: isFetchFoldersLoading } = useGetFolders()

  const updateFolderStructure = useCallback(
    (structure: GoogleDriveItem[], newItem: GoogleDriveItem): GoogleDriveItem[] => {
      return structure.map((item) => {
        if (item.id === currentFolder?.id) {
          return {
            ...item,
            children: [...(item.children || []), newItem],
          }
        } else if (item.children) {
          return {
            ...item,
            children: updateFolderStructure(item.children, newItem),
          }
        }
        return item
      })
    },
    [currentFolder],
  )

  useEffect(() => {
    if (data) {
      console.log("currentFolder: ", currentFolder)
      setItems(currentFolder ? currentFolder.children || [] : data.structure)
    }
  }, [data, currentFolder])

  const onDrillDown = useCallback((item: GoogleDriveItem) => {
    if (item.type === "folder") {
      setCurrentFolder(item);
  
      setBreadcrumbs((prev) => {
        // Prevent duplicates by checking for existing IDs
        if (prev.some((breadcrumb) => breadcrumb.id === item.id)) {
          return prev;
        }
        return [...prev, item];
      });
  
      setItems(item.children || []);
    }
  }, []);
  

  const onBreadcrumbClick = useCallback(
    (item: GoogleDriveItem | null, index: number) => {
      if (index === 0) {
        // Navigating to the root
        setCurrentFolder(null);
        setBreadcrumbs([]);
        setItems(data?.structure || []);
      } else {
        // Navigate to the selected breadcrumb
        setCurrentFolder(item);
        setBreadcrumbs((prev) => prev.slice(0, index + 1)); // Trim breadcrumbs after the selected one
        setItems(item?.children || []); // Update items to reflect the selected folder
      }
    },
    [data]
  );

  const columns = createColumns({
    onDrillDown,
    currentFolder,
    setCurrentFolder,
    selectedFiles,
    onFileSelect: (item: GoogleDriveItem) => {
      // Download file and pass to parent
      const downloadFileForUpload = async () => {
        try {
          const response = await fetch(`/api/gdrive/${item.id}/download-item`);
          if (!response.ok) {
            toast.error(`HTTP error! status: ${response.status}`);
          }
          const blob = await response.blob();
          const convertedFile = new File([blob], item.name, { type: blob.type });
          onFileSelect?.(item); // Pass original GoogleDriveItem
  
          onClose();
        } catch (error) {
          console.error('File download error:', error);
        }
      };

      downloadFileForUpload();
    }
  });

  const disabled = isFetchFoldersLoading

  if (isFetchFoldersLoading && items?.length === 0) {
    return <Loading />
  }

  return (
    <ResponsiveModal open={isOpen} onOpenChange={onClose}>
      <Card>
        <CardContent className="px-3 py-2">
            <div className="space-y-0">
            <div className="flex items-center justify-between space-x-2">
                <div className="flex items-center space-x-2">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onBreadcrumbClick(null, 0)}
                    disabled={breadcrumbs.length === 0}
                >
                    Root
                </Button>
                {breadcrumbs.map((item, index) => (
                    <div key={item.id} className="flex items-center">
                    <span className="mx-0">/</span>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onBreadcrumbClick(item, index)} // Use the correct index
                        disabled={index === breadcrumbs.length - 1} // Disable the last breadcrumb (current folder)
                    >
                        {item.name}
                    </Button>
                    </div>
                ))}
                </div>
            </div>

            <DataTable columns={columns} data={items} />
            {items?.length === 0 && (
                <div className="flex items-center justify-center">
                <GoogleDriveAuthStatus />
                </div>
            )}
            </div>
        </CardContent>
      </Card>
    </ResponsiveModal>
  )
}


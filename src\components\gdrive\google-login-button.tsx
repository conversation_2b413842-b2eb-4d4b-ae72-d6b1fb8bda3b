'use client';
import config from "@/lib/config/env"

export default function GoogleLoginButton() {
  const handleLogin = () => {
    const state = generateOAuthState();
    sessionStorage.setItem('oauth_state', state);
    window.location.href = getGoogleOAuthURL(state);
  };

  return (
    <button 
      onClick={handleLogin}
      className="flex items-center justify-center gap-2 px-4 py-2 border rounded-lg"
    >
      Sign in with Google
    </button>
  );
}

export const generateOAuthState = () => {
  // Generate random state string
  return Math.random().toString(36).substring(2);
};

export const getGoogleOAuthURL = (state: string) => {
  const { publicClientId, redirectUri } = config.env.google
  const rootUrl = 'https://accounts.google.com/o/oauth2/v2/auth';
  
  if (!redirectUri || !publicClientId) {
    throw new Error('Missing required environment variables for Google OAuth');
  }

  const options = {
    redirect_uri: redirectUri,
    client_id: publicClientId,
    access_type: 'offline',
    response_type: 'code',
    prompt: 'consent',
    scope: [
      'https://www.googleapis.com/auth/drive.metadata.readonly',
      "https://www.googleapis.com/auth/drive.file",
      "https://www.googleapis.com/auth/drive"
    ].join(' '),
    state
  } as Record<string, string>; // Type assertion to satisfy URLSearchParams

  const qs = new URLSearchParams(options);
  return `${rootUrl}?${qs.toString()}`;
};
"use client";

import React, { useCallback, useRef, useState } from 'react'
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { 
  DndContext,
  useSensor,
  useSensors,
  PointerSensor,
 } from '@dnd-kit/core'
import { cn } from '@/lib/utils';
import { useSchematicEntitlement } from '@schematichq/schematic-react';
import { uploadPDF } from '@/actions/receipts/uploadPDF';
import { AlertCircle, CheckCircle, CloudUpload } from 'lucide-react';
import { Button } from '@/components/ui/button';


function PDFDropzone() {
  const sensors = useSensors(
    useSensor(PointerSensor),
  )
  const router = useRouter()
  const user = useUser()
  const [isDraggingOver, setIsDraggingOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const {
    value: isFeatureEnabled,
    featureUsageExceeded,
    featureAllocation,
  } = useSchematicEntitlement("scans")


  const handleFileUpload = useCallback(async (files: FileList | File[]) => {
    if (!user) {
      alert("Please sign in to upload a PDF")
      return
    }
    if (featureUsageExceeded) {
      alert("You have exceeded your PDF upload limit")
      return
    }
    setIsUploading(true)  
    const fileArray = Array.from(files);
    const pdfFiles = fileArray.filter(
      (file) => 
        file.type === "application/pdf" 
    )
    if (pdfFiles.length === 0) {
      alert("Please upload a valid PDF")
      setIsUploading(false)
      return
    }

    try {
      const newUploadedFiles: string[] = []
      for (const file of pdfFiles) {
        const formData = new FormData()
        formData.append("files", file)
        const result = await uploadPDF(formData)

        if (!result?.success) {
          throw new Error(result?.error || "An error occurred while uploading the PDF. Please try again.")
        }

        newUploadedFiles.push(file.name)        
      }

      setUploadedFiles((prev) => [...prev, ...newUploadedFiles])
      // Clear uploaded files list after 5 seconds
      setTimeout(() => {
        setUploadedFiles([])
      }, 5000)

      router.push("/receipts")
    } catch (error) {
      console.error("Error uploading PDF:", error)
      alert("An error occurred while uploading the PDF. Please try again.")
    } finally {
      setIsUploading(false)
    }
  }, [user, featureUsageExceeded, router])

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDraggingOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDraggingOver(false)
  }, [])
  
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDraggingOver(false)
    if (!user) {
      alert("Please sign in to upload a PDF")
      return
    }
    if (e.dataTransfer.files &&e.dataTransfer.files.length > 0) {
      handleFileUpload(e.dataTransfer.files)
    }
  }, [user, handleFileUpload])

  const triggerFileInput = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      handleFileUpload(files)
    }
  }, [handleFileUpload])

  const isUserSignedIn = !!user?.isSignedIn
  const canUpload = isUserSignedIn && isFeatureEnabled
  
  return (
    <DndContext sensors={sensors}>
      <div className="w-full max-w-md mx-auto">
        <div 
        onDragOver={canUpload ? handleDragOver : undefined}
        onDragLeave={canUpload ? handleDragLeave : undefined}
        onDrop={canUpload ? handleDrop : (e) => e.preventDefault()}
        className={cn("border-2 border-dashed border-gray-300 rounded-lg text-center transition-colors p-8", 
          isDraggingOver ? "border-blue-500" : "border-gray-300", 
          !canUpload ? "opacity-70 cursor-not-allowed" : "")}
        >
          {isUploading ? (
            <div className='flex flex-col items-center'>
              <div className='animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500 mb-2'>
                <p className='sr-only'>Uploading...</p>
              </div>
            </div>
          ) : !isUserSignedIn ? (
            <>
              <CloudUpload className='mx-auto size-12 text-gray-400' />
              <p className="mt-2 text-sm text-gray-600">
                Please sign in to upload files
              </p>
            </>
          ) : (
            <>
              <CloudUpload className='mx-auto size-12 text-gray-400' />
              <p className="mt-2 text-sm text-gray-600">
                Drag and drop your PDF files here, or click to select files
              </p>
              <input
                type="file"
                ref={fileInputRef}
                accept="application/pdf, .pdf"
                className="hidden"
                onChange={handleFileChange}
                multiple
              />
              <Button
                className="mt-4 px-4 y-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed hover:cursor-pointer"
                disabled={!isFeatureEnabled}
                onClick={triggerFileInput}
              >
                {isFeatureEnabled ? "Select Files" : "Upgrade to upload"}
              </Button>
            </>
          )}

          
        </div>

        <div className='mt-4'>
          {featureUsageExceeded && (
            <div className='flex items-center p-3 bg-red-50 border border-red-200 rounded-md text-red-600'>
              <AlertCircle className='size-5 mr-2 flex-shrink-0' />
              <span>
                You have exceeded your limit of {featureAllocation} scans.
                Please upgrade to continue.
              </span>
            </div>
          )}

          {uploadedFiles.length > 0 && (
            <div className='mt-4'>
              <h3 className='text-lg font-medium mb-2'>Uploaded Files</h3>
              <ul className='mt-2 text-sm text-gray-600 space-y-1'>
                {uploadedFiles.map((fileName, index) => (
                  <li key={index} className='flex items-center'>
                    <CheckCircle className='size-5 text-green-500 mr-2' />
                    {fileName}
                    </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </DndContext>
  )
}

export default PDFDropzone
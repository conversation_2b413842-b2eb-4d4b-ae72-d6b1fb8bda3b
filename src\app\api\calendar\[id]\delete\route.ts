"use server";

import { currentUser } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { google } from "googleapis";
import { calendarEventSchema } from '@/components/tasks/schemas';
import { z } from "zod";
import { NextRequest, NextResponse } from 'next/server';

type DeleteMeetingResponse = {
  success?: boolean;
  data?: any;
  error?: string;
  message?: string;
};

const SCOPES = [
  "https://www.googleapis.com/auth/calendar",
  "https://www.googleapis.com/auth/calendar.events",
];

const calendarId = process.env.QF_GOOGLE_CALENDAR_ID;
const initGoogleCalendar = async () => {
  try {
    // Replace escaped newlines with actual newlines
    const privateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n');

    const credentials = {
      client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
      client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      project_id: process.env.QF_GOOGLE_PROJECT_ID,
      private_key: privateKey
    }

    const auth = new google.auth.GoogleAuth({
      credentials,
      scopes: SCOPES,
    });

    const calendar = google.calendar({ version: "v3", auth });
    if (!calendar) {
      throw new Error("Failed to initialize Google Calendar");
    }
    return calendar;
  } catch (error) {
    console.error("Error initializing Google Calendar API:", error);
    throw error;
  }
};

export async function POST(
  req: NextRequest, 
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {

    const user = await currentUser();
    const userId = user?.id;
  
    if (!userId) {
        return NextResponse.json({
        error: "Unauthorized.",
        success: false
      });
    }

    const eventId = params.id;
    
    if (!eventId) {
      return NextResponse.json({
        error: "No event data provided.",
        success: false
      })
    }

    const calendar = await initGoogleCalendar();

    const deletedMeeting = (await calendar?.events.delete({
      calendarId: calendarId,
      eventId,

    }));

    if (!deletedMeeting) {
      return NextResponse.json({
        success: false,
        error: "Failed to deleted meeting"
      });
    }
    revalidatePath("/");

    return NextResponse.json({
      data: deletedMeeting, 
      success: true,
      message: "Meeting has been deletedd" 
    });

  } catch (error) {
    console.error('Error deleting meeting:', error);
    return NextResponse.json({ 
      success: false,
      error: error instanceof Error ? error.message : "Failed to deleted meeting"
    });
  }
}

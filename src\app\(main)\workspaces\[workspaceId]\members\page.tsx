import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { MembersList } from "@/components/workspaces/members-list";

interface WorkspaceIdMembersPageProps {
  params: Promise<{
    workspaceId: string;
  }>;
}

const WorkspaceIdMembersPage = async (
  props: WorkspaceIdMembersPageProps
) => {
  const user = await getCurrentUser();
  if (!user) redirect("/");
  const params = await props.params;

  return (
    <div className="w-full lg:max-w-xl">
      <MembersList workspaceId={params.workspaceId} />
    </div>
  )
}

export default WorkspaceIdMembersPage
"use client"

import Link from "next/link";
import { Member, User, Project, Workspace, Task } from "@prisma/client";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { useGetWorkspaceAnalytics } from "@/components/workspaces/use-get-workspace-analytics";
import { useCreateProjectModal } from "@/hooks/use-create-project-modal";
import { useCreateTaskModal } from "@/hooks/use-create-task-modal";
import { Analytics } from "@/components/analytics";
import { CalendarIcon, Loader, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { formatDistanceToNow } from "date-fns";
import { ProjectAvatar } from "@/components/projects/project-avatar";
import { MemberAvatar } from "@/components/workspaces/member-avatar";


type WorkspaceData = Workspace & {
  members: Array<Member & { user: User }>;
  projects: Project[];
  tasks: (Task & { project: Project | null })[];
};

interface WorkspaceIdClientProps {
  initialValues: WorkspaceData
}

export const WorkspaceIdClient = ({
    initialValues,
}: WorkspaceIdClientProps) => {
  const workspaceId = useWorkspaceId();
  const { data, isLoading } =  useGetWorkspaceAnalytics({
    workspaceId,
  })

  if (isLoading) return (
    <div className="w-full flex items-center justify-center"><Loader className="h-4 w-4 animate-spin" /></div>
  )

  if (!data || !initialValues) return null;

  return (
    <div className="h-full flex flex-col space-y-4">
      <Analytics data={data} />
      <div className="grid grid-col-1 lg:grid-cols-2 gap-4">
        <TaskList data={initialValues.tasks} total={initialValues.tasks.length} />
        <div>
          <ProjectList data={initialValues.projects} total={initialValues.projects.length} />
          <Separator className="py-2 bg-transparent" />
          <MemberList data={initialValues.members} total={initialValues.members.length} />          
        </div>

      </div>
    </div>
  )
}

interface TaskListProps {
  data: (Task & { project: Project | null })[];
  total: number;
}

export const TaskList = ({ data, total }: TaskListProps) => {
  const { open: createTask } = useCreateTaskModal();
  const workspaceId = useWorkspaceId();
  return (
    <div className="flex flex-col gap-y-2 col-span-1">
      <div className="bg-foreground/5 border rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-row items-center gap-x-2">
            <p className="text-md font-semibold">
              Tasks
            </p>
            <div className="size-5 flex items-center justify-center rounded-md bg-neutral-200 text-neutral-700 text-sm font-bold">
              {total}
            </div>
          </div>
          <Button variant="secondary" size="icon" onClick={createTask}>
            <Plus className="w-4 h-4" />
          </Button>
        </div>
        <Separator className="py-2 bg-transparent" />
        <ul className="flex flex-col pt-2 gap-y-2">
          {data && data.map((task) => (
            <li key={task.id}>
              <Link href={`/workspaces/${task.workspaceId}/tasks/${task.id}`}>
                <Card className="p-4">
                  <CardContent className="p-0 items-center gap-x-2">
                    <p className="text-md font-semibold truncate">{task.name}</p>
                    <div className="flex items-center gap-x-2">
                      <p className="text-sm font-semibold truncate">{task.project?.name}</p>
                      <div className="size-1 rounded-full bg-neutral-300" />
                      <div className="text-sm text-muted-foreground flex items-center">
                        <CalendarIcon className="size-3" />
                        <span className="bg-background px-2 text-muted-foreground">
                          {formatDistanceToNow(new Date(task.dueDate))}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </li>
          ))}
          <li className="text-sm text-muted-foreground text-center hidden first-of-type:block">No tasks found</li>
        </ul>
        <Button asChild variant="outline" size="icon" className="mt-4 w-full">
          <Link href= {`/workspaces/${workspaceId}/tasks`}>
            Show All
          </Link>
        </Button>
      </div>
    </div>
  )
}

interface ProjectListProps {
  data: Project[];
  total: number;
}

export const ProjectList = ({ data, total }: ProjectListProps) => {
  const { open: createProject } = useCreateProjectModal();
  const workspaceId = useWorkspaceId();
  return (
    <div className="flex flex-col gap-y-2 col-span-1">
      <div className="bg-foreground/5 border rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-row items-center gap-x-2">
            <p className="text-md font-semibold">
              Projects
            </p>
            <div className="size-5 flex items-center justify-center rounded-md bg-neutral-200 text-neutral-700 text-sm font-bold">
              {total}
            </div>
          </div>
          <Button variant="secondary" size="icon" onClick={createProject}>
            <Plus className="w-4 h-4" />
          </Button>
        </div>
        <Separator className="py-2 bg-transparent" />
        <ul className="grid grid-col-1 lg:grid-cols-2 gap-4">
          {data && data.map((project) => (
            <li key={project.id}>
              <Link href={`/workspaces/${project.workspaceId}/projects/${project.id}`}>
                <Card className="p-4">
                  <CardContent className="p-0 items-center gap-x-2">
                    <div className="flex flex-row items-center gap-x-2">
                      <ProjectAvatar
                        name={project.name}
                        image={project.imageUrl!}
                        className="size-4"
                        fallbackClassName="text-xs"
                      />
                      <p className="text-sm font-semibold">
                        {project.name}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

interface MemberListProps {
  data: Array<Member & { user: User }>;
  total: number;
}

export const MemberList = ({ data, total }: MemberListProps) => {
  const workspaceId = useWorkspaceId();
  
  return (
    <div className="flex flex-col gap-y-2 col-span-1">
      <div className="bg-foreground/5 border rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-row items-center gap-x-2">
            <p className="text-md font-semibold">
              Members
            </p>
            <div className="size-5 flex items-center justify-center rounded-md bg-neutral-200 text-neutral-700 text-sm font-bold">
              {total}
            </div>
          </div>
          <Button variant="secondary" size="icon">
            <Plus className="w-4 h-4" />
          </Button>
        </div>
        <Separator className="py-2 bg-transparent" />
        <ul className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {data.length > 0 ? (
            data.map((member) => (
              <li key={member.user.id}>
                <Link href={`/workspaces/${member.workspaceId}/members/${member.user.id}`}>
                  <Card className="p-4">
                    <CardContent className="p-0 items-center gap-x-2">
                      <div className="flex flex-row items-center gap-x-2">
                        <MemberAvatar
                          name={member.user.name || "No Name"}
                          image={member.user.image || ""}
                          className="size-4"
                          fallbackClassName="text-sm"
                        />
                        <p className="text-sm font-semibold">
                          {member.user.name || "No Name"}
                        </p>
                      </div>
                        <p className="indent-6 text-xs font-light text-muted-foreground">
                          {member.user.email || "No Email"}
                        </p>
                    </CardContent>
                  </Card>
                </Link>
              </li>
            ))
          ) : (
            <li className="text-sm text-muted-foreground text-center">
              No members found
            </li>
          )}
        </ul>
      </div>
    </div>
  );
};

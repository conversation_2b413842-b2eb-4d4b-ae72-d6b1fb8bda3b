"use client"


import { Loader, PlusIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@radix-ui/react-dropdown-menu";
import { useWorkspaceId } from "../workspaces/use-workspace-id";
import { useCreateSpeechModal } from "@/hooks/use-create-speech-modal";
import { useGetSpeeches } from "./use-get-speeches";
import { DataFilters } from "./data-filters";
import { useSpeechFilters } from "./use-speech-filters";
import { DataTable } from "./data-table";
import { columns } from "./columns";
import { SpeechWithRelations } from "./types";

interface SpeechViewerProps {
  initialProjectId?: string;
  hideProjectFilter?: boolean;
}

export const SpeechViewer = ({
  initialProjectId,
  hideProjectFilter,
}: SpeechViewerProps) => {
  const workspaceId = useWorkspaceId();
  const [{
    projectId,
    search,
    startDate,
    endDate,
  }] = useSpeechFilters();

  const { speeches, isLoading: isLoadingSpeechs } = useGetSpeeches({
    workspaceId,
    projectId: initialProjectId || projectId,
    search,
    startDate,
    endDate,
  });
  
  const { open } = useCreateSpeechModal();

  return (
      <div className="h-full flex flex-col overflow-auto p-4">
        <div className="flex flex-col gap-y-2 lg:flex-row justify-between items-center">
          <Button onClick={open} size="sm" variant="outline" className="w-full lg:w-auto">
            <PlusIcon className="size-4" />
            New
          </Button>
        </div>
        <Separator className="my-4"/>
        <DataFilters hideProjectFilter={hideProjectFilter} />
        <Separator className="my-4"/>
          {isLoadingSpeechs ? (
            <div className="w-full border rounded-lg h-[200px] flex flex-col items-center justify-center">
              <Loader className="size-5 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <DataTable<SpeechWithRelations, any>
              columns={columns}
              data={speeches}
            />          
          )}
      </div>
  ) 
}
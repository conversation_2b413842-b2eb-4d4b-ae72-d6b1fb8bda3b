'use client';

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog-spreadsheet"
import { Button } from "@/components/ui/button";
import { Spreadsheet } from "./BaseSpreadsheet";
import "@/app/(copilot)/spreadsheet/_components/styles.css";
import { FolderInput } from "lucide-react";

type Props = {
  accounts: {id: string; name: string; accountType: string }[]
  categories: {id: string; name: string }[]
}
export function SpreadsheetDialog({
  accounts,
  categories
}: Props) {

  return (
    <div>
      <Dialog modal={false} >
        <DialogTrigger asChild>
          <Button variant="outline">
            <FolderInput />匯入交易紀錄
          </Button>
        </DialogTrigger>
        <DialogContent 
          onInteractOutside={(e: any) => e.preventDefault()} 
          className="max-h-screen max-w-screen p-0 overflow-auto"
         >
          <DialogHeader>
          <DialogTitle></DialogTitle>
            <DialogDescription>
              
            </DialogDescription>
          </DialogHeader>
          <Spreadsheet bankAccounts={accounts} categories={categories!} />
          <DialogFooter className="sm:justify-start">
            <DialogClose className="fixed lef-0">
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

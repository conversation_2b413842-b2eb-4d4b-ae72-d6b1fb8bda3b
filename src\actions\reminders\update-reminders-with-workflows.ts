import { prisma } from "@/lib/db";
import { workflowClient } from "@/lib/workflow";
import { Reminder, Task, } from "@prisma/client";
import config from "@/lib/config/env";

export async function updateRemindersWithWorkflows(
  taskId: string,
  task: Task,
  updatedReminders: Reminder[],  
  assignerName: string,
  assignerEmail: string,
  timezone: string,
) {
  try {
    const existingReminders = await prisma.reminder.findMany({
      where: { taskId },
      include: { workflowRuns: true },
    });

    const existingReminderMap = new Map(existingReminders.map(r => [r.id, r]));

    for (const updatedReminder of updatedReminders) {
      const existingReminder = existingReminderMap.get(updatedReminder.id);

      if (existingReminder) {
        // Check for changes in the reminder
        const hasChanged =
          updatedReminder.enabled !== existingReminder.enabled ||
          updatedReminder.basis !== existingReminder.basis ||
          updatedReminder.daysBefore !== existingReminder.daysBefore ||
          updatedReminder.customDate?.toISOString() !==
            existingReminder.customDate?.toISOString();

        if (hasChanged) {
          // Cancel existing workflows
          if (existingReminder.workflowRuns.length > 0) {
            const workflowRunIds = existingReminder.workflowRuns.map(run => run.workflowRunId);
            await workflowClient.cancel({ ids: workflowRunIds });
          }
          

          // Trigger a new workflow
          const { workflowRunId } = await workflowClient.trigger({
            url: `${config.env.prodApiEndpoint}/api/workflows/reminding`,
            body: { reminder: updatedReminder, task, assignerName, assignerEmail, timezone },
          });

          // Update reminder in the database
          await prisma.reminder.update({
            where: { id: updatedReminder.id },
            data: {
              ...updatedReminder,
              workflowRuns: {
                create: { workflowRunId, status: "active" },
              },
            },
          });
        }
      } else {
        // New reminder logic
        const { workflowRunId } = await workflowClient.trigger({
          url: `${config.env.prodApiEndpoint}/api/workflows/reminding`,
          body: { reminder: updatedReminder, task, assignerName, assignerEmail, timezone },
        });

        await prisma.reminder.create({
          data: {
            ...updatedReminder,
            taskId,
            workflowRuns: {
              create: { workflowRunId, status: "active" },
            },
          },
        });
      }
    }

    // Handle deletions
    const updatedReminderIds = new Set(updatedReminders.map(r => r.id));
    for (const existingReminder of existingReminders) {
      if (!updatedReminderIds.has(existingReminder.id)) {
        // Cancel workflows and delete reminder
        if (existingReminder.workflowRuns.length > 0) {
          const workflowRunIds = existingReminder.workflowRuns.map(run => run.workflowRunId);
          await workflowClient.cancel({ ids: workflowRunIds });
        }

        await prisma.reminder.delete({ where: { id: existingReminder.id } });
      }
    }
  } catch (error) {
    console.error('Failed to update reminders and workflows:', error);
    throw error;
  }
}

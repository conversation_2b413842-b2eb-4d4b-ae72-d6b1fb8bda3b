import Events from "./constants"
import { 
  gemini,
  createNetwork,
  getDefaultRoutingAgent,
 } from "@inngest/agent-kit"
import { createServer } from "@inngest/agent-kit/server"
import { inngest } from "./client"
import { databaseAgent } from "./agents/databaseAgent"
import { receiptScanningAgent } from "./agents/receiptScanningAgent"

// Define the structure of the state data
interface AgentStateData {
  "saved-to-database"?: boolean;
  receipt?: any; // Replace 'any' with a more specific type if known
}

const agentNetwork = createNetwork<AgentStateData>({
  name: "Agent Team",
  agents: [
    databaseAgent, receiptScanningAgent
  ],
  defaultModel: gemini({
    model: "gemini-2.0-flash-exp",
    baseUrl: "https://generativelanguage.googleapis.com/v1beta/",
    defaultParameters: {
        generationConfig: {
          maxOutputTokens: 1000
        }
    },
  }),
  defaultRouter: ({ network }) => {
    const savedToDatabase = network.state.data["saved-to-database"]

    // Terminate the agent process if the receipt has already been saved to the database
    if (savedToDatabase) {
      return undefined
    }

    return getDefaultRoutingAgent()
  }
})

export const server = createServer({
  agents: [
    databaseAgent,
    receiptScanningAgent
  ],
  networks: [agentNetwork]
})

export const extractAndSavePDF = inngest.createFunction(
  { id: "Extract PDF and Save in Database",  retries: 3, },
  { event: Events.EXTRACT_DATA_FROMPDF_AND_SAVE_TO_DATABASE },
  async ({ event }) => {
    const result = await agentNetwork.run(
      `Extract the key data from this pdf: ${event.data.url}. Once the data has been extracted, save it to the database by using Database Agent with the receiptId: ${event.data.receiptId}.
      Once the receipt has been successfully saved to the database, You can terminate the agent process. Start with the Supervisor agent.`
    )
    return result.state.data.receipt
    //return result.state.kv.get("receipt")
  }
)

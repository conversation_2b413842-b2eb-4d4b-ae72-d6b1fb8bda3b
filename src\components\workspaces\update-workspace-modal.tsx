"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { User, Workspace } from "@prisma/client";
import { ResponsiveModal } from "@/components/responsive-modal";
import { UpdateWorkspaceForm } from "./update-workspace-form";
import { useUpdateWorkspaceModal } from "@/hooks/use-update-workspace-modal";
import { getWorkspace } from "@/actions/workspaces/get-workspace";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";


type UserWorkspace = {
  user: Pick<User, "id" | "name" | "email" | "image"> & {
    workspaces: Workspace[]; // user has access to multiple workspaces
    memberRoles: { workspaceId: string; role: string }[]; // User roles in those workspaces
  };
}

export const UpdateWorkspaceModal = () => {
  const { isOpen, close } = useUpdateWorkspaceModal();
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Get workspaceId from URL only when needed
  const workspaceId = isOpen ? useWorkspaceId() : null;
  // Fetch workspace data only once when modal opens
  const fetchWorkspace = async (id: string) => {
    setIsLoading(true);
    try {
      const response = await getWorkspace(id);
      if (response.success && response.data) {
        setWorkspace(Array.isArray(response.data) ? response.data[0] : response.data);
      }
    } catch (err) {
      console.error("Failed to fetch workspace:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle modal opening
  useEffect(() => {
    if (isOpen && workspaceId && !workspace) {
      fetchWorkspace(workspaceId);
    }
  }, [isOpen]); // Only depend on isOpen

  // Handle modal closing
  const handleClose = () => {
    close();
    setWorkspace(null);
    router.replace(`/workspaces/${workspaceId}`);
  };

  return (
    <ResponsiveModal 
      open={isOpen} 
      onOpenChange={(open) => {
        if (!open) handleClose();
      }}
    >
      {isLoading ? (
        <div>Loading workspace details...</div>
      ) : (
        workspace && (
          <UpdateWorkspaceForm 
            workspace={workspace} 
            onCancel={handleClose}
          />
        )
      )}
    </ResponsiveModal>
  );
};
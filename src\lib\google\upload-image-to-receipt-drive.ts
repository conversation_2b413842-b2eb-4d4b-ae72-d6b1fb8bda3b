import { google } from 'googleapis';
import { Readable } from 'stream';

export const uploadImageToReceiptDrive = async (buffer: Buffer, filename: string) => {
  try {
    // Use service account credentials
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/drive.file'],
    });

    const drive = google.drive({ version: "v3", auth });
    
    const fileMetadata: any = {
      name: filename,
      mimeType: 'image/jpeg',
    };
    if (process.env.GOOGLE_DRIVE_RECEIPTS_FOLDER_ID) {
      fileMetadata.parents = [process.env.GOOGLE_DRIVE_RECEIPTS_FOLDER_ID];
    }

    const media = {
      mimeType: 'image/jpeg',
      body: Readable.from(buffer),
    };

    const response = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id, webViewLink',
    });

    return {
      fileId: response.data.id,
      webViewLink: response.data.webViewLink
    };
  } catch (error) {
    console.error('Error uploading to Google Drive:', error);
    throw error;
  }
};

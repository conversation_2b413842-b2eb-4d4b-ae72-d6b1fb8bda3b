"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, SubmitHandler } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { startOfDay } from "date-fns";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Loader2 } from "lucide-react";
import { updateTimeDeposit } from "@/actions/time-deposits/update-deposit";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Pop<PERSON>, <PERSON>overContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";

const timeDepositSchema = z.object({
  id: z.string(),
  type: z.enum(["AVAILABLE", "WITHDRAWN"], { message: "Please select a valid type" }),
  amount: z.number().min(1, "Amount is required"),
  certificateNo: z.string().min(3, "Certificate Number is required"),
  period: z.string().optional(),
  interestRate: z.number().optional(),
  categoryId: z.string().min(3, "Please select a category"),
  accountId: z.string().min(1, "Please select an account"),
  description: z.string().optional(),
  date: z.date().default(startOfDay(new Date())),
});

export type TimeDepositFormValues = z.infer<typeof timeDepositSchema>;

type Props = {
  timeDeposit: TimeDepositFormValues;
  onClose: () => void;
};

export function UpdateTimeDepositComponent({ timeDeposit, onClose }: Props) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm({
    resolver: zodResolver(timeDepositSchema),
    defaultValues: timeDeposit,
  });

  const onSubmit: SubmitHandler<TimeDepositFormValues> = async (data) => {
    try {
      setIsLoading(true);

      const result = await updateTimeDeposit(data);

      if (!result.success) {
        toast.error("Failed to update time deposit.");
        return;
      }

      toast.success("Time Deposit updated successfully!");
      onClose();
    } catch (error) {
      toast.error("An error occurred while updating the time deposit.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Time Deposit</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Type Field */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="AVAILABLE">Available</SelectItem>
                        <SelectItem value="WITHDRAWN">Withdrawn</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Amount Field */}
              <FormField 
                control={form.control} 
                name="amount" 
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Amount</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} 
              />

              {/* Certificate Number Field */}
              <FormField 
                control={form.control} 
                name="certificateNo" 
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Certificate Number</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} 
              />

              {/* Period Field */}
              <FormField 
                control={form.control} 
                name="period" 
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Period</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., 6 months" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} 
              />

              {/* Date Field */}
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Interest Rate Field */}
              <FormField 
                control={form.control} 
                name="interestRate" 
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Interest Rate</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        {...field} 
                        onChange={(e) => field.onChange(e.target.value === '' ? undefined : parseFloat(e.target.value))}
                        value={field.value === undefined || field.value === null ? '' : field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )} 
              />     
            </div>
            
            {/* Description Field */}
            <FormField 
              control={form.control} 
              name="description" 
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Additional notes about the time deposit"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} 
            />
            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : "Update Time Deposit"}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
"use client"

import { useState } from "react";
import { TaskStatus } from "@prisma/client";
import type { TaskData } from "@/actions/tasks/get-task";
import { useUpdateTask } from "./use-update-task";
import { updateTaskSchema } from "./schemas";
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { type Reminder, ReminderForm } from "@/components/reminders/reminder-form";
import { Input } from '@/components/ui/input';
import { Textarea } from "@/components/ui/textarea";
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner"
import { Loader2 } from "lucide-react";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { MemberAvatar } from "../workspaces/member-avatar";
import { ProjectAvatar } from "../projects/project-avatar";

interface UpdateTaskFormProps {
  onCancel?: () => void;
  projectOptions: {id: string, name: string, imageUrl: string | null}[]
  memberOptions: {id: string, name: string | null}[]
  initialValues: TaskData;
}

type FormValues = z.infer<typeof updateTaskSchema>;

export const UpdateTaskForm = ({ 
  onCancel, 
  projectOptions, 
  memberOptions,
  initialValues
}: UpdateTaskFormProps) => {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: initialValues.task.startDate ? new Date(initialValues.task.startDate) : new Date(), 
    to: initialValues.task.dueDate ? new Date(initialValues.task.dueDate) : new Date(),
  });

  const [reminders, setReminders] = useState<Reminder[]>(initialValues.reminders);

  const { mutate, isPending } = useUpdateTask();

  const form = useForm<FormValues>({
    resolver: zodResolver(updateTaskSchema),
    defaultValues: {
      id: initialValues.task.id,
      name: initialValues.task.name,
      status: initialValues.task.status,
      projectId: initialValues.task.projectId,
      assigneeId: initialValues.task.assigneeId,
      startDate: dateRange?.from ? dateRange.from : undefined,
      dueDate: dateRange?.to ? dateRange.to : undefined,
      description: initialValues.task.description || undefined,
      reminders: [],
    },
  });

  const onSubmit = async (values: FormValues) => {
    const { from: startDate, to: dueDate } = dateRange || {};
  
    if (!startDate || !dueDate) {
      toast.error("Please select a valid date range.");
      return;
    }
  
    mutate({
      ...values,
      id: initialValues.task.id,
      startDate,
      dueDate,
      reminders,
    }, {
      onSuccess: ({ data }) => {
        form.reset();
        onCancel?.()
      }
    })
  }

  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7 py-4">
        <CardTitle className="text-xl font-bold">
          Update a new task
        </CardTitle>
      </CardHeader>
      <div className="px-7">
        <Separator />
      </div>
      <CardContent className="p-7">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                  <FormLabel>Task Name</FormLabel>
                  <FormControl>
                    <Input className="h-8" placeholder="Enter task name" {...field} />
                  </FormControl>
                  <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                  <FormLabel>Start to Due Date</FormLabel>
                  <FormControl>
                    <DateRangePicker
                      initialDateFrom={dateRange?.from}
                      initialDateTo={dateRange?.to}
                      showCompare={false}
                      onUpdate={(values) => {
                        const { from, to } = values.range;
                        if (from && to) {
                          setDateRange({ from, to });
                          // Update form values directly
                          form.setValue("startDate", from);
                          form.setValue("dueDate", to);
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="assigneeId"
                render={({ field }) => (
                  <FormItem>
                  <FormLabel>Assignee</FormLabel>
                    <Select
                      defaultValue={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select an assignee" />
                        </SelectTrigger>
                      </FormControl>
                      <FormMessage />
                      <SelectContent>
                        {memberOptions.map((member) => (
                          <SelectItem key={member.id} value={member.id}>
                            <div className="flex items-center gap-x-2">
                              <MemberAvatar className="size-4" name={member?.name!} />
                              {member.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                  <FormLabel>Status</FormLabel>
                    <Select
                      defaultValue={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <FormMessage />
                      <SelectContent>
                        <SelectItem value={TaskStatus.BACKLOG}>Backlog</SelectItem>
                        <SelectItem value={TaskStatus.IN_PROGRESS}>In Progress</SelectItem>
                        <SelectItem value={TaskStatus.IN_REVIEW}>In Review</SelectItem>
                        <SelectItem value={TaskStatus.TODO}>Todo</SelectItem>
                        <SelectItem value={TaskStatus.DONE}>Done</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="projectId"
                render={({ field }) => (
                  <FormItem>
                  <FormLabel>Project</FormLabel>
                    <Select
                      defaultValue={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select project" />
                        </SelectTrigger>
                      </FormControl>
                      <FormMessage />
                      <SelectContent>
                        {projectOptions.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            <div className="flex items-center gap-x-2">
                              <ProjectAvatar 
                                className="size-4" 
                                name={project?.name!}
                                image={project?.imageUrl!} 
                               />
                              {project.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="reminders"
                render={() => (
                  <FormItem>
                    <FormLabel>Remind Me</FormLabel>
                    <FormControl>
                      <ReminderForm 
                        reminders={reminders} 
                        setReminders={(newRemindersOrFunc) => {
                          // Handle both direct value and function updates
                          const newReminders = typeof newRemindersOrFunc === 'function' 
                            ? newRemindersOrFunc(reminders)
                            : newRemindersOrFunc;

                          const formattedReminders = newReminders.map((reminder: Reminder) => ({
                            ...reminder,
                            customDate: reminder.customDate || undefined
                          }));
                          
                          setReminders(newReminders);
                          form.setValue('reminders', formattedReminders);
                        }} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                  <FormLabel>Description</FormLabel>
                  <Textarea 
                    placeholder="Add task description (Optional)"
                    value={field.value}
                    rows={1}
                    onChange={field.onChange}
                  />
                  <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Separator className="px-7" />
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" size="sm" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" size="sm" disabled={isPending}>
                {isPending ? (
                  <><Loader2 className="h-4 w-4 animate-spin" /> Update Task</>
                ) : (
                  "Update Task"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>

  );
};
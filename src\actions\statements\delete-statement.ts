"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

export async function deleteStatement(
  statementId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    // 首先嘗試在 income 表中查找並刪除
    const income = await prisma.income.findUnique({
      where: { id: statementId },
    });

    if (income) {
      await prisma.income.delete({
        where: { id: statementId },
      });
    } else {
      // 如果在 income 表中找不到，則嘗試在 expense 表中查找並刪除
      const expense = await prisma.expense.findUnique({
        where: { id: statementId },
      });

      if (!expense) {
        throw new Error("Statement not found");
      }

      await prisma.expense.delete({
        where: { id: statementId },
      });
    }

    revalidatePath("/statements");
    return { success: true };
  } catch (error) {
    console.error("Error deleting statement:", error);
    return {
      success: false,
      error: "Failed to delete statement",
    };
  }
} 
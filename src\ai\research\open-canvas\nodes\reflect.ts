//import { Client } from "@langchain/langgraph-sdk";
import { prisma } from "@/lib/db";
import { OpenCanvasGraphAnnotation } from "../state";
import { LangGraphRunnableConfig } from "@langchain/langgraph";
import { ToolMessage, HumanMessage } from "@langchain/core/messages";
import { graph } from '../../reflection/index'
import { updateThread } from '@/actions/canvas/thread';

export const reflectNode = async (
  state: typeof OpenCanvasGraphAnnotation.State,
  config: LangGraphRunnableConfig
) => {
  try {
    /*const langGraphClient = new Client({
      apiUrl: `http://localhost:${process.env.PORT}`,
    });*/

    let modelMessages = [];
    for (let i = state._messages.length - 1; i >= 0; i--) {
      modelMessages.push(state._messages[i]);
      if (modelMessages.length >= 5) {
        if (!ToolMessage.isInstance(modelMessages[modelMessages.length - 1])) {
          break;
        }
      }
    }
    modelMessages.reverse();

    const reflectionInput = {
      messages: modelMessages,
      artifact: state.artifact,
    };
    const reflectionConfig = {
      configurable: {
        // Ensure we pass in the current graph's assistant ID as this is
        // how we fetch & store the memories.
        open_canvas_assistant_id: config.configurable?.assistant_id,
        thread_id: config.configurable?.thread_id,
      },
    };
    //console.log("reflectionInput==========", reflectionInput)

    /*const newThread = await prisma.thread.create({
      data: {
        status: "idle", // Provide a valid ThreadStatus value
        values: {},
        metadata: {},
      },
    });
    const newRun = await prisma.run.create({
      data: {
        thread_id: config.configurable?.thread_id,//newThread.thread_id,
        // Provide an existing assistant_id or a default value.
        assistant_id: config.configurable?.assistant_id!, 
        status: "pending", // Starting status for the run
        multitask_strategy: "enqueue", // According to your enum
        metadata: {}
      },
    });*/

    const reflectionOutput = await graph.invoke(reflectionInput, reflectionConfig);
    //const jsonResult = JSON.parse(JSON.stringify(reflectionOutput));
    console.log("///////////////////////reflectionOutput///////////////////////: ", reflectionOutput)
    if (reflectionOutput && reflectionOutput?.messages) {
      const plainMessages = reflectionOutput.messages.map(convertMessageToPlainObject);
      await updateThread(config.configurable?.thread_id, undefined, plainMessages);
    }
    
    /*await prisma.run.update({
      where: { run_id: newRun.run_id },
      data: {
        status: "success",
        metadata: {
          ...((typeof newRun.metadata === "object" && newRun.metadata !== null)
            ? (newRun.metadata as Record<string, unknown>)
            : {}),
          result: jsonResult,
        },
      },
    });*/

    //const newThread = await langGraphClient.threads.create();
    // Create a new reflection run, but do not `wait` for it to finish.
    // Intended to be a background run.
    /*await langGraphClient.runs.create(
      // We enqueue the memory formation process on the same thread.
      // This means that IF this thread doesn't receive more messages before `afterSeconds`,
      // it will read from the shared state and extract memories for us.
      // If a new request comes in for this thread before the scheduled run is executed,
      // that run will be canceled, and a **new** one will be scheduled once
      // this node is executed again.
      newThread.thread_id,
      // Pass the name of the graph to run.
      "reflection",
      {
        input: reflectionInput,
        config: reflectionConfig,
        // This memory-formation run will be enqueued and run later
        // If a new run comes in before it is scheduled, it will be cancelled,
        // then when this node is executed again, a *new* run will be scheduled
        multitaskStrategy: "enqueue",
        // This lets us "debounce" repeated requests to the memory graph
        // if the user is actively engaging in a conversation. This saves us $$ and
        // can help reduce the occurrence of duplicate memories.
        afterSeconds: 5 * 60, // 5 minutes
      }
    );*/

  } catch (e) {
    console.error("Failed to start reflection", e);
  }

  return {};
};

// Helper function to convert LangChain messages to plain objects
function convertMessageToPlainObject(message: any) {
  return {
    id: message.id,
    content: message.content,
    type: message instanceof HumanMessage ? 'human' : 'ai',
    // Add other necessary fields but keep the structure simple
  };
}

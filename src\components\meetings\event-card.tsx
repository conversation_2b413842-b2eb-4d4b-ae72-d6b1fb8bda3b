import { useState } from "react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { DeleteMeetingButton } from "@/components/meetings/delete-meeting-button";

interface EventCardProps {
  id: string;
  title: string;
  description: string;
  status: string;
  start: Date;
  end: Date;
}

export const EventCard = ({
  id,
  title,
  description,
  status,
  start,
  end,
}: EventCardProps) => {
  const [isAlertOpen, setIsAlertOpen] = useState<boolean>(false);
  return (
    <div className="px-2">
      <div 
        className={
          cn("p-1 text-xs text-primary border-0 border-b-indigo-700 rounded-md border-b-4 flex flex-col gap-y-1.5 cursor-pointer hover:opacity-75 transition"
      )}>
        <DeleteMeetingButton 
          meetingId={id} 
          isAlertOpen={isAlertOpen}
          setIsAlertOpen={setIsAlertOpen}
        />
        <span className="flex items-center text-[0.7rem] p-0" >
          {format(start.toLocaleString(), 'h:mm a')} - {format(end.toLocaleString(), 'h:mm a')}
        </span>
        <p>{title}</p>
      </div>
    </div>
  )
}
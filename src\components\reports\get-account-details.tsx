"use server";

import { prisma } from "@/lib/db";
import { Income, Expense } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";
import { format, parseISO, startOfMonth, endOfMonth, eachMonthOfInterval } from 'date-fns';

interface MonthlyData {
  [key: string]: number;
}

interface TableDataItem {
  description: string;
  monthlyData: MonthlyData;
  sum: number;
  percentage: number;
}

interface AccountDetails {
  account: {
    id: string;
    name: string;
    type: string;
    balance: number;
    accountNumber?: string;
    lastUpdated: Date;
  };
  transactions: {
    dateRange: string[];
    incomes: {
      tableData: TableDataItem[];
      monthlySums: MonthlyData;
      total: number;
    };
    expenses: {
      tableData: TableDataItem[];
      monthlySums: MonthlyData;
      total: number;
    };
  };
}

export async function getAccountDetails(
  accountId: string,
  startDate: Date,
  endDate?: Date,
): Promise<{ success: boolean; data?: AccountDetails; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const account = await prisma.bankAccount.findFirst({
      where: { id: accountId, userId },
      include: {
        Balance: {
          orderBy: { date: "desc" },
          take: 1,
        },
        Income: {
          where: { date: { gte: startDate, lte: endDate } },
          orderBy: { date: "asc" },
        },
        Expense: {
          where: { date: { gte: startDate, lte: endDate } },
          orderBy: { date: "asc" },
        },
      },
    });

    if (!account) {
      throw new Error("Account not found");
    }

    const incomes = account.Income;
    const expenses = account.Expense;

    // Determine the date range
    const allDates = [...incomes, ...expenses].map(item => item.date);
    const minDate = startOfMonth(new Date(Math.min(...allDates.map(d => d.getTime()))));
    const maxDate = endOfMonth(new Date(Math.max(...allDates.map(d => d.getTime()))));
    
    const dateRange = eachMonthOfInterval({ start: minDate, end: maxDate })
      .map(date => format(date, 'yyyy-MM'));

    // Helper to calculate monthly breakdown and sums
    const groupByDescription = (transactions: (Income | Expense)[]) => {
      const grouped = transactions.reduce((acc, transaction) => {
        const description = transaction.description || "Uncategorized";
        const monthKey = format(transaction.date, 'yyyy-MM');
        if (!acc[description]) {
          acc[description] = Object.fromEntries(dateRange.map(month => [month, 0]));
        }
        acc[description][monthKey] += Number(transaction.amount);
        return acc;
      }, {} as Record<string, MonthlyData>);

      return Object.entries(grouped).map(([description, monthlyData]) => {
        const sum = Object.values(monthlyData).reduce((a, b) => a + b, 0);
        return { description, monthlyData, sum };
      });
    };

    // Process data
    const incomeData = groupByDescription(incomes);
    const expenseData = groupByDescription(expenses);

    // Calculate totals and percentages for incomes
    const incomeTotal = incomeData.reduce((acc, item) => acc + item.sum, 0);
    const incomeMonthlySums = dateRange.reduce((acc, month) => {
      acc[month] = incomeData.reduce((sum, item) => sum + (item.monthlyData[month] || 0), 0);
      return acc;
    }, {} as MonthlyData);
    const incomeTableData = incomeData.map((item) => ({
      ...item,
      percentage: incomeTotal ? (item.sum / incomeTotal) * 100 : 0,
    }));

    // Calculate totals and percentages for expenses
    const expenseTotal = expenseData.reduce((acc, item) => acc + item.sum, 0);
    const expenseMonthlySums = dateRange.reduce((acc, month) => {
      acc[month] = expenseData.reduce((sum, item) => sum + (item.monthlyData[month] || 0), 0);
      return acc;
    }, {} as MonthlyData);
    const expenseTableData = expenseData.map((item) => ({
      ...item,
      percentage: expenseTotal ? (item.sum / expenseTotal) * 100 : 0,
    }));

    return {
      success: true,
      data: {
        account: {
          id: account.id,
          name: account.name,
          type: account.accountType,
          balance: account.Balance[0]?.amount || 0,
          accountNumber: account.originalId || undefined,
          lastUpdated: account.Income[0]?.date || account.updatedAt,
        },
        transactions: {
          dateRange,
          incomes: {
            tableData: incomeTableData,
            monthlySums: incomeMonthlySums,
            total: incomeTotal,
          },
          expenses: {
            tableData: expenseTableData,
            monthlySums: expenseMonthlySums,
            total: expenseTotal,
          },            
        },
      },
    };
  } catch (error) {
    console.error("Error in getAccountDetails:", error);
    return {
      success: false,
      error: "Failed to fetch account details",
    };
  }
}


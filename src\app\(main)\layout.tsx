import { Member } from "@prisma/client";
import { getWorkspaces } from "@/actions/workspaces/get-workspaces";
import { AppSidebar } from "@/components/app-sidebar";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
} from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Breadcrumbs } from "@/components/breadcrumbs";
import { CreateWorkspaceModal } from "@/components/workspaces/create-workspace-modal";
import { CreateProjectModal } from "@/components/projects/create-project-modal";
import { CreateTaskModal } from "@/components/tasks/create-task-modal";
import { CreateMeetingModal } from "@/components/meetings/create-meeting-modal";
import { UpdateMeetingModal } from "@/components/meetings/update-meeting-modal";
import { CreateSpeechModal } from "@/components/speeches/create-speech-modal";

interface DashboardLayoutProps {
  children?: React.ReactNode;
  params: Promise<{ 
    id: string
  }>
}

type Workspaces = { id: string; name: string; members: Member[] };

export default async function DashboardLayout(
  props: DashboardLayoutProps) {
    const response = await getWorkspaces();
    const workspaces = response?.data as Workspaces[] || [];
  return (
    <SidebarProvider>
      <CreateWorkspaceModal />
      <CreateProjectModal />
      <CreateTaskModal />
      <CreateMeetingModal />
      <UpdateMeetingModal />
      <CreateSpeechModal />
      <AppSidebar workspaces={workspaces} />
      <SidebarInset>
        <header id="no-print" className="flex h-12 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumbs />
          </div>
        </header>
        <main className="flex flex-1 flex-col gap-4 p-4 pt-0">{props.children}</main>
      </SidebarInset>
    </SidebarProvider>
  );
}

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, MoreVertical } from "lucide-react";
import { format } from "date-fns";
import { DepositAction } from "./deposit-actions";
import { TimeDeposit } from "./account-deposits";

export const columns: ColumnDef<TimeDeposit>[] = [
  {
    accessorKey: "certificateNo",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="px-0"
      >
        Certificate No.
        <ArrowUpDown className="h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => row.original.certificateNo || "N/A",
    enableGlobalFilter: true, 
  },
  {
    accessorKey: "date",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Date
        <ArrowUpDown className="h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => format(new Date(row.original.date), "MMM d, yyyy"),
    enableGlobalFilter: true, 
  },
  {
    accessorKey: "type",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Type
        <ArrowUpDown className="h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => row.original.type,
    enableGlobalFilter: true, 
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => row.original.description || "N/A",
    enableGlobalFilter: true, 
  },
  {
    accessorKey: "period",
    header: "Period",
    cell: ({ row }) => row.original.period || "N/A",
    enableGlobalFilter: true, 
  },
  {
    accessorKey: "interestRate",
    header: "Interest Rate",
    cell: ({ row }) =>
      row.original.interestRate !== null ? `${row.original.interestRate}%` : "N/A",
    enableGlobalFilter: true, 
  },
  {
    accessorKey: "amount",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="text-right w-full"
      >
        Amount
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) =>
      <div className="text-right">
        {(row.original.amount || 0).toLocaleString("zh-TW", {
          style: "currency",
          currency: row.original.currency || "TWD",
        })}
      </div>
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const id = row.original.id;
      return (
        <DepositAction id={id}>
          <Button variant={"ghost"} size="xs">
            <MoreVertical className="size-4" />
          </Button>
        </DepositAction>
      );
    },
  },
];

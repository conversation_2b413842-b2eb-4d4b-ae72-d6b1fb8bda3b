"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

export async function saveSpeechResult({
  speechId,
  text,
  language,
}: {
  speechId: string;
  text: string;
  language: string;
}) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }
    const transcription = await prisma.transcription.create({
      data: {
        speechId,
        text,
        language,
      },
    });

    return { success: true, data: transcription };
  } catch (error) {
    console.error('Failed to save transcription:', error);
    return { success: false, error: 'Failed to save transcription.' };
  }
}

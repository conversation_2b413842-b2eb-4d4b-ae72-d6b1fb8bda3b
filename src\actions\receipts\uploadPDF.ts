"use server"

import { api } from "@/convex/_generated/api"
import { auth } from "@clerk/nextjs/server"
import convex from "@/lib/convexClient"
import { getFileDownloadUrl } from "./getFileDownloadUrl"
import { inngest } from "@/inngest/client"
import Events from "@/inngest/constants"

export async function uploadPDF(formData: FormData) {
  const { userId } = await auth()
  if (!userId) {
    return {
      success: false,
      error: "Unauthorized",
    }
  }

  try {
    const file = formData.get("files") as File
    if (!file) {
      return {
        success: false,
        error: "No file uploaded",
      }
    }
    
    if (
      !file.type.includes("application/pdf") &&
      !file.name.toLowerCase().endsWith(".pdf")
    ) {
      return {
        success: false,
        error: "Only PDF files are allowed",
      }
    }
    // Get upload URL from Convex
    const uploadUrl = await convex.action(api.receipts.generateUploadUrl, {})

    // Convert file to ArrayBuffer for fetch API
    const arrayBuffer = await file.arrayBuffer()

    const uploadResponse = await fetch(uploadUrl, {
      method: "POST",
      headers: {
        "Content-Type": file.type,
      },
      body: new Uint8Array(arrayBuffer),
    })

    if (!uploadResponse.ok) {
      throw new Error(`Failed to upload file: ${uploadResponse.statusText}`)
    }

    // Get the storage Id from the response
    const { storageId } = await uploadResponse.json()

    // Create a new receipt to the database
    const receiptId = await convex.mutation(api.receipts.storeReceipt, {
      userId,
      fileId: storageId,
      fileName: file.name,
      size: file.size,
      mimeType: file.type,
    })

    // Generate the download URL
    const fileUrl = await getFileDownloadUrl(storageId)

    // Trigger inngest agent flow...
    await inngest.send({
      name: Events.EXTRACT_DATA_FROMPDF_AND_SAVE_TO_DATABASE,
      data: {
        receiptId,
        url: fileUrl.downloadUrl,
      }
    })

    return {
      success: true,
      data: {
        receiptId,
        fileName: file.name,
      }
    }
  } catch (error) {
    console.error("Error uploading PDF:", error)
    return {
      success: false,
      error: "An error occurred while uploading the PDF. Please try again.",
    }
  }
}

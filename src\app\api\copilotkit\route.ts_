import {
    CopilotRuntime,
    copilotRuntimeNextJSAppRouterEndpoint,
    langGraphPlatformEndpoint, copilotKitEndpoint,
    LangChainAdapter,
  } from "@copilotkit/runtime";
  import { Action } from "@copilotkit/shared";
  import { ChatGoogleGenerativeAI } from "@langchain/google-genai"
  import { NextRequest } from "next/server";
  
  const gemini = new ChatGoogleGenerativeAI({
    temperature: 0,
    model: "gemini-1.5-pro-002",
  })

  const llmAdapter = new LangChainAdapter({
    chainFn: async ({ messages, tools }) => {
      return gemini.bindTools(tools).stream(messages);
    },
  })

  const researchAction: Action<any> = {
    name: "research_agent",
    description:
      "Research agent",
    parameters: [
      {
        name: "research_agent",
        description: "Research agent",
      },
      {
        name: "research_agent_google_genai",
        description: "Research agent",
        assistantId: "9dc0ca3b-1aa6-547d-93f0-e21597d2011c",
      },
    ],
    handler: async ({ topic }) => {
      console.log("Researching topic: ", topic);
      return await researchWithLangGraph(topic);
    },
  }


  const langsmithApiKey = process.env.LANGSMITH_API_KEY as string
  
  export const POST = async (req: NextRequest) => {
    const actions: Action<any>[] = []
  
  if (
    process.env["TAVILY_API_KEY"] &&
    process.env["TAVILY_API_KEY"] !== "NONE"
  ) {
    actions.push(researchAction);
  }

  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime: new CopilotRuntime({ actions }),
    serviceAdapter: llmAdapter,
    endpoint: req.nextUrl.pathname,
  });

  return handleRequest(req);
};
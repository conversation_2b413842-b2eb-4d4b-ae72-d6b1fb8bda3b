'use client';

import React from 'react';
import cuid from "cuid";
import { useGetContractChats } from "./use-get-contract-chats"
import { Button } from '@/components/ui/button';
import { 
  Card, 
  CardContent, 
} from "@/components/ui/card";
import { Loader } from "lucide-react";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { MessageCircleIcon } from "lucide-react";
import Chat from "./chat";
import HistoryContainer from './history-container'

interface AIChatButtonProps {
  contractId: string;
  filePath?: string;
  children?: React.ReactNode;
  buttonVariant?: "default" | "outline" | "secondary" | "ghost";
  buttonSize?: "default" | "sm" | "lg" | "icon";
  dialogTitle?: string;
}

export function AIChatButton({
  contractId,
  filePath,
  children,
  buttonVariant = "outline",
  buttonSize = "icon",
  dialogTitle = "Ask AI About Your Contract",
}: AIChatButtonProps) {
  const assistantId = "cm5apqebj006fmm2vskl1p0dt"
  const { data: chats, isLoading: isGetContractChatLoading } = useGetContractChats({contractId, assistantId})
  const [chatId, setChatId] = React.useState<string>(() => {
    const storageKey = `chat_${contractId}_${filePath}`;
    return localStorage.getItem(storageKey) ?? "";
  });
  
  React.useEffect(() => {
    if (!isGetContractChatLoading && chats) {
      const storageKey = `chat_${contractId}_${filePath}`;
      const storedChatId = localStorage.getItem(storageKey);
  
      // Check if storedChatId exists in chats
      const chatExists = chats.some(chat => chat.id === storedChatId);
  
      let finalChatId = storedChatId && chatExists ? storedChatId : chats[0]?.id;
  
      if (!finalChatId) {
        finalChatId = cuid(); // Generate new ID if no valid chat exists
      }
  
      localStorage.setItem(storageKey, finalChatId);
      setChatId(finalChatId);
    }
  }, [chats, isGetContractChatLoading, contractId, filePath]);
  
  // Get messages based on the correct chat
  const selectedChat = chats?.find(chat => chat.id === chatId) //|| chats?.[0];
  const chatMessages = selectedChat?.messages || [];

  console.log("contractId, chatId", {contractId, chatId})
  //const { data: initialValues, isLoading: isGetChatLoading } = useGetContractChat({contractId, chatId})
  console.log("chats", chats)

  const isLoading = isGetContractChatLoading
  if (isLoading) {
    <Card className="w-full h-[714px] border-none shadow-none">
      <CardContent className="flex items-center jistify-center h-full">
        <Loader className="size-5 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>;
  }

  return (
    <Sheet modal={false}>
      <SheetTrigger asChild>
        {children || (
          <Button className="rounded-full" variant={buttonVariant} size={buttonSize}>
            <MessageCircleIcon className="h-4 w-4" />
            <span className="sr-only">{dialogTitle}</span>
          </Button>
        )}
      </SheetTrigger>
      <SheetContent
        side="right"
        className="sm:max-w-[420px] h-full"
        onInteractOutside={(e: any) => e.preventDefault()}
      >
        <SheetHeader>
          <SheetTitle className='flex flex-rows gap-x-2 underline'>
            {chats && (
              <HistoryContainer 
                assistantId={assistantId}
                contractId={contractId} 
                chats={chats}
                chatId={chatId}
                setChatId={setChatId}
                filePath={filePath!}
                path={`/contracts/${contractId}`} 
                location="header" 
              />              
            )}
            {dialogTitle}
          </SheetTitle>
        </SheetHeader>
        <div className="">
          <Chat 
            contractId={contractId!} 
            filePath={filePath!} 
            initMessages={chatMessages}
            chatId={chatId}
            setChatId={setChatId}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}

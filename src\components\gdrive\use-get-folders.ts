import { useQuery } from "@tanstack/react-query";

export const useGetFolders = () => {
  const { data, isLoading, refetch, error } = useQuery({
    queryKey: [
      "googledrive",
    ],
    queryFn: async () => {
      const response = await fetch(`/api/gdrive/folders`);

      const data = await response.json();
      console.log("data", data)

      if (!response.ok || !data.structure) {
        throw new Error(`Failed to get folders.`);
      }
      
      return data;
    },
  });

  return {
    data,
    isLoading,
    refetch,
    error,
  };
};
import { differenceInDays, format } from "date-fns";
import  { cn } from "@/lib/utils";

interface TaskDateProps {
  value: Date | null;
  className?: string;
}

export const TaskDate = ({ value, className }: TaskDateProps) => {
  const today = new Date();
  const dueDate = new Date(value!);
  const daysRemaining = differenceInDays(dueDate, today);

  let textColor = "text-muted-foreground";
  if (daysRemaining <= 3) {
    textColor = "text-red-500";
  } else if (daysRemaining <= 7) {
    textColor = "text-orange-500";  
  } else if (daysRemaining <= 14) {
    textColor = "text-yellow-500";
  }

  return (
    <div className={textColor}>
      <span className={cn("truncate", className)}>
        {format(value!, "PP")}
      </span>
    </div>
  )
}

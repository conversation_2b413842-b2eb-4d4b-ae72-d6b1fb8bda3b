import { Workspace } from "@prisma/client";
import { useQuery } from "@tanstack/react-query";
import { getWorkspaceAnalytics } from "@/actions/workspaces/analytics";


interface UseGetWorkspaceAnalyticsProps {
  workspaceId: string;
}

export const useGetWorkspaceAnalytics = ({ workspaceId }: UseGetWorkspaceAnalyticsProps) => {
    const { data, isLoading, error } = useQuery({
    queryKey: ["workspace-analytics", workspaceId],
    queryFn: async () => {
      const response = await getWorkspaceAnalytics(workspaceId);
      if (!response.success || !response.data) throw new Error("Failed to fetch workspaces");
      return response?.data;
    },
    enabled: !!workspaceId,
  })

  return { data, isLoading, error };
}
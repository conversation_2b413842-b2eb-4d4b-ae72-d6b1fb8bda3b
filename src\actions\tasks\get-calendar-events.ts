"use server"
import { GoogleAuth } from 'google-auth-library';

type CalendarEvent = {
  id: string;
  summary: string;
  status: string;
  description: string;
  start: { dateTime?: string; date?: string };
  end: { dateTime?: string; date?: string };
};

type CalendarEventsResponse = {
  items: CalendarEvent[];
};

export async function getCalendarEvents() {
  console.log("Service Account Email:", process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL);
  const auth = new GoogleAuth({
    credentials: {
      client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    },
    scopes: ['https://www.googleapis.com/auth/calendar.readonly'],
  });

  const client = await auth.getClient();
  const calendarId = process.env.QF_GOOGLE_CALENDAR_ID;
  const url = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events`;
  const response = await client.request<CalendarEventsResponse>({ url });
  const events = response.data.items.map((event) => ({
    id: event.id,
    summary: event.summary,
    status: event.status,
    description: event.status,
    start: new Date(event.start.dateTime ?? event.start.date ?? ''),
    end: new Date(event.end.dateTime ?? event.end.date ?? ''),
  }))

  return events;
}

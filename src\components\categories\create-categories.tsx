"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { createCategories } from "@/actions/categories/create-categories";
import { IconPicker } from "./icon-picker";
import { ColorPicker } from "./color-picker";
import { Plus, Loader2, Trash } from "lucide-react";

// Define schema for category creation
const createCategorySchema = z.object({
  categories: z
    .array(
      z.object({
        name: z
          .string()
          .min(2, "Category name must be at least 2 characters")
          .max(50, "Category name cannot exceed 50 characters"),
        icon: z.string().min(1, "Icon is required"),
        color: z.string().min(1, "Color is required"),
      })
    )
    .nonempty("At least one category must be added"),
});

type CreateCategoryFormValues = z.infer<typeof createCategorySchema>;

export function CreateCategoryDialog() {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CreateCategoryFormValues>({
    resolver: zodResolver(createCategorySchema),
    defaultValues: {
      categories: [{ name: "", icon: "Lightbulb", color: "hsl(var(--chart-1))" }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "categories",
  });

  async function onSubmit(data: CreateCategoryFormValues) {
    try {
      setIsLoading(true);

      // Handle creation of all categories
      for (const category of data.categories) {
        // Transform the category name to uppercase
        const categoryWithUppercaseName = {
          ...category,
          name: category.name.toUpperCase()
        };
        
        const result = await createCategories([categoryWithUppercaseName]);
        if (!result.success) {
          toast.error(`Failed to create category: ${category.name}`);
          return;
        }
      }

      toast.success("Categories created successfully!");
      setOpen(false);
      form.reset();
    } catch (error) {
      toast.error("Failed to create categories");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" className="px-1 py-2 text-orange-600">
          <Plus className="w-4 h-4 text-orange-600" /> Add Categories
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-screen sm:max-w-[600px] overflow-auto">
        <DialogHeader>
          <DialogTitle className="my-3">Create Categories</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="space-y-2 border-b pb-4">
                <FormField
                  control={form.control}
                  name={`categories.${index}.name`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter category name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-4">
                  <FormLabel>Select Icon</FormLabel>
                  <IconPicker
                    selectedIcon={form.watch(`categories.${index}.icon`)}
                    onIconSelect={(icon) =>
                      form.setValue(`categories.${index}.icon`, icon)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <FormLabel>Select Color</FormLabel>
                  <ColorPicker
                    selectedColor={form.watch(`categories.${index}.color`)}
                    onColorSelect={(color) =>
                      form.setValue(`categories.${index}.color`, color)
                    }
                  />
                </div>

                <div className="flex justify-end">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="text-red-700"
                    onClick={() => remove(index)}
                  >
                    <Trash color={"red"} className="w-4 h-4" />
                    Remove
                  </Button>
                </div>
              </div>
            ))}

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  append({ name: "", icon: "Lightbulb", color: "hsl(var(--chart-1))" })
                }
              >
                <Plus className="w-4 h-4 mr-2" />
                Add More
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Category"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

'use client'

import { useTransition, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>etHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { ChevronLeft, MessagesSquare } from 'lucide-react'
import { cn } from '@/lib/utils'
import { History as HistoryIcon } from 'lucide-react'
import { Suspense } from 'react'
import { HistorySkeleton } from './history-skeleton'

type HistoryProps = {
  location: 'sidebar' | 'header'
  children?: React.ReactNode
}

export function History({ location, children }: HistoryProps) {
  const router = useRouter()
  const [isMounted, setIsMounted] = useState(false);
  const [isPending, startTransition] = useTransition()

  const onOpenChange = (open: boolean) => {
    //if (open) {
    //  startTransition(() => {
        //router.refresh()
    //  })
    //}
  }

  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  if (!isMounted) {
    return null;
  }

  return (
    <Sheet modal={false}  defaultOpen={false} onOpenChange={onOpenChange} >
      <SheetTrigger asChild>
        <Button
          variant="default"
          size="xs"
          className={cn({
            'rounded-full text-foreground/30': location === 'sidebar'
          })}
        >
          {location === 'header' ? <MessagesSquare size={18} /> : <ChevronLeft size={18} />}
        </Button>
      </SheetTrigger>
      <SheetContent
        side={"left"}
        onInteractOutside={(e: any) => e.preventDefault()}
        className="w-68 border-0 rounded-tl-xl rounded-bl-xl"
      >
        <SheetHeader>
          <SheetTitle className="flex items-center gap-1 text-sm font-normal mb-2">
            <HistoryIcon size={14} />
            History
          </SheetTitle>
        </SheetHeader>
        <div className="my-2 h-full pb-12 md:pb-10">
          <Suspense fallback={<HistorySkeleton />}>{children}</Suspense>
        </div>
      </SheetContent>
    </Sheet>
  )
}
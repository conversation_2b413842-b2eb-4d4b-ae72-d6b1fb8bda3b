"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { getIconNameFromComponent, CATEGORIES } from "@/lib/config/categories";
import { $Enums } from "@prisma/client";

interface Category {
  id: string;
  name: string;
  icon: string;
}

export async function getCategories(): Promise<{
  success: boolean;
  data?: Category[];
  error?: string;
}> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    // Get categories from the database
    const categories = await prisma.category.findMany({
      where: { userId },
      select: {
        id: true,
        name: true,
        icon: true,
      },
    });
    //console.log("=============categories==================", categories)

    // If no categories exist, create them from defaults
    if (categories.length === 0) {
      const defaultCategories = await ensureDefaultCategories(userId);
      return {
        success: true,
        data: defaultCategories.map(category => ({
          id: category.id,
          name: category.name,
          icon: category.icon,
        })),
      };
    }

    return {
      success: true,
      data: categories,
    };
  } catch (error) {
    console.error("Error fetching categories:", error);
    return {
      success: false,
      error: "Failed to fetch categories",
    };
  }
}

export async function ensureDefaultCategories(userId: string) {
  // Create default categories from CATEGORIES
  const defaultCategories = await Promise.all(
    CATEGORIES.map(async (category) => {
      const dbCategory = await prisma.category.upsert({
        where: {
          name_userId: {
            name: category.name,
            userId,
          },
        },
        update: {}, // No updates if exists
        create: {
          name: category.name,
          icon: category.icon,
          color: category.color,
          keywords: category.keywords,
          type: category.type as $Enums.CategoryType,
          userId,
        },
      });
      return { ...dbCategory, ruleId: category.id };
    })
  );

  return defaultCategories;
}

export async function updateTransactionCategory(
  transactionId: string,
  categoryId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    // First ensure we have default categories and get the mapping
    const defaultCategories = await ensureDefaultCategories(userId);

    // Try to find the category directly in the database
    let dbCategory = await prisma.category.findFirst({
      where: {
        userId,
        id: categoryId,
      },
    });

    // If not found directly, try to find it from default categories mapping
    if (!dbCategory) {
      const matchingCategory = defaultCategories.find(
        (cat) => cat.ruleId === categoryId
      );

      if (!matchingCategory) {
        throw new Error("Category not found");
      }

      dbCategory = await prisma.category.findUnique({
        where: {
          id: matchingCategory.id,
        },
      });

      if (!dbCategory) {
        throw new Error("Category not found");
      }
    }

    // Update the transaction category
    await prisma.transaction.update({
      where: {
        id: transactionId,
        userId,
      },
      data: {
        categoryId: dbCategory.id,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating transaction category:", error);
    if (error instanceof Error) {
      return { success: false, error: error.message };
    }
    return {
      success: false,
      error: "Failed to update transaction category",
    };
  }
}

export async function updateStatementCategory(
  transactionId: string,
  categoryId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    // First ensure we have default categories and get the mapping
    const defaultCategories = await ensureDefaultCategories(userId);
    console.log("defaultCategories", defaultCategories);
    console.log("categoryId", categoryId);
    console.log("transactionId", transactionId);

    // Try to find the category directly in the database first
    let dbCategory = await prisma.category.findFirst({
      where: {
        userId,
        id: categoryId,
      },
    });

    // If not found directly, try to find it from default categories mapping
    if (!dbCategory) {
      const matchingCategory = defaultCategories.find(
        (cat) => cat.ruleId === categoryId
      );

      if (!matchingCategory) {
        throw new Error("Category not found");
      }

      dbCategory = await prisma.category.findUnique({
        where: {
          id: matchingCategory.id,
        },
      });

      if (!dbCategory) {
        throw new Error("Category not found in database");
      }
    }

    // First, try to find the transaction in the income table
    const income = await prisma.income.findUnique({
      where: { id: transactionId },
    });

    if (income) {
      // Update income record
      await prisma.income.update({
        where: { id: transactionId },
        data: {
          categoryId: dbCategory.id,
          categoryValidated: true,
        },
      });
    } else {
      // If not found in income, try expense table
      const expense = await prisma.expense.findUnique({
        where: { id: transactionId },
      });

      if (!expense) {
        throw new Error("Transaction not found");
      }

      // Update expense record
      await prisma.expense.update({
        where: { id: transactionId },
        data: {
          categoryId: dbCategory.id,
          categoryValidated: true,
        },
      });
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating statement category:", error);
    return {
      success: false,
      error: "Failed to update statement category",
    };
  }
}
'use server';

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { z } from "zod";
import { revalidatePath } from "next/cache";
import { cancelWorkflowRuns } from "@/actions/reminders/cancel-workfowruns";

const deleteSchema = z.object({
  taskId: z.string().min(1),
});

export async function deleteTask(input: z.infer<typeof deleteSchema>) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false,
      };
    }

    const validatedFields = deleteSchema.safeParse(input);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false,
      };
    }

    const { taskId } = validatedFields.data;

    // Fetch the task to ensure it exists and check workspace permissions
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        reminders: true, // Include reminders to access workflow runs
        workspace: true,
      },
    });

    if (!task) {
      return {
        error: "Task not found.",
        success: false,
      };
    }

    // Check if user is a member of the workspace associated with the task
    const member = await prisma.member.findFirst({
      where: {
        userId,
        workspaceId: task.workspaceId,
      },
    });

    if (!member) {
      return {
        error: "Unauthorized. You don't have access to this task.",
        success: false,
      };
    }

    // Cancel associated workflows
    const workflowRunIds = task.reminders
    .flatMap((reminder) => reminder.workflowRunId ? [reminder.workflowRunId] : [])
      .filter(Boolean); // Filter out undefined or null values

    if (workflowRunIds.length > 0) {
      await cancelWorkflowRuns(workflowRunIds); // Cancel the workflows using your helper
    }

    // Delete the task in a transaction
    const deletedTask = await prisma.$transaction(async (tx) => {
      // Delete the task along with related reminders
      await tx.reminder.deleteMany({
        where: { taskId },
      });

      return tx.task.delete({
        where: { id: taskId },
      });
    });

    revalidatePath(`/workspaces`);

    return {
      success: true,
      data: deletedTask,
      message: "Task deleted successfully.",
    };
  } catch (error) {
    console.error("Error deleting task:", error);
    return {
      error: "Failed to delete task.",
      success: false,
    };
  }
}

"use client";

import { useState } from "react";
import {
  Check,
  Building2,
  CreditCard,
  Wallet,
  MoreHorizontal,
  Loader2,
  LucideIcon,
  Plus,
  Edit,
  Trash,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { updateBankAccount } from "@/actions/banking/update-bank-account";
import { deleteBankAccount } from "@/actions/banking/delete-bank-account";
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON>it<PERSON>,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { AccountType } from "@prisma/client";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DatePicker } from "@/components/ui/date-picker";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Add imports for updating transactions and time deposits
import { createTransactions } from "@/actions/transactions/create-transaction";
import { createTimeDeposits } from "@/actions/time-deposits/create-deposits";

interface BankAccountSelectorProps {
  data: {
    bankAccounts: {
      id: string;
      name: string;
      type: string;
      originalPayload?: Record<string, any>;
      balance: number;
      percentage: number;
      accountNumber?: string;
      lastTransaction?: {
        amount: number;
        date: Date;
        description: string;
      };
      timeDeposits?: {
        id: string;
        certificateNo: string;
        period: string;
        interestRate: number;
        description: string;
        amount: number;
        type: "AVAILABLE" | "WITHDRAWN";
        date: Date;
      }[];
      transactions?: {
        id: string;
        description: string;
        amount: number;
        type: "CREDIT" | "DEBIT";
        date: Date;
      }[];
    }[];
  };
}

const iconMap = {
  BANK: Building2,
  INVESTMENT: CreditCard,
  CRYPTO: Wallet,
};

function getInitials(name: string): string {
  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
}

// Create new schemas for transactions and time deposits
const transactionSchema = z.object({
  id: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  amount: z.coerce.number().min(0, "Amount must be positive"),
  type: z.enum(["CREDIT", "DEBIT"]),
  date: z.date(),
});

const timeDepositSchema = z.object({
  id: z.string().optional(),
  certificateNo: z.string().min(1, "Certificate number is required"),
  period: z.string().min(1, "Period is required"),
  interestRate: z.coerce.number().min(0, "Interest rate must be positive"),
  description: z.string().min(1, "Description is required"),
  amount: z.coerce.number().min(0, "Amount must be positive"),
  type: z.enum(["AVAILABLE", "WITHDRAWN"]),
  date: z.date(),
});

// Extend the update schema
const updateBankAccountSchema = z.object({
  id: z.string().min(1, "Account ID is required"),
  name: z.string().min(2, "Account name must be at least 2 characters."),
  accountType: z.enum(["BANK", "SAVINGS", "INVESTMENT", "CRYPTO", "VIRTUAL"]),
  description: z.string().optional(),
  transactions: z.array(transactionSchema).optional(),
  timeDeposits: z.array(timeDepositSchema).optional(),
});

type UpdateBankAccountSchema = z.infer<typeof updateBankAccountSchema>;
type TransactionSchema = z.infer<typeof transactionSchema>;
type TimeDepositSchema = z.infer<typeof timeDepositSchema>;

function formatAccountDisplay(name: string, accountNumber: string) {
  const lastFour = accountNumber.slice(-4);
  return {
    name,
    displayNumber: `****${lastFour}`,
  };
}

// Add a helper function to get the appropriate icon
function getAccountIcon(type: AccountType): LucideIcon {
  switch (type) {
    case AccountType.SAVINGS:
      return Wallet;
    case AccountType.BANK:
    default:
      return Building2;
  }
}

export default function BankAccountSelector({
  data,
}: BankAccountSelectorProps) {
  const [selectedAccounts, setSelectedAccounts] = useState<string[]>(
    data.bankAccounts.map(account => account.id)
  );
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("account");
  const [selectedAccount, setSelectedAccount] = useState<{
    id: string;
    name: string;
    type: string;
    originalPayload?: Record<string, any>;
    transactions?: TransactionSchema[];
    timeDeposits?: TimeDepositSchema[];
  } | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [transactions, setTransactions] = useState<TransactionSchema[]>([]);
  const [timeDeposits, setTimeDeposits] = useState<TimeDepositSchema[]>([]);
  const [editingTransaction, setEditingTransaction] = useState<TransactionSchema | null>(null);
  const [editingTimeDeposit, setEditingTimeDeposit] = useState<TimeDepositSchema | null>(null);

  const form = useForm<UpdateBankAccountSchema>({
    defaultValues: {
      name: "",
      accountType: "BANK",
      description: "",
      transactions: [],
      timeDeposits: [],
    },
  });

  const transactionForm = useForm<TransactionSchema>({
    defaultValues: {
      description: "",
      amount: 0,
      type: "CREDIT",
      date: new Date(),
    },
  });

  const timeDepositForm = useForm<TimeDepositSchema>({
    defaultValues: {
      certificateNo: "",
      period: "",
      interestRate: 0,
      description: "",
      amount: 0,
      type: "AVAILABLE",
      date: new Date(),
    },
  });

  const toggleAccount = (accountId: string) => {
    setSelectedAccounts((prev) =>
      prev.includes(accountId)
        ? prev.filter((id) => id !== accountId)
        : [...prev, accountId]
    );
  };

  const totalBalance = data.bankAccounts
    .filter((account) => selectedAccounts.includes(account.id))
    .reduce((sum, account) => sum + account.balance, 0);

  const handleActionClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent row click when clicking actions
  };

  const handleUpdateAccount = async (account: {
    id: string;
    name: string;
    type: string;
    originalPayload?: Record<string, any>;
  }) => {
    try {
      const result = await updateBankAccount({
        id: account.id,
        name: account.name,
        accountType: account.type as "BANK" | "CRYPTO" | "INVESTMENT",
        description: account.originalPayload?.description
      });

      if (!result.success) {
        throw new Error(result.error as string);
      }

      toast.success("Account updated successfully");
    } catch (error) {
      console.error("Error updating account:", error);
      toast.error("Failed to update account");
    }
  };

  const handleDeleteAccount = async (accountId: string) => {
    try {
      setIsDeleting(true);
      const result = await deleteBankAccount({ id: accountId });

      if (!result.success) {
        throw new Error(result.error as string);
      }

      toast.success("Account deleted successfully");
    } catch (error) {
      console.error("Error deleting account:", error);
      toast.error("Failed to delete account");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleEditClick = (account: {
    id: string;
    name: string;
    type: string;
    originalPayload?: Record<string, any>;
  }) => {
    const accountData = data.bankAccounts.find(acc => acc.id === account.id);
    
    setSelectedAccount({
      ...account,
      transactions: accountData?.transactions || [],
      timeDeposits: accountData?.timeDeposits || []
    });
    
    form.reset({
      id: account.id,
      name: account.name,
      accountType: account.type as "BANK" | "SAVINGS" | "INVESTMENT" | "CRYPTO" | "VIRTUAL",
      description: account.originalPayload?.description,
    });
    
    setTransactions(accountData?.transactions || []);
    setTimeDeposits(accountData?.timeDeposits || []);
    
    // Set initial active tab based on account type
    if (account.type === "SAVINGS") {
      setActiveTab("timeDeposits");
    } else if (account.type === "BANK") {
      setActiveTab("transactions");
    } else {
      setActiveTab("account");
    }
    
    setEditDialogOpen(true);
  };

  const onSubmit = async (data: UpdateBankAccountSchema) => {
    try {
      setIsSubmitting(true);
      
      // Update bank account
      const result = await updateBankAccount({
        id: data.id,
        name: data.name,
        accountType: data.accountType,
        description: data.description
      });

      if (!result.success) {
        throw new Error(result.error as string);
      }
      
      // Update transactions if any exist and account type is BANK
      if (data.accountType === "BANK" && transactions.length > 0) {
        const txResult = await createTransactions({
          transactions: transactions.map(tx => ({
            ...tx,
            accountId: data.id,
          }))
        });
        
        if (!txResult.success) {
          throw new Error("Failed to update transactions");
        }
      }
      
      // Update time deposits if any exist and account type is SAVINGS
      if (data.accountType === "SAVINGS" && timeDeposits.length > 0) {
        const tdResult = await createTimeDeposits({
          timeDeposits: timeDeposits.map(td => ({
            ...td,
            accountId: data.id,
          }))
        });
        
        if (!tdResult.success) {
          throw new Error("Failed to update time deposits");
        }
      }

      toast.success("Account updated successfully");
      setEditDialogOpen(false);
      setSelectedAccount(null);
      form.reset();
      setTransactions([]);
      setTimeDeposits([]);
    } catch (error) {
      console.error("Error updating account:", error);
      toast.error("Failed to update account");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle transaction form submission
  const handleAddTransaction = (data: TransactionSchema) => {
    if (editingTransaction) {
      // Update existing transaction
      setTransactions(prev => 
        prev.map(tx => tx.id === editingTransaction.id ? data : tx)
      );
    } else {
      // Add new transaction
      setTransactions(prev => [...prev, { ...data, id: `temp-${Date.now()}` }]);
    }
    
    transactionForm.reset({
      description: "",
      amount: 0,
      type: "CREDIT",
      date: new Date(),
    });
    setEditingTransaction(null);
  };

  // Handle time deposit form submission
  const handleAddTimeDeposit = (data: TimeDepositSchema) => {
    if (editingTimeDeposit) {
      // Update existing time deposit
      setTimeDeposits(prev => 
        prev.map(td => td.id === editingTimeDeposit.id ? data : td)
      );
    } else {
      // Add new time deposit
      setTimeDeposits(prev => [...prev, { ...data, id: `temp-${Date.now()}` }]);
    }
    
    timeDepositForm.reset({
      certificateNo: "",
      period: "",
      interestRate: 0,
      description: "",
      amount: 0,
      type: "AVAILABLE",
      date: new Date(),
    });
    setEditingTimeDeposit(null);
  };

  // Functions to handle editing existing items
  const handleEditTransaction = (transaction: TransactionSchema) => {
    setEditingTransaction(transaction);
    transactionForm.reset(transaction);
  };

  const handleEditTimeDeposit = (timeDeposit: TimeDepositSchema) => {
    setEditingTimeDeposit(timeDeposit);
    timeDepositForm.reset(timeDeposit);
  };

  // Functions to handle deleting items
  const handleDeleteTransaction = (id: string) => {
    setTransactions(prev => prev.filter(tx => tx.id !== id));
  };

  const handleDeleteTimeDeposit = (id: string) => {
    setTimeDeposits(prev => prev.filter(td => td.id !== id));
  };

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <CardTitle>
            {data.bankAccounts[0].type === ("VIRTUAL" as AccountType)
              ? `Your Management Accounts`
              : `Your Bank Accounts`}
          </CardTitle>
          <CardDescription>
            Manage and monitor all your financial accounts in one place.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {data.bankAccounts.map((account) => {
              const Icon = getAccountIcon(account.type as AccountType);
              return (
                <div
                  key={account.id}
                  className={cn(
                    "flex items-center space-x-4 rounded-md border p-4",
                    selectedAccounts.includes(account.id) && "border-primary"
                  )}
                >
                  <Avatar className="h-10 w-10 rounded-lg">
                    <AvatarFallback className="rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                      <Icon className="h-5 w-5" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <Link
                        href={
                          account.type === ("VIRTUAL" as AccountType)
                            ? `/statements/${account.id}`
                            : `/banking/${account.id}`
                        }
                        className="text-sm font-medium leading-none hover:underline"
                      >
                        {account.name}
                      </Link>
                      {account.accountNumber && (
                        <span className="text-sm text-muted-foreground">
                          {
                            formatAccountDisplay(
                              account.name,
                              account.accountNumber
                            ).displayNumber
                          }
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {account.type.charAt(0) +
                        account.type.slice(1).toLowerCase()}
                    </p>
                    {account.lastTransaction && (
                      <p className="text-xs text-muted-foreground">
                        Last updated:{" "}
                        {formatDistanceToNow(account.lastTransaction.date, {
                          addSuffix: true,
                        })}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <span
                        className={cn(
                          "text-sm font-medium",
                          account.balance < 0 && "text-destructive"
                        )}
                      >
                        {account.type != "VIRTUAL" &&
                          account.balance.toLocaleString("zh-TW", {
                            style: "currency",
                            currency: "TWD",
                          })}
                      </span>
                    </div>
                    <Button
                      variant={
                        selectedAccounts.includes(account.id)
                          ? "secondary"
                          : "outline"
                      }
                      size="icon"
                      onClick={() => toggleAccount(account.id)}
                    >
                      <Check
                        className={cn(
                          "h-4 w-4",
                          selectedAccounts.includes(account.id)
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      <span className="sr-only">
                        {selectedAccounts.includes(account.id)
                          ? "Deselect"
                          : "Select"}{" "}
                        {account.name}
                      </span>
                    </Button>
                    <div onClick={handleActionClick}>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 p-0"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Link
                              href={
                                account.type === "VIRTUAL"
                                  ? `/statements/${account.id}`
                                  : account.type === "SAVINGS"
                                    ? `/banking/${account.id}`
                                    : `/banking/${account.id}`
                              }
                              className="flex w-full"
                            >
                              {account.type === "VIRTUAL" && "View Statements"}
                              {account.type === "SAVINGS" && "View Savings"}
                              {account.type === "CHECKING" &&
                                "View Transactions"}
                              {account.type !== "VIRTUAL" &&
                                account.type !== "SAVINGS" &&
                                account.type !== "CHECKING" &&
                                "View Transactions"}
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEditClick(account)}
                          >
                            Edit Account
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleDeleteAccount(account.id)}
                            disabled={isDeleting}
                          >
                            {isDeleting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Removing...
                              </>
                            ) : (
                              "Remove Account"
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex items-center space-x-2">
            <CreditCard className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              {selectedAccounts.length}{" "}
              {selectedAccounts.length === 1 ? "account" : "accounts"} selected
            </span>
          </div>
          {data.bankAccounts[0].type != "VIRTUAL" && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Total Balance:</span>
              <span
                className={cn(
                  "text-sm font-medium",
                  totalBalance < 0 && "text-destructive"
                )}
              >
                {totalBalance.toLocaleString("zh-TW", {
                  style: "currency",
                  currency: "TWD",
                })}
              </span>
            </div>
          )}
        </CardFooter>
      </Card>

      {/* Enhanced dialog with tabs for different account types */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Edit Account</DialogTitle>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="account">Account Details</TabsTrigger>
              {form.watch("accountType") === "BANK" && (
                <TabsTrigger value="transactions">Transactions</TabsTrigger>
              )}
              {form.watch("accountType") === "SAVINGS" && (
                <TabsTrigger value="timeDeposits">Time Deposits</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="account">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter account name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="accountType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Type</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={(value: AccountType) => {
                              field.onChange(value);
                              // Set appropriate tab when account type changes
                              if (value === "BANK") {
                                setActiveTab("transactions");
                              } else if (value === "SAVINGS") {
                                setActiveTab("timeDeposits");
                              } else {
                                setActiveTab("account");
                              }
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select account type" />
                            </SelectTrigger>
                            <SelectContent>
                              {selectedAccount?.type === "VIRTUAL" ? (
                                <>
                                  <SelectItem value={AccountType.VIRTUAL}>
                                    Virtual Account
                                  </SelectItem>
                                </>
                              ) : (
                                <>
                                  <SelectItem value={AccountType.BANK}>
                                    Checking Account
                                  </SelectItem>
                                  <SelectItem value={AccountType.SAVINGS}>
                                    Savings Account
                                  </SelectItem>
                                  <SelectItem value={AccountType.INVESTMENT}>
                                    Investment Account
                                  </SelectItem>
                                  <SelectItem value={AccountType.CRYPTO}>
                                    Crypto Account
                                  </SelectItem>
                                </>
                              )}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter account description"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setEditDialogOpen(false);
                        setSelectedAccount(null);
                        form.reset();
                        setTransactions([]);
                        setTimeDeposits([]);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Save Changes"
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </TabsContent>

            {/* Transactions Tab */}
            <TabsContent value="transactions">
              <div className="space-y-4">
                <Form {...transactionForm}>
                  <form
                    onSubmit={transactionForm.handleSubmit(
                      handleAddTransaction
                    )}
                    className="space-y-4 border p-4 rounded-md"
                  >
                    <h3 className="text-lg font-medium">
                      {editingTransaction
                        ? "Edit Transaction"
                        : "Add Transaction"}
                    </h3>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={transactionForm.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Transaction description"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={transactionForm.control}
                        name="amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Amount</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseFloat(e.target.value))
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={transactionForm.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Type</FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="CREDIT">Credit</SelectItem>
                                  <SelectItem value="DEBIT">Debit</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={transactionForm.control}
                        name="date"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Date</FormLabel>
                            <FormControl>
                              <DatePicker
                                date={field.value}
                                setDate={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end space-x-2">
                      {editingTransaction && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setEditingTransaction(null);
                            transactionForm.reset({
                              description: "",
                              amount: 0,
                              type: "CREDIT",
                              date: new Date(),
                            });
                          }}
                        >
                          Cancel
                        </Button>
                      )}
                      <Button type="submit">
                        {editingTransaction ? "Update" : "Add"} Transaction
                      </Button>
                    </div>
                  </form>
                </Form>

                {/* Transactions List */}
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Description</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {transactions.length === 0 ? (
                        <TableRow>
                          <TableCell
                            colSpan={5}
                            className="text-center text-muted-foreground"
                          >
                            No transactions added yet
                          </TableCell>
                        </TableRow>
                      ) : (
                        transactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>{transaction.description}</TableCell>
                            <TableCell>
                              {transaction.amount.toLocaleString("zh-TW", {
                                style: "currency",
                                currency: "TWD",
                              })}
                            </TableCell>
                            <TableCell>{transaction.type}</TableCell>
                            <TableCell>
                              {transaction.date.toLocaleDateString()}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() =>
                                  handleEditTransaction(transaction)
                                }
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() =>
                                  handleDeleteTransaction(transaction.id!)
                                }
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>

            {/* Time Deposits Tab */}
            <TabsContent value="timeDeposits">
              <div className="space-y-4">
                <Form {...timeDepositForm}>
                  <form
                    onSubmit={timeDepositForm.handleSubmit(
                      handleAddTimeDeposit
                    )}
                    className="space-y-4 border p-4 rounded-md"
                  >
                    <h3 className="text-lg font-medium">
                      {editingTimeDeposit
                        ? "Edit Time Deposit"
                        : "Add Time Deposit"}
                    </h3>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={timeDepositForm.control}
                        name="certificateNo"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Certificate Number</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Certificate number"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={timeDepositForm.control}
                        name="period"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Period</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g. 3 months, 1 year"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={timeDepositForm.control}
                        name="interestRate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Interest Rate (%)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseFloat(e.target.value))
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={timeDepositForm.control}
                        name="amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Amount</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseFloat(e.target.value))
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <FormField
                        control={timeDepositForm.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem className="col-span-1">
                            <FormLabel>Description</FormLabel>
                            <FormControl>
                              <Input placeholder="Description" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={timeDepositForm.control}
                        name="type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Type</FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="AVAILABLE">
                                    Available
                                  </SelectItem>
                                  <SelectItem value="WITHDRAWN">
                                    Withdrawn
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={timeDepositForm.control}
                        name="date"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Date</FormLabel>
                            <FormControl>
                              <DatePicker
                                date={field.value}
                                setDate={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end space-x-2">
                      {editingTimeDeposit && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setEditingTimeDeposit(null);
                            timeDepositForm.reset({
                              certificateNo: "",
                              period: "",
                              interestRate: 0,
                              description: "",
                              amount: 0,
                              type: "AVAILABLE",
                              date: new Date(),
                            });
                          }}
                        >
                          Cancel
                        </Button>
                      )}
                      <Button type="submit">
                        {editingTimeDeposit ? "Update" : "Add"} Time Deposit
                      </Button>
                    </div>
                  </form>
                </Form>

                {/* Time Deposits List */}
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Certificate No</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Period</TableHead>
                        <TableHead>Interest Rate</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {timeDeposits.length === 0 ? (
                        <TableRow>
                          <TableCell
                            colSpan={8}
                            className="text-center text-muted-foreground"
                          >
                            No time deposits added yet
                          </TableCell>
                        </TableRow>
                      ) : (
                        timeDeposits.map((timeDeposit) => (
                          <TableRow key={timeDeposit.id}>
                            <TableCell>{timeDeposit.certificateNo}</TableCell>
                            <TableCell>{timeDeposit.description}</TableCell>
                            <TableCell>{timeDeposit.period}</TableCell>
                            <TableCell>{timeDeposit.interestRate}%</TableCell>
                            <TableCell>
                              {timeDeposit.amount.toLocaleString("zh-TW", {
                                style: "currency",
                                currency: "TWD",
                              })}
                            </TableCell>
                            <TableCell>{timeDeposit.type}</TableCell>
                            <TableCell>
                              {timeDeposit.date.toLocaleDateString()}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() =>
                                  handleEditTimeDeposit(timeDeposit)
                                }
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() =>
                                  handleDeleteTimeDeposit(timeDeposit.id!)
                                }
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
}

import { ReminderB<PERSON> } from "@prisma/client";
import { DateTimePicker } from "@/components/ui/date-time-picker";
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { TrashIcon } from "lucide-react";

export type Reminder = { 
  enabled: boolean;
  basis: ReminderBasis;
  daysBefore?: number;
  customDate?: Date | null;
}

interface ReminderFormProps {
  reminders: Reminder[];
  setReminders: React.Dispatch<React.SetStateAction<Reminder[]>>;
}

export const ReminderForm: React.FC<ReminderFormProps> = ({ reminders, setReminders }) => {
  const addReminder = () => {
    setReminders((prev) => [
      ...prev,
      { enabled: false, basis: ReminderBasis.DUE_DATE, daysBefore: 1, customDate: null },
    ]);
  };

  const removeReminder = (indexToRemove: number) => {
    setReminders((prev) => prev.filter((_, index) => index !== indexToRemove));
  };

  const updateReminder = <K extends keyof Reminder>(index: number, field: K, value: Reminder[K]) => {
    setReminders((prev) => {
      const updated = [...prev];
      updated[index] = {
        ...updated[index],
        [field]: value,
      };
      return updated;
    });
  };

  return (
    <div className="space-y-4">
      {reminders.map((reminder, index) => (
        <div key={index} className="space-y-2 p-4 border rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox
                id={`enabled-${index}`}
                checked={reminder.enabled}
                onCheckedChange={(checked) => updateReminder(index, "enabled", checked as boolean)}
              />
              <Label htmlFor={`enabled-${index}`}>Enabled</Label>
            </div>
            <Button 
              type="button" 
              variant="ghost" 
              size="sm" 
              onClick={() => removeReminder(index)}
              className="text-destructive hover:text-destructive hover:bg-destructive/10"
            >
              <TrashIcon className="size-4" />
              Remove
            </Button>
          </div>
          
          <div className="space-y-1">
            <Label htmlFor={`basis-${index}`}>Basis</Label>
            <Select
              value={reminder.basis}
              onValueChange={(value) => updateReminder(index, "basis", value as ReminderBasis)}
            >
              <SelectTrigger id={`basis-${index}`} className="h-8">
                <SelectValue placeholder="Select basis" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={ReminderBasis.START_DATE}>Start Date</SelectItem>
                <SelectItem value={ReminderBasis.DUE_DATE}>Due Date</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-1">
            <Label htmlFor={`days-before-${index}`}>Days Before</Label>
            <Input
              className="h-8"
              id={`days-before-${index}`}
              type="number"
              value={reminder.daysBefore || 1}
              onChange={(e) => updateReminder(index, "daysBefore", Number(e.target.value))}
            />
          </div>
          
          <div className="space-y-1">
            <Label htmlFor={`custom-date-${index}`}>Custom Date (Optional)</Label>
            <DateTimePicker
              id={`custom-date-${index}`}
              date={reminder.customDate ? new Date(reminder.customDate) : undefined}
              onDateChange={(date) => updateReminder(index, "customDate", date)}
            />
          </div>
        </div>
      ))}
      <Button type="button" onClick={addReminder} variant="outline" className="h-8">
        Add Reminder
      </Button>
    </div>
  );
};


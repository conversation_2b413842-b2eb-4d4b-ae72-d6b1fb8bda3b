import { google } from 'googleapis';

type AutoPayResidentRecord = {
  residentId: string;
  amount: number;
};

export type AutoPayResidentData = {
  printDate?: string;
  processingDate?: string;
  organizationId?: string;
  googleDriveLink?: string;
  records: AutoPayResidentRecord[];
};

export const appendAutoPayResidentToSheet = async (data: AutoPayResidentData) => {
  try {
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });

    const sheets = google.sheets({ version: 'v4', auth });

    // Prepare rows for each record
    const rows = data.records.map((record: AutoPayResidentRecord) => [
      data.printDate || '',
      data.processingDate || '',
      data.organizationId || '',
      record.residentId || '',
      record.amount || '',
      data.googleDriveLink || ''
    ]);

    // Append all rows at once
    await sheets.spreadsheets.values.append({
      spreadsheetId: process.env.GOOGLE_AUTOPAY_RESIDENT_SHEETS_ID,
      range: 'AutoPayResident!A:E',
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values: rows
      },
    });

    const spreadsheetId = process.env.GOOGLE_AUTOPAY_RESIDENT_SHEETS_ID;
    const spreadsheetUrl = spreadsheetId
      ? `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit#gid=0`
      : undefined;

    console.log(`Successfully uploaded ${rows.length} auto-pay resident records to Google Sheets`);
    return {
      success: true,
      spreadsheetUrl,
      googleDriveLink: data.googleDriveLink || undefined,
      rowCount: rows.length
    };
  } catch (error) {
    console.error('Error uploading to Google Sheets:', error);
    throw error;
  }
};
"use client"

import Link from "next/link";
import { useQ<PERSON>y, useQueryClient } from '@tanstack/react-query';
import { useTransition, useState } from "react";
import { Member, Role } from "@prisma/client";
import { 
  <PERSON>, 
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  DropdownMenu, 
  DropdownMenuContent,
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ArrowLeftIcon, Loader2, MoreVerticalIcon } from "lucide-react";
import { getMembers } from "@/actions/workspaces/get-members";
import { deleteMember } from "@/actions/workspaces/delete-member";
import { updateMember } from "@/actions/workspaces/update-member";
import { toast } from "sonner";

type MembersWithUser = Member & { 
  user: { name: string | null; email: string | null } 
};

interface MembersListProps {
  workspaceId: string;
}

export const MembersList = ({ workspaceId }: MembersListProps) => {
  const queryClient = useQueryClient();
  const [selectedMember, setSelectedMember] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();
  
  const { data: members, isLoading, error } = useQuery({
    queryKey: ['members', workspaceId],
    queryFn: async () => {
      const response = await getMembers(workspaceId);
      if (!response.success || !response.data) {
        throw new Error('Failed to fetch members');
      }
      return response.data as MembersWithUser[];
    },
  });


  if (isLoading) return <div className="w-full flex justify-center"><Loader2 className="h-4 w-4 animate-spin" /></div>
  if (error instanceof Error) return <p>Error: {error.message}</p>;

  const handleUpdateMember = (memberId: string, role: Role) => {
    startTransition(async () => {
      try {
        const response = await updateMember(workspaceId, memberId, role);
        if (response.success && response.message) {
          toast.success(response.message);
          //window.location.reload();
          queryClient.invalidateQueries({ queryKey: ['members', workspaceId] });
        } else {
          toast.error(response.error || 'Failed to update member')
        }
      } catch (error) {
        console.error('Error updating member:', error);
      }
    })
  }

  const handleDeleteMember = (memberId: string) => {
    startTransition(async () => {
      try {
        const response = await deleteMember({ workspaceId, memberId });
        if (response.success && response.message) {
          toast.success(response.message);
          queryClient.invalidateQueries({ queryKey: ['members', workspaceId] });
          //window.location.reload();
        } else {
          toast.error(response.error || 'Failed to delete member')
        }
      } catch (error) {
        console.error('Error deleting member:', error);
      }    
    })
  }
  
  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex flex-row items-center gap-x-4 p-7 pt-0">
        <Button asChild variant="secondary" size="sm">
          <Link href={`/workspaces/${workspaceId}`}>
            <ArrowLeftIcon className="size-4" />
            Back
          </Link>
        </Button>
        <CardTitle className="text-xl font-bold">
          Members List
        </CardTitle>
      </CardHeader>
      <div className="px-7">
        <Separator />
      </div>
      <CardContent className="px-7 py-0">
        {isLoading ? (
          <div>Loading members...</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="h-8 p-2 px-4 text-sm">Name</TableHead>
                <TableHead className="h-8 p-2 px-4 text-sm">Email</TableHead>
                <TableHead className="w-fit h-8 p-2 text-sm text-center">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {members && members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="h-8 p-2 px-4 text-sm">{member.user.name || "Unnamed User"}</TableCell>
                  <TableCell className="h-8 p-2 px-4 text-sm">{member.user.email || "No Email"}</TableCell>
                  <TableCell className="w-fit h-8 p-2 text-sm text-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button className="ml-auto" variant="ghost" size="icon">
                        <MoreVerticalIcon className="size-4 text-muted-foreground" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent side="bottom" align="end">
                        <DropdownMenuItem
                          className="font-medium"
                          onClick={()=>{handleUpdateMember(member.id, Role.ADMIN)}}
                          disabled={isPending}
                        >
                          Set as Administrator
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="font-medium"
                          onClick={()=>{handleUpdateMember(member.id, Role.MEMBER)}}
                          disabled={isPending}
                        >
                          Set as Member
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="font-medium text-amber-700"
                          onClick={() => setSelectedMember(member.id)}
                          disabled={isPending}
                        >
                          Remove {member.user.name}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
                ))}
            </TableBody>
          </Table>
        )}
        {/* AlertDialog for Delete Confirmation */}
        <AlertDialog
          open={!!selectedMember}
          onOpenChange={(open) => !open && setSelectedMember(null)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove this member? This action cannot be
                undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel
                className="h-8"
                onClick={() => setSelectedMember(null)}
              >
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                className="h-8"
                onClick={() => {
                  if (selectedMember) {
                    handleDeleteMember(selectedMember);
                    setSelectedMember(null);
                  }
                }}
              >
                Confirm
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
      <CardFooter>
        <Separator />
      </CardFooter>
    </Card>
  )
}
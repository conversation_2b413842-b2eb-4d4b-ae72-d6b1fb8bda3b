import { useState  } from "react";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Download, PencilIcon, TrashIcon, UsersRound } from "lucide-react";
import { GoogleDriveItem } from "./columns";
import { useDeleteItem } from "./use-delete-item";
import { RenameGdriveItemModal } from './rename-gdrive-item-modal'
import { ShareGdriveItemModal } from './share-gdrive-item-modal'
import { DropdownMenuSeparator } from "@radix-ui/react-dropdown-menu";


interface ItemActionProps {
  item: GoogleDriveItem;
  currentFolder: GoogleDriveItem | null;
  setCurrentFolder: (item: GoogleDriveItem | null) => void;
  children: React.ReactNode;
}

export function ItemAction({ item, currentFolder, setCurrentFolder, children }: ItemActionProps) {
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const { mutate: deleteItemMutate, isPending: isDeleteItemLoading } = useDeleteItem();

  const handleDelete = async () => {
    try {
      deleteItemMutate(item.id, {
        onSuccess: () => {  
          // Update the current folder to remove the deleted item
          if (currentFolder) {
            const updatedChildren = currentFolder.children?.filter((child) => child.id !== item.id) || [];
            setCurrentFolder({
              ...currentFolder,
              children: updatedChildren,
            });
          }
        },
        onError: (error) => {
          console.error("Error deleting item:", error);
        },
      });
    } catch (error) {
      console.error("Error deleting item:", error);
    }
  };
  

  const handleDownload = async (item: GoogleDriveItem) => {
    if (item.type !== 'file') {
      toast.error("Can only download files");
      return;
    }
    try {
      const loadingToast = toast.loading("Preparing download...");
      // Get current origin
      const origin = window.location.origin;
      const downloadUrl = `${origin}/api/gdrive/${item.id}/download-item`;
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = item.name;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.dismiss(loadingToast);
      toast.success("Download started");
    } catch (error) {
      console.error('Download error:', error);
      toast.dismiss(); 
      toast.error("Failed to download file");
    }
  };

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {item.type === "file" && (
            <DropdownMenuItem
              onClick={() => handleDownload(item)}
              className="font-medium p-[10px]"
            >
              <Download className="size-4 mr-2 stroke-2" />
              Download
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={() =>  setShowRenameModal(true)}
            className="font-medium p-[10px]"
          >
            <PencilIcon className="size-4 mr-2 stroke-2" />
            Rename
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setShowShareModal(true)}
            className="font-medium p-[10px]"
          >
            <UsersRound className="size-4 mr-2 stroke-2" />
            Share
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleDelete}
            className="text-destructive focus:text-destructive font-medium p-[10px]"
            disabled={isDeleteItemLoading}
          >
            <TrashIcon className="size-4 mr-2 stroke-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <RenameGdriveItemModal
        isOpen={showRenameModal}
        onClose={() => setShowRenameModal(false)}
        item= {item}
        currentFolder={currentFolder}
        setCurrentFolder={setCurrentFolder}
      />
      <ShareGdriveItemModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        item={item}
      />
    </>
  );
}

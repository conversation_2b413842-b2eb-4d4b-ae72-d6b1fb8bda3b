import { useQuery } from "@tanstack/react-query";
import { getCalendarEvents} from '@/actions/tasks/get-calendar-events';

export const useGetGoogleCalendarEvents = () => {
  const { data, isLoading, error } = useQuery({
    queryKey: [
      "calendar-events",
    ],
    queryFn: async () => {
      const events = await getCalendarEvents();
      
      if (!events) {
        throw new Error("Failed to fetch events");
      }
      return events;
    },
  });

  return {
    data,
    isLoading,
    error,
  };
};
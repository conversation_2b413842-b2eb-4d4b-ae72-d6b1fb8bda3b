import { generateObject } from 'ai';
import { google as googleApi } from 'googleapis';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { messagingApi } from '@line/bot-sdk';
import { 
  receiptSchema, 
  autoPayResidentSchema, 
  expenseReportSchema, 
  ExpenseReport 
} from './schemas';

const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
});

const MAX_RETRIES = 3;
const RETRY_DELAY_MS = 1000;

export async function getMessageContentWithRetry(
  blobClient: messagingApi.MessagingApiBlobClient,
  messageId: string,
  retries = 0
) {
  try {
    return await blobClient.getMessageContent(messageId);
  } catch (error) {
    if (retries < MAX_RETRIES) {
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS * Math.pow(2, retries)));
      return getMessageContentWithRetry(blobClient, messageId, retries + 1);
    }
    throw error;
  }
}

export const analyzeAutoPayResidentImage = async (base64Image: string) => {
  const { object } = await generateObject({
    model: google('gemini-2.0-flash-exp'),
    schema: autoPayResidentSchema,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `請分析這張國泰世華商業銀行代扣繳費授權和款項案內容清單，並提取以下資訊：

1. 列印日期 (printDate) - 格式：YYYY年MM月DD日
2. 委託單位統編 (organizationId) - 數字編號
3. 交易記錄列表 (records)，每筆記錄包含：
   - 用戶代號 (userId) - 格式通常為 XX-XX-X
   - 金額 (amount) - 數字金額

請仔細識別表格中的每一筆交易記錄，確保：
- 正確提取用戶代號（在最右側欄位）
- 準確識別金額數字（移除逗號轉為數字）
- 計算或驗證合計金額是否正確
- 統計借方交易總筆數

若某項資訊無法清楚辨識，請根據上下文合理推測。`
          },
          {
            type: 'image',
            image: base64Image,
          }
        ],
      }
    ],
  });
  
  return object;
};

export async function analyzeExpenseReportImage(base64ImageWithPrefix: string): Promise<ExpenseReport> {
  if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
    console.error("GOOGLE_GENERATIVE_AI_API_KEY is not set.");
    throw new Error("GOOGLE_GENERATIVE_AI_API_KEY is not set.");
  }

  // The model expects a raw base64 string, not a data URL.
  // If your base64Image comes with a prefix like "data:image/jpeg;base64,", strip it.
  let base64Data = base64ImageWithPrefix;
  if (base64ImageWithPrefix.startsWith('data:')) {
    base64Data = base64ImageWithPrefix.split(',')[1];
  }

  try {
    const { object } = await generateObject({
      model: google('gemini-2.5-flash-preview-04-17'),
      schema: expenseReportSchema,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `請分析這張支出明細表圖片，並提取以下資訊，嚴格按照提供的 schema 結構輸出：
              
1.  **文件標題 (title)**: 圖片最上方的標題。
2.  **文件期間 (民國年/月) (document_period_minguo)**: 例如 "114年05月"。
3.  **表格欄位標頭 (table_headers)**: 表格中每一欄的名稱。
4.  **支出項目列表 (items)**: 表格中的每一行資料。每項包含：
    *   序號 (serial_no)
    *   會計科目 (account_item)
    *   支出摘要 (expense_summary)
    *   月份 (month_year): 例如 "114/05"。
    *   金額 (amount): 請確保為數字格式。
    *   付款方式 (payment_method): 根據圖片中的勾選或文字，從 '匯款', '領現', '轉帳', '自動扣款' 中選擇。注意，"匯款" 可能表示為 "匯款☑領現☐"，"領現" 可能表示為 "匯款☐領現☑"。
5.  **總計 (total)**: 包含：
    *   標籤 (label): 例如 "總合計"。
    *   金額 (amount): 總計金額，請確保為數字格式。
6.  **簽核人列表 (approvers)**: 圖片底部的簽核區域。每項包含：
    *   角色 (role): 例如 "主任委員", "監察委員", "財務委員", "承辦人"。
    *   姓名 (name): 如果圖片中簽核區域有姓名（可能來自印章或手寫），請提取；若無則為 null。
    *   是否有蓋章 (stamp_present): 如果該角色旁邊有印章，則為 true，否則為 false。

請盡可能準確地提取所有資訊。若某項資訊在圖片中不存在或無法辨識，請根據 schema 的要求處理（例如，字串欄位使用 null 或空字串，數值欄位使用 0 或 null，布林值根據情況判斷）。確保所有金額都是數字。`
            },
            {
              type: 'image',
              image: base64Data,
            }
          ],
        }
      ],
      // You might want to adjust temperature for more deterministic vs. creative output
      // temperature: 0.2, 
    });
    
    // The 'object' is already typed and validated by generateObject according to expenseReportSchema
    return object as ExpenseReport; // Cast to ExpenseReport for type safety in consuming code
  } catch (error) {
    console.error("Error analyzing expense report image with AI:", error);
    // Depending on your error handling strategy, you might re-throw,
    // return a default/error object, or handle it differently.
    if (error instanceof Error) {
      throw new Error(`Failed to analyze expense report image: ${error.message}`);
    }
    throw new Error("An unknown error occurred while analyzing the expense report image.");
  }
}

export const analyzeReceiptImage = async (base64Image: string) => {
  const { object } = await generateObject({
    model: google('gemini-2.0-flash-exp'),
    schema: receiptSchema,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `請分析這張收據圖片，並提取以下資訊：
            
1. 商家名稱 (merchantName)
2. 商家地址 (merchantAddress)
3. 商家聯絡資訊，如電話號碼 (merchantContact)
4. 交易日期 (transactionDate)
5. 總金額 (transactionAmount)
6. 貨幣單位 (currency)
7. 收據概要 - 簡短描述主要購買內容 (receiptSummary)
8. 購買項目列表 (items)，每項包含：
   - 品名 (name)
   - 數量 (quantity)
   - 單價 (unitPrice)
   - 總價 (totalPrice)

請盡可能準確地提取所有資訊。若某項資訊在圖片中不存在，請使用合理的推測或標示為"未提供"。對於items列表，請識別所有可見的購買項目。`
          },
          {
            type: 'image',
            image: base64Image,
          }
        ],
      }
    ],
  });
  
  return object;
};

export const initializeAutoPayResidentSheet = async () => {
  try {
    const auth = new googleApi.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });

    const sheets = googleApi.sheets({ version: 'v4', auth });
    
    // First check if we can access the spreadsheet
    const spreadsheet = await sheets.spreadsheets.get({
      spreadsheetId: process.env.GOOGLE_AUTOPAY_RESIDENT_SHEETS_ID, // Different sheet ID for auto-pay data
    });

    // Check if 'AutoPayResident' sheet exists or create it
    let sheetExists = false;
    let sheetId = 0;

    for (const sheet of spreadsheet.data.sheets || []) {
      if (sheet.properties?.title === 'AutoPayResident') {
        sheetExists = true;
        sheetId = sheet.properties.sheetId || 0;
        break;
      }
    }

    if (!sheetExists) {
      // Add the AutoPayResident sheet
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId: process.env.GOOGLE_AUTOPAY_RESIDENT_SHEETS_ID,
        requestBody: {
          requests: [
            {
              addSheet: {
                properties: {
                  title: 'AutoPayResident',
                  gridProperties: {
                    rowCount: 10000,
                    columnCount: 5
                  }
                }
              }
            }
          ]
        }
      });
    }

    // Set up headers
    const headers = [
      ['列印日期', '委託單位統編', '用戶代號', '金額', '圖片連結']
    ];

    await sheets.spreadsheets.values.update({
      spreadsheetId: process.env.GOOGLE_AUTOPAY_RESIDENT_SHEETS_ID,
      range: 'AutoPayResident!A1:E1',
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values: headers
      },
    });

    // Format headers
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId: process.env.GOOGLE_AUTOPAY_RESIDENT_SHEETS_ID,
      requestBody: {
        requests: [
          {
            repeatCell: {
              range: {
                sheetId: sheetId,
                startRowIndex: 0,
                endRowIndex: 1,
                startColumnIndex: 0,
                endColumnIndex: 5
              },
              cell: {
                userEnteredFormat: {
                  textFormat: { bold: true },
                  backgroundColor: { 
                    red: 0.8,
                    green: 0.9,
                    blue: 1.0
                  }
                }
              },
              fields: 'userEnteredFormat(textFormat,backgroundColor)'
            }
          }
        ]
      }
    });

    return true;
  } catch (error) {
    console.error('Error initializing AutoPayResident Google Sheets:', error);
    throw error;
  }
};

export const initializeReceiptSheet = async () => {
  try {
    const auth = new googleApi.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });

    const sheets = googleApi.sheets({ version: 'v4', auth });
    
    // First check if we can access the spreadsheet
    const spreadsheet = await sheets.spreadsheets.get({
      spreadsheetId: process.env.GOOGLE_RECEIPTS_SHEETS_ID,
    });

    // Check if 'Receipts' sheet exists or create it
    let sheetExists = false;
    let sheetId = 0;

    for (const sheet of spreadsheet.data.sheets || []) {
      if (sheet.properties?.title === 'Receipts') {
        sheetExists = true;
        sheetId = sheet.properties.sheetId || 0;
        break;
      }
    }

    if (!sheetExists) {
      // Add the Receipts sheet
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId: process.env.GOOGLE_RECEIPTS_SHEETS_ID,
        requestBody: {
          requests: [
            {
              addSheet: {
                properties: {
                  title: 'Receipts',
                  gridProperties: {
                    rowCount: 1000,
                    columnCount: 13
                  }
                }
              }
            }
          ]
        }
      });
    }

    // Set up headers
    const headers = [
      ['商家名稱', '地址', '聯絡方式', '交易日期', '總金額', '貨幣', '收據概要', 
       '品項名稱', '數量', '單價', '總價', '收據圖片連結', '記錄時間']
    ];

    await sheets.spreadsheets.values.update({
      spreadsheetId: process.env.GOOGLE_RECEIPTS_SHEETS_ID,
      range: 'Receipts!A1:M1',
      valueInputOption: 'USER_ENTERED',
      requestBody: {
        values: headers
      },
    });

    // Format headers
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId: process.env.GOOGLE_RECEIPTS_SHEETS_ID,
      requestBody: {
        requests: [
          {
            repeatCell: {
              range: {
                sheetId: sheetId,
                startRowIndex: 0,
                endRowIndex: 1,
                startColumnIndex: 0,
                endColumnIndex: 13
              },
              cell: {
                userEnteredFormat: {
                  textFormat: { bold: true },
                  backgroundColor: { 
                    red: 0.9,
                    green: 0.9,
                    blue: 0.9
                  }
                }
              },
              fields: 'userEnteredFormat(textFormat,backgroundColor)'
            }
          }
        ]
      }
    });

    return true;
  } catch (error) {
    console.error('Error initializing Google Sheets:', error);
    throw error;
  }
};

// Utility function to get date ranges
export function getDateRanges() {
  const now = new Date();
  const currentDay = now.getDay(); // 0 is Sunday, 1 is Monday, etc.
  
  // Get this week's range (Monday to Sunday)
  const monday = new Date(now);
  monday.setDate(now.getDate() - currentDay + (currentDay === 0 ? -6 : 1));
  monday.setHours(0, 0, 0, 0);
  
  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);

  // Get this month's range
  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  firstDayOfMonth.setHours(0, 0, 0, 0);
  
  const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  lastDayOfMonth.setHours(23, 59, 59, 999);

  return {
    today: now.toISOString().split('T')[0],
    thisWeek: {
      start: monday.toISOString().split('T')[0],
      end: sunday.toISOString().split('T')[0]
    },
    thisMonth: {
      start: firstDayOfMonth.toISOString().split('T')[0],
      end: lastDayOfMonth.toISOString().split('T')[0]
    }
  };
}

// Utility function to convert stream to buffer
export async function streamToBuffer(stream: any): Promise<Buffer> {
  const chunks: Buffer[] = [];
  for await (const chunk of stream) {
    chunks.push(typeof chunk === 'string' ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}

export function createProductFlexMessage(selectedItems: any[]) {
  const flexMessage = [
    {
      "data": selectedItems.map(item => ({
        type: "bubble" as const,
        "hero": {
          "type": "image",
          "url": `${item["images.0"]}.jpg`,
          "size": "full",
          "aspectRatio": "20:13",
          "aspectMode": "cover",
          "action": {
            "type": "uri",
            "uri": "https://line.me/"
          }
        },
        "body": {
          "type": "box",
          "layout": "vertical",
          "contents": [
            {
              "type": "text",
              "text": `${item.title}`,
              "weight": "bold",
              "size": "xl"
            },
            {
              "type": "box",
              "layout": "baseline",
              "margin": "md",
              "contents": [
                {
                  "type": "text",
                  "size": "md",
                  "color": "#EED202",
                  "margin": "none",
                  "flex": 0,
                  "text": `${item.ratingEmoji}`
                },
                {
                  "type": "text",
                  "text": `${item?.ratingEmoji2}-`,
                  "size": "md",
                  "color": "#999999",
                  "margin": "none",
                  "flex": 0
                },
                {
                  "type": "text",
                  "text": `${item.rating}`,
                  "size": "md",
                  "color": "#999999",
                  "margin": "none",
                  "flex": 0
                },
                {
                  "type": "text",
                  "text": `(${item["Number of Reviews"]})`,
                  "size": "md",
                  "color": "#999999",
                  "margin": "md",
                  "flex": 0
                }
              ]
            },
            {
              "type": "box",
              "layout": "vertical",
              "margin": "lg",
              "spacing": "sm",
              "contents": [
                {
                  "type": "box",
                  "layout": "vertical",
                  "spacing": "sm",
                  "contents": [
                    {
                      "type": "text",
                      "text": "產品介紹",
                      "color": "#aaaaaa",
                      "size": "lg",
                      "flex": 1
                    },
                    {
                      "type": "text",
                      "text": `${item.description}`,
                      "wrap": true,
                      "color": "#666666",
                      "size": "md",
                      "flex": 5
                    }
                  ]
                },
                {
                  "type": "box",
                  "layout": "vertical",
                  "spacing": "sm",
                  "contents": [
                    {
                      "type": "text",
                      "text": "剩餘數量",
                      "color": "#aaaaaa",
                      "size": "lg",
                      "flex": 1
                    },
                    {
                      "type": "text",
                      "text": `${item.inStock}`,
                      "wrap": true,
                      "color": "#666666",
                      "size": "lg",
                      "flex": 5
                    }
                  ],
                  "margin": "lg"
                }
              ]
            }
          ]
        },
        "footer": {
          "type": "box",
          "layout": "vertical",
          "spacing": "sm",
          "contents": [
            {
              "type": "button",
              "style": "link",
              "height": "sm",
              "action": {
                "type": "uri",
                "label": "立即下訂",
                "uri": "https://line.me/"
              }
            },
            {
              "type": "box",
              "layout": "vertical",
              "contents": [],
              "margin": "sm"
            }
          ],
          "flex": 0
        }
      }))
    }
  ];
  return flexMessage;
}
'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

export async function getWorkspaceName(workspaceId?: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return null
    }
    // Get a single workspace with its members (users)
    const workspace = await prisma.workspace.findUnique({
      where: { id: workspaceId },
      select: { id: true, name: true }  
    });

    if (!workspace) {
      return null
    }

    console.log("workspace", workspace);
    return workspace;

  } catch (error) {
    console.error('Error fetching workspace:', error);
    return null
  }
}


"use client"
import Head from 'next/head'
import styles from '@/components/gdrive/home.module.css'
import HeaderImage from '@/components/gdrive/header-image';
import GoogleDriveSearch from '@/components/gdrive/google-drive-search'
import SimpleSignOn from '@/components/gdrive/simple-signon'
import PlayBookFolders from '@/components/gdrive/playbook-folders';
import PlayBookFiles from '@/components/gdrive/playbook-files';
import FolderName from '@/components/gdrive/folder-name';


export default function Drilldown() {
  return (
    <div className={styles.container}>
      <Head>
        <title>Create Next App</title>
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <SimpleSignOn>
        <main className={styles.main}>
          <HeaderImage />
          <GoogleDriveSearch />
          <FolderName />
          <PlayBookFolders />
          <PlayBookFiles />
        </main>
        <footer className={styles.footer}>
        </footer>
      </SimpleSignOn>
    </div>
  )
}
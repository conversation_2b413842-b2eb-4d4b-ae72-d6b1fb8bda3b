"use client"

import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog"
import { useContractStore } from "@/store/zustand"
import { AnimatePresence, motion } from "framer-motion"
import { Brain, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useRouter } from "next/navigation"
import { useUploadFile } from "./use-upload-file"
import { FileUpload } from "./file-upload"

interface IUploadModalProps {
  isOpen: boolean
  onClose: () => void
  onUploadComplete: () => void
}

export function UploadModal({ isOpen, onClose, onUploadComplete }: IUploadModalProps) {
  const { setAnalysisResults } = useContractStore()
  const router = useRouter()

  const {
    file,
    detectedType,
    error,
    step,
    isDetecting,
    isProcessing,
    handleFileSelect,
    handleFileUpload,
    handleAnalyzeContract,
    reset,
  } = useUploadFile({
    onUploadComplete,
    onError: (error: any) => console.error("Upload error:", error),
  })

  const handleClose = () => {
    onClose()
    reset()
  }

  const renderContent = () => {
    switch (step) {
      case "upload": {
        return (
          <FileUpload
            onFileSelect={handleFileSelect}
            onAnalyze={handleFileUpload}
            isProcessing={isDetecting || isProcessing}
          />
        )
      }
      case "detecting": {
        return (
          <AnimatePresence>
            <motion.div className="flex flex-col items-center justify-center py-8">
              <Loader2 className="size-16 animate-spin text-primary" />
              <p className="mt-4 text-lg font-semibold">Detecting contract type...</p>
            </motion.div>
          </AnimatePresence>
        )
      }
      case "confirm": {
        return (
          <AnimatePresence>
            <motion.div>
              <div className="flex flex-col space-y-4 mb-4">
                <p>
                  We have detected the following contract type:
                  <span className="font-semibold"> {detectedType}</span>
                </p>
                <p>Would you like to analyze this contract with our AI?</p>
              </div>
              <div className="flex space-x-4">
                <Button onClick={handleAnalyzeContract}>Yes, I want to analyze it</Button>
                <Button onClick={() => handleFileSelect(null)} variant="outline" className="flex-1">
                  No, Try another file
                </Button>
              </div>
            </motion.div>
          </AnimatePresence>
        )
      }
      case "processing": {
        return (
          <AnimatePresence>
            <motion.div className="flex flex-col items-center justify-center py-8">
              <motion.div
                animate={{
                  scale: [1, 1.2, 1],
                  rotate: [0, 360],
                }}
                transition={{
                  duration: 3,
                  repeat: Number.POSITIVE_INFINITY,
                  ease: "easeInOut",
                }}
              >
                <Brain className="size-20 text-primary" />
              </motion.div>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="mt-6 text-lg font-semibold text-gray-700"
              >
                AI is analyzing your contract...
              </motion.p>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
                className="mt-2 text-sm text-gray-700"
              >
                This may take some time.
              </motion.p>
              <motion.div
                className="w-64 h-2 bg-gray-200 rounded-full mt-6 overflow-hidden"
                initial={{ width: 0 }}
                animate={{ width: "100%" }}
                transition={{ duration: 10, ease: "linear" }}
              >
                <motion.div
                  className="h-full bg-primary"
                  initial={{ width: "0%" }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 10, ease: "linear" }}
                />
              </motion.div>
            </motion.div>
          </AnimatePresence>
        )
      }
      case "done": {
        return (
          <AnimatePresence>
            <motion.div>
              <Alert className="mt-4">
                <AlertTitle>Analysis completed</AlertTitle>
                <AlertDescription>Your contract has been analyzed. You can now view the results.</AlertDescription>
              </Alert>
              <motion.div className="mt-6 flex flex-col space-y-3 relative">
                <Button onClick={() => router.push(`/contracts/results`)}>View results</Button>
                <Button variant="outline" onClick={handleClose}>
                  Close
                </Button>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        )
      }
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogTitle>Upload Contract</DialogTitle>
        {renderContent()}
        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </DialogContent>
    </Dialog>
  )
}


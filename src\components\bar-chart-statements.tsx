"use client"

import { TrendingUp, TrendingDown } from "lucide-react"
import { <PERSON>, <PERSON>C<PERSON>, CartesianGrid, XAxi<PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

type ChartConfig = Record<
  string,
  {
    label: string; // Display label for the bar
    color: string; // Bar color
  }
>;

interface BarChartCardProps {
  title: string;
  description?: string;
  data: any;
  chartConfig: ChartConfig;
  xAxisKey: string;
}

export function BarChartCard({
  title,
  description,
  data,
  chartConfig,
}: BarChartCardProps) {
  const { data: chartData, trending } = data

  return (
    <Card>
      <CardHeader>
        {/*<CardTitle className="text-sm">{title}</CardTitle>*/}
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="p-2">
        <ChartContainer config={chartConfig}>
          <BarChart accessibilityLayer data={chartData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="monthYear"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="dashed" />}
            />
            {Object.keys(chartConfig).map((key) => (
              <Bar
                key={key}
                dataKey={key}
                fill={chartConfig[key].color}
                name={chartConfig[key].label}
                radius={4}
                fillOpacity={0.9}
              />
            ))}
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-1 text-xs p-2">
        <div className="flex gap-2 font-medium leading-none">
          Trending {trending.direction} by {trending.percentage}% 
          {trending.direction === 'up' ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
        </div>
        <div className="leading-none text-muted-foreground">
          Total transactions for the lastest year
        </div>
      </CardFooter>
    </Card>
  );
}
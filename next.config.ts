/** @type {import('next').NextConfig} */
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  transpilePackages: ['lucide-react', '@lobehub/tts', '@smithery/sdk', '@modelcontextprotocol/sdk'],
  images: {
    remotePatterns: [
      { hostname: 'localhost' },
      { hostname: 'randomuser.me' },
      {
        protocol: 'https',
        hostname: 'img.clerk.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'images.clerk.dev',
        pathname: '**',
      },
    ],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  serverExternalPackages: ['pdf-parse', 'canvas'],
  allowedDevOrigins: ['*************', '3db7-111-71-212-31.ngrok-free.app'],
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  experimental: {
    staleTimes: {
      dynamic: 30,
      static: 180,
    },
    serverActions: {
      bodySizeLimit: '5mb',
    },
  },  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.output.globalObject = 'self';
    }
    if (isServer) {
      config.externals = [...(config.externals || []), '@napi-rs/canvas'];
    }
    return config;
  },
  /*async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `
              default-src 'self';
              script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://apis.google.com https://www.gstatic.com https://www.gstatic.com https://chief-condor-26.clerk.accounts.dev;
              style-src 'self' 'unsafe-inline' https://accounts.google.com https://fonts.googleapis.com https://rsms.me;
              img-src 'self' data: https: blob: https://www.gstatic.com https://img.clerk.com https://images.clerk.dev https://img.clerk.com;
              font-src 'self' https://fonts.gstatic.com https://rsms.me;
              frame-src 'self' https://accounts.google.com https://apis.google.com https://docs.google.com https://content.googleapis.com;
              connect-src 'self' https://accounts.google.com https://www.googleapis.com https://chief-condor-26.clerk.accounts.dev;
              worker-src 'self' blob:;
              child-src blob: https://docs.google.com;
              frame-ancestors 'self' https://docs.google.com;
            `.replace(/\s{2,}/g, ' ').trim(),
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin-allow-popups',
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp',
          },
        ],
      },
    ];
  },*/
};

module.exports = nextConfig;

"use client";

import { useUser } from "@clerk/nextjs";
import { TrendingUp } from "lucide-react";
import {
  Area,
  AreaChart,
  CartesianGrid,
  XAxis,
  ResponsiveContainer,
} from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import * as React from "react";
import { Label, Pie, PieChart } from "recharts";
import { getBudgetData } from "@/actions/dashboard/get-budgets-overview"
import { startOfMonth, endOfMonth, format } from 'date-fns';

type ChartData = {
  date: string;
  value: number;
}

type ResponseBudget = {
  date: string;
} & {
  [category: string]: number | string;
};

type BudgetData = { 
  date: string; 
  [key: string]: number | string; // Allow dynamic categories with number values, and 'date' as a string
}

type TotalAssets = {
  category: string;
  amount: number;
  fill: string;
}

type TotalLiabilities = {
  category: string;
  amount: number;
  fill: string;
};

type AssetsConfig = {
  [key: string]: { label: string; color: string };
}

const COLORS = [
  "#8884d8", // Blue
  "#82ca9d", // Green
  "#ffc658", // Yellow
  "#ff7300", // Orange
  "#ff0000", // Red
  "#a856ff", // Purple
];

// Generate more realistic data with a slight upward trend
const generateChartData = (): ChartData[] => {
  const baseValue = 2800000;
  const volatility = 15000;
  const trend = 500; // Daily upward trend

  return Array.from({ length: 30 }, (_, i) => {
    const randomChange = (Math.random() - 0.5) * volatility;
    const trendValue = trend * i;
    return {
      date: `2024-01-${(i + 1).toString().padStart(2, "0")}`,
      value: baseValue + randomChange + trendValue,
    };
  });
};

const data_ = generateChartData();

const budgetConfig = {
  value: {
    label: "Monthly budget",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig;

const assetsData = [
  { category: "stocks", value: 1514220, fill: "hsl(var(--chart-1))" },
  { category: "crypto", value: 1017110, fill: "hsl(var(--chart-2))" },
  { category: "real-estate", value: 517110, fill: "hsl(var(--chart-3))" },
  { category: "cash", value: 176042, fill: "hsl(var(--chart-4))" },
];

const assetsConfig = {
  value: {
    label: "Total Assets",
  },
  stocks: {
    label: "Stocks",
    color: "hsl(var(--chart-1))",
  },
  crypto: {
    label: "Crypto",
    color: "hsl(var(--chart-2))",
  },
  "real-estate": {
    label: "Real Estate",
    color: "hsl(var(--chart-3))",
  },
  cash: {
    label: "Cash",
    color: "hsl(var(--chart-4))",
  },
} satisfies ChartConfig;

const liabilitiesConfig = {
  mortgage: {
    label: "Mortgage",
    color: "hsl(var(--chart-1))",
  },
  "credit-card": {
    label: "Credit Card",
    color: "hsl(var(--chart-2))",
  },
  "car-loan": {
    label: "Car Loan",
    color: "hsl(var(--chart-3))",
  },
  "student-loan": {
    label: "Student Loan",
    color: "hsl(var(--chart-4))",
  },
};

type Props = {
  data: any
}
export function TopCharts({ data }: Props) {
  const { user } = useUser()
  const userId = user?.id!
  const [view, setView] = React.useState("assets");
  const [selectedRange, setSelectedRange] = React.useState("1M");
  const [budgetData, setBudgetData] = React.useState<BudgetData[]>([]);
  const [totals, setTotals] = React.useState({ currentTotal: 0, previousTotal: 0, difference: 0 });
  const totalAssets = React.useMemo(() => {
    return data.totalAssets.reduce((acc: number, curr: TotalAssets) => acc + curr.amount, 0);
  }, [data.totalAssets]);

  const totalLiabilities = React.useMemo(() => {
    return data.totalLiabilities.reduce((acc: number, curr: TotalLiabilities) => acc + curr.amount, 0);
  }, [data.totalLiabilities]);

  const handleSelectChange = (value: string) => {
    setView(value);
  };

  const title = view === "assets" ? "Assets Overview" : "Debts Overview";
  const chartData = view === "assets" ? data.totalAssets : data.totalLiabilities;
  const chartConfig = view === "assets" ? assetsConfig : liabilitiesConfig;

  // Fetch budget data on range change
  React.useEffect(() => {
    async function fetchData() {
      const rangeMapping: Record<string, number> = {
        "1M": 1,
        "3M": 3,
        "6M": 6,
        "1Y": 12,
      };
  
      const monthsRange = rangeMapping[selectedRange];
      const response = await getBudgetData(userId, monthsRange);
  
      if (response.success && response.data && response.data?.length > 0) {
        // Expand data for monthly trend visualization
        console.log("response.data: =======>", response.data)
        const expandedData = expandMonthlyTrendData(response.data);
        setBudgetData(expandedData);
        setTotals(response.totals);
        console.log("budgetData: =======>", budgetData)
        console.log("budgtTotal: =======>", response.totals)
      } else {
        console.log(response.error);
      }
    }
  
    fetchData();
  }, [selectedRange, userId]);
  

  // Extract dynamic category keys (exclude "date" field)
  const budgetCategories = Object.keys(budgetData[0] || {}).filter((key) => key !== "date");
  console.log("budgetCategories: ", budgetCategories)
  return (
    <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
      <Card className="col-span-2">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Monthly Budget</CardTitle>
          <Select defaultValue="1M" value={selectedRange} onValueChange={(value) => setSelectedRange(value)}>
            <SelectTrigger className="w-[70px] h-8 text-xs">
              <SelectValue placeholder="Period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1M">1M</SelectItem>
              <SelectItem value="3M">3M</SelectItem>
              <SelectItem value="6M">6M</SelectItem>
              <SelectItem value="1Y">1Y</SelectItem>
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-2">
            <div>
              <div className="text-2xl font-bold">
                ${totals.currentTotal.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {totals.difference >= 0 ? (
                  <span className="text-green-500">
                    +${totals.difference.toLocaleString()} more spent
                  </span>
                ) : (
                  <span className="text-red-500">
                    -${Math.abs(totals.difference).toLocaleString()} less spent
                  </span>
                )}
                  {" "} vs last month
              </p>
            </div>
            <div className="h-[180px]">
              <ResponsiveContainer width="100%" height="100%">
                <ChartContainer config={budgetConfig}>
                <AreaChart
                  data={budgetData} // Expanded data with daily points
                  margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                >
                  <CartesianGrid vertical={false} strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis
                    dataKey="date"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return new Intl.DateTimeFormat("en-US", {
                        month: "short",
                        day: "numeric",
                      }).format(date); // Format as 'Dec 17'
                    }}
                    className="text-xs text-muted-foreground"
                  />
                  <ChartTooltip
                      cursor={false}
                      content={<ChartTooltipContent />}
                    />
                  {budgetCategories.map((category, index) => (
                    <Area
                      key={category}
                      type="monotone"
                      dataKey={category}
                      stackId="1"
                      stroke={COLORS[index % COLORS.length]}
                      fill="none"
                    />
                  ))}
                </AreaChart>

                </ChartContainer>
              </ResponsiveContainer>
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <TrendingUp className="h-3.5 w-3.5 text-green-500" />
              <span>You spent -${Math.abs(totals.difference).toLocaleString()} more this month</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <Select value={view} onValueChange={handleSelectChange}>
            <SelectTrigger className="w-[80px] h-8 text-xs">
              <SelectValue placeholder="View" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="assets">Assets</SelectItem>
              <SelectItem value="debts">Debts</SelectItem>
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col h-full">
            <ChartContainer
              config={assetsConfig}
              className="mx-auto aspect-square h-[180px]"
            >
              <PieChart>
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />
                <Pie
                  data={chartData}
                  dataKey="amount"
                  nameKey="category"
                  innerRadius={60}
                  strokeWidth={4}
                >
                  <Label
                    content={({ viewBox }) => {
                      if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                        const total = view === "assets" ? totalAssets : totalLiabilities;
                        const label = view === "assets" ? "Total Assets" : "Total Debts";
                        return (
                          <text
                            x={viewBox.cx}
                            y={viewBox.cy}
                            textAnchor="middle"
                            dominantBaseline="middle"
                          >
                            <tspan
                              x={viewBox.cx}
                              y={viewBox.cy}
                              className="fill-foreground text-xl font-bold"
                            >
                              ${(total / 1000000).toFixed(1)}M
                            </tspan>
                            <tspan
                              x={viewBox.cx}
                              y={(viewBox.cy || 0) + 20}
                              className="fill-muted-foreground text-xs"
                            >
                              {label}
                            </tspan>
                          </text>
                        );
                      }
                    }}
                  />
                </Pie>
              </PieChart>
            </ChartContainer>
            <div className="flex flex-wrap gap-2 mt-4 justify-center">
              {chartData.map((item: TotalAssets) => (
                <div key={item.category} className="flex items-center gap-1.5">
                  <div
                    className="h-2.5 w-2.5 rounded-full"
                    style={{ backgroundColor: item.fill }}
                  />
                  <span className="text-xs text-muted-foreground">
                    {item.category}
                    <span className="ml-1 text-gray-500">
                      ${(item.amount / 1000000).toFixed(1)}M
                    </span>
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

const expandMonthlyTrendData = (responseData: ResponseBudget[]): BudgetData[] => {
  const expandedData: BudgetData[] = [];

  // Destructure the first item from the responseData array
  const { date, ...categories } = responseData[0];  // Extract the 'date' and category fields
  
  // Initialize dailyData with the category values
  const dailyData = categories; // The remaining fields are category values

  // Parse the input date to get the month start and end
  const inputDate = new Date(date); // Assuming date is in 'YYYY-MM-DD' format

  // Get the start and end of the month
  const start = startOfMonth(inputDate); // Returns a Date object for the start of the month
  const end = endOfMonth(inputDate); // Returns a Date object for the end of the month

  // Loop through the days of the month and build the expanded data
  while (start <= end) {
    const dateStr = start.toISOString().split('T')[0]; // Format the date as 'YYYY-MM-DD'

    // Push the entry for the current day with the same value for each category
    expandedData.push({ date: dateStr, ...dailyData });

    // Move to the next day
    start.setDate(start.getDate() + 1);
  }

  return expandedData;
};
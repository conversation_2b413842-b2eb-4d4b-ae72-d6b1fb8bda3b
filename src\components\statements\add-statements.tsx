"use client";

import { BankAccount, Category } from "@prisma/client";
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray, SubmitHandler } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { format, parse, isValid, startOfDay } from "date-fns";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Loader2, Plus } from "lucide-react";
import { CreateCategoryDialog } from "@/components/categories/create-categories";
import { createStatement } from "@/actions/statements/create-statement";

const statementSchema = z.object({
  type: z.enum(["CREDIT", "DEBIT"], { message: "請選擇有效的類型" }),
  amount: z.number().min(1, "金額為必填"),
  categoryId: z.string().min(3, "請選擇類別"),
  accountId: z.string().min(1, "請選擇帳戶"),
  description: z.string().optional(),
  transactionDate: z
    .union([
      z.date(),
      z.string().transform((val) => {
        if (val === '') return startOfDay(new Date());
        const parsedDate = parse(val, 'yyyy-MM-dd', new Date());
        if (isValid(parsedDate) && parsedDate <= new Date()) {
          return startOfDay(parsedDate);
        }
        throw new Error("日期格式無效");
      })
    ])
    .optional()
    .default(startOfDay(new Date())),
});

type StatementFormValues = z.infer<typeof statementSchema>;
type AccountData = Pick<BankAccount, 'id' | 'name' | 'accountType'>
type CategoryData = Pick<Category, 'id' | 'name'>

export function AddStatementComponent({ 
  categories, 
  accounts 
}: { 
  categories: CategoryData[], 
  accounts: AccountData[] 
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm<{ statements: StatementFormValues[] }>({
    resolver: zodResolver(z.object({
      statements: z.array(statementSchema)
    })),
    defaultValues: {
      statements: [{ 
        type: "DEBIT", 
        amount: 0, 
        categoryId: "", 
        accountId: "",
        description: "",
        transactionDate: startOfDay(new Date()),
      }],
    }
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "statements",
  });

  const onSubmit: SubmitHandler<{ statements: StatementFormValues[] }> = async (formData) => {
    try {
      setIsLoading(true);
  
      // 根據交易類型創建 Income 或 Expense 記錄
      const result = await createStatement({
        statements: formData.statements.map((statement) => ({
          accountId: statement.accountId,
          description: statement?.description ?? "",
          amount: statement.amount,
          type: statement.type,
          categoryId: statement.categoryId,
          currencyIso: "TWD",
          date: statement.transactionDate || startOfDay(new Date()),
        })),
      });
  
      if (!result.success) {
        toast.error(result.error);
        return;
      }
  
      toast.success("記錄已成功創建！");
      setIsDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error("提交表單時發生錯誤:", error);
      toast.error("創建記錄失敗");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Plus className="h-4 w-4" /> 新增記錄
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-screen sm:max-w-[800px] overflow-auto">
        <DialogHeader>
          <DialogTitle className="my-4">新增收支記錄</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="space-y-4 border-b pb-4">
                {/* 交易欄位 */}
                <div className="grid grid-cols-3 gap-4">
                  {/* 金額欄位 */}
                  <FormField 
                    control={form.control} 
                    name={`statements.${index}.amount`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>金額</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="輸入金額"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* 交易類型下拉選單 */}
                  <FormField 
                    control={form.control} 
                    name={`statements.${index}.type`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>交易類型</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="選擇類型" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="CREDIT">收入</SelectItem>
                              <SelectItem value="DEBIT">支出</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                  {/* 交易日期 */}
                  <FormField
                    control={form.control}
                    name={`statements.${index}.transactionDate`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>交易日期</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            {...field}
                            value={
                              field.value 
                                ? format(field.value, 'yyyy-MM-dd') 
                                : format(new Date(), 'yyyy-MM-dd')
                            }
                            onChange={(e) => {
                              const inputDate = e.target.value;
                              const parsedDate = parse(inputDate, 'yyyy-MM-dd', new Date());
                              
                              if (isValid(parsedDate) && parsedDate <= new Date()) {
                                field.onChange(startOfDay(parsedDate));
                              } else {
                                field.onChange(startOfDay(new Date()));
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* 類別下拉選單 */}
                  <FormField
                    control={form.control} 
                    name={`statements.${index}.categoryId`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel><div className="h-8">類別 <CreateCategoryDialog /></div></FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="選擇類別" />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((category) => (
                                <SelectItem key={category.id} value={category.id}>
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* 帳戶下拉選單 */}
                  <FormField 
                    control={form.control} 
                    name={`statements.${index}.accountId`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel><div className="h-8 flex items-end">銀行帳戶</div></FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="選擇帳戶" />
                            </SelectTrigger>
                            <SelectContent>
                              {accounts
                                .filter(account => account.accountType === "VIRTUAL")
                                .map((account) => (
                                <SelectItem key={account.id} value={account.id}>
                                  {account.name} ({account.accountType || "未知"})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                </div>
                {/* 描述欄位 */}
                <FormField 
                  control={form.control} 
                  name={`statements.${index}.description`} 
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>描述（選填）</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="新增交易詳情"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} 
                />

                {/* 移除交易按鈕 */}
                {fields.length > 1 && (
                  <Button 
                    type="button" 
                    variant="destructive" 
                    size="sm"
                    onClick={() => remove(index)}
                  >
                    移除記錄
                  </Button>
                )}
              </div>
            ))}
            {/* 操作按鈕 */}
            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => append({ type: "CREDIT", amount: 0, categoryId: "", accountId: "" })}
              >
                <Plus className="mr-2 h-4 w-4" /> 新增更多記錄
              </Button>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    處理中...
                  </>
                ) : (
                  "儲存記錄"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
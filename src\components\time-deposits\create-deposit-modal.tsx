"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { CreateDepositFormWrapper } from "./create-deposit-form-wrapper";
import { useCreateDepositModal } from "@/hooks/use-create-deposit-modal";

export const CreateDepositModal = () => {
  const { isOpen, setIsOpen, close } = useCreateDepositModal()
  return (
    <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
      <CreateDepositFormWrapper onCancel={close} />
    </ResponsiveModal>
  )
}
import { parse, endOfMonth, isSameDay, format } from 'date-fns';

export function getEndOfMonthDate(endDateString: string) {
  // Parse the date string (e.g., "********") to a Date object
  const parsedDate = parse(endDateString, 'yyyyMMdd', new Date());

  // Get the actual end of month for that date
  const actualEndOfMonth = endOfMonth(parsedDate);

  // Check if the provided date is already the end of month
  const isAlreadyEndOfMonth = isSameDay(parsedDate, actualEndOfMonth);

  // Get current date (today)
  const currentDate = new Date();

  // Safety check: current date must be greater than the end-of-month date
  // This prevents updating data on the same day when bank clearance might not be complete
  const isSafeToUpdate = currentDate > actualEndOfMonth;

  // Format back to your desired format
  const formattedEndOfMonth = format(actualEndOfMonth, 'yyyyMMdd');

  return {
    originalDate: endDateString,
    endOfMonthDate: formattedEndOfMonth,
    isAlreadyEndOfMonth: isAlreadyEndOfMonth,
    isSafeToUpdate: isSafeToUpdate,
    currentDate: format(currentDate, 'yyyyMMdd')
  };
}

// Example output:
// {
//   originalDate: "********",
//   endOfMonthDate: "********",
//   isAlreadyEndOfMonth: true,
//   isSafeToUpdate: true,
//   currentDate: "********"
// }

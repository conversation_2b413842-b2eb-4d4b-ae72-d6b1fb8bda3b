import { parse, endOfMonth, isSameDay, format } from 'date-fns';

export function getEndOfMonthDate(endDateString: string) {
  // Parse the date string (e.g., "20250131") to a Date object
  const parsedDate = parse(endDateString, 'yyyyMMdd', new Date());
  
  // Get the actual end of month for that date
  const actualEndOfMonth = endOfMonth(parsedDate);
  
  // Check if the provided date is already the end of month
  const isAlreadyEndOfMonth = isSameDay(parsedDate, actualEndOfMonth);
  
  // Format back to your desired format
  const formattedEndOfMonth = format(actualEndOfMonth, 'yyyyMMdd');
  
  return {
    originalDate: endDateString,
    endOfMonthDate: formattedEndOfMonth,
    isAlreadyEndOfMonth: isAlreadyEndOfMonth
  };
}

// Example output:
// {
//   originalDate: "20250131",
//   endOfMonthDate: "20250131", 
//   isAlreadyEndOfMonth: true
// }

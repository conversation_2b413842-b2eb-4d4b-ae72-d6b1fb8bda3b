import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useWorkspaceId } from '../workspaces/use-workspace-id';
import { deleteProject } from '@/actions/projects/delete-project';
import { toast } from 'sonner';


export function useDeleteProject() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const workspaceId = useWorkspaceId()

  const mutation = useMutation({
    mutationFn: async (projectId: string) => {
      const response = await deleteProject({ projectId });
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to delete project.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Project deleted!');
      router.push(`/workspaces/${workspaceId}`)
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete project.');
    },
  });

  return mutation;
}

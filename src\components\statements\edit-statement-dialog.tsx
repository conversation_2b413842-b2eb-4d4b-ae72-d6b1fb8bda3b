"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Loader2 } from "lucide-react";
import { updateStatement } from "@/actions/statements/update-statement";
import { CategorySelect } from "@/components/categories/category-select";

const editStatementSchema = z.object({
  description: z.string().optional(),
  amount: z.number().min(0, "金額必須大於 0"),
  date: z.date(),
  type: z.enum(["CREDIT", "DEBIT"]),
  categoryId: z.string().min(1, "請選擇類別"),
});

type EditStatementFormValues = z.infer<typeof editStatementSchema>;

interface EditStatementDialogProps {
  statement: {
    id: string;
    description?: string;
    amount: number;
    date: Date;
    type: "CREDIT" | "DEBIT";
    categoryId: string;
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTransactionUpdate: () => Promise<void>;
}

export function EditStatementDialog({
  statement,
  open,
  onOpenChange,
  onTransactionUpdate,
}: EditStatementDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<EditStatementFormValues>({
    resolver: zodResolver(editStatementSchema),
    defaultValues: {
      description: statement.description || "",
      amount: Number(statement.amount),
      date: new Date(statement.date),
      type: statement.type,
      categoryId: statement.categoryId,
    },
  });

  async function onSubmit(data: EditStatementFormValues) {
    try {
      setIsLoading(true);
      const result = await updateStatement({
        id: statement.id,
        ...data,
      });

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("交易更新成功！");
      onOpenChange(false);
      form.reset();
      await onTransactionUpdate();
    } catch (error) {
      toast.error("更新交易失敗");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>修改交易</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>金額</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="輸入金額"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>交易類型</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="選擇類型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="CREDIT">收入</SelectItem>
                          <SelectItem value="DEBIT">支出</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>交易日期</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      value={format(field.value, "yyyy-MM-dd")}
                      onChange={(e) => {
                        const date = new Date(e.target.value);
                        field.onChange(date);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述（選填）</FormLabel>
                  <FormControl>
                    <Input placeholder="輸入交易描述" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>類別</FormLabel>
                  <FormControl>
                    <CategorySelect
                      onSelect={(categoryId) => field.onChange(categoryId)}
                      currentCategoryId={field.value}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    處理中...
                  </>
                ) : (
                  "儲存變更"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
"use client";

import { ArrowUp } from "lucide-react";
import { useState, useEffect } from "react";
import { generateId} from "ai"
import { Header } from "@/components/copilot/spreadsheet/Header";
import SpreadsheetSidebar from "@/components/copilot/spreadsheet/SpreadsheetSidebar";
import SingleSpreadsheet from "@/components/copilot/spreadsheet/SingleSpreadsheet";
import {
  CopilotKit,
  useCopilotAction,
  useCopilotReadable,
} from "@copilotkit/react-core";
import { canonicalSpreadsheetData } from "@/utils/canonicalSpreadsheetData";
import { SpreadsheetData } from "@/lib/types";
import { PreviewSpreadsheetChanges } from "@/components/copilot/spreadsheet/PreviewSpreadsheetChanges";

export type BankAccount  = {
  bankAccounts: {id: string; name: string; accountType: string }[]
  categories: {id: string; name: string }[]
}

export const Spreadsheet = ({
  bankAccounts,
  categories, 
}: BankAccount) => {
  return (
    <CopilotKit
      runtimeUrl="/api/copilotkit/spreadsheet"
    >
      <Main bankAccounts={bankAccounts} categories={categories} />
    </CopilotKit>
  );
};

export const Main = ({bankAccounts, categories}: BankAccount) => {
  const [spreadsheets, setSpreadsheets] = useState<SpreadsheetData[]>([
    {
      id: generateId(),
      title: `Sheet ${new Date().toLocaleString()}`,
      rows: Array(8).fill(Array(12).fill({ value: "" })),
    },
  ]);

  const [selectedSpreadsheetIndex, setSelectedSpreadsheetIndex] = useState(0);

  useEffect(() => {
    const savedData = localStorage.getItem("spreadsheets");
    if (savedData) {
      setSpreadsheets(JSON.parse(savedData));
    }
  }, []);

  useCopilotAction({
    name: "createSpreadsheet",
    description: "Create a new  spreadsheet",
    parameters: [
      {
        name: "rows",
        type: "object[]",
        description: "The rows of the spreadsheet",
        attributes: [
          {
            name: "cells",
            type: "object[]",
            description: "The cells of the row",
            attributes: [
              {
                name: "value",
                type: "string",
                description: "The value of the cell",
              },
            ],
          },
        ],
      },
      {
        name: "title",
        type: "string",
        description: "The title of the spreadsheet",
      },
    ],
    render: (props) => {
      const { rows, title } = props.args;
      const newRows = canonicalSpreadsheetData(rows);

      return (
        <PreviewSpreadsheetChanges
          preCommitTitle="Create spreadsheet"
          postCommitTitle="Spreadsheet created"
          newRows={newRows}
          commit={(rows) => {
            const newSpreadsheet: SpreadsheetData = {
              id: generateId(),
              title: title || "Untitled Spreadsheet",
              rows: rows,
            };
            setSpreadsheets((prev) => [...prev, newSpreadsheet]);
            setSelectedSpreadsheetIndex(spreadsheets.length);
          }}
        />
      );
    },
    handler: ({ rows, title }) => {
      // Do nothing.
      // The preview component will optionally handle committing the changes.
    },
  });

  useCopilotReadable({
    description: "Today's date",
    value: new Date().toLocaleDateString(),
  });

  return (
    <div className="flex">
      <SpreadsheetSidebar
        spreadsheets={spreadsheets}
        selectedSpreadsheetIndex={selectedSpreadsheetIndex}
        setSelectedSpreadsheetIndex={setSelectedSpreadsheetIndex}
        setSpreadsheets={setSpreadsheets}
      />
      <div className="flex-1 flex-col overflow-auto">
        <Header 
          bankAccounts={bankAccounts}
          categories={categories}
          currentSheetIndex={selectedSpreadsheetIndex}
          setCurrentSheetIndex={setSelectedSpreadsheetIndex}
          spreadsheets={spreadsheets}
          setSpreadsheets={setSpreadsheets}
          spreadsheet={spreadsheets[selectedSpreadsheetIndex]}
          setSpreadsheet={(spreadsheet) => {
            setSpreadsheets((prev) => {
              console.log("setSpreadsheet", spreadsheet);
              const newSpreadsheets = [...prev];
              newSpreadsheets[selectedSpreadsheetIndex] = spreadsheet;
              return newSpreadsheets;
            });
          }}
        /> 
        <SingleSpreadsheet
          spreadsheet={spreadsheets[selectedSpreadsheetIndex]}
          setSpreadsheet={(spreadsheet) => {
            setSpreadsheets((prev) => {
              console.log("setSpreadsheet", spreadsheet);
              const newSpreadsheets = [...prev];
              newSpreadsheets[selectedSpreadsheetIndex] = spreadsheet;
              return newSpreadsheets;
            });
          }}
        />        
      </div>

    </div>
  );
};


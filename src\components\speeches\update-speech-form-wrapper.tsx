"use client"

import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { useGetSpeech } from "./use-get-speech";
import { useGetProjects } from "@/components/projects/use-get-projects";
import { UpdateSpeechForm } from "./update-speech-form";
import { 
  Card, 
  CardContent, 
} from "@/components/ui/card";

import { Loader } from "lucide-react";

interface UpdateSpeechFormWrapperProps {
  onCancel?: () => void;
  id: string;
}

export const UpdateSpeechFormWrapper = ({ onCancel, id }: UpdateSpeechFormWrapperProps) => {
  const workspaceId = useWorkspaceId();
  
  const { data: initialValues, isLoading: isLoadingSpeech } = useGetSpeech({ 
    speechId: id 
  })
  const { projects, isLoading: isLoadingProjects } = useGetProjects({
    workspaceId,
  });

  const projectOptions = projects?.map((project) => ({
    id: project.id,
    name: project.name,
    imageUrl: project.imageUrl
  }));


  const isLoading = isLoadingSpeech || isLoadingProjects || !workspaceId;

  if (isLoading) {
    <Card className="w-full h-[714px] border-none shadow-none">
      <CardContent className="flex items-center jistify-center h-full">
        <Loader className="size-5 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>;
  }

  if (!initialValues) return null;

  return (
    <UpdateSpeechForm 
      onCancel={onCancel}
      projectOptions={projectOptions ?? []}
      initialValues={initialValues}
    />
  );
};

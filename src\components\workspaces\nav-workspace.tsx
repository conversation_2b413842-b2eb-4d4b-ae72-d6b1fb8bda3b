'use client';

import { Member } from "@prisma/client";
import { WorkspaceProvider } from "./workspace-provider";
import { WorkspaceSwitcher } from "./workspace-switcher";
import { ProjectSwitcher } from "../projects/project-switcher";
import { useWorkspaces } from "@/hooks/use-workspaces";
import { useWorkspaceId } from "./use-workspace-id";

interface NavWorkspaceProps {
  workspaces: { id: string; name: string; members: Member[] }[];
}

export function NavWorkspace({ workspaces }: NavWorkspaceProps) {
  const { currentWorkspaceId } = useWorkspaces();
  const workspaceId = useWorkspaceId();

  return (
    <WorkspaceProvider initialWorkspaces={workspaces}>
      <div className="flex flex-col gap-y-4">
        <WorkspaceSwitcher 
          initialWorkspaces={workspaces} 
          initialWorkspaceId={workspaceId || currentWorkspaceId} 
        />
        <ProjectSwitcher workspaceId={workspaceId ?? currentWorkspaceId} />
      </div>
    </WorkspaceProvider>
  );
}
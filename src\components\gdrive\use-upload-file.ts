"use client"
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUploadFile() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (formData: FormData) => {
      //console.log("formData", formData)
      const response = await fetch(`/api/gdrive/upload-file`, {
        method: 'POST',
        body: formData,
      });
      const responseData = await response.json();
      //console.log("responseData", responseData)

      if (!response.ok || !responseData.file) {
        throw new Error(`Failed to upload file.`);
      }
      
      return responseData.file;
    },
    onSuccess: ({ data }) => {
      toast.success('File uploaded!');
      queryClient.invalidateQueries({ queryKey: ['googledrive'] });
      queryClient.invalidateQueries({ queryKey: ['googledrive', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload file.');
    },
  });

  return mutation;
}

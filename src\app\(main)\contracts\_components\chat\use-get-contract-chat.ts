import { useQuery } from "@tanstack/react-query";
import { getChatById } from "@/actions/chats/actions";

type UseChatProps = {
  contractId: string
  chatId: string
}

export const useGetContractChat = ({ contractId, chatId }: UseChatProps) => {
  const { data, isLoading, refetch, error } = useQuery({
    queryKey: [
      "contracts", chatId,
    ],
    queryFn: async () => {
      const data = await getChatById({contractId, id: chatId});

      console.log("chat", data)

      if (!data) {
        throw new Error(`Failed to get chats.`);
      }
      
      return data;
    },
    enabled: Boolean(contractId && chatId),
  });

  return {
    data,
    isLoading,
    refetch,
    error,
  };
};
import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { serialize } from "cookie";
import { decrypt } from "@/lib/encryption";
import { rateLimit } from "@/lib/rate-limit";
import { validateCsrfToken } from "@/lib/csrf";
import axios from "axios";
import config from "@/lib/config/env";

const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500
});

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) return NextResponse.json({ error: "Unauthorized", success: false }, { status: 401 });
    // Rate limiting
    const clientIp = req.headers.get("x-forwarded-for") || "0.0.0.0";
    try {
      await limiter.check(3, clientIp); // 3 refresh attempts per minute
    } catch {
      return NextResponse.json(
        { error: "Too many refresh attempts" },
        { status: 429 }
      );
    }

    // CSRF protection
    const csrfToken = req.headers.get("x-csrf-token");
    if (!csrfToken) {
      return NextResponse.json(
        { error: "Missing CSRF token" },
        { status: 403 }
      );
    }
    if (!validateCsrfToken(csrfToken)) {
      return NextResponse.json(
        { error: "Invalid request" },
        { status: 403 }
      );
    }

    const { clientId, clientSecret } = config.env.google;
    const cookies = req.headers.get("cookie") || "";
    console.log("Cookies refresh-token:", cookies);
    
    // Get encrypted refresh token
    const encryptedRefreshToken = cookies.match(/refresh_token=([^;]*)/)?.[1];
    if (!encryptedRefreshToken) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Decrypt refresh token
    const refreshToken = decrypt(decodeURIComponent(encryptedRefreshToken));

    // Exchange refresh token for new access token
    const response = await axios.post(
      "https://oauth2.googleapis.com/token",
      {
        refresh_token: refreshToken,
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: "refresh_token",
      },
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    const { access_token: newAccessToken, expires_in } = response.data;

    // Set new access token cookie
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax" as const,
      path: "/",
    };

    const accessTokenCookie = serialize("access_token", newAccessToken, {
      ...cookieOptions,
      maxAge: expires_in,
    });

    // Set security headers
    const responseHeaders = new Headers({
      "X-Content-Type-Options": "nosniff",
      "X-Frame-Options": "DENY",
      "Content-Security-Policy": "default-src 'self'",
      "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    });

    responseHeaders.append("Set-Cookie", accessTokenCookie);

    return NextResponse.json(
      { success: true },
      { headers: responseHeaders }
    );

  } catch (error: any) {
    console.error("Refresh Token Error:", error);

    // Clear cookies on refresh token failure
    const clearCookies = [
      serialize("access_token", "", { maxAge: 0, path: "/" }),
      serialize("refresh_token", "", { maxAge: 0, path: "/" }),
      serialize("session_id", "", { maxAge: 0, path: "/" })
    ];

    const responseHeaders = new Headers();
    clearCookies.forEach(cookie => {
      responseHeaders.append("Set-Cookie", cookie);
    });

    return NextResponse.json(
      { error: "Authentication failed" },
      { 
        status: 401,
        headers: responseHeaders
      }
    );
  }
}
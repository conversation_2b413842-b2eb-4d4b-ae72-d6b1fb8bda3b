import { useCallback, useEffect, useState } from "react";
import { TaskStatus } from "@prisma/client";
import { TaskWithRelations } from "@/actions/tasks/get-tasks";
import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult
} from "@hello-pangea/dnd";
import { KanbanColumHeader } from "./kanban-board-header";
import { KanbanCard } from "./kanban-card";


const boards: TaskStatus[] = [
  TaskStatus.BACKLOG,    
  TaskStatus.TODO,
  TaskStatus.IN_PROGRESS,
  TaskStatus.IN_REVIEW,
  TaskStatus.DONE,
];

type TaskState = {
  [key in TaskStatus]: TaskWithRelations[];
}

interface DataKanbanProps {
  data: TaskWithRelations[];
  onChange: (tasks: { id: string; status: TaskStatus; position: number; }[]) => void
}

export const DataKanban = ({ data, onChange }: DataKanbanProps) => {
  const [tasks, setTasks] = useState<TaskState>(() => {
    const initialTasks: TaskState = {
      [TaskStatus.BACKLOG]: [],
      [TaskStatus.TODO]: [],
      [TaskStatus.IN_PROGRESS]: [],
      [TaskStatus.IN_REVIEW]: [],
      [TaskStatus.DONE]: [],
    };
    data.forEach((task) => {
      initialTasks[task.status].push(task);
    });
    // Sort tasks by position
    Object.keys(initialTasks).forEach((status) => {
      initialTasks[status as TaskStatus].sort((a, b) => a.position - b.position);
    })
    return initialTasks;
  });

  useEffect(() => {
    const newTasks: TaskState = {
      [TaskStatus.BACKLOG]: [],
      [TaskStatus.TODO]: [],
      [TaskStatus.IN_PROGRESS]: [],
      [TaskStatus.IN_REVIEW]: [],
      [TaskStatus.DONE]: [],
    };

    data.forEach((task) => {
      newTasks[task.status].push(task);
    });
    Object.keys(newTasks).forEach((status) => {
      newTasks[status as TaskStatus].sort((a, b) => a.position - b.position);
    })

    setTasks(newTasks)
  } , [data]);


  const onDragEnd = useCallback((result: DropResult) => {
    if (!result.destination) return;
  
    const { source, destination } = result;

    // Return early if dropped in the same position
    if (
      source.droppableId === destination.droppableId && 
      source.index === destination.index
    ) {
      return;
    }
    
    const sourceStatus = source.droppableId as TaskStatus;
    const destStatus = destination.droppableId as TaskStatus;
    
    setTasks((prevTasks) => {
      const newTasks = { ...prevTasks };
      const sourceColumn = [...newTasks[sourceStatus]];
      const destColumn = sourceStatus === destStatus ? sourceColumn : [...newTasks[destStatus]];
      
      // Remove the task from source
      const [movedTask] = sourceColumn.splice(source.index, 1);
      if (!movedTask) return prevTasks;
  
      // Insert task at new position
      const updatedMovedTask = { ...movedTask, status: destStatus };
      
      if (sourceStatus === destStatus) {
        sourceColumn.splice(destination.index, 0, updatedMovedTask);
        newTasks[sourceStatus] = sourceColumn;
      } else {
        destColumn.splice(destination.index, 0, updatedMovedTask);
        newTasks[sourceStatus] = sourceColumn;
        newTasks[destStatus] = destColumn;
      }
  
      // Calculate positions after the state is updated
      const updatedPayload: { id: string; status: TaskStatus; position: number; }[] = [];
  
      // Only process the destination column
      newTasks[destStatus].forEach((task, index) => {
        const newPosition = Math.min((index + 1) * 1000, 1_000_000);
        updatedPayload.push({
          id: task.id,
          status: destStatus,
          position: newPosition
        });
      });
  
      // If task moved between columns, update source column positions
      if (sourceStatus !== destStatus) {
        newTasks[sourceStatus].forEach((task, index) => {
          const newPosition = Math.min((index + 1) * 1000, 1_000_000);
          updatedPayload.push({
            id: task.id,
            status: sourceStatus,
            position: newPosition
          });
        });
      }
  
      // Call onChange after all calculations are done
      setTimeout(() => onChange(updatedPayload), 0);
      
      return newTasks;
    });
  }, [onChange]);

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex overflow-x-auto">
        {boards.map((board) => {
          return (
            <div key={board} className="flex-1 mr-2 last-of-type:mr-0 bg-muted p-1.5 rounded-md min-w-[180px]">
              <KanbanColumHeader 
                board={board}
                taskCount={tasks[board].length}
              />
              <Droppable droppableId={board} key={board}>
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="min-h-[200px] py-1"
                  >
                    {tasks[board].map((task, index) => (
                      <Draggable
                        key={task.id}
                        draggableId={task.id}
                        index={index}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="mb-2"
                          >
                            <KanbanCard task={task} />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          )
        })}
      </div>
    </DragDropContext>
  )
}
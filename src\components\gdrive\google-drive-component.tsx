"use client"

import { useState, useEffect, useCallback } from "react"
import { useGetFolders } from "./use-get-folders"
import { useCreateFolder } from "./use-create-folder"
import { useUploadFile } from "./use-upload-file"
import { GoogleDriveMenu } from "./google-drive-menu"
import { GoogleDriveAuthStatus } from "./google-drive-auth-status"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Loading } from "@/components/ui/loader"
import { DataTable } from "./data-table"
import { createColumns, type GoogleDriveItem } from "./columns"


export default function GoogleDriveComponent() {
  const [items, setItems] = useState<GoogleDriveItem[]>([]);
  const [currentFolder, setCurrentFolder] = useState<GoogleDriveItem | null>(null);
  const [breadcrumbs, setBreadcrumbs] = useState<GoogleDriveItem[]>([])
  const [folderName, setFolderName] = useState("");
  const [file, setFile] = useState<File | null>(null)
  const [selectedFiles, setSelectedFiles] = useState<GoogleDriveItem[]>([]);
  const [downloadedFiles, setDownloadedFiles] = useState<File[]>([]);
  const driveId = "none" //process.env.NEXT_PUBLIC_SHARED_DRIVE_ID

  const { data, isLoading: isFetchFoldersLoading, refetch } = useGetFolders()
  const { mutate: createFolderMutate, isPending: isCreateFolderLoading } = useCreateFolder()
  const { mutate: uploadFileMutate, isPending: isUploadFileLoading } = useUploadFile()

  const updateFolderStructure = useCallback(
    (structure: GoogleDriveItem[], newItem: GoogleDriveItem): GoogleDriveItem[] => {
      return structure.map((item) => {
        if (item.id === currentFolder?.id) {
          return {
            ...item,
            children: [...(item.children || []), newItem],
          }
        } else if (item.children) {
          return {
            ...item,
            children: updateFolderStructure(item.children, newItem),
          }
        }
        return item
      })
    },
    [currentFolder],
  )

  useEffect(() => {
    if (data) {
      console.log("currentFolder: ", currentFolder)
      setItems(currentFolder ? currentFolder.children || [] : data.structure)
    }
  }, [data, currentFolder])

  const onDrillDown = useCallback((item: GoogleDriveItem) => {
    if (item.type === "folder") {
      setCurrentFolder(item);
  
      setBreadcrumbs((prev) => {
        // Prevent duplicates by checking for existing IDs
        if (prev.some((breadcrumb) => breadcrumb.id === item.id)) {
          return prev;
        }
        return [...prev, item];
      });
  
      setItems(item.children || []);
    }
  }, []);
  

  const onBreadcrumbClick = useCallback(
    (item: GoogleDriveItem | null, index: number) => {
      if (index === 0) {
        // Navigating to the root
        setCurrentFolder(null);
        setBreadcrumbs([]);
        setItems(data?.structure || []);
      } else {
        // Navigate to the selected breadcrumb
        setCurrentFolder(item);
        setBreadcrumbs((prev) => prev.slice(0, index + 1)); // Trim breadcrumbs after the selected one
        setItems(item?.children || []); // Update items to reflect the selected folder
      }
    },
    [data]
  );

  const handleFileSelect = useCallback((item: GoogleDriveItem) => {
    setSelectedFiles((prev: GoogleDriveItem[]) => 
      prev.some((f: GoogleDriveItem) => f.id === item.id)
        ? prev.filter((f: GoogleDriveItem) => f.id !== item.id)
        : [...prev, item]
    );
  }, []);

  const downloadSelectedFiles = useCallback(async () => {
    const downloadPromises = selectedFiles.map(async (file) => {
      try {
        const response = await fetch(`/api/gdrive/${file.id}/download-item`);
        const blob = await response.blob();
        return new File([blob], file.name, { type: blob.type });
      } catch (error) {
        console.error(`Error downloading file ${file.name}:`, error);
        return null;
      }
    });
  
    const downloadedFilesResult = await Promise.all(downloadPromises);
    const validFiles = downloadedFilesResult.filter(file => file !== null) as File[];
    
    setDownloadedFiles(validFiles);
    console.log("validFiles", validFiles)
    console.log("downloadedFiles", downloadedFiles)
  }, [selectedFiles]);
   
  const columns = createColumns({
    onDrillDown,
    currentFolder,
    setCurrentFolder,
    selectedFiles,
    onFileSelect: handleFileSelect
  });

  const disabled = isFetchFoldersLoading || isCreateFolderLoading || isUploadFileLoading

  if (isFetchFoldersLoading && items?.length === 0) {
    return <Loading />
  }

  return (
    <Card>
      <CardContent className="px-3 py-2">
        <div className="space-y-0">
          <div className="flex items-center justify-between space-x-2">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onBreadcrumbClick(null, 0)}
                disabled={breadcrumbs.length === 0}
              >
                Root
              </Button>
              {breadcrumbs.map((item, index) => (
                <div key={item.id} className="flex items-center">
                  <span className="mx-0">/</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onBreadcrumbClick(item, index)} // Use the correct index
                    disabled={index === breadcrumbs.length - 1} // Disable the last breadcrumb (current folder)
                  >
                    {item.name}
                  </Button>
                </div>
              ))}
            </div>
            {selectedFiles && selectedFiles.length > 0 && 
              <Button
                className="h-8"
                variant="secondary"
                size="sm"
                onClick={() => { downloadSelectedFiles() }}
                disabled={disabled}
              >
                Use Selected ({selectedFiles?.length || 0})
              </Button>
            }
            <GoogleDriveMenu
              disabled={disabled}
              currentFolder={currentFolder}
              data={data}
              file={file}
              folderName={folderName}
              driveId={driveId}
              createFolderMutate={createFolderMutate}
              uploadFileMutate={uploadFileMutate}
              isCreateFolderLoading={isCreateFolderLoading}
              setCurrentFolder={setCurrentFolder}
              setItems={setItems}
              setFile={setFile}
              setFolderName={setFolderName}
              refetch={refetch}
            />
          </div>

          <DataTable columns={columns} data={items} />
          {items?.length === 0 && (
            <div className="flex items-center justify-center">
              <GoogleDriveAuthStatus />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}


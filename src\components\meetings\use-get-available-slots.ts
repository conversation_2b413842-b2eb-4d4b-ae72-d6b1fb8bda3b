import { useQuery } from "@tanstack/react-query";
import { getAvailableSlots } from "@/actions/tasks/schedule-meeting";

export const useGetAvailableSlots = (workingHours: {
  start: number;
  end: number;
  slotDuration: number;
  minGapBetweenMeetings: number;
  timezone: string;
}) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["available-slots", workingHours],
    queryFn: async () => {
      const today = new Date();
      const slots = await getAvailableSlots(today, workingHours);
      
      if (!slots) {
        throw new Error("Failed to fetch available slots");
      }
      return slots;
    },
    enabled: !!workingHours,
  });

  return {
    availableSlots: data,
    isLoading,
    error,
  };
};


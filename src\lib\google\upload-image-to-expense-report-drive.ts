import { google } from 'googleapis';
import { Readable } from 'stream';

export const uploadImageToExpenseReportDrive = async (buffer: Buffer, filename: string) => {
  const auth = new google.auth.GoogleAuth({
    credentials: {
      client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    },
    scopes: ['https://www.googleapis.com/auth/drive.file'],
  });
  const drive = google.drive({ version: 'v3', auth });
  const folderId = process.env.GOOGLE_DRIVE_EXPENSE_REPORT_FOLDER_ID;
  if (!folderId) throw new Error('GOOGLE_DRIVE_EXPENSE_REPORT_FOLDER_ID not set');

  const fileMetadata = {
    name: filename,
    parents: [folderId],
  };
  const media = {
    mimeType: 'image/jpeg',
    body: Readable.from(buffer),
  };
  const response = await drive.files.create({
    requestBody: fileMetadata,
    media,
    fields: 'id, webViewLink',
    supportsAllDrives: true,
  });
  return {
    fileId: response.data.id,
    webViewLink: response.data.webViewLink,
  };
};

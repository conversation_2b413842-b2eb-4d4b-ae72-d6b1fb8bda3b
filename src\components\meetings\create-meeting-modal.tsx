"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { CreateMeetingForm } from "./create-meeting-form";
import { useCreateMeetingModal } from "@/hooks/use-create-meeting-modal";

export const CreateMeetingModal = () => {
  const { isOpen, setIsOpen, close } = useCreateMeetingModal()
  return (
    <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
      <CreateMeetingForm onCancel={close} />
    </ResponsiveModal>
  )
}
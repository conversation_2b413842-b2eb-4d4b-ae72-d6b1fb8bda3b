//import { createSupabaseClient } from "@/lib/supabase/client";
//import { User } from "@supabase/supabase-js";
import { useUser } from "@clerk/nextjs"
import { UserResource as User } from "@clerk/types"
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

type UserContentType = {
  getUser: () => Promise<User | undefined>;
  user: User | undefined;
  loading: boolean;
};

const UserContext = createContext<UserContentType | undefined>(undefined);

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User>();
  const [loading, setLoading] = useState(true);
  const { user: clerkUser } = useUser();

  useEffect(() => {
    if (user || typeof window === "undefined") return;

    getUser();
  }, [clerkUser]);

  async function getUser() {
    if (user) {
      setLoading(false);
      return user;
    }

    /*const supabase = createSupabaseClient();

    const {
      data: { user: supabaseUser },
    } = await supabase.auth.getUser();
    setUser(supabaseUser || undefined);
    setLoading(false);
    return supabaseUser || undefined;*/

    if (clerkUser) {
      setUser(clerkUser);
      setLoading(false);
      return clerkUser;
    }

    setUser(undefined);
    setLoading(false);
    return undefined;
  }

  const contextValue: UserContentType = {
    getUser,
    user,
    loading,
  };

  return (
    <UserContext.Provider value={contextValue}>{children}</UserContext.Provider>
  );
}

export function useUserContext() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUserContext must be used within a UserProvider");
  }
  return context;
}

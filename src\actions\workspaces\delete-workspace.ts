'use server'

import { prisma } from "@/lib/db";
import { Role } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";
import { z } from "zod";
import { revalidatePath } from "next/cache"

const deleteSchema = z.object({
  workspaceId: z.string().min(1),
});


export async function deleteWorkspace(input: z.infer<typeof deleteSchema>) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    const validatedFields = deleteSchema.safeParse(input);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false
      }
    }

    const { workspaceId } = validatedFields.data;

    // Check if user exists and has ADMIN role
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId },
      select: { role: true }
    });
    console.log("member", member)

    if (!member || member.role !== Role.ADMIN) {
      return {
        error: "Only administrators can delete workspaces.",
        success: false
      }
    }

    // Check if user belongs to the workspace
    const userWorkspace = await prisma.workspace.findUnique({
      where: {
        id: workspaceId,
        members: {
          some: {
            userId: userId
          }
        }
      },
      include: {
        members: {
          include: {
            bankAccount: true
          }
        }
      }
    });

    if (!userWorkspace) {
      return {
        error: "You don't have access to this workspace.",
        success: false
      }
    }

    // Delete workspace and all related data in a transaction
    await prisma.$transaction(async (tx) => {
      // 1. Delete workflow runs and reminders
      await tx.workflowRun.deleteMany({
        where: {
          reminder: {
            task: {
              workspaceId: workspaceId
            }
          }
        }
      });
    
      await tx.reminder.deleteMany({
        where: {
          task: {
            workspaceId: workspaceId
          }
        }
      });
    
      // 2. Delete tasks
      await tx.task.deleteMany({
        where: {
          workspaceId: workspaceId
        }
      });
    
      // 3. Delete projects
      await tx.project.deleteMany({
        where: {
          workspaceId: workspaceId
        }
      });
    
      // 4. Delete members first (since they reference bank accounts)
      await tx.member.deleteMany({
        where: {
          workspaceId: workspaceId
        }
      });
    
      // 5. Delete bank accounts if needed
      // Note: Only delete bank accounts that are no longer referenced by any members
      /*await tx.bankAccount.deleteMany({
        where: {
          workspaceId: workspaceId
        }
      });*/
    
      // 6. Finally, delete the workspace
      await tx.workspace.delete({
        where: {
          id: workspaceId
        }
      });
    });

    revalidatePath('/workspaces')

    return {
      success: true,
      message: "Workspace deleted successfully"
    }

  } catch (error) {
    console.error('Error deleting workspace:', error)
    return {
      error: "Failed to delete workspace.",
      success: false
    }
  }
}

"use client";

import { SpreadsheetData } from "@/lib/types";
import React, { useState } from "react";
import * as XLSX from "xlsx";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ChevronDown, Save } from "lucide-react";
import { DynamicKeyValueButton } from "./DynamicKVButton";

type SaveMenuProps = { 
  bankAccounts: {id: string; name: string; accountType: string }[]
  categories: {id: string; name: string; }[]
  spreadsheets: SpreadsheetData[] 
}
export const SaveMenu = ({ bankAccounts, categories, spreadsheets }: SaveMenuProps) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [saveType, setSaveType] = useState<"single" | "multi" | null>(null);
  const [normalizedData, setNormalizedData] = useState<any[]>([]); // Holds normalized data for preview

  /** Handles saving based on method */
  const handleSave = async (
    method: "xlsx" | "localStorage" | "dynamicSingleKeyValueMapping" | "dynamicMultiKeyValueMapping"
  ) => {
    if (method === "dynamicSingleKeyValueMapping") {
      setNormalizedData(normalizeSingleKeyValue(spreadsheets));
      setSaveType("single");
      setDialogOpen(true);
    } else if (method === "dynamicMultiKeyValueMapping") {
      setNormalizedData(normalizeMultiKeyValue(spreadsheets));
      setSaveType("multi");
      setDialogOpen(true);
    } else {
      // Other save methods
      await saveSpreadsheetData(spreadsheets, method);
    }
  };

  return (
    <div className="my-auto">
      {/* Save Menu */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="flex items-center text-black dark:text-white bg-transparent hover:bg-transparent mr-2">
            <Save size={18} /> <ChevronDown size={14} />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuItem onClick={() => handleSave("xlsx")}>
            Save as Excel (.xlsx)
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleSave("localStorage")}>
            Save to Local Storage
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleSave("dynamicSingleKeyValueMapping")}>
            Save as Single Key Value
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleSave("dynamicMultiKeyValueMapping")}>
            Save as Multi Key Value
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Dynamic Key Value Dialog */}
      <DynamicKeyValueButton
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        normalizedData={normalizedData}
        mappingType={saveType}
        bankAccounts={bankAccounts}
        categories={categories}
        spreadsheets={spreadsheets}
      />
    </div>
  );
};

/** Helper: Normalize Single Key-Value Mapping */
const normalizeSingleKeyValue = (spreadsheetData: SpreadsheetData[]) => {
  return spreadsheetData.flatMap((sheet) => {
    const result: any[] = [];
    const headers = sheet.rows[0]?.map((cell) => cell?.value) || [];
    sheet.rows.forEach((row, rowIndex) => {
      if (rowIndex <= 0) return; // Skip header rows
      const item = row[0]?.value || "";
      row.slice(1).forEach((cell, colIndex) => {
        const date = headers[colIndex + 1] || "Unknown Month";
        if (cell?.value) {
          result.push({ date, item, value: cell.value, sheetName: sheet.title });
        }
      });
    });
    return result;
  });
};

/** Helper: Normalize Multi Key-Value Mapping */
const normalizeMultiKeyValue = (spreadsheetData: SpreadsheetData[]) => {
  return spreadsheetData.flatMap((sheet) => {
    const result: { date: string; item: string; value: any, sheetName: string }[] = [];
    const headers = sheet.rows[0]?.map((cell) => cell?.value) || [];
    sheet.rows.forEach((row, rowIndex) => {
      if (rowIndex <= 0) return; // Skip header rows
      row.forEach((cell, colIndex) => {
        if (colIndex % 2 === 1) {
          const date = headers[colIndex - 1] || "Unknown Month";
          const item = row[colIndex - 1]?.value || "";
          if (cell?.value) {
            result.push({ date, item, value: cell.value, sheetName: sheet.title });
          }
        }
      });
    });
    return result;
  });
};

/** Helper: Save Spreadsheet Data */
const saveSpreadsheetData = async (
  data: SpreadsheetData[],
  method: "xlsx" | "localStorage"
) => {
  try {
    console.log(`Saving data using method: ${method}`, data);

    if (method === "localStorage") {
      // Save to Local Storage
      localStorage.setItem("spreadsheets", JSON.stringify(data));
      alert("Data saved to Local Storage");
    } else if (method === "xlsx") {
      // Save as Excel File (.xlsx)
      const workbook = XLSX.utils.book_new();

      data.forEach((sheetData) => {
        const sheet = XLSX.utils.aoa_to_sheet(sheetData.rows.map((row) => row.map((cell) => cell?.value || "")));
        XLSX.utils.book_append_sheet(workbook, sheet, sheetData.title || "Sheet");
      });

      // Trigger download
      XLSX.writeFile(workbook, "spreadsheet_data.xlsx");
      alert("Excel file has been saved successfully!");
    } else if (method === "database") {
      // Save to Database (Assume an API exists)
      const response = await fetch("/api/saveSpreadsheet", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        alert("Data saved to Database.");
      } else {
        throw new Error("Database save failed.");
      }
    }
  } catch (error: any) {
    console.error("Error saving spreadsheet data:", error);
    alert(`Error: ${error.message}`);
  }
};
"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON>, LineChart, ResponsiveContainer } from "recharts";
import {
  Briefcase,
  Building2,
  Coins,
  Computer,
  CreditCard,
  Home,
  Landmark,
  LifeBuoy,
  Wallet,
  Bitcoin,
} from "lucide-react";
import * as LucideIcons from 'lucide-react';

// Sample data for the sparklines
const generateData = (count: number, positive: boolean) => {
  return Array.from({ length: count }, (_, i) => ({
    amount: positive ? 50 + Math.random() * 20 : 70 - Math.random() * 20,
  }));
};

export function MetricCards({ overview, assets }: any) {
  const totalInvestment = React.useMemo(() => {
    return assets.totalInvestments.reduce((acc: number, curr: any) => acc + curr.amount, 0);
  }, [assets.totalInvestments]);
  const metrics = [
    {
      title: "Income",
      amount: overview.monthlyIncome.current,
      decimal: (overview.monthlyIncome.current - Math.floor(overview.monthlyIncome.current)).toFixed(2).slice(1),
      change: overview.monthlyIncome.change > 0 ? `+$${overview.monthlyIncome.change}` : `-$${overview.monthlyIncome.change}`,
      percentage: overview.monthlyIncome.changePercentage > 0 ? `+${overview.monthlyIncome.changePercentage}%` : `-${overview.monthlyIncome.changePercentage}%`,
      trend: overview.monthlyIncome.change > 0 ? "positive" : "negative",
      details: overview.recentIncomeTransactions,
    },
    {
      title: "Spending",
      amount: overview.monthlyExpenses.current,
      decimal: (overview.monthlyExpenses.current - Math.floor(overview.monthlyExpenses.current)).toFixed(2).slice(1),
      change: overview.monthlyExpenses.change > 0 ? `+$${overview.monthlyExpenses.change}` : `-$${overview.monthlyExpenses.change}`,
      percentage: overview.monthlyExpenses.changePercentage > 0 ? `+${overview.monthlyExpenses.changePercentage}%` : `-${overview.monthlyExpenses.changePercentage}%`,
      trend: overview.monthlyExpenses.change > 0 ? "negative" : "positive",
      details: overview.recentExpenseTransactions,
    },
    {
      title: "Savings Rate",
      amount: overview.savingsGoals.current,
      decimal: (overview.savingsGoals.current - Math.floor(overview.savingsGoals.current)).toFixed(2).slice(1),
      change: overview.savingsGoals.change > 0 ? `+$${overview.savingsGoals.change}` : `-$${overview.savingsGoals.change}`,
      percentage: overview.savingsGoals.changePercentage > 0 ? `+${overview.savingsGoals.changePercentage}%` : `-${overview.savingsGoals.changePercentage}%`,
      trend: overview.savingsGoals.change > 0 ? "positive" : "negative",
      details: overview.savingsGoals.recentSavingsTransactions,
    },
    {
      title: "Investing",
      amount:  Math.floor(totalInvestment),
      decimal: (totalInvestment - Math.floor(totalInvestment)).toFixed(2).slice(1),
      change: "+$4,987.65",
      percentage: "+1.9%",
      trend: "positive",
      details: assets.totalInvestments,
    },
  ];

  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {metric.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metric.amount}
              <span className="text-xl">{metric.decimal}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              <span
                className={
                  metric.trend === "positive"
                    ? "text-green-500"
                    : "text-red-500"
                }
              >
                {metric.change} {metric.percentage}
              </span>{" "}
              vs last month
            </div>
            <div className="h-[80px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={generateData(20, metric.trend === "positive")}>
                  <Line
                    type="monotone"
                    dataKey="amount"
                    stroke={metric.trend === "positive" ? "#22c55e" : "#ef4444"}
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="flex gap-2 mt-4 flex-wrap">
              {metric.details.map((detail: any, index: any) => (
                <div key={index} className="flex items-center gap-1.5">
                      <DynamicIcon 
                        name={detail.icon} 
                        className="h-3.5 w-3.5 text-muted-foreground" 
                      />
                  <span className="text-xs text-muted-foreground">
                    {typeof detail.amount === "number"
                      ? detail.amount.toFixed(2)
                      : parseFloat(detail.amount || "0").toFixed(2)}
                    <span className="ml-1 text-gray-500">{detail.category}</span>
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

const DynamicIcon = ({ name, className }: { 
  name: string, 
  className?: string 
}) => {
  const IconComponent = LucideIcons[name as keyof typeof LucideIcons] as React.ComponentType<{ className?: string }>;
  
  return IconComponent ? (
    <IconComponent className={className} />
  ) : (
    <LucideIcons.HelpCircle className={className} />
  );
};
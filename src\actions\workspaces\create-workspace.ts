'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache"
import { z } from "zod"
import { createWorkspaceSchema } from "@/components/workspaces/schemas";
import { generateInviteCode } from "@/lib/utils";

export async function createWorkspace(values: z.infer<typeof createWorkspaceSchema>) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }
    const validatedFields = createWorkspaceSchema.safeParse(values)
    
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        success: false
      }
    }

    const { name, orgnr, address, postalCode, city } = validatedFields.data

    const workspace = await prisma.workspace.create({
      data: {
        name,
        orgnr,
        address,
        postalCode,
        city,
        inviteCode: generateInviteCode(6),
        members: {
          create: {
            userId,
            role: "ADMIN" // Assign the creator as an ADMIN for the workspace
          }
        }
      }
    })

    revalidatePath('/workspaces')
    
    return {
      data: workspace,
      success: true
    }

  } catch (error) {
    console.error('Error creating workspace:', error)
    return {
      error: "Failed to create workspace.",
      success: false
    }
  }
}
// In actions/speeches/update-speech.ts

"use server"

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { updateSpeechSchema } from "@/components/speeches/schemas";
import { z } from "zod";

export async function updateSpeech(values: z.infer<typeof updateSpeechSchema>) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return {
        success: false,
        error: "Unauthorized",
      };
    }

    // Validate input with zod
    const validatedData = updateSpeechSchema.parse(values);

    // Find speech to verify ownership
    const existingSpeech = await prisma.speech.findUnique({
      where: {
        id: validatedData.id,
        userId: userId,
      },
    });

    if (!existingSpeech) {
      return {
        success: false,
        error: "Speech not found",
      };
    }

    // Update the speech
    const updated = await prisma.speech.update({
      where: {
        id: validatedData.id,
      },
      data: {
        title: validatedData.title,
        description: validatedData.description,
        projectId: validatedData.projectId,
      },
      include: {
        recordings: true,
        transcriptions: true,
        analyses: true,
        project: true,
      },
    });

    return {
      success: true,
      data: updated,
    };
  } catch (error) {
    console.error("Error updating speech:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update speech",
    };
  }
}

export async function updateSpeechDescription(
  speechId: string,
  data: Partial<z.infer<typeof updateSpeechSchema>>
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return {
        success: false,
        error: "Unauthorized",
      };
    }

    // Validate input
    if (!speechId) {
      return {
        success: false,
        error: "Speech ID is required",
      };
    }

    // Find speech to verify ownership
    const existingSpeech = await prisma.speech.findUnique({
      where: {
        id: speechId,
        userId,
      },
    });

    if (!existingSpeech) {
      return {
        success: false,
        error: "Speech not found",
      };
    }

    // Update speech description
    const updated = await prisma.speech.update({
      where: {
        id: speechId,
      },
      data: {
        // Only update the fields that are provided
        title: data.title,
        description: data.description,
        projectId: data.projectId,
      },
      include: {
        recordings: true,
        transcriptions: true,
        analyses: true,
        project: true,
      },
    });

    return {
      success: true,
      data: updated,
    };
  } catch (error) {
    console.error("Error updating speech:", error);
    return {
      success: false,
      error: "Failed to update speech",
    };
  }
}
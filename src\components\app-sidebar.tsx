"use client";

import * as React from "react";
import { Workspace, Member } from "@prisma/client";
import { usePathname } from "next/navigation";
import {
  Building,
  Command,
  CreditCard,
  Frame,
  Layers,
  Calculator,
  LayoutDashboard,
  LayoutTemplate,
  Leaf,
  LifeBuoy,
  Map,
  PieChart,
  PieChartIcon,
  Send,
  Signal,
  Scale,
  ScanText,
  ScrollText,
  TextQuote,
  CalendarClock,
} from "lucide-react";
import { LiaGoogleDrive } from "react-icons/lia";
import { PiChatsCircle } from "react-icons/pi";

import { NavMain } from "@/components/nav-main";
//import { NavProjects } from "@/components/nav-projects";
import { useWorkspaces } from "@/hooks/use-workspaces";
import { NavSecondary } from "@/components/nav-secondary";
import { NavWorkspace } from "./workspaces/nav-workspace";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  <PERSON>barContent,
  <PERSON>barFooter,
  <PERSON><PERSON><PERSON>eader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import  { useUser } from "@clerk/nextjs" 
import { useWorkspaceId } from "./workspaces/use-workspace-id";

const data = {
  navMain: [
    {
      title: "Financial",
      url: "#",
      icon: PieChart,
      items: [
        {
          title: "Overview",
          url: "/dashboard",
          icon: LayoutDashboard,
        },
        {
          title: "Banking",
          url: "/banking",
          icon: Layers,
        },
        {
          title: "社區管理費",
          url: "/deposits",
          icon: LifeBuoy,
        },
        {
          title: "Statements",
          url: "/statements",
          icon: Calculator,
        },
        {
          title: "Categories",
          url: "/categories",
          icon: PieChartIcon,
        },
        {
          title: "Investments",
          url: "/investments",
          icon: Signal,
        },
        {
          title: "Assets",
          url: "/assets",
          icon: Building,
        },
        {
          title: "Savings",
          url: "/savings",
          icon: Leaf,
        },
        {
          title: "Liabilities",
          url: "/liabilities",
          icon: CreditCard,
        },
      ],
    },
    {
      title: "Contracts",
      url: "/contracts",
      icon: Scale,
    },
    {
      title: "Reports",
      url: "#",
      icon: PieChart,
      items: [
        {
          title: "Monthly",
          url: "/reports/month",
          icon: ScrollText
        },
        {
          title: "Annual",
          url: "/reports/annual",
          icon: ScrollText
        },
      ],
    },
    {
      title: "My Tasks",
      url: "/tasks",
      icon: Frame,
    },
    {
      title: "My Calendar",
      url: "/meeting",
      icon: CalendarClock,
    },
    {
      title: "Voice2Text",
      url: "/speeches",
      icon: TextQuote,
    },
  ],
  navSecondary: [
    {
      title: "Open Canvas",
      url: "/canvas",
      icon: PiChatsCircle,
    },
    {
      title: "Receipt Scan",
      url: "/receipt-scan",
      icon: ScanText,
    },
    {
      title: "Google Drive",
      url: "/gdrive",
      icon: LiaGoogleDrive,
    },
    {
      title: "Feedback",
      url: "#",
      icon: Send,
    },
  ],
  projects: [
    {
      name: "Projects",
      url: "/projects",
      icon: Frame,
    },
    /*{
      name: "Account 2",
      url: "#",
      icon: PieChart,
    },
    {
      name: "Account 3",
      url: "#",
      icon: Map,
    },*/
  ],
};

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  workspaces:  { id: string; name: string; members: Member[] }[];
}

export function AppSidebar({ workspaces, ...props }: AppSidebarProps) {
  const pathname = usePathname();
  const { user } = useUser();
  const { currentWorkspaceId } = useWorkspaces();
  const workspaceId = useWorkspaceId();

  const profile = {
    name: user?.username!,
    email: user?.primaryEmailAddress?.emailAddress!,
    avatar: user?.imageUrl!,
  };

  // Add isActive property based on current pathname
  const navMainWithActive = data.navMain.map((item) => {
    if (!item.url) return item;
    const activeWorkspaceId =  currentWorkspaceId ? currentWorkspaceId : workspaceId;

    let effectiveUrl = item.url;
    if (item.title === "My Tasks" && activeWorkspaceId) {
      effectiveUrl = `/workspaces/${activeWorkspaceId}/tasks`;
    }
    if (item.title === "My Calendar" && activeWorkspaceId) {
      effectiveUrl = `/workspaces/${activeWorkspaceId}/meeting`;
    }
    if (item.title === "Voice2Text" && activeWorkspaceId) {
      effectiveUrl = `/workspaces/${activeWorkspaceId}/speeches`;
    }
  
    return {
      ...item,
      url: effectiveUrl,
      isActive: pathname === effectiveUrl || pathname.startsWith(effectiveUrl + "/"),
      items: item.items?.map((subItem) => ({
        ...subItem,
        isActive: pathname === subItem.url,
      })),
    };
  });
  
  /*const projectsWithActive = data.projects.map((item) => ({
    ...item,
    isActive: pathname === item.url,
  }))*/

  return (
    <Sidebar id="no-print" variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="#">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Command className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Badget</span>
                  <span className="truncate text-xs">Enterprise</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
      <NavMain items={navMainWithActive} />
      {/*<NavProjects projects={wrokspacesWithActive} />*/}
      <NavWorkspace workspaces={workspaces} />
      <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={profile} />
      </SidebarFooter>
    </Sidebar>
  );
}

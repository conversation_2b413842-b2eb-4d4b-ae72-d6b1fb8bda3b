import axios from "axios";
import { useRouter } from "next/router";

const handleAccessTokenExpiration = async () => {
  // Instead of using localStorage, we assume the refresh token is stored in HTTP-only cookies
  try {
    // Send a request to your backend to refresh the access token using the refresh token in cookies
    const response = await axios.post("/api/auth/refresh-token");

    if (response.status === 200) {
      // Assuming the backend sends the new access token in the response body (not recommended to expose access_token to client)
      const accessToken = response.data.access_token; // Your backend should not expose access_token in this way
      return accessToken;
    } else {
      throw new Error("Failed to refresh access token");
    }
  } catch (err) {
    console.error("Error during token refresh:", err);
    // Optionally, handle token refresh failure, e.g., redirecting to login page
    useRouter().push("/glogin");
    return null;
  }
};

export default handleAccessTokenExpiration;

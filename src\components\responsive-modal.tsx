import { useMedia } from "react-use";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog"
import { Drawer, DrawerContent } from "@/components/ui/drawer"
import * as VisuallyHidden from "@radix-ui/react-visually-hidden";
import { cn } from "@/lib/utils";

interface ResponsiveModalProps {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  className?: string;
}

export const ResponsiveModal = ({
  children,
  open,
  onOpenChange,
  className,
}: ResponsiveModalProps ) => {
  const isDesktop = useMedia("(min-width: 1024px)", true)
  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className={cn("w-full p-0 border-none overflow-y-auto hide-scrollbar max-h-[85vh] sm:max-w-xl", className)}>
          <DialogTitle>
            <VisuallyHidden.Root>x</VisuallyHidden.Root>
          </DialogTitle>
          {children}
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent>
        <div className="overflow-y-auto hide-scrollbar max-h-[85vh]">
          {children}  
        </div>
      </DrawerContent>
    </Drawer>
  )
}

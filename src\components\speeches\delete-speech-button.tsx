'use client'

import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useDeleteSpeech } from "./use-delete-speech"
import { Loader2 } from "lucide-react"

interface DeleteSpeechButtonProps {
  speechId: string
  isAlertOpen: boolean;
  setIsAlertOpen: (open: boolean) => void;
}

export function DeleteSpeechButton({ 
  speechId, 
  isAlertOpen,
  setIsAlertOpen,
}: DeleteSpeechButtonProps) {
  const { mutate, isPending } = useDeleteSpeech();
  const onDelete = async () => {
    mutate(speechId)
  }

  return (
    <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
      <AlertDialogTrigger asChild onClick={(e) => {
        e.preventDefault(); // Prevent dropdown from closing
        setIsAlertOpen(true);
      }}>
        <button disabled={isPending}>
          {isPending ? (
            <><Loader2 className="h-4 w-4 animate-spin" />Delete Speech</>
          ) : (
            "Delete Speech"
          )}
        </button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the speech
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={() => setIsAlertOpen(false)}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onDelete}>
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
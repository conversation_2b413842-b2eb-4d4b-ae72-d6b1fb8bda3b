import { NextResponse } from "next/server";
import { google } from "googleapis";
import { authenticateGoogleOAuth2 } from "../authenticate";
import config from "@/store/config.json";

export async function GET(req: Request) {
  try {
    const auth = await authenticateGoogleOAuth2();
    if (!auth) {
      throw new Error('Authentication failed');
    }

    const { searchParams } = new URL(req.url);
    const folderId = searchParams.get("folderId");
    if (!folderId) {
      return NextResponse.json(
        { error: "Folder ID is required" },
        { status: 400 }
      );
    }
    
    const drive = google.drive({
      version: 'v3',
      auth: auth
    });

    const response = await drive.files.list({
      q: `mimeType!='application/vnd.google-apps.folder' and trashed = false and parents in '${folderId}'`,
      corpora: config.directory.team_drive ? "teamDrive" : "allDrives",
      includeTeamDriveItems: true,
      supportsAllDrives: true,
      teamDriveId: config.directory.team_drive,
      fields: "files(id, name, mimeType)"
    });

    return NextResponse.json({ files: response.data.files });
  } catch (error: any) {
    console.error("Drive API Error:", error);
    
    if (error.response?.status === 401) {
      return NextResponse.json(
        { error: "Token expired" },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch files" },
      { status: 500 }
    );
  }
}
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { getProject } from "@/actions/projects/get-project";
import { UpdateProjectForm } from "@/components/projects/update-project-form";

interface ProjectIdSettingsPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default async function ProjectIdPage(props: ProjectIdSettingsPageProps) {
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const params = await props.params;
  const initialValues = await getProject(params.projectId);

  if (!initialValues) {
    throw new Error("Project not found");
  }

  
  return (
    <div className="w-full lg:max-w-xl mx-auto">
      <UpdateProjectForm project={initialValues} />
    </div>
  )
}
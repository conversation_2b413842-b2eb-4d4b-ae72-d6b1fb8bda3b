'use client'

import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useDeleteTask } from "@/components/tasks/use-delete-task"
import { Loader2 } from "lucide-react"

interface DeleteTaskButtonProps {
  taskId: string
  isAlertOpen: boolean;
  setIsAlertOpen: (open: boolean) => void;
}

export function DeleteTaskButton({ 
  taskId, 
  isAlertOpen,
  setIsAlertOpen,
}: DeleteTaskButtonProps) {
  const { mutate, isPending } = useDeleteTask();
  const onDelete = async () => {
    mutate(taskId)
  }

  return (
    <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
      <AlertDialogTrigger asChild onClick={(e) => {
        e.preventDefault(); // Prevent dropdown from closing
        setIsAlertOpen(true);
      }}>
        <button disabled={isPending}>
          {isPending ? (
            <><Loader2 className="h-4 w-4 animate-spin" />Delete Task</>
          ) : (
            "Delete Task"
          )}
        </button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the task
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={() => setIsAlertOpen(false)}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onDelete}>
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
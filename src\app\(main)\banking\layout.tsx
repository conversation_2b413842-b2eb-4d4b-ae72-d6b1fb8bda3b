import { CreateDepositModal } from "@/components/time-deposits/create-deposit-modal";
import { UpdateDepositModal } from "@/components/time-deposits/update-deposit-modal";


interface BankingLayoutProps {
  children?: React.ReactNode;
}

export default async function BankingLayout(props: BankingLayoutProps) {
  return (
    <div>
      {/* Render the modal */}
      <CreateDepositModal />
      <UpdateDepositModal />
      {props.children}
    </div>
  );
}

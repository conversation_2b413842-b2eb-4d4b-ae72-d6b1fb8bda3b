"use client";

import { useForm, useFieldArray, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useCreateDeposit } from "./use-create-deposits";
import { createDepositSchema } from "./schemas";
import { format, parse, isValid, startOfDay } from "date-fns";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator";
import { Loader2, Plus } from "lucide-react";
import { toast } from "sonner";

interface DepositFormProps {
  onCancel?: () => void;
  bankAccountId: string;
}

type FormValues = z.infer<typeof createDepositSchema>;

export const CreateDepositForm = ({ onCancel, bankAccountId }: DepositFormProps) => {
  const { mutate, isPending } = useCreateDeposit(bankAccountId);
  console.log("bankAccountId", bankAccountId)

  const form = useForm<{ timeDeposits: FormValues[] }>({
    resolver: zodResolver(z.object({
      timeDeposits: z.array(createDepositSchema)
    })),
    defaultValues: {
      timeDeposits: [{ 
        type: "AVAILABLE", 
        amount: 0,
        certificateNo: "",
        period: "1",
        interestRate: 1.665,
        categoryId: "cm53sxms00001jv036gtap8fq", 
        accountId: bankAccountId,
        transactionDate: startOfDay(new Date()),
      }],
    }
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "timeDeposits",
  });

  const onSubmit: SubmitHandler<{ timeDeposits: FormValues[] }> = async (formData) => {
    try {
      console.log("Full formData", JSON.stringify(formData, null, 2));
  
      // Validate each time deposit
      const validationResults = formData.timeDeposits.map(deposit => {
        try {
          createDepositSchema.parse(deposit);
          return { valid: true, deposit };
        } catch (error) {
          console.error("Validation error for deposit", error);
          return { valid: false, deposit, error };
        }
      });
  
      // Filter out invalid deposits
      const validTimeDeposits = validationResults
        .filter(result => result.valid)
        .map(result => result.deposit);
  
      if (validTimeDeposits.length === 0) {
        toast.error("No valid time deposits to create");
        return;
      }
  
      const values = {
        timeDeposits: validTimeDeposits.map((timeDeposit) => ({
          accountId: timeDeposit.accountId || bankAccountId,
          certificateNo: timeDeposit.certificateNo || "", // Allow empty string
          period: timeDeposit?.period ?? "1",
          interestRate: timeDeposit?.interestRate ?? 1.665,
          description: timeDeposit?.description ?? "",
          amount: timeDeposit.amount,
          type: timeDeposit.type,
          categoryId: timeDeposit.categoryId ?? "cm53sxms00001jv036gtap8fq",
          currencyIso: "TWD",
          date: timeDeposit.transactionDate || startOfDay(new Date()),
        })),
      };
      
      console.log("Processed values for mutation", JSON.stringify(values, null, 2));
  
      mutate(values.timeDeposits, {
        onSuccess: () => {
          form.reset();
          onCancel?.();
        },
        onError: (error) => {
          console.error("Mutation error", error);
          toast.error("Failed to create time deposits");
        }
      });
    } catch (error) {
      console.error("Unexpected error", error);
      toast.error("An unexpected error occurred");
    }
  };

  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7 py-4">
        <CardTitle className="text-xl font-bold">Create a new deposit</CardTitle>
      </CardHeader>
      <div className="px-7">
        <Separator />
      </div>
      <CardContent className="p-7">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="space-y-4 border-b pb-4">
                {/* TimeDeposit Fields */}
                <div className="grid grid-cols-3 gap-4">
                  {/* Amount Field */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.amount`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter amount"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* TimeDeposit Type Dropdown */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.type`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Type</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="AVAILABLE">Available</SelectItem>
                              <SelectItem value="WITHDRAWN">Withdrawn</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                  {/* TimeDeposit Date */}
                  <FormField
                    control={form.control}
                    name={`timeDeposits.${index}.transactionDate`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            {...field}
                            value={
                              field.value 
                                ? format(field.value, 'yyyy-MM-dd') 
                                : format(new Date(), 'yyyy-MM-dd') // Fallback to today
                            }
                            onChange={(e) => {
                              const inputDate = e.target.value;
                              
                              const parsedDate = parse(inputDate, 'yyyy-MM-dd', new Date());
                              
                              if (isValid(parsedDate) && parsedDate <= new Date()) {
                                field.onChange(startOfDay(parsedDate));
                              } else {
                                // If invalid, reset to today
                                field.onChange(startOfDay(new Date()));
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  {/* CertificateNo Field */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.certificateNo`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Certificate Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Time Deposit Certificate No"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                  
                  {/* Interest Rate Field */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.interestRate`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Interest Rate %</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field} 
                            onChange={(e) => field.onChange(e.target.value === '' ? undefined : parseFloat(e.target.value))}
                            value={field.value === undefined || field.value === null ? '' : field.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* TimeDeposit Period */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.period`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Period</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Time Deposit Period"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                </div>

                {/* Description Field */}
                <FormField 
                  control={form.control} 
                  name={`timeDeposits.${index}.description`} 
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Add details about the timeDeposit"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} 
                />

                {/* Remove TimeDeposit Button */}
                {fields.length > 1 && (
                  <Button 
                    type="button" 
                    variant="destructive" 
                    size="sm"
                    onClick={() => remove(index)}
                  >
                    Remove TimeDeposit
                  </Button>
                )}
              </div>
            ))}
            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => append({ 
                  type: "AVAILABLE", amount: 0, categoryId: "cm53sxms00001jv036gtap8fq", 
                  accountId: "", certificateNo: "", period: "1", interestRate: 1.665, description: ""
                })}
              >
                <Plus className="mr-2 h-4 w-4" /> Add More TimeDeposit
              </Button>

              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  "Create TimeDeposits"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

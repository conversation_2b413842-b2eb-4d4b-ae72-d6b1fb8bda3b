"use client"
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreateFolder() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({ values }: {
      values: { folderId: string | null; folderName: string }
    }) => {
      //console.log("values", values)
      const response = await fetch(`/api/gdrive/create-folder`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });
      const responseData = await response.json();
      //console.log("responseData", responseData)

      if (!response.ok || !responseData.folder) {
        throw new Error(`Failed to create folder.`);
      }
      
      return responseData
    },
    onSuccess: ({ folder, existingFolder }) => {
      if (existingFolder) {
        toast.info('Folder already exists!');
      } else {
        toast.success('Folder created!');
      }
      
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['googledrive'] });
      queryClient.invalidateQueries({ queryKey: ['googledrive', folder?.id] });
    },
    onError: (error: any) => {
      console.log("error", error)
      toast.error(error.message || 'Failed to create folder.');
    },
  });

  return mutation;
}

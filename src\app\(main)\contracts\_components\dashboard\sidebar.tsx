"use client";

import { cn } from "@/lib/utils";
import { FileText, LayoutDashboard, PanelLeft } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ElementType, useState } from "react";

export const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(true);

  return (
    <aside
      className={cn(
        "bg-background/95 text-foreground hidden lg:flex flex-row transition-all",
        isCollapsed ? "w-auto" : "max-w-[120px]"
      )}
    >
      {/*<div className="flex items-center justify-between p-1 mt-1">
        {!isCollapsed && <span className="font-semibold text-sm">Dashboard</span>}
        <Button
          size={"icon"}
          variant={"ghost"}
          onClick={() => setIsCollapsed((prev) => !prev)}
        >
          <PanelLeft className="size-4" />
        </Button>
      </div>*/}
      {SidebarContent({ isCollapsed })}
    </aside>
  );
};

export const SidebarContent = ({ isCollapsed }: { isCollapsed: boolean }) => {
  const pathname = usePathname();

  const sidebarItems = [
    {
      icon: LayoutDashboard,
      label: "Contracts",
      href: "/contracts",
    },
    {
      icon: FileText,
      label: "Results",
      href: "/contracts/results",
    },
  ];

  return (
    <div className="flex flex-row flex-grow">
      <nav className="flex-grow pt-4">
        <ul role="list" className="flex flex-row">
          {sidebarItems.map((item) => (
            <Navlink key={item.label} path={pathname} link={item} isCollapsed={isCollapsed} />
          ))}
        </ul>
      </nav>
    </div>
  );
};

const Navlink = ({
  path,
  link,
  isCollapsed,
}: {
  path: string;
  link: {
    icon: ElementType;
    label: string;
    href: string;
    target?: string;
  };
  isCollapsed: boolean;
}) => {
  return (
    <li>
      <Link
        href={link.href}
        target={link.target}
        className={cn(
          "group flex items-center gap-x-3 rounded-md px-3 py-2 text-sm leading-5 text-foreground",
          path === link.href ? "bg-gray-200 dark:bg-gray-200/10" : "hover:bg-gray-200 dark:hover:bg-gray-200/10"
        )}
      >
        <link.icon className="size-4 shrink-0" />
        {!isCollapsed && <span>{link.label}</span>}
      </Link>
    </li>
  );
};

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="flex flex-col h-screen bg-gray-100">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">{children}</div>
    </div>
  );
}

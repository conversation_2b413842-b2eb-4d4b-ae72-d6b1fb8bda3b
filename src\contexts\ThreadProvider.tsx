import {
  HAS_EMPTY_THREADS_CLEARED_COOKIE,
  THREAD_ID_COOKIE_NAME,
  THREAD_ID_LS_NAME,
  THREAD_ID_QUERY_PARAM,
  WEB_SEARCH_RESULTS_QUERY_PARAM,
} from "@/constants";
import {
  ALL_MODEL_NAMES,
  ALL_MODELS,
  DEFAULT_MODEL_CONFIG,
  DEFAULT_MODEL_NAME,
} from "@badget/shared/models";
import { Thread } from "@prisma/client";
import { 
  findThread,
  findThreads,
  createNewThread, 
  removeThread,
  fetchAndDeleteThreads,
} from "@/actions/canvas/thread";
import { getCookie, removeCookie, setCookie } from "@/lib/cookies";
import { CustomModelConfig } from "@badget/shared/types";
import { useRouter, useSearchParams } from "next/navigation";
import { createContext, ReactNode, useContext, useMemo, useState } from "react";
import { useUserContext } from "./UserContext";
import { useToast } from "@/hooks/use-toast";
import useLocalStorage from "@/hooks/useLocalStorage";

export type ThreadMetadata = {
  clerk_user_id?: string;
  customModelName?: ALL_MODEL_NAMES;
  modelConfig?: CustomModelConfig;
  thread_title?: string;
}

type ThreadContentType = {
  threadId: string | undefined;
  userThreads: Thread[];
  isUserThreadsLoading: boolean;
  modelName: ALL_MODEL_NAMES;
  modelConfig: CustomModelConfig;
  modelConfigs: Record<ALL_MODEL_NAMES, CustomModelConfig>;
  createThreadLoading: boolean;
  removeThreadIdQueryParam: () => void;
  setThreadIdQueryParam: (id: string) => void;
  clearThreadsWithNoValues: () => Promise<void>;
  searchOrCreateThread: (isNewThread?: boolean) => Promise<Thread | undefined>;
  getUserThreads: () => Promise<void>;
  deleteThread: (id: string, clearMessages: () => void) => Promise<void>;
  setThreadId: (id: string) => void;
  setModelName: (name: ALL_MODEL_NAMES) => void;
  setModelConfig: (
    modelName: ALL_MODEL_NAMES,
    config: CustomModelConfig
  ) => void;
};

const ThreadContext = createContext<ThreadContentType | undefined>(undefined);

export function ThreadProvider({ children }: { children: ReactNode }) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user } = useUserContext();
  const { toast } = useToast();
  const [threadId, setThreadId] = useState<string>();
  const [userThreads, setUserThreads] = useState<Thread[]>([]);
  const [isUserThreadsLoading, setIsUserThreadsLoading] = useState(false);
  const [modelName, setModelName] =
    useState<ALL_MODEL_NAMES>(DEFAULT_MODEL_NAME);
  const [createThreadLoading, setCreateThreadLoading] = useState(false);
  const [threadIdLS, setThreadIdLS] = useLocalStorage<string>(
    THREAD_ID_LS_NAME,
    ""
  );

  const [modelConfigs, setModelConfigs] = useState<
    Record<ALL_MODEL_NAMES, CustomModelConfig>
  >(() => {
    // Initialize with default configs for all models
    const initialConfigs: Record<ALL_MODEL_NAMES, CustomModelConfig> =
      {} as Record<ALL_MODEL_NAMES, CustomModelConfig>;

    ALL_MODELS.forEach((model) => {
      const modelKey = model.modelName || model.name;

      initialConfigs[modelKey] = {
        ...model.config,
        provider: model.config.provider,
        temperatureRange: {
          ...(model.config.temperatureRange ||
            DEFAULT_MODEL_CONFIG.temperatureRange),
        },
        maxTokens: {
          ...(model.config.maxTokens || DEFAULT_MODEL_CONFIG.maxTokens),
        },
        ...(model.config.provider === "azure_openai" && {
          azureConfig: {
            azureOpenAIApiKey: process.env._AZURE_OPENAI_API_KEY || "",
            azureOpenAIApiInstanceName:
              process.env._AZURE_OPENAI_API_INSTANCE_NAME || "",
            azureOpenAIApiDeploymentName:
              process.env._AZURE_OPENAI_API_DEPLOYMENT_NAME || "",
            azureOpenAIApiVersion:
              process.env._AZURE_OPENAI_API_VERSION || "2024-08-01-preview",
            azureOpenAIBasePath: process.env._AZURE_OPENAI_API_BASE_PATH,
          },
        }),
      };
    });
    return initialConfigs;
  });

  const modelConfig = useMemo(() => {
    // Try exact match first, then try without "azure/" or "groq/" prefixes
    return (
      modelConfigs[modelName] || modelConfigs[modelName.replace("azure/", "")]
    );
  }, [modelName, modelConfigs]);

  const setModelConfig = (
    modelName: ALL_MODEL_NAMES,
    config: CustomModelConfig
  ) => {
    setModelConfigs((prevConfigs) => {
      if (!config || !modelName) {
        return prevConfigs;
      }
      return {
        ...prevConfigs,
        [modelName]: {
          ...config,
          provider: config.provider,
          temperatureRange: {
            ...(config.temperatureRange ||
              DEFAULT_MODEL_CONFIG.temperatureRange),
          },
          maxTokens: {
            ...(config.maxTokens || DEFAULT_MODEL_CONFIG.maxTokens),
          },
          ...(config.provider === "azure_openai" && {
            azureConfig: {
              ...config.azureConfig,
              azureOpenAIApiKey:
                config.azureConfig?.azureOpenAIApiKey ||
                process.env._AZURE_OPENAI_API_KEY ||
                "",
              azureOpenAIApiInstanceName:
                config.azureConfig?.azureOpenAIApiInstanceName ||
                process.env._AZURE_OPENAI_API_INSTANCE_NAME ||
                "",
              azureOpenAIApiDeploymentName:
                config.azureConfig?.azureOpenAIApiDeploymentName ||
                process.env._AZURE_OPENAI_API_DEPLOYMENT_NAME ||
                "",
              azureOpenAIApiVersion:
                config.azureConfig?.azureOpenAIApiVersion ||
                "2024-08-01-preview",
              azureOpenAIBasePath:
                config.azureConfig?.azureOpenAIBasePath ||
                process.env._AZURE_OPENAI_API_BASE_PATH,
            },
          }),
        },
      };
    });
  };

  const createThread = async (): Promise<Thread | undefined> => {
    if (!user) {
      toast({
        title: "Failed to create thread",
        description: "User not found",
        duration: 5000,
        variant: "destructive",
      });
      return;
    }
    setCreateThreadLoading(true);

    try {
      const response = await createNewThread(modelName, modelConfig)
      if (response?.success && response.thread) {
        const thread = response.thread
        setThreadId(thread.thread_id);
        setThreadIdLS(thread.thread_id);
        // Fetch updated threads so the new thread is included.
        await getUserThreads();
        return thread;        
      }
    } catch (e) {
      console.error("Failed to create thread", e);
      toast({
        title: "Failed to create thread",
        description:
          "An error occurred while trying to create a new thread. Please try again.",
        duration: 5000,
        variant: "destructive",
      });
    } finally {
      setCreateThreadLoading(false);
    }
  };

  const getUserThreads = async () => {
    if (!user) {
      toast({
        title: "Failed to create thread",
        description: "User not found",
        duration: 5000,
        variant: "destructive",
      });
      return;
    }

    setIsUserThreadsLoading(true);
    try {
      const response = await findThreads(100)
      if (response?.success && response.results) {
        const userThreads: Thread[] = response?.results
        console.log("response:", response)
        const lastInArray = userThreads[0];
        const allButLast = userThreads.slice(1, userThreads.length);
        const filteredThreads = allButLast.filter(
          (thread) => thread.values && Object.keys(thread.values).length > 0
        );
        setUserThreads([...filteredThreads, lastInArray]);        
      }
    } finally {
      setIsUserThreadsLoading(false);
    }
  };

  const getThreadId = (ignoreQueryParams = false) => {
    const threadIdQueryParam = !ignoreQueryParams
      ? searchParams.get(THREAD_ID_QUERY_PARAM)
      : undefined;
    const threadIdCookie = getCookie(THREAD_ID_COOKIE_NAME);
    if (threadIdCookie) {
      // Cookie is legacy. Remove it
      setThreadIdLS(threadIdCookie);
      removeCookie(THREAD_ID_COOKIE_NAME);
    }

    return threadIdQueryParam || threadIdLS || threadIdCookie;
  };

  const searchOrCreateThread = async (isNewThread?: boolean) => {
    const storedThreadId = getThreadId(isNewThread);

    if (!storedThreadId) {
      const newThread = await createThread();
      return newThread;
    }

    // Thread ID is in cookies.
    const thread = await getThreadById(storedThreadId);
    if (thread) {
      const isEmptyThread =
        !thread.values || Object.keys(thread.values).length === 0;
      const shouldUseExistingThread =
        !isNewThread || (isNewThread && isEmptyThread);

      if (shouldUseExistingThread) {
        setThreadId(storedThreadId);
        return thread;
      }
    }

    // Current thread has activity. Create a new thread.
    const newThread = await createThread();
    return newThread;
  };

  const clearThreadsWithNoValues = async () => {
    if (!user) {
      toast({
        title: "Failed to clear threads",
        description: "User not found",
        duration: 5000,
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await fetchAndDeleteThreads(threadId);
      if (!result.success) {
        toast({
          title: "Error clearing threads",
          description: result.error || "An unknown error occurred",
          duration: 5000,
          variant: "destructive",
        });
      }
    } catch (e) {
      console.error("Error clearing threads", e);
      toast({
        title: "Error clearing threads",
        description: e instanceof Error ? e.message : "An unknown error occurred",
        duration: 5000,
        variant: "destructive",
      });
    }
  };

  const getThreadById = async (id: string): Promise<Thread | undefined> => {
    try {
      const response = await findThread(id);
      // ✅ Explicitly return undefined if null
      if (!response?.success && !response?.userThread) return undefined
  
      const thread = response?.userThread!
  
      const metadata = thread?.metadata as ThreadMetadata; // ✅ Cast metadata
  
      if (metadata?.customModelName) {
        setModelName(metadata.customModelName);
      } else {
        setModelName(DEFAULT_MODEL_NAME);
      }
  
      if (metadata?.modelConfig) {
        setModelConfig(metadata.customModelName ?? DEFAULT_MODEL_NAME, metadata.modelConfig);
      } else {
        setModelConfig(DEFAULT_MODEL_NAME, DEFAULT_MODEL_CONFIG);
      }
  
      return thread;
    } catch (e) {
      console.error(`Failed to get thread with ID ${id}`, e);
    }
  };

  const deleteThread = async (id: string, clearMessages: () => void) => {
    setUserThreads((prevThreads) => {
      const newThreads = prevThreads.filter(
        (thread) => thread.thread_id !== id
      );
      return newThreads;
    });
    if (id === threadId) {
      clearMessages();
      // Create a new thread. Use .then to avoid blocking the UI.
      // Once completed, `createThread` will re-fetch all user
      // threads to update UI.
      void createThread();
    }

    try {
      await removeThread(id);
    } catch (e) {
      console.error(`Failed to delete thread with ID ${id}`, e);
    }
  };

  const removeThreadIdQueryParam = () => {
    const threadIdQueryParam = searchParams.get(THREAD_ID_QUERY_PARAM);
    const webSearchResultsQueryParam = searchParams.get(
      WEB_SEARCH_RESULTS_QUERY_PARAM
    );

    if (threadIdQueryParam || webSearchResultsQueryParam) {
      const params = new URLSearchParams(searchParams.toString());
      params.delete(THREAD_ID_QUERY_PARAM);
      params.delete(WEB_SEARCH_RESULTS_QUERY_PARAM);
      // If there are still params, replace with the new URL. Else, replace with /
      if (params.size > 0) {
        router.replace(`?${params.toString()}`, { scroll: false });
      } else {
        router.replace("/canvas", { scroll: false });
      }
    }
  };

  const setThreadIdQueryParam = (id: string) => {
    const threadIdQueryParam = searchParams.get(THREAD_ID_QUERY_PARAM);
    const params = new URLSearchParams(searchParams.toString());
    if (threadIdQueryParam !== id) {
      params.set(THREAD_ID_QUERY_PARAM, id);
      // Remove the web search results param to avoid showing web results from the wrong thread
      params.delete(WEB_SEARCH_RESULTS_QUERY_PARAM);
      router.replace(`?${params.toString()}`, { scroll: false });
    }
  };

  const contextValue: ThreadContentType = {
    threadId,
    userThreads,
    isUserThreadsLoading,
    modelName,
    modelConfig,
    modelConfigs,
    createThreadLoading,
    removeThreadIdQueryParam,
    setThreadIdQueryParam,
    clearThreadsWithNoValues,
    searchOrCreateThread,
    getUserThreads,
    deleteThread,
    setThreadId,
    setModelName,
    setModelConfig,
  };

  return (
    <ThreadContext.Provider value={contextValue}>
      {children}
    </ThreadContext.Provider>
  );
}

export function useThreadContext() {
  const context = useContext(ThreadContext);
  if (context === undefined) {
    throw new Error("useThreadContext must be used within a ThreadProvider");
  }
  return context;
}

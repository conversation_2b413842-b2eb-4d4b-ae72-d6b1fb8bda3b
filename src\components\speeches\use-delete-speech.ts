import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteSpeech } from '@/actions/speeches/delete-speech';
import { toast } from 'sonner';


export function useDeleteSpeech() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (speechId: string) => {
      const response = await deleteSpeech({ speechId });
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to delete speech.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Speech deleted!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['speeches'] });
      queryClient.invalidateQueries({ queryKey: ['speech', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete speech.');
    },
  });

  return mutation;
}

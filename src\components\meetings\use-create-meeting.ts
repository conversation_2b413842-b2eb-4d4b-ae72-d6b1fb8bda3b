import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createMeeting } from "@/actions/tasks/schedule-meeting"
import { z } from 'zod';
//import { createMeetingSchema } from '@/components/meetings/schemas';
import { toast } from 'sonner';


export function useCreateMeeting() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await createMeeting(formData);
      return response;
    },
    onSuccess: (response) => {
      if (response.success) {
        toast.success(response.message || 'Meeting created');
        queryClient.invalidateQueries({ queryKey: ['meetings'] });
      } else {
        toast.error(response.error || 'Failed to create meeting');
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to create meeting');
    },
  });
}

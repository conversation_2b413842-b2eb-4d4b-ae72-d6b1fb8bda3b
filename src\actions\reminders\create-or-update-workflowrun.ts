import { prisma } from "@/lib/db";

// Helper function to create or update a workflow run
export async function createOrUpdateWorkflowRun(
  reminderId: string, 
  workflowRunId: string, 
  status?: string
) {
  return prisma.workflowRun.upsert({
    where: {
      workflowRunId,
    },
    update: {
      status: status ? status : "active" as const,
    },
    create: {
      reminderId: reminderId,
      workflowRunId,
      status: status ? status : "active" as const,
    },
  });
}

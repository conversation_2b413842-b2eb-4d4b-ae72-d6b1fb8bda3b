import { TaskWithRelations } from "@/actions/tasks/get-tasks";
import { TaskAction } from "./task-actions";
import { But<PERSON> } from "../ui/button";
import { MoreVertical } from "lucide-react";
import { Separator } from "@radix-ui/react-dropdown-menu";
import { MemberAvatar } from "../workspaces/member-avatar";
import { TaskDate } from "./task-date";
import { ProjectAvatar } from "../projects/project-avatar";


type KanbanCardProps = {
  task: TaskWithRelations;
}

export const KanbanCard = ({ task }: KanbanCardProps) => {

  return (
    <div className="bg-lime-500/20 py-2 px-2 mb-1 rounded shadow-sm spacy-y-3">
      <div className="flex items-center justify-between gap-x-2">
        <p className="text-sm line-clamp-2">{task.name}</p>
        <TaskAction id={task.id} projectId={task.projectId}>
          <Button variant={"ghost"} size="xs">
            <MoreVertical className="size-[18px] strok-1 shrink-0 text-foreground/80 hover:opacity-75 transition" />
          </Button>
        </TaskAction>
      </div>
      <Separator />
      <div className="flex items-center gap-x-1.5">
        <MemberAvatar 
          name={task.assignee?.user.name!}
          image={task.assignee?.user.image!}
          fallbackClassName="text-[9px]"
        />
        <div className="size-1 rounded-full bg-neutral-300" />        
        <TaskDate value={task.dueDate} className="text-xs" />
        <p className="text-[10px] align-super text-purple-400 line-clamp-1">due</p>
      </div>
      <div className="flex items-center gap-x-1.5">
        <ProjectAvatar 
          name={task.project?.name!}
          image={task.project?.imageUrl!}
          className="size-3.5"
          fallbackClassName="text-[9px]"
        />
        <span className="text-xs font-medium">{task?.project?.name}</span>
      </div>
    </div>
  )
}


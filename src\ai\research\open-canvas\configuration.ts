import { Annotation, LangGraphRunnableConfig } from "@langchain/langgraph";
import { CustomModelConfig } from "@badget/shared/types";
import { v4 as uuidv4 } from 'uuid';

export const ConfigurationAnnotation = Annotation.Root({
  assistant_id: Annotation<string>(),
  thread_id: Annotation<string>(),
  system_prompt: Annotation<string>(),
  customModelName: Annotation<string | undefined>(),
  modelConfig: Annotation<CustomModelConfig | undefined>(),
});

export type Configuration = typeof ConfigurationAnnotation.State;


export function ensureConfiguration(config?: LangGraphRunnableConfig): Configuration {
  const configurable = config?.configurable || {};
  
  return {
    assistant_id: configurable?.assistant_id || uuidv4(),
    thread_id: configurable?.thread_id || uuidv4(),
    system_prompt: configurable?.system_prompt || "",
    customModelName: configurable?.customModelName || "deepseek-r1-distill-llama-70b",
    modelConfig: configurable?.modelConfig || {
        "provider": "groq",
        "maxTokens": {
            "max": 8000,
            "min": 1,
            "current": 4096,
            "default": 4096
        },
        "temperatureRange": {
            "max": 1,
            "min": 0,
            "current": 0.5,
            "default": 0.5
        }
    },
  };
}
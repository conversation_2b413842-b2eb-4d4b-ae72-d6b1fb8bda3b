"use client"

import { TrendingUp, TrendingDown } from 'lucide-react'
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Cell, Legend } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

type ChartConfig = Record<
  string,
  {
    label: string; // Display label for the bar
    color: string; // Bar color
  }
>;

interface PieChartCardProps {
  title: string;
  description?: string;
  data: {
    data: Array<{
      monthYear: string;
      sortKey: string;
      income: number;
      expense: number;
    }>;
    trending: { direction: 'up' | 'down'; percentage: string };
  };
  chartConfig: ChartConfig;
  xAxisKey: string;
}

export function PieChartCard({
  title,
  description,
  data,
}: PieChartCardProps) {
  const { data: chartData } = data

  const totalIncome = chartData.reduce((acc, item) => acc + item.income, 0);
  const totalExpense = chartData.reduce((acc, item) => acc + item.expense, 0);

  const processedData = [
    { name: 'Total Income', value: totalIncome },
    { name: 'Total Expense', value: totalExpense },
  ];

  const totalValue = totalIncome + totalExpense;
  const remaining = (totalIncome - totalExpense).toFixed(0);

  const COLORS = ['hsl(var(--chart-2))', 'hsl(var(--chart-1))'];

  return (
    <Card>
      <CardHeader>
        {/*<CardTitle>{title}</CardTitle>*/}
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="p-2">
        <ChartContainer
          config={{
            income: {
              label: "Total Income",
              color: COLORS[0],
            },
            expense: {
              label: "Total Expense",
              color: COLORS[1],
            },
          }}
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
              <Pie
                data={processedData}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                innerRadius={30}
                outerRadius={45}
                paddingAngle={8}
                strokeWidth={5}

              >
                {processedData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Legend 
                verticalAlign="bottom" 
                iconSize={9}
                height={16}
                formatter={(value, entry, index) => (
                  <span style={{ fontSize: '11px', color: entry.color }}>
                    {value}: ${processedData[index].value.toLocaleString(undefined, {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    })}
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-center text-xs p-2 mt-4">
        <div className="flex gap-x-2 font-medium leading-none">
          Remaining ${remaining}
          {Number(remaining) > 0 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
        </div>
        <div className="leading-none text-muted-foreground">
        </div>
      </CardFooter>
    </Card>
  )
}
"use client"
import { cn } from "@/lib/utils";
import { CATEGORY_COLORS } from "@/lib/config/categories";

interface ColorPickerProps {
  selectedColor: string;
  onColorSelect: (color: string) => void;
}

export function ColorPicker({ selectedColor, onColorSelect }: ColorPickerProps) {
  return (
    <div className="grid grid-cols-6 gap-2 pl-14">
      {CATEGORY_COLORS.map((color) => (
        <div
          key={color.value}
          className={cn(
            "w-8 h-4 rounded-full cursor-pointer border-2 transition-all hover:scale-110",
            selectedColor === color.value 
              ? "border-foreground shadow-lg ring-2 ring-offset-2 ring-foreground/20" 
              : "border-muted-foreground/20 hover:border-muted-foreground/40"
          )}
          style={{ backgroundColor: color.bg }}
          onClick={() => onColorSelect(color.value)}
          title={color.name}
        />
      ))}
    </div>
  );
}

"use client"

import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { RiAddCircleFill, RiEditLine } from "react-icons/ri";
import { useGetProjects } from "./use-get-projects";
import { useCreateProjectModal } from "@/hooks/use-create-project-modal";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProjectAvatar } from "./project-avatar";
import { Frame } from "lucide-react";


export const ProjectSwitcher = ({
  workspaceId
}: {
  workspaceId: string;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { open } = useCreateProjectModal();
  const { projects } = useGetProjects({workspaceId})

  const onProjectChange = (projectId: string) => {
    router.push(`/workspaces/${workspaceId}/projects/${projectId}`);
  };
  
  return (
    <div className="flex flex-col items-start gap-x-2 ml-4">
      <div className="flex flex-row items-center gap-x-2">
        <Frame size={"16"} className="mb-[0.1rem] rounded-md" />
        <p className="text-xs uppercase text-primary font-semibold">Projects</p>
        <RiAddCircleFill
          className="size-5 text-neutral-500 cursor-pointer hover:text-neutral-400"
          onClick={open}
        />
      </div>
      <Select 
        onValueChange={onProjectChange}
        value={pathname.match(/\/projects\/([^/?]+)/)?.[1] || undefined}
      >
        <SelectTrigger className="h-7 max-w-fit bg-neutral-200/10 dark:bg-neutral-900 text-xs font-medium border-0 p-1 px-3 ml-4 gap-x-1 rounded-xl focus:ring-offset-0 focus:ring-0">
          <SelectValue placeholder="Select a project" />
        </SelectTrigger>
        <SelectContent>
          {projects && projects.length > 0 ? (
            projects.map((project) => (
              <div key={project.id} className="flex items-center justify-between">
                <SelectItem key={project.id} value={project.id} className="flex items-center gap-2 text-sm">
                  <div className="flex flex-row items-center gap-x-1">
                    <ProjectAvatar image={project.imageUrl ?? ''} name={project.name} />
                    {project.name}                  
                  </div>

                </SelectItem>
                {project?.id && 
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      router.push(`/workspaces/${project.workspaceId}/projects/${project.id}/settings`);
                    }}
                    className="p-1 hover:text-primary"
                  >
                    <RiEditLine className="h-4 w-4" />
                  </button>
                }
              </div>
            ))
          ) : (
            <div className="text-xs">No projects available</div>
          )}
        </SelectContent>
      </Select>

    </div>
  )
}
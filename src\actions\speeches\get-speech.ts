"use server"

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";

export async function getSpeech(speechId: string) {
  const { userId } = await auth();
  if (!userId) {
    return { error: "Unauthorized", success: false };
  }

  try {
    const speech = await prisma.speech.findUnique({
      where: { id: speechId },
      include: {
        project: true,
        recordings: true,
        transcriptions: true,
        analyses: true,
        user: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    if (!speech) {
      return { error: "Speech not found", success: false };
    }

    // Extract project and remove it from speech object to avoid duplication
    const { project, ...speechWithoutProject } = speech;

    return { 
      success: true, 
      data: { 
        speech: speechWithoutProject, 
        project 
      } 
    };
  } catch (error) {
    console.error("Error fetching speech:", error);
    return { error: "Failed to fetch speech", success: false };
  }
}

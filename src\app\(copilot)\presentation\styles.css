.markdown h1,
.markdown h2,
.markdown h3,
.markdown h4,
.markdown h5,
.markdown h6 {
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.markdown h1 {
  font-size: 1.5em;
}

.markdown h2 {
  font-size: 1.25em;
  font-weight: 600;
}

.markdown h3 {
  font-size: 1.1em;
}

.markdown h4 {
  font-size: 1em;
}

.markdown h5 {
  font-size: 0.9em;
}

.markdown h6 {
  font-size: 0.8em;
}

.markdown p {
  margin-bottom: 1.25em;
  text-align: left;
}

.markdown pre {
  margin-bottom: 1.25em;
}

.markdown ul {
  list-style-type: disc;
  padding-left: 20px;
  overflow: visible;
  text-align: left;
  margin-bottom: 1.25em;
}

.markdown li {
  list-style-type: inherit;
  list-style-position: outside;
  margin-left: 0;
  padding-left: 0;
  position: relative;
  overflow: visible;
  text-align: left;
}

@media (min-width: 640px) {
  .copilotKitSidebarContentWrapper.sidebarExpanded {
    margin-right: 24rem;
  }
}

@media (min-width: 640px) {
  .copilotKitWindow {
    transform-origin: bottom right;
    bottom: 5rem;
    right: 1rem;
    top: auto;
    left: auto;
    border-width: 0px;
    margin-bottom: 1rem;
    width: 24rem;
    height: 600px;
    min-height: 200px;
    max-height: calc(100% - 6rem);
  }

  .copilotKitSidebar .copilotKitWindow {
    bottom: 0;
    right: 0;
    top: auto;
    left: auto;
    width: 24rem;
    min-height: 100%;
    margin-bottom: 0;
    max-height: none;
  }
}

.copilotKitInput {
  border-top: none;
  background-color: white;
  margin-bottom: 10px;
  margin-left: 10px;
  margin-right: 10px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 10px;
}

::-webkit-scrollbar {
  background-color: #468497;
  width: 6px;
  height: 2px;
}

::-webkit-scrollbar-track {
  background-color: #f0f5f7;
  border-radius: 20px;
  width: 6px;
  height: 2px;
}

::-webkit-scrollbar-thumb {
  background-color: #c6ecb6;
  border-radius: 20px;
  width: 6px;
  height: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #c0c0c0;
  width: 6px;
  height: 2px;
}
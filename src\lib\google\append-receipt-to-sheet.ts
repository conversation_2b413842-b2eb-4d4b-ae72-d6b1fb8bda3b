import { google } from 'googleapis';

type ReceiptItem = {
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export type ReceiptData = {
  merchantName: string;
  merchantAddress: string;
  merchantContact?: string;
  transactionDate: string;
  transactionAmount: string;
  currency: string;
  receiptSummary: string;
  items: ReceiptItem[];
  googleDriveLink?: string;
}

export const appendReceiptToSheet = async (receiptData: ReceiptData) => {
  try {
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });

    const sheets = google.sheets({ version: 'v4', auth });
      // Ensure we have valid items array
    const items = receiptData.items || [];
    
    // Create a single row if no items, or one row per item if there are items
    const rows = items.length ? items.map((item: ReceiptItem) => [
      receiptData.merchantName || '',
      receiptData.merchantAddress || '',
      receiptData.merchantContact || '',
      receiptData.transactionDate || '',
      receiptData.transactionAmount || '',
      receiptData.currency || '',
      receiptData.receiptSummary || '',
      item.name || '',
      item.quantity || 0,
      item.unitPrice || 0,
      item.totalPrice || 0,
      receiptData.googleDriveLink || '',
      new Date().toISOString()
    ]) : [[
      receiptData.merchantName || '',
      receiptData.merchantAddress || '',
      receiptData.merchantContact || '',
      receiptData.transactionDate || '',
      receiptData.transactionAmount || '',
      receiptData.currency || '',
      receiptData.receiptSummary || '',
      '', // item name
      0,  // quantity
      0,  // unit price
      0,  // total price
      receiptData.googleDriveLink || '',
      new Date().toISOString()
    ]];

    await sheets.spreadsheets.values.append({
      spreadsheetId: process.env.GOOGLE_RECEIPTS_SHEETS_ID,
      range: 'Receipts!A:M',
      valueInputOption: 'USER_ENTERED',
      insertDataOption: 'INSERT_ROWS',
      requestBody: {
        values: rows
      }
    });

    return true;
  } catch (error) {
    console.error('Error appending to Google Sheets:', error);
    throw error;
  }
};
"use client";

import { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getCategories } from "@/actions/categories/manage-categories";
import { getIconByName } from "@/lib/config/categories";
import { Skeleton } from "@/components/ui/skeleton";
import { MoreHorizontal } from "lucide-react";

interface CategorySelectProps {
  onSelect: (categoryId: string) => void;
  currentCategoryId?: string;
}

interface Category {
  id: string;
  name: string;
  icon: string;
}

export function CategorySelect({
  onSelect,
  currentCategoryId,
}: CategorySelectProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchCategories() {
      const result = await getCategories();
      if (result.success && result.data) {
        setCategories(result.data);
      }
      setLoading(false);
    }
    fetchCategories();
  }, []);

  const renderCategoryWithIcon = (category: Category) => {
    const IconComponent = getIconByName(category.icon) || MoreHorizontal;
    return (
      <div className="flex items-center gap-2">
        <IconComponent className="h-4 w-4" />
        {/* Capitalize first letter of each word */}
        <span>
          {category.name
            .split(" ")
            .map(
              (word) =>
                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
            )
            .join(" ")}
        </span>
      </div>
    );
  };

  if (loading) {
    return <Skeleton className="h-10 w-full" />;
  }

  const currentCategory = categories.find((c) => c.id === currentCategoryId);

  return (
    <Select defaultValue={currentCategoryId} onValueChange={onSelect}>
      <SelectTrigger className="w-[200px]">
        <SelectValue>
          {currentCategory ? (
            renderCategoryWithIcon(currentCategory)
          ) : (
            <span className="text-muted-foreground">Select category</span>
          )}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {categories.map((category) => (
          <SelectItem
            key={category.id}
            value={category.id}
            className="capitalize"
          >
            {renderCategoryWithIcon(category)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

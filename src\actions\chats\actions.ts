"use server"

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { Prisma, Message, Visibility } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { experimental_createMCPClient as createMCPClient, smoothStream, streamText } from 'ai';
import { generateText, Message as VercelMessage } from "ai";
import { groq } from '@ai-sdk/groq';
import { createGoogleGenerativeAI } from '@ai-sdk/google';

export async function saveChat({
  id,
  assistantId,
  contractId,
  title,
  messages = [],
}: {
  id: string;
  assistantId: string;
  contractId?: string;
  title: string;
  messages?: Array<{
    chatId: string;
    role: string;
    content: any; // or Prisma.InputJsonValue if available
    createdAt?: Date;
  }>;
}) {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  
  console.log("saveChat", { id, assistantId, title, messages });
  
  // Process messages to ensure content is always a valid JSON object
  const processedMessages = messages.map(msg => {
    let safeContent: Record<string, any>;
    if (msg.content === null || msg.content === undefined) {
      safeContent = { text: '' };
    } else if (typeof msg.content === 'string') {
      safeContent = { text: msg.content };
    } else if (Array.isArray(msg.content)) {
      safeContent = { text: JSON.stringify(msg.content) };
    } else if (typeof msg.content === 'object') {
      // Ensure we spread the object so it is a plain object
      safeContent = { ...msg.content };
    } else {
      safeContent = { text: String(msg.content) };
    }
    
    console.log("Safe content:", JSON.stringify(safeContent, null, 2));
    
    return {
      id: id,
      role: msg.role,
      content: msg.content !== null ? (msg.content as Prisma.InputJsonValue) : Prisma.JsonNull,
      createdAt: msg.createdAt || new Date(),
    };
  });
  
  console.log("processedMessages:", JSON.stringify(processedMessages, null, 2));
  
  try {
    const chat = await prisma.chat.upsert({
      where: { id },
      update: { title },
      create: {
        id,
        createdAt: new Date(),
        userId,
        assistantId,
        contractId,
        title,
      },
      include: {
        messages: true,
      },
    });

    // Optionally filter out messages already existing if needed
    await prisma.message.createMany({
      data: messages.map(({ chatId, role, content }) => ({
        chatId,
        role,
        content: content !== null ? (content as Prisma.InputJsonValue) : Prisma.JsonNull,
      })),
      skipDuplicates: true, // prevents error if message with same id exists
    });
  } catch (error) {
    console.error("Failed to save chat in database", error);
    throw error;
  }
}


export async function deleteChatById({ id }: { id: string }) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized: No user found");
    }
    // Delete messages associated with the chat
    await prisma.message.deleteMany({ where: { chatId: id } });

    // Delete the chat itself
    return await prisma.chat.delete({ where: { id } });
  } catch (error) {
    console.error('Failed to delete chat by id from database', error);
    throw error;
  }
}

export async function getChatsByAssistantId({ id }: { id: string }) {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  try {
    return await prisma.chat.findMany({
      where: { userId, assistantId: id },
      orderBy: { createdAt: 'desc' },
      include: {
        messages: {
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });
  } catch (error) {
    console.error('Failed to get chats by user from database', error);
    throw error;
  }
}

export async function getChatsByContractId({ contractId, assistantId }: { contractId: string, assistantId: string }) {
  const { userId } = await auth();
  if (!userId || !contractId || !assistantId) {
    return []
  }
  try {
    return await prisma.chat.findMany({
      where: { userId, assistantId, contractId },
      orderBy: { createdAt: 'desc' },
      include: {
        messages: {
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });
  } catch (error) {
    console.error('Failed to get chats by user from database', error);
    throw error;
  }
}

export async function getChatById({ contractId, id }: { contractId: string, id: string }) {
  if (!contractId || id) return
  try {
    return await prisma.chat.findUnique({ 
      where: { id, contractId },
      include: {
        messages: {
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });
  } catch (error) {
    console.error('Failed to get chat by id from database', error);
    throw error;
  }
}

export async function removeChatById({ 
  id, contractId, path
}: { 
  id: string, 
  contractId: string, 
  path?: string
}) {
  const { userId } = await auth();
  if (!userId || !id || !contractId) {
    return {
      error: "Missing required fields.",
      success: false
    }
  }
  console.log("removeChatById", {id, contractId})
  try {
    const deletedChat = await prisma.chat.delete({ 
      where: { id, contractId },
    });
    if (path) {
      revalidatePath(path)
    }
    return {
      success: true,
      data: deletedChat
    }
  } catch (error) {
    console.error('Failed to get chat by id from database', error);
    return {
      error: "Failed to delete project.",
      success: false
    }
  }
}

export async function saveMessages({ messages }: { messages: Array<Message> }) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized: No user found");
    }
    return await prisma.message.createMany({
      data: messages.map(({ chatId, role, content }) => ({
        chatId,
        role,
        content: content !== null ? (content as Prisma.InputJsonValue) : Prisma.JsonNull,
      })),
    });
  } catch (error) {
    console.error('Failed to save messages in database', error);
    throw error;
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized: No user found");
    }
    return await prisma.message.findMany({
      where: { chatId: id },
      orderBy: { createdAt: 'asc' },
    });
  } catch (error) {
    console.error('Failed to get messages by chat id from database', error);
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await prisma.message.findUnique({ where: { id } });
  } catch (error) {
    console.error('Failed to get message by id from database', error);
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    // Find message IDs to delete
    const messagesToDelete = await prisma.message.findMany({
      where: { chatId, createdAt: { gte: timestamp } },
      select: { id: true },
    });

    const messageIds = messagesToDelete.map((msg) => msg.id);

    if (messageIds.length > 0) {
      return await prisma.message.deleteMany({
        where: { chatId, id: { in: messageIds } },
      });
    }
  } catch (error) {
    console.error(
      'Failed to delete messages by id after timestamp from database',
      error
    );
    throw error;
  }
}

export async function updateChatVisibilityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public';
}) {
  try {
    return await prisma.chat.update({
      where: { id: chatId },
      data: { visibility: visibility.toUpperCase() as Visibility },
    });
  } catch (error) {
    console.error('Failed to update chat visibility in database', error);
    throw error;
  }
}

export async function clearChats({
  assistantId,
  contractId,
  path,
}: {
  assistantId: string;
  contractId: string | undefined;
  path: string;
}): Promise<void> {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized: No user found");
    }

    if (!assistantId || !contractId) {
      throw new Error('Missing required fields');
    }

    // Delete all threads from the database
    const clearChats = await prisma.chat.deleteMany({
      where: {
        userId,
        assistantId,
        contractId
      },
    });

    if (!clearChats) {
      throw new Error("Chats not cleared");
    }

    // Revalidate the path if provided
    if (path) {
      revalidatePath(path);
    }

  } catch (error) {
    console.log("[CHATS_CLEARED]", error);
    throw new Error("Chats not cleared");
  }
}



export async function chat({ messages }: { messages: VercelMessage[] }) {

  const { userId } = await auth();
  if (!userId) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const google = createGoogleGenerativeAI({
    apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
  });
  const prompt = messages
  .filter((message: { role: string }) => message.role === 'user')
  .pop()
  const userPrompt: string | undefined = prompt?.content

  console.log("chat messages", JSON.stringify(messages.slice(-4), null, 2))
  
  let mcpClient;
  try {
    mcpClient = await createMCPClient({
      name: 'zapier',
      transport: {
        type: 'sse',
        url: 'https://mcp.zapier.com/api/mcp/s/NDAyODViYjItZmY1ZC00NDk3LWJkMjgtZGJmZDYwNTU3YjgyOjA0OTVjZTU4LTRiNTEtNDNjYy1iYjgzLTI2M2RkYjhiOWNmMQ==/sse',
      },
    });

    const tools = await mcpClient.tools();
    const response = await generateText({
      model: google('gemini-2.5-flash-preview-04-17'), //openai('gpt-4o-mini'),
      tools,
      maxSteps: 10,
      messages: messages.slice(-1)
    });

    console.log("response: ", response.text);


    if (response?.text && response.text.length > 0) {
      const result = streamText({
        model: groq('llama-3.3-70b-versatile'), //google("gemini-2.5-pro-exp-03-25"), //
        experimental_transform: smoothStream(),
        messages: [
          {
            role: 'user', 
            content: `Make a list of followings: ` + response.text!,
          }
        ],

      })

      return result.toDataStreamResponse();
    }

  } catch (error) {
    console.error(error);
  } finally {
    await mcpClient?.close();
  }
}
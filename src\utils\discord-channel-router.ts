import axios from 'axios';

const CHANNEL_MAPPING = {
  // 社區管理費
  'management_fee': {
    channelId: process.env.DISCORD_CHANNEL_MANAGEMENT_FEE, // 社區管理費
    keywords: ['管理費', '繳費', '費用', '帳單', '催繳', '收費', '月費', '季費', '年費']
  },
  
  // 電梯
  'elevator': {
    channelId: process.env.DISCORD_CHANNEL_ELEVATOR, // 電梯
    keywords: ['電梯', '升降機', '電梯維修', '電梯故障', '電梯保養', '電梯檢查', '困梯', '電梯停止', '電梯異常']
  },

  // 機電消防
  'electrical_fire': {
    channelId: process.env.DISCORD_CHANNEL_ELECTRICAL_FIRE, // 機電消防
    keywords: ['機電', '消防', '電力', '發電機', '消防設備', '火警', '煙霧', '滅火器', '水電']
  },
  
  // 機械車位
  'parking': {
    channelId: process.env.DISCORD_CHANNEL_PARKING, // 機械車位
    keywords: ['車位', '停車', '機械', '車庫', '停車場', '車位租賃', '停車費']
  },

  // 水塔蓄水池
  'water_tank': {
    channelId: process.env.DISCORD_CHANNEL_WATER_TANK, // 水塔蓄水池
    keywords: ['水塔', '蓄水池', '水質', '清洗', '水壓', '供水', '停水', '水管', '漏水']
  },

  // 外牆維護
  'exterior_maintenance': {
    channelId: process.env.DISCORD_CHANNEL_EXTERIOR_MAINTENANCE, // 外牆維護
    keywords: ['外牆', '外觀維護', '外牆漏水', '外牆剝落', '外牆磁磚']
  },

  // 設備維修
  'equipment_maintenance': {
    channelId: process.env.DISCORD_CHANNEL_EQUIPMENT_MAINTENANCE, // 設備維修
    keywords: ['設備', '維修', '故障', '壞了', '修理', '機器', '設施', '損壞']
  }, 
  // 組織報備
  'management_committee': {
    channelId: process.env.DISCORD_CHANNEL_MANAGEMENT_COMMITTEE, // 組織報備
    keywords: ['組織報備', '當選會議', '印鑑', '扣繳單位', '授權書', '管委會成立', '移失切結書']
  },
  
  // 公告 (一般公告)
  'announcement': {
    channelId: process.env.DISCORD_CHANNEL_ANNOUNCEMENT, // 公告
    keywords: ['公告', '通知', '重要', '會議', '投票', '活動', '注意', '提醒']
  },
  
  // 意見
  'feedback': {
    channelId: process.env.DISCORD_CHANNEL_FEEDBACK, // 意見
    keywords: ['意見', '建議', '反映', '投訴', '抱怨', '改善', '問題', '討論']
  },
  
  // 預設頻道 (如果沒有匹配到任何關鍵字，使用預設頻道)
  'default': {
    channelId: process.env.DISCORD_CHANNEL_DEFAULT, // 預設使用預設頻道
    keywords: []
  }
};

export function determineChannel(message: string, messageType = null) {
  // 如果有明確指定訊息類型
  if (messageType && CHANNEL_MAPPING[messageType]) {
    return CHANNEL_MAPPING[messageType as keyof typeof CHANNEL_MAPPING].channelId;
  }

  // 將訊息轉為小寫以便比對
  const lowerMessage = message.toLowerCase();

  // 根據關鍵字自動判斷（按優先順序檢查）
  const priorityOrder = [
    'management_committee',
    'management_fee',
    'elevator',
    'electrical_fire',
    'water_tank',
    'parking',
    'exterior_maintenance',
    'announcement',
    'feedback',
    'equipment_maintenance',
    'default'
  ];

  for (const type of priorityOrder) {
    const config = CHANNEL_MAPPING[type as keyof typeof CHANNEL_MAPPING];
    if (config.keywords.some(keyword => lowerMessage.includes(keyword))) {
      return config.channelId;
    }
  }

  // 如果沒有匹配到任何關鍵字，使用預設頻道
  return CHANNEL_MAPPING.default.channelId;
}
// 取得所有頻道選項（供前端下拉選單使用）
export function getAllChannels() {
  return [
    { id: 'management_committee', name: '組織報備', emoji: '💼' },
    { id: 'management_fee', name: '社區管理費', emoji: '💰' },
    { id: 'elevator', name: '電梯', emoji: ' Elevator' },
    { id: 'electrical_fire', name: '機電消防', emoji: '🔥' },
    { id: 'water_tank', name: '水塔蓄水池', emoji: '🚰' },
    { id: 'parking', name: '機械車位', emoji: '🚗' },
    { id: 'exterior_maintenance', name: '外牆維護', emoji: '🚪' },
    { id: 'announcement', name: '公告', emoji: '📢' },
    { id: 'feedback', name: '意見', emoji: '💬' },
    { id: 'equipment_maintenance', name: '設備維修', emoji: '🔧' },
    { id: 'default', name: '未知頻道', emoji: '❓' }
  ];
}

// 根據頻道類型取得頻道名稱
export function getChannelName(channelType: string) {
  const channelNames = {
    'management_committee': '組織報備',
    'management_fee': '社區管理費',
    'elevator': '電梯',    
    'electrical_fire': '機電消防',
    'water_tank': '水塔蓄水池',
    'parking': '機械車位',
    'exterior_maintenance': '外牆維護',
    'announcement': '公告',
    'feedback': '意見',
    'equipment_maintenance': '設備維修',
    'default': '未知頻道'
  };
  
  return channelNames[channelType as keyof typeof channelNames] || '未知頻道';
}

export async function sendToDiscordMessage(userPrompt: string, displayName?: string) {
  // 檢查是否包含「」格式的文字
  const discordTextMatch = userPrompt.match(/「([^」]*)」/);
  
  if (!discordTextMatch) {
    console.log('No Discord text found in message');
    return false;
  }
  
  // 提取「」內的文字
  const textToSend = discordTextMatch[1].trim();
  
  if (!textToSend) {
    console.log('Discord text is empty');
    return false;
  }
  
  try {
    // Prepare message for Discord - 使用 content 而不是 text
    const discordMessage = {
      content: `${displayName || "AI巧管家"}：${textToSend}`,
    };

    const channelId = determineChannel(textToSend);
    
    // 確保 channelId 存在
    if (!channelId) {
      console.error('No valid channel ID found');
      return false;
    }
    
    const response = await axios.post(
      `https://discord.com/api/v10/channels/${channelId}/messages`,
      discordMessage,
      {
        headers: {
          'Authorization': `Bot ${process.env.DISCORD_BOT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('Message sent to Discord successfully');
    return true;
  } catch (error: any) {
    console.error('Error sending message to Discord:', error.response?.data || error.message);
    return false;
  }
}

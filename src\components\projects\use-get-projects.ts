import { Project } from "@prisma/client";
import { useQuery } from "@tanstack/react-query";
import { getProjects } from "@/actions/projects/get-projects";


interface UseGetProjectsProps {
  workspaceId: string;
}

export const useGetProjects = ({ workspaceId }: UseGetProjectsProps) => {
    const { data: projects, isLoading, error } = useQuery({
    queryKey: ["projects", workspaceId],
    queryFn: async () => {
      const response = await getProjects(workspaceId);
      if (!response.success || !response.data) throw new Error("Failed to fetch projects");
      return response?.data || [] as Project[];
    },
    enabled: !!workspaceId,
  })

  return { projects, isLoading, error };
}
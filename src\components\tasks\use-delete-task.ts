import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteTask } from '@/actions/tasks/delete-task';
import { toast } from 'sonner';


export function useDeleteTask() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (taskId: string) => {
      const response = await deleteTask({ taskId });
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to delete task.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Task deleted!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['workspace-analytics'] });
      queryClient.invalidateQueries({ queryKey: ['project-analytics'] });
      queryClient.invalidateQueries({ queryKey: ['task', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete task.');
    },
  });

  return mutation;
}

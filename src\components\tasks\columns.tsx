"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreVertical } from "lucide-react";
import { TaskWithRelations } from "@/actions/tasks/get-tasks"
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ProjectAvatar } from "../projects/project-avatar";
import { MemberAvatar } from "../workspaces/member-avatar";
import { TaskDate } from "./task-date";
import { TaskAction } from "./task-actions";
import { snakeCaseToTitleCase } from "@/lib/utils";


export const columns: ColumnDef<TaskWithRelations>[] = [
  // Name column
  {
    accessorKey: "name",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => { 
      const name = row.original.name 
      return <p className="line-clamp-1">{name}</p>
    },
  },
  // Project column
  {
    accessorKey: "project",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Project
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => { 
      const project = row.original.project 
      return (
        <div className="flex items-center gap-x-1 text-sm">
          <ProjectAvatar
            className="size-4"
            name={project?.name!}
            image={project?.imageUrl!}
          />
          <p className="line-clamp-1">{project?.name}</p>
        </div>
      )
    },
  },
  // Assignee column
  {
    accessorKey: "assignee",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Assignee
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => { 
      const assignee = row.original.assignee 
      return (
        <div className="flex items-center gap-x-1 text-sm">
          <MemberAvatar
            className="size-4"
            fallbackClassName="text-xs"
            name={assignee?.user.name!}
            image={assignee?.user.image!}
          />
          <p className="line-clamp-1">{assignee?.user.name}</p>
        </div>
      )
    },
  },
  // Start Date column
  {
    accessorKey: "startDate",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Start Date
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
        const startDate = row.original.startDate;
        return <TaskDate value={startDate ? new Date(startDate) : null} />
      }
  },
  // Due Date column
  {
    accessorKey: "dueDate",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Due Date
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const dueDate = row.original.dueDate;
      return <TaskDate value={dueDate ? new Date(dueDate) : null} />
    }
  },
  // Status column
  {
    accessorKey: "status",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Status
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const status = row.original.status;
      return <Badge variant={status}>{snakeCaseToTitleCase(status)}</Badge>
    }
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const id = row.original.id;
      const projectId = row.original.projectId;
      return <TaskAction id={id} projectId={projectId}>
        <Button variant={"ghost"} size="xs">
          <MoreVertical className="size-4" />
        </Button>
      </TaskAction>
    }
  }
];

// PlayBookFiles.tsx
import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import axios from "axios";
import config from "@/store/config.json";
import styles from "./home.module.css";
import handleGoogleDriveShortcutLink from "./handle-google-drive-shortcut-link";
import handleAccessTokenExpiration from "./handle-access-token-expiration";
import { getGoogleToken } from "@/actions/get-google-token";

type FileResult = {
  id: string;
  name: string;
  mimeType: string;
};

const PlayBookFiles = () => {
  const router = useRouter();
  const fid =
    typeof router.query.fid !== "undefined"
      ? router.query.fid
      : config.directory.target_folder;
  const teamDriveId = config.directory.team_drive;
  const [results, setResults] = useState<FileResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<{ message: string } | null>(null);
  const corpora = teamDriveId ? "teamDrive" : "allDrives";

  useEffect(() => {
    const getFiles = async () => {
      const { accessToken } = await getGoogleToken();
      if (!accessToken) {
        throw new Error("Unable to get a valid access token.");
      }
      setLoading(true);
      setError(null);
      setResults([]);

      try {
        const res = await axios.get(
          "https://www.googleapis.com/drive/v3/files",
          {
            headers: { Authorization: `Bearer ${accessToken}` },
            params: {
              source: "PlayBookFiles",
              corpora: corpora,
              includeTeamDriveItems: true,
              supportsAllDrives: true,
              teamDriveId: teamDriveId,
              q: `mimeType!='application/vnd.google-apps.folder' and trashed = false and parents in '${fid}'`,
            },
          }
        );

        setResults(res.data.files);
      } catch (err: any) {
        if (err.response && err.response.status === 401) {
          handleAccessTokenExpiration();
        } else {
          setError({ message: err.message });
        }
      }

      setLoading(false);
    };

    getFiles();
  }, [fid]);

  return (
    fid !== config.directory.target_folder && (
      <div style={{ width: "100%", textAlign: "left" }}>
        {loading && <div>Loading...</div>}
        {error && <div>{error.message}</div>}
        <ul className={styles.filesContainer} style={{ width: "100%", textAlign: "left" }}>
          {results.map((result) => (
            <li key={result.id} className={styles.fileResult}>
              <a
                href={`https://docs.google.com/document/d/${result.id}/edit`}
                data-file-id={result.id}
                style={{ display: "block" }}
                target="_blank"
                rel="noopener noreferrer"
                data-mime-type={result.mimeType}
                onClick={handleGoogleDriveShortcutLink}
              >
                {result.name}
              </a>
            </li>
          ))}
        </ul>
      </div>
    )
  );
};

export default PlayBookFiles;

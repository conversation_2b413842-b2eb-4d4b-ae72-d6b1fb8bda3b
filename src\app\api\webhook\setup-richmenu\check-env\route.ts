import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET() {
  const envStatus = {
    CHANNEL_ACCESS_TOKEN: !!process.env.CHANNEL_ACCESS_TOKEN,
    CHANNEL_SECRET: !!process.env.CHANNEL_SECRET,
  };

  const missingEnvVars = Object.entries(envStatus)
    .filter(([_, value]) => !value)
    .map(([key]) => key);

  if (missingEnvVars.length > 0) {
    return NextResponse.json(
      {
        success: false,
        error: 'LINE Bot 憑證未設定完整',
        missingVariables: missingEnvVars,
        status: envStatus,
      },
      { status: 400 }
    );
  }

  return NextResponse.json(
    {
      success: true,
      message: 'LINE Bot 憑證設定完整',
      status: envStatus,
    },
    { status: 200 }
  );
}

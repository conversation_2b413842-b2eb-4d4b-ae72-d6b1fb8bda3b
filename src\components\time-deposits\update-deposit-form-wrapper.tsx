"use client"

import { useGetDeposit } from "./use-get-deposit";
import { UpdateDepositForm } from "./update-deposit-form";
import { 
  Card, 
  CardContent, 
} from "@/components/ui/card";

import { Loader } from "lucide-react";

interface UpdateDepositFormWrapperProps {
  onCancel?: () => void;
  id: string;
}

export const UpdateDepositFormWrapper = ({ onCancel, id }: UpdateDepositFormWrapperProps) => {
  
  const { data: initialValues, isLoading: isLoadingDeposit } = useGetDeposit({ 
    depositId: id 
  })
  console.log("data", initialValues)


  const isLoading = isLoadingDeposit

  if (isLoading) {
    <Card className="w-full h-[714px] border-none shadow-none">
      <CardContent className="flex items-center jistify-center h-full">
        <Loader className="size-5 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>;
  }

  if (!initialValues) return null;

  return (
    <UpdateDepositForm 
      onCancel={onCancel}
      timeDeposit={initialValues}
    />
  );
};

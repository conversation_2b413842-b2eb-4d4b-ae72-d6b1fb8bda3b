'use server';

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";

export async function getMembers(workspaceId: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    // Check if user exists
    const member = await prisma.member.findFirst({
      where: { userId },
    });

    if (!member) {
      return {
        error: "Unauthorized",
        success: false
      }
    }

    const members = await prisma.member.findMany({
      where: { workspaceId },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });
    console.log("members", members)
       
    return {
      data: members,
      success: true
    }

  } catch (error) {
    //console.error('Error geting members:', error);
    return {
      error: "Failed to get members of the workspace.",
      success: false
    }
  }
}

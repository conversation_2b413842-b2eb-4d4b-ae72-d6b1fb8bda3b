export const SERVER_ADDRESS = process.env.BACKEND_API_URL || "http://127.0.0.1:8000";

const apiBasePath = process.env.NEXT_PUBLIC_BASE_URL //process.env.VERCEL_URL
  ? `${process.env.NEXT_PUBLIC_BASE_URL}`
  : `http://localhost:3000`;
export const apiBaseUrl = `${apiBasePath}/api`;

export const MAX_FREE_COUNTS = 15;
export const API_MULTIPLIER = 10;
export const UNIT_AMOUNT = 1.99;

export const MAX_DATE_RANGE_DAYS = 1825;

export const defaultBackground = 'var(--gradient)';

// For LlamaIndex
export enum FileName {
  Bots = "bots.json",
}

export const REQUEST_TIMEOUT_MS = 60000;

export const CHAT_PAGE_SIZE = 15;
export const MAX_RENDER_MSG_COUNT = 45;

export const ALLOWED_IMAGE_EXTENSIONS = ["jpeg", "jpg", "png", "gif", "webp"];
export const ALLOWED_TEXT_EXTENSIONS = ["pdf", "txt"];
export const ALLOWED_DOCUMENT_EXTENSIONS = [
  ...ALLOWED_TEXT_EXTENSIONS,
  ...ALLOWED_IMAGE_EXTENSIONS,
];
export const DOCUMENT_FILE_SIZE_LIMIT = 1024 * 1024 * 10; // 10 MB
export const DOCUMENT_FILE_UPLOAD_LIMIT = 3;

export const DOCUMENT_TYPES = [
  "text/html",
  "application/pdf",
  "text/plain",
] as const;

export type DocumentType = (typeof DOCUMENT_TYPES)[number];

export const IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
] as const;

export type ImageType = (typeof IMAGE_TYPES)[number];

export const STREAMABLE_VALUE_TYPE = Symbol.for('ui.streamable.value');

/* OpenCanvas */
export const LANGGRAPH_API_URL =
  process.env.LANGGRAPH_API_URL ?? "http://localhost:54367";
// v2 is tied to the 'open-canvas-prod' deployment.
export const ASSISTANT_ID_COOKIE = "oc_assistant_id_v2";
// export const ASSISTANT_ID_COOKIE = "oc_assistant_id";
export const HAS_ASSISTANT_COOKIE_BEEN_SET = "has_oc_assistant_id_been_set";
/**
 * @deprecated - use THREAD_ID_LS_NAME and local storage from now on.
 */
export const THREAD_ID_COOKIE_NAME = "oc_thread_id_v2";
export const THREAD_ID_LS_NAME = "oc_thread_id";
export const HAS_EMPTY_THREADS_CLEARED_COOKIE = "has_empty_threads_cleared";
export const OC_HAS_SEEN_CUSTOM_ASSISTANTS_ALERT =
  "oc_has_seen_custom_assistants_alert";
export const THREAD_ID_QUERY_PARAM = "threadId";
export const WEB_SEARCH_RESULTS_QUERY_PARAM = "webSearchResults";

export const ALLOWED_AUDIO_TYPES = new Set([
  "audio/mp3",
  "audio/mp4",
  "audio/mpeg",
  "audio/mpga",
  "audio/m4a",
  "audio/wav",
  "audio/webm",
]);
export const ALLOWED_AUDIO_TYPE_ENDINGS = [
  ".mp3",
  ".mpga",
  ".m4a",
  ".wav",
  ".webm",
];
export const ALLOWED_VIDEO_TYPES = new Set([
  "video/mp4",
  "video/mpeg",
  "video/webm",
]);
export const ALLOWED_VIDEO_TYPE_ENDINGS = [".mp4", ".mpeg", ".webm"];

export const CHAT_COLLAPSED_QUERY_PARAM = "chatCollapsed";
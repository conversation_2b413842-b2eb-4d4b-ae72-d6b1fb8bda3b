'use server';

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { z } from "zod";
import { codeSchema } from "@/components/workspaces/schemas";
import { Role } from "@prisma/client"; // Import the Role enum

export async function joinWorkspace(workspaceId: string, inviteCode: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false,
      };
    }

    if (!workspaceId || !inviteCode) {
      return {
        error: "workspaceId and inviteCode are required.",
        success: false,
      };
    }

    // Validate the invite code
    const validatedFields = codeSchema.safeParse({ code: inviteCode });
    if (!validatedFields.success) {
      return {
        error: "Invalid invite code format.",
        success: false,
      };
    }

    const { code } = validatedFields.data;

    // Get the workspace
    const currentWorkspace = await prisma.workspace.findUnique({
      where: { id: workspaceId },
      include: { members: true },
    });

    if (!currentWorkspace) {
      return {
        error: "Workspace not found.",
        success: false,
      };
    }

    // Check if the invite code matches
    if (currentWorkspace.inviteCode !== code) {
      return {
        error: "Invalid invite code.",
        success: false,
      };
    }

    // Check if the user is already a member of the workspace
    const isUserAlreadyMember = currentWorkspace.members.some(
      (member) => member.userId === userId
    );
    if (isUserAlreadyMember) {
      return {
        error: "You are already a member of this workspace.",
        success: false,
      };
    }

    // Add the user to the workspace with a default role of MEMBER
    const newMember = await prisma.member.create({
      data: {
        userId: userId,
        workspaceId: workspaceId,
        role: Role.MEMBER, // Assign the user as a member by default
      },
    });

    return {
      data: newMember,
      success: true,
    };
  } catch (error) {
    console.error("Error joining workspace:", error);
    return {
      error: "Failed to join the workspace.",
      success: false,
    };
  }
}

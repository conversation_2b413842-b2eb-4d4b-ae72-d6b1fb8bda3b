"use client"
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import { calendarEventSchema } from '@/components/tasks/schemas';
import { toast } from 'sonner';


export function useUpdateCalendarEvents() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({ eventId, values }: {
      eventId?: string;
      values: z.infer<typeof calendarEventSchema>
    }) => {
      console.log(eventId, values)
      const url = eventId ? `/api/calendar/${eventId}/update` : `/api/calendar/create`;
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({values}),
      });
      const responseData = await response.json();
      console.log("responseData", responseData)

      if (!response.ok || !responseData.success) {
        throw new Error(`Failed to ${eventId ? 'update' : 'create'} event.`);
      }
      
      return responseData;
    },
    onSuccess: ({ data }) => {
      toast.success('Calendar updated!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      queryClient.invalidateQueries({ queryKey: ['calendar-events', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update calendar events.');
    },
  });

  return mutation;
}

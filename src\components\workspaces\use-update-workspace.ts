import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateWorkspace } from '@/actions/workspaces/update-workspace';
import { z } from 'zod';
import { updateWorkspaceSchema } from '@/components/workspaces/schemas';
import { toast } from 'sonner';


export function useUpdateWorkspace(workspaceId: string) {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof updateWorkspaceSchema>) => {
      console.log("values@useUpdateWorkspace", values)
      const response = await updateWorkspace(workspaceId, values);
      
      if (!response?.success || !response?.data) {
        throw new Error(response.error || 'Failed to update workspaces.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Workspace updated!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      queryClient.invalidateQueries({ queryKey: ['workspace', data?.id] })
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update workspace.');
    },
  });

  return mutation;
}

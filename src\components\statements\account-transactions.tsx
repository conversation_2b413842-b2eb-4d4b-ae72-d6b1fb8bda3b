"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MAX_DATE_RANGE_DAYS } from "@/constants";
import {
  differenceInDays,
  subYears,
  startOfYear,
  endOfYear,
  format,
} from "date-fns";
import {
  Row,
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  flexRender,
  createColumnHelper,
  ColumnFiltersState,
  VisibilityState,
} from "@tanstack/react-table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import {
  MoreHorizontal,
  Tag,
  CheckCircle,
  Pencil,
  Plus,
  Loader2,
  Trash,
} from "lucide-react";
import { toast } from "sonner";
import { Fragment, useState, useEffect, useMemo } from "react";
import { CategorySelect } from "@/components/categories/category-select";
import { updateStatementCategory } from "@/actions/categories/manage-categories";
import {
  ShoppingCart,
  Utensils,
  Home,
  CreditCard,
  Briefcase,
  Coins,
  Zap,
  Car,
  Plane,
  Gift,
  Coffee,
  type LucideIcon,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { suggestCategory } from "@/lib/utils/transaction-categorization";
import { validateTransactionCategory } from "@/actions/categories/validate-transaction-category";
import { cn } from "@/lib/utils";
import { deleteStatement } from "@/actions/statements/delete-statement";
import { EditStatementDialog } from "@/components/statements/edit-statement-dialog";
import {
  useGetCategories,
  SelectCategoryData,
} from "@/hooks/use-get-categories";
import { useGetAccounts } from "@/hooks/use-get-accounts";
import { AddStatementFromRowDialog } from "@/components/statements/add-statement-dialog";
import { getIconByName } from "@/lib/config/categories";

interface Transaction {
  id: string;
  date: Date;
  description: string;
  amount: number;
  type: "CREDIT" | "DEBIT";
  categoryId: string;
  accountId: string;
  category?: {
    id: string;
    name: string;
    icon: string;
  };
  categoryValidated?: boolean;
}

interface AccountTransactionsProps {
  dataRange: { from: Date; to: Date };
  transactions: Transaction[];
  onTransactionUpdate: () => Promise<void>;
}

function getConfidenceBadgeStyles(confidence: number) {
  if (confidence >= 0.8) {
    return "bg-green-100 hover:bg-green-200 text-green-700 border-green-300";
  }
  if (confidence >= 0.5) {
    return "bg-yellow-100 hover:bg-yellow-200 text-yellow-700 border-yellow-300";
  }
  return "bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300";
}

function getConfidenceLabel(confidence: number) {
  if (confidence >= 0.8) return "High Match";
  if (confidence >= 0.5) return "Possible Match";
  return "Suggested";
}

function renderCategoryWithIcon(
  transaction: {
    id: string;
    category?: { name: string; icon: string } | null;
    type: string;
    description: string;
    amount: number;
    categoryValidated?: boolean;
  },
  setEditingTransaction: (id: string | null) => void,
  onApproveCategory: (transactionId: string, categoryId: string) => void,
  approvingCategory: string | null,
  suggestedCategory?: {
    categoryId: string;
    name: string;
    confidence: number;
    icon: string;
  } | null
) {
  // Get the category from either the transaction or suggestion
  const effectiveCategory = transaction.category || suggestedCategory;
  
  // Get the icon component
  const IconComponent = effectiveCategory?.icon
    ? getIconByName(effectiveCategory.icon as string)
    : ShoppingCart;

  return (
    <div className="flex items-center gap-2">
      {!transaction.categoryValidated && suggestedCategory && (
        <Badge
          variant="outline"
          className={cn(
            "mr-2 cursor-pointer transition-all hover:scale-105",
            getConfidenceBadgeStyles(suggestedCategory.confidence),
            approvingCategory === transaction.id &&
              "pointer-events-none opacity-70"
          )}
          onClick={(e) => {
            e.stopPropagation();
            onApproveCategory(transaction.id, suggestedCategory.categoryId);
          }}
        >
          {approvingCategory === transaction.id ? (
            <>
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              Approving...
            </>
          ) : (
            <>
              <CheckCircle className="h-3 w-3 mr-1" />
              Accept Suggestion
            </>
          )}
        </Badge>
      )}
      <div className="flex items-center gap-2 cursor-pointer hover:text-primary">
        <IconComponent className="h-4 w-4 text-muted-foreground" />
        <span>
          {effectiveCategory
            ? effectiveCategory.name
                .split(" ")
                .map(
                  (word) =>
                    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                )
                .join(" ")
            : "Uncategorized"}
        </span>
      </div>
    </div>
  );
}

const columnHelper = createColumnHelper<Transaction>();

export default function AccountTransactions({
  dataRange,
  transactions,
  onTransactionUpdate,
}: AccountTransactionsProps) {
  console.log(
    "==========================dataRange==========================",
    dataRange
  );
  const {
    categories,
    isLoading: isLoadingCategories,
    error: categoriesError,
  } = useGetCategories();
  const {
    accounts,
    isLoading: isLoadingAccounts,
    error: accountsError,
  } = useGetAccounts();
  const [editingTransaction, setEditingTransaction] = useState<string | null>(
    null
  );
  const [approvingCategory, setApprovingCategory] = useState<string | null>(
    null
  );
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: dataRange.from,
    to: dataRange.to,
  });
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [amountFilter, setAmountFilter] = useState<{
    operator: string;
    value: number | null;
  }>({
    operator: ">=", // Default operator
    value: null, // Default value
  });
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [unifiedFilter, setUnifiedFilter] = useState<string>("");
  const [editingStatementData, setEditingStatementData] =
    useState<Transaction | null>(null);
  const [addStatementFromRowDialogOpen, setAddStatementFromRowDialogOpen] =
    useState(false);
  const [defaultAccountId, setDefaultAccountId] = useState(
    transactions[0]?.accountId
  );
  const [suggestedCategories, setSuggestedCategories] = useState<Record<string, any>>({});

  useEffect(() => {
    const fetchSuggestedCategories = async () => {
      const suggestions: Record<string, any> = {};

      for (const transaction of transactions) {
        if (!transaction.categoryValidated) {
          try {
            const suggestion = await suggestCategory(
              transaction.description,
              transaction.amount,
              transaction.type as "CREDIT" | "DEBIT"
            );
            suggestions[transaction.id] = suggestion;
          } catch (error) {
            console.error(
              `Failed to get suggestion for transaction ${transaction.id}:`,
              error
            );
          }
        }
      }

      setSuggestedCategories(suggestions);
    };

    fetchSuggestedCategories();
  }, [transactions]);

  // Calculate totals
  const calculateTotals = (rows: Row<(typeof transactions)[0]>[]) => {
    return rows.reduce(
      (acc, row) => {
        const amount = Number(row.original.amount);
        if (row.original.type === "DEBIT") {
          acc.debits += amount;
        } else {
          acc.credits += amount;
        }
        acc.total = acc.credits - acc.debits; // Changed to credits - debits
        return acc;
      },
      { debits: 0, credits: 0, total: 0 }
    );
  };

  const columns = [
    columnHelper.accessor("date", {
      header: "Date",
      cell: (info) => (
        <TableCell className="text-left px-4 py-0">
          <span className={""}>{format(info.getValue(), "MMM d, yyyy")}</span>
        </TableCell>
      ),
      filterFn: "includesString",
    }),
    columnHelper.accessor("description", {
      header: "Description",
      cell: (info) => (
        <TableCell className="px-2 py-0">{info.getValue()}</TableCell>
      ),
      filterFn: "includesString",
    }),
    columnHelper.accessor("type", {
      header: "Type",
      cell: (info) => (
        <TableCell className="px-2 py-0">
          <Badge
            variant={info.getValue() === "CREDIT" ? "success" : "destructive"}
            className="capitalize"
          >
            {info.getValue().toLowerCase()}
          </Badge>
        </TableCell>
      ),
      filterFn: (row, columnId, filterValue) => {
        if (!filterValue) return true;
        return row.original.type.toUpperCase() === filterValue.toUpperCase();
      },
    }),
    columnHelper.accessor("category", {
      header: "Category",
      cell: (info) => (
        <TableCell className="px-2 py-0">
          {editingTransaction === info.row.original.id ? (
            <CategorySelect
              onSelect={(newCategory) => {
                handleCategoryUpdate(newCategory);
              }}
              currentCategoryId={info.row.original.category?.id}
            />
          ) : (
            <div
              className="flex items-center gap-2 cursor-pointer hover:text-primary"
              onClick={() => setEditingTransaction(info.row.original.id)}
            >
              {renderCategoryWithIcon(
                info.row.original,
                setEditingTransaction,
                handleApproveCategory,
                approvingCategory,
                suggestedCategories[info.row.original.id]
              )}
            </div>
          )}
        </TableCell>
      ),
      filterFn: (row, columnId, filterValue) => {
        console.log("filterValue", filterValue);
        if (!filterValue) return true; // Show all if no filter
        const category = row.original.category?.name;
        return category?.toUpperCase() === filterValue;
      },
    }),
    columnHelper.accessor("amount", {
      header: () => "Amount",
      cell: (info) => (
        <TableCell className="text-right px-2 py-0">
          <span
            className={
              info.row.original.type === "DEBIT" ? "text-destructive" : ""
            }
          >
            {info.getValue().toLocaleString("zh-TW", {
              style: "currency",
              currency: "TWD",
            })}
          </span>
        </TableCell>
      ),
      filterFn: (row, columnId, filterValue) => {
        if (!filterValue?.value) return true; // Show all rows if no filter value

        const amount = row.getValue(columnId) as number;
        switch (filterValue.operator) {
          case "=":
            return amount === filterValue.value;
          case ">=":
            return amount >= filterValue.value;
          case "<=":
            return amount <= filterValue.value;
          default:
            return true;
        }
      },
    }),
    columnHelper.accessor("id", {
      header: "",
      cell: (info) => (
        <TableCell className="w-10 flex-start px-2 py-0">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => setEditingTransaction(info.row.original.id)}
              >
                <Pencil className="mr-2 h-4 w-4" />
                更新類別
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setDefaultAccountId(info.row.original.accountId);
                  setAddStatementFromRowDialogOpen(true);
                }}
              >
                <Plus className="mr-2 h-4 w-4" />
                新增項目
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setEditingStatementData(info.row.original);
                }}
              >
                <Pencil className="mr-2 h-4 w-4" />
                修改項目
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => handleDelete(info.row.original.id)}
              >
                <Trash className="mr-2 h-4 w-4" />
                刪除項目
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCell>
      ),
    }),
  ];

  const filteredTransactions = useMemo(() => {
    return transactions.filter((transaction) => {
      const transactionDate = new Date(transaction.date);

      // Date filter
      const withinDateRange =
        transactionDate >=
          (dateRange?.from ?? startOfYear(subYears(new Date(), 1))) &&
        transactionDate <= (dateRange?.to ?? endOfYear(new Date()));

      // Amount filter
      const matchesAmountFilter = amountFilter.value
        ? amountFilter.operator === ">="
          ? transaction.amount >= amountFilter.value
          : amountFilter.operator === "<="
            ? transaction.amount <= amountFilter.value
            : transaction.amount === amountFilter.value
        : true;

      // Type filter
      const matchesTypeFilter =
        typeFilter === "all" || transaction.type === typeFilter.toUpperCase();

      // Unified filter
      const matchesUnifiedFilter =
        unifiedFilter === "" ||
        Object.entries(transaction).some(([key, value]) => {
          // Handle category
          if (key === "category") {
            return transaction.category?.name
              ?.toLowerCase()
              .includes(unifiedFilter.toLowerCase());
          }
          // Handle date
          if (key === "date") {
            const formattedDate = format(
              new Date(value),
              "MMM d, yyyy"
            ).toLowerCase();
            return formattedDate.includes(unifiedFilter.toLowerCase());
          }
          // Handle all other fields
          return value
            ?.toString()
            .toLowerCase()
            .includes(unifiedFilter.toLowerCase());
        });

      return (
        withinDateRange &&
        matchesAmountFilter &&
        matchesTypeFilter &&
        matchesUnifiedFilter
      );
    });
  }, [transactions, dateRange, amountFilter, unifiedFilter, typeFilter]);

  const table = useReactTable({
    data: filteredTransactions,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFilteredRowModel: getFilteredRowModel(), // Add this
    onColumnFiltersChange: setColumnFilters,
    state: {
      columnVisibility,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: 100,
      },
    },
  });

  const handleCategoryUpdate = async (categoryId: string) => {
    if (!editingTransaction) return;

    try {
      const result = await updateStatementCategory(
        editingTransaction,
        categoryId
      );
      if (!result.success) {
        throw new Error(result.error);
      }
      toast.success("Category updated successfully");
      setEditingTransaction(null);
      // Refresh the data
      await onTransactionUpdate();
    } catch (error) {
      toast.error("Failed to update category");
    }
  };

  const handleApproveCategory = async (
    transactionId: string,
    categoryId: string
  ) => {
    try {
      setApprovingCategory(transactionId);
      // Update the category
      const updateResult = await updateStatementCategory(
        transactionId,
        categoryId
      );
      console.log("updateResult", updateResult);
      if (!updateResult.success) {
        throw new Error(updateResult.error);
      }

      toast.success("Category approved successfully");
      // Refresh the data
      await onTransactionUpdate();
    } catch (error) {
      toast.error("Failed to approve category");
    } finally {
      setApprovingCategory(null);
    }
  };

  const handleDelete = async (transactionId: string) => {
    try {
      const result = await deleteStatement(transactionId);
      if (!result.success) {
        throw new Error(result.error);
      }
      toast.success("交易已刪除");
      // 刷新數據
      await onTransactionUpdate();
    } catch (error) {
      toast.error("刪除交易失敗");
    }
  };

  if (isLoadingCategories || isLoadingAccounts) {
    return <div className="p-4 text-center">Loading data...</div>;
  }

  if (categoriesError || accountsError) {
    return (
      <div className="p-4 text-red-500 text-center">
        <p>Error loading data:</p>
        {categoriesError && <p>- Categories: {categoriesError.message}</p>}
        {accountsError && <p>- Accounts: {accountsError.message}</p>}
      </div>
    );
  }

  return (
    <div className="space-y-1">
      <div id="no-print" className="flex gap-2">
        <DateRangePicker
          key={`${dataRange.from}-${dataRange.to}`}
          initialDateFrom={dataRange.from}
          initialDateTo={dataRange.to}
          showCompare={false}
          onUpdate={(values) => {
            const { from, to } = values.range;
            if (!from || !to) return;
            if (differenceInDays(to, from) > MAX_DATE_RANGE_DAYS) {
              toast.error(
                `The selected date range is too big. Max allowed range is ${MAX_DATE_RANGE_DAYS} days!`
              );
              return;
            }
            setDateRange({ from, to });
          }}
        />
        <div>
          <Input
            placeholder="Search all columns..."
            value={unifiedFilter}
            onChange={(e) => setUnifiedFilter(e.target.value)}
            className="h-8 max-w-sm"
          />
        </div>
        <div className="flex items-center">
          <Select
            value={typeFilter}
            onValueChange={(value) => setTypeFilter(value)}
          >
            <SelectTrigger className="h-8 w-[120px] text-sm">
              <SelectValue placeholder="Filter by Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="CREDIT">Credit</SelectItem>
              <SelectItem value="DEBIT">Debit</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center">
          <Select
            value={
              (table.getColumn("category")?.getFilterValue() as string) ?? "all"
            }
            onValueChange={(value) => {
              table
                .getColumn("category")
                ?.setFilterValue(value === "all" ? "" : String(value));
            }}
            disabled={isLoadingCategories}
          >
            <SelectTrigger className="h-8 w-[180px] text-sm">
              <SelectValue
                placeholder={
                  isLoadingCategories ? "Loading..." : "Filter by category"
                }
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((cat: SelectCategoryData) => (
                <SelectItem key={cat.id} value={cat.name}>
                  {cat.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <Select
            value={amountFilter.operator}
            onValueChange={(value) =>
              setAmountFilter((prev) => ({ ...prev, operator: value }))
            }
          >
            <SelectTrigger className="h-8 w-fit gap-x-1">
              <SelectValue placeholder="Operator" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="=">=</SelectItem>
              <SelectItem value=">=">{`>=`}</SelectItem>
              <SelectItem value="<=">{`<=`}</SelectItem>
            </SelectContent>
          </Select>
          <Input
            type="number"
            placeholder="Amount"
            value={amountFilter.value ?? ""}
            onChange={(e) =>
              setAmountFilter((prev) => ({
                ...prev,
                value: e.target.value ? parseFloat(e.target.value) : null,
              }))
            }
            className="h-8 max-w-[150px]"
          />
          <DropdownMenu>
            <DropdownMenuTrigger className="h-8" asChild>
              <Button variant="outline">Columns</Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table.getAllLeafColumns().map((column) => (
                <DropdownMenuCheckboxItem
                  key={column.id}
                  className="capitalize"
                  checked={column.getIsVisible()}
                  onCheckedChange={(value) => column.toggleVisibility(!!value)}
                >
                  {column.id}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className={cn(
                      "h-8",
                      header.id === "amount" && "px-2 text-right"
                    )}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                    {header.column.getIsSorted() && (
                      <span>
                        {header.column.getIsSorted() === "asc" ? " ↑" : " ↓"}
                      </span>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Fragment key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Fragment>
                ))}
              </TableRow>
            ))}
            {table.getFilteredRowModel().rows.length > 0 && (
              <TableRow className="font-bold bg-muted">
                <TableCell className="p-2">Totals</TableCell>
                <TableCell className="p-2" />
                <TableCell className="p-2" />
                <TableCell className="p-2" />
                <TableCell className="text-right p-2">
                  {(() => {
                    const totals = calculateTotals(
                      table.getFilteredRowModel().rows
                    );
                    return (
                      <div className="space-y-1">
                        <div className="text-destructive">
                          Debits:{" "}
                          {new Intl.NumberFormat("zh-TW", {
                            style: "currency",
                            currency: "TWD",
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          }).format(totals.debits)}
                        </div>
                        <div>
                          Credits:{" "}
                          {new Intl.NumberFormat("zh-TW", {
                            style: "currency",
                            currency: "TWD",
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          }).format(totals.credits)}
                        </div>
                        <div className="border-t pt-1">
                          Net:{" "}
                          {new Intl.NumberFormat("zh-TW", {
                            style: "currency",
                            currency: "TWD",
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          }).format(totals.total)}
                        </div>
                      </div>
                    );
                  })()}
                </TableCell>
                <TableCell className="p-2" />
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
        <div className="text-sm text-muted-foreground">
          Page {table.getState().pagination.pageIndex + 1} of{" "}
          {table.getPageCount()}
        </div>
      </div>

      {editingStatementData && (
        <EditStatementDialog
          statement={editingStatementData}
          open={!!editingStatementData}
          onOpenChange={(open) => {
            if (!open) setEditingStatementData(null);
          }}
          onTransactionUpdate={onTransactionUpdate}
        />
      )}

      {addStatementFromRowDialogOpen && (
        <AddStatementFromRowDialog
          categories={categories}
          accounts={accounts}
          open={addStatementFromRowDialogOpen}
          onOpenChange={setAddStatementFromRowDialogOpen}
          defaultAccountId={defaultAccountId}
          onTransactionUpdate={onTransactionUpdate}
        />
      )}
    </div>
  );
}

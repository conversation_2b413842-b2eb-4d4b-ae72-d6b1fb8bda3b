import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateSpeechDescription } from '@/actions/speeches/update-speech';
import { z } from 'zod';
import { updateSpeechSchema } from '@/components/speeches/schemas';
import { toast } from 'sonner';

export function useUpdateSpeechDescription() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      speechId, 
      data 
    }: { 
      speechId: string; 
      data: Partial<z.infer<typeof updateSpeechSchema>> 
    }) => {
      const response = await updateSpeechDescription(speechId, data);
      if (!response.success && response.data) {
        throw new Error(response.error || 'Failed to update speech.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Speech updated!');
      queryClient.invalidateQueries({ queryKey: ['speeches'] });
      queryClient.invalidateQueries({ queryKey: ['speech', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update speech.');
    },
  });
}
import { LRUCache } from 'lru-cache';

export function rateLimit({ interval, uniqueTokenPerInterval = 500 }: { interval: number; uniqueTokenPerInterval?: number }) {
  const tokenCache = new LRUCache({
    max: uniqueTokenPerInterval,
    ttl: interval,
  });

  return {
    check: (limit: number, token: string) =>
      new Promise<void>((resolve, reject) => {
        const tokenCount = (tokenCache.get(token) as number[] || [0])[0];
        
        if (tokenCount >= limit) {
          reject(new Error('Rate limit exceeded'));
        } else {
          tokenCache.set(token, [tokenCount + 1]);
          resolve();
        }
      }),
  };
}
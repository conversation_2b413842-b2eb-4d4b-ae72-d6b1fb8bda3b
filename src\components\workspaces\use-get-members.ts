import { Workspace } from "@prisma/client";
import { useQuery } from "@tanstack/react-query";
import { getMembers } from "@/actions/workspaces/get-members";


export const useGetMembers = ({ workspaceId }: { workspaceId: string }) => {
    const { data: members, isLoading, error } = useQuery({
    queryKey: ["members"],
    queryFn: async () => {
      const response = await getMembers(workspaceId);
      if (!response.success || !response.data) throw new Error("Failed to fetch members");
      return response?.data || [] as Workspace[];
    },
    enabled: !!workspaceId,
  })

  return { members, isLoading, error }
}
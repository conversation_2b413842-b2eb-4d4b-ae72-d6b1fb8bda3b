"use client";

import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { Button } from "@/components/ui/button";
import { X, XCircle } from "lucide-react";
import { Droppable } from "../droppable";
import { Draggable } from "../draggable";

export type Banner = {
  id: string;
  title: string;
  color?: string;
  type: 'income' | 'expense' | 'custom';
  shared?: boolean;
  description?: string;
  userId?: string;
};

interface BannerListProps {
  banners: Banner[];
  onDelete?: (id: string) => void;
  onDragEnd?: (event: DragEndEvent) => void;
}

export function BannerList({ banners, onDelete, onDragEnd }: BannerListProps) {
  return (
    <DndContext onDragEnd={onDragEnd}>
      <Droppable id="banner-list">
        <div className="space-y-2 min-h-[100px]">
          {banners.map((banner) => (
            <Draggable key={banner.id} id={banner.id}>
              <div className="group relative">
                <div 
                  style={{ color: banner.color }}
                  className="p-2 rounded text-center border border-gray-200"
                >
                  {banner.title}
                  {onDelete && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-1"
                    onClick={() => onDelete(banner.id)}
                  >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Delete banner</span>
                  </Button>
                  )}
                </div>
              </div>
            </Draggable>
          ))}
        </div>
      </Droppable>
    </DndContext>
  );
}

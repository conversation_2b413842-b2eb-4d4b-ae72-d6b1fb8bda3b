"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { Prisma } from "@prisma/client";
import { z } from "zod";

// Schema for validating the update progress request
const updateSavingProgressSchema = z.object({
  goalId: z.string().min(1, "Goal ID is required"),
  amount: z.number().min(1, "Progress amount is required"),
});

export async function updateSavingProgress(input: {
  goalId: string;
  amount: number;
}) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized: No user found");
    }

    console.log("Updating savings progress with input:", input);

    // Validate input
    const validatedFields = updateSavingProgressSchema.parse(input);

    // Check if the goal exists
    const goal = await prisma.savingsGoal.findUnique({
    where: { id: validatedFields.goalId },
    include: { progress: true }, // Include progress to calculate total
    });

    if (!goal) {
    throw new Error("Goal not found");
    }

    // Add new progress entry
    const newProgress = await prisma.savingsProgress.create({
    data: {
        amount: new Prisma.Decimal(validatedFields.amount),
        date: new Date(),
        goalId: validatedFields.goalId,
    },
    });

    // Calculate the new total progress
    const totalProgress = goal.progress.reduce(
        (sum, prog) => sum.plus(prog.amount),
        new Prisma.Decimal(0)
      ).plus(new Prisma.Decimal(validatedFields.amount));

    // Update the `current` field in the goal
    const updatedGoal = await prisma.savingsGoal.update({
    where: { id: validatedFields.goalId },
    data: { current: new Prisma.Decimal(totalProgress) },
    });

    revalidatePath("/savings");
    return { success: true, progress: newProgress, goal: updatedGoal };
  } catch (error) {
    console.error("Error in updateSavingProgress:", error);
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors };
    }
    if (error instanceof Error) {
      return { success: false, error: error.message };
    }
    return {
      success: false,
      error: "An unexpected error occurred while updating savings progress",
    };
  }
}

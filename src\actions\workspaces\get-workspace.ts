'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

export async function getWorkspace(workspaceId?: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      };
    }

    if (workspaceId) {
      // Get a single workspace with its members (users)
      const workspace = await prisma.workspace.findUnique({
        where: { id: workspaceId },
        include: {
          members: {
            include: {
              user: true,
            },
          },
        }        
      });

      if (!workspace) {
        return {
          error: "Workspace not found.",
          success: false
        };
      }

      //console.log("workspace", workspace);
      return {
        data: workspace, // workspace is a single object now
        success: true
      };
    } else {
      // Get the user's workspaces through the Members table
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          members: { // Include the workspace relations through the Members table
            include: {
              workspace: true // Include workspace details here
            }
          }
        }
      });

      // Extract the workspace details from the Members relation
      const workspaces = user?.members.map((member) => member.workspace) || [];

      return {
        data: workspaces,
        success: true
      };
    }
  } catch (error) {
    console.error('Error fetching workspace:', error);
    return {
      error: "Failed to fetch workspace.",
      success: false
    };
  }
}


"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { Prisma, Income, Expense } from "@prisma/client";
import { suggestCategory } from "@/lib/utils/transaction-categorization";
import { calculateTransactionHash } from "@/lib/utils/hash-calculation";

type Statement = {
  accountId: string;
  description: string;
  amount: number;
  type: "CREDIT" | "DEBIT";
  categoryId: string;
  currencyIso?: string;
  date?: Date;
};

export async function createStatement({
  statements,
}: {
  statements: Statement[];
}) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("未經授權");
    }

    // 驗證輸入
    if (!statements || statements.length === 0) {
      throw new Error("未提供記錄資料");
    }

    const createdStatements: (Income | Expense)[] = [];
    const accountBalanceChanges: Record<string, number> = {};

    // 開始交易
    await prisma.$transaction(async (tx) => {
      for (const statement of statements) {
        // Determine the suggested category
        const { categoryId: suggestedCategoryId, confidence } = suggestCategory(
          statement.description,
          statement.amount,
          statement.type
        );

        // 根據類型創建 Income 或 Expense 記錄
        if (statement.type === "CREDIT") {
          const date = statement.date || new Date();
          const importHash = calculateTransactionHash({
            date,
            type: statement.type,
            categoryId: statement.categoryId,
            accountId: statement.accountId,
            amount: statement.amount,
            description: statement.description,
          });

          const income = await tx.income.create({
            data: {
              amount: new Prisma.Decimal(statement.amount),
              description: statement.description,
              date,
              categoryId: statement.categoryId,
              userId,
              accountId: statement.accountId,
              type: statement.type,
              categoryValidated: statement.categoryId === suggestedCategoryId,
              suggestedCategoryId,
              importHash,
            },
          });
          createdStatements.push(income);
        } else {
          const date = statement.date || new Date();
          const importHash = calculateTransactionHash({
            date,
            type: statement.type,
            categoryId: statement.categoryId,
            accountId: statement.accountId,
            amount: statement.amount,
            description: statement.description,
          });

          const expense = await tx.expense.create({
            data: {
              amount: new Prisma.Decimal(statement.amount),
              description: statement.description,
              date,
              categoryId: statement.categoryId,
              userId,
              accountId: statement.accountId,
              type: statement.type,
              categoryValidated: statement.categoryId === suggestedCategoryId,
              suggestedCategoryId,
              importHash,
            },
          });
          createdStatements.push(expense);
        }

        // 更新帳戶餘額變動
        const amount = statement.amount;
        if (!accountBalanceChanges[statement.accountId]) {
          accountBalanceChanges[statement.accountId] = 0;
        }
        accountBalanceChanges[statement.accountId] +=
          statement.type === "CREDIT" ? amount : -amount;
      }

      // 更新每個帳戶的餘額
      /*for (const [accountId, change] of Object.entries(accountBalanceChanges)) {
        await tx.balance.create({
          data: {
            amount: change,
            date: new Date(),
            bankAccount: {
              connect: {
                id: accountId,
              },
            },
            currency: {
              connect: {
                iso: "TWD",
              },
            },
          },
        });
      }*/
    });

    revalidatePath("/statements");
    return { success: true, data: createdStatements };
  } catch (error) {
    console.error("創建記錄時發生錯誤:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "創建記錄失敗",
    };
  }
}

"use client";

import { useQuery } from "@tanstack/react-query";
import { getSelectCategories } from "@/actions/categories/get-select-categories";
import { Category } from "@prisma/client";

// Define the structure of the data we expect (matching CategorySelect needs)
export type SelectCategoryData = Pick<Category, 'id' | 'name'>;

// Define the expected return structure from the server action
interface GetSelectCategoriesResponse {
  success: boolean;
  categories?: SelectCategoryData[];
  error?: string;
}

/**
 * Fetches categories suitable for select dropdowns using React Query.
 * @returns {object} The query result containing categories data, loading state, and error state.
 */
export function useGetCategories() {
  const query = useQuery<SelectCategoryData[], Error>({ // Specify return type and error type
    // Unique query key for categories used in select components
    queryKey: ["categories", "select"],

    // Query function that calls the server action
    queryFn: async (): Promise<SelectCategoryData[]> => {
      const response: GetSelectCategoriesResponse = await getSelectCategories();

      if (!response.success || !response.categories) {
        // Throw an error that React Query will catch
        throw new Error(response.error || "Failed to fetch categories");
      }
      // Return the categories data on success
      return response.categories;
    },

    // Optional: Configure stale time, cache time, etc.
    // staleTime: 5 * 60 * 1000, // 5 minutes
    // cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Return the relevant data from useQuery, renaming 'data' for clarity
  // Provide a default empty array for categories to prevent errors before data loads
  return {
    categories: query.data ?? [],
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch // Expose refetch if needed
  };
}
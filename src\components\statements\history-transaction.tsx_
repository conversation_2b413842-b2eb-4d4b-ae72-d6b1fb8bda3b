'use client'

import React, { useState } from "react";
import { ActivityTable } from './activity-table'
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { MAX_DATE_RANGE_DAYS } from "@/constants";
import { differenceInDays, startOfMonth } from "date-fns";
import { toast } from "sonner";
import './styles.css';


export const Activity = () => {
  const [isOpen, setIsOpen] = React.useState(true)
  const [chartHeight, setChartHeight] = useState<number>(120);
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: startOfMonth(new Date()),
    to: new Date(),
  });

  const handleOpenChange = () => {
    setIsOpen(!isOpen);
    setChartHeight(!isOpen ? 120 : 400); // Example heights, adjust according to your requirements
  };

  return (
    <div className="flex flex-col h-full items-center overflow-y-auto">
      <div className="flex felx-rows items-center justify-end my-2">
        <DateRangePicker
          initialDateFrom={dateRange.from}
          initialDateTo={dateRange.to}
          showCompare={false}
          onUpdate={(values) => {
            const { from, to } = values.range;
            // We update the date range only if both dates are set

            if (!from || !to) return;
            if (differenceInDays(to, from) > MAX_DATE_RANGE_DAYS) {
              toast.error(
                `The selected date range is too big. Max allowed range is ${MAX_DATE_RANGE_DAYS} days!`
              );
              return;
            }

            setDateRange({ from, to });
          }}
        /> 
      </div>
      <ActivityTable from={dateRange.from} to={dateRange.to} />
    </div>
  );
};
import { Balance } from './../../../node_modules/.prisma/client/index.d';
import { prisma } from "@/lib/db";

// Define colors for liability categories (adjust as needed)
function getLiabilityColor(category: string) {
  const liabilityConfig = {
    mortgage: "hsl(var(--chart-6))",
    "credit-card": "hsl(var(--chart-7))",
    "car-loan": "hsl(var(--chart-8))",
    "student-loan": "hsl(var(--chart-9))",
  };

  return liabilityConfig[category as keyof typeof liabilityConfig] || "hsl(var(--chart-default))";
}

export async function getAssetsOverview(userId: string) {
  try {
    // Fetch assets, investments, and liabilities from the database
    const [cashes, assets, investments, liabilities] = await Promise.all([
      prisma.bankAccount.findMany({
        where: { userId },
        include: {
          Balance: { orderBy: { date: "desc" }, take: 1, },
        },
      }),
      prisma.asset.findMany({
        where: { userId },
        include: {
          valuations: { orderBy: { date: "desc" } },
        },
      }),
      prisma.investment.findMany({
        where: { userId },
        include: {
          valuations: { orderBy: { date: "desc" } },
        },
      }),
      prisma.liability.findMany({
        where: { userId },
      }),
    ]);

    // Aggregate cash by category
    //console.log("cashes==================", JSON.stringify(cashes, null, 2))
    const cashData = cashes.reduce((acc, cash) => {
      const category = "cash" //cash.accountType.toLowerCase();
      
      // Sum all valuations or use the asset's base value if no valuations
      const totalValue = cash.Balance.length > 0 
        ? cash.Balance.reduce((sum, balance) => sum + Number(balance.amount), 0)
        : Number(cash.initialAmount);
    
      acc[category] = (acc[category] || 0) + totalValue;
      return acc;
    }, {} as Record<string, number>);

    // Aggregate assets by category
    const assetData = assets.reduce((acc, asset) => {
      const category = asset.type.toLowerCase().replace("_", "-");
      
      // Sum all valuations or use the asset's base value if no valuations
      const totalValue = asset.valuations.length > 0 
        ? asset.valuations.reduce((sum, valuation) => sum + Number(valuation.value), 0)
        : Number(asset.value);
    
      acc[category] = (acc[category] || 0) + totalValue;
      return acc;
    }, {} as Record<string, number>);

    console.log("investments: ", investments)
    // Aggregate investments by category
    const investmentData = investments.reduce((acc, investment) => {
      const category = investment.type.toLowerCase();
      
      const totalValue = investment.valuations.length > 0
        ? investment.valuations.reduce((sum, valuation) => sum + Number(valuation.value), 0)
        : Number(investment.amount);
    
      acc[category] = (acc[category] || 0) + totalValue;
      return acc;
    }, {} as Record<string, number>);

    // Aggregate liabilities by category
    const liabilityData = liabilities.reduce((acc, liability) => {
      const category = liability.type.toLowerCase().replace("_", "-"); // e.g., MORTGAGE -> mortgage
      const value = liability.amount;

      acc[category] = (acc[category] || 0) + Number(value);
      return acc;
    }, {} as Record<string, number>);

    // Combine cashes, assets, investments, and liabilities into totalAssets
    const totalAssets = [
      ...Object.entries(cashData).map(([category, value]) => ({
        category,
        amount: value,
        fill: getCategoryColor(category),
      })),
      ...Object.entries(assetData).map(([category, value]) => ({
        category,
        amount: value,
        fill: getCategoryColor(category),
      })),
      ...Object.entries(investmentData).map(([category, value]) => ({
        category,
        amount: value,
        fill: getCategoryColor(category),
      })),
    ];

    const totalInvestments = [
      ...Object.entries(investmentData).map(([category, value]) => ({
        category,
        amount: value,
        fill: getCategoryColor(category),
      })),
    ]

    // Aggregate liabilities and map to categories
    const totalLiabilities = [
      ...Object.entries(liabilityData).map(([category, value]) => ({
        category,
        amount: value,
        fill: getLiabilityColor(category),
      })),
    ];

    return { success: true, data: { totalAssets, totalInvestments, totalLiabilities } };
  } catch (error) {
    console.error("Error fetching data:", error);
    return { success: false, error: "Failed to fetch data" };
  }
}

// Helper function to get color for asset categories
function getCategoryColor(category: string) {
  const assetsConfig = {
    stocks: "hsl(var(--chart-1))",
    crypto: "hsl(var(--chart-2))",
    "real-estate": "hsl(var(--chart-3))",
    vehicle: "hsl(var(--chart-4))",
    cash: "hsl(var(--chart-5))",
  };

  return assetsConfig[category as keyof typeof assetsConfig] || "hsl(var(--chart-default))";
}

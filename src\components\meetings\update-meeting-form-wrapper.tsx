"use client"

import { useGetMeeting } from "./use-get-meeting";
import { UpdateMeetingForm } from "./update-meeting-form";
import { 
  Card, 
  CardContent, 
} from "@/components/ui/card";

import { Loader } from "lucide-react";

interface UpdateMeetingFormWrapperProps {
  onCancel?: () => void;
  id: string;
}

export const UpdateMeetingFormWrapper = ({ onCancel, id }: UpdateMeetingFormWrapperProps) => {
  
  const { data: initialValues, isLoading } = useGetMeeting({eventId: id})


  if (isLoading) {
    <Card className="w-full h-[714px] border-none shadow-none">
      <CardContent className="flex items-center jistify-center h-full">
        <Loader className="size-5 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>;
  }

  if (!initialValues) return null;

  return (
    <UpdateMeetingForm 
      onCancel={onCancel}
      initialValues={initialValues.data}
    />
  );
};

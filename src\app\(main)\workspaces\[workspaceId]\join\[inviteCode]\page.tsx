import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { getWorkspaceName } from "@/actions/workspaces/get-workspace-name";
import { JoinWorkspaceForm } from "@/components/workspaces/join-workspace-form";

interface WorkspaceIdJoinPageProps {
  params: Promise<{
    workspaceId: string;
    inviteCode: string;
  }>;
}
const WorkspaceIdJoinPage = async (props: WorkspaceIdJoinPageProps) => {
  const params = await props.params
  console.log("Params:", params);
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const initialValues = await getWorkspaceName(params.workspaceId);

  if (!initialValues) redirect("/dashboard")

  return (
    <div className="w-full lg:max-w-xl">
      <JoinWorkspaceForm initialValues={initialValues} inviteCode={params.inviteCode} />
    </div>
  );
  
}

export default WorkspaceIdJoinPage
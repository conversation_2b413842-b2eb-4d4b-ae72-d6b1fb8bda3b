"use server"

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { createSpeechSchema } from "@/components/speeches/schemas";
import { z } from "zod";

export async function createSpeech(data: z.infer<typeof createSpeechSchema>) {
  const { userId } = await auth();
  if (!userId) {
    return {
      error: "Unauthorized.",
      success: false
    }
  }
  console.log("data", data)
  const validatedData = createSpeechSchema.safeParse(data);
  if (!validatedData.success) {
    return { success: false, error: "Invalid data." };
  }

  try {
    const speech = await prisma.speech.create({
      data: {
        userId,
        workspaceId: validatedData.data.workspaceId,
        projectId: validatedData.data.projectId,
        title: validatedData.data.title,
        description: validatedData.data.description,
        recordings: {
          create: validatedData.data.recordings || []
        },
        transcriptions: {
          create: validatedData.data.transcriptions || []
        },
        analyses: {
          create: validatedData.data.analyses || []
        },
      },
      include: { project: true, recordings: true, transcriptions: true, analyses: true },
    });
    return { success: true, data: speech };
  } catch (error) {
    return { success: false, error: "Database error." };
  }
}

import { TaskData } from "@/actions/tasks/get-task";
import { AlarmClock } from "lucide-react";
import { Pencil1Icon } from "@radix-ui/react-icons";
import { useUpdateTaskModal } from "@/hooks/use-update-task-modal";
import { OverviewProperty } from "./overview-property";
import { MemberAvatar } from "../workspaces/member-avatar";
import { TaskDate } from "./task-date";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { snakeCaseToTitleCase } from "@/lib/utils";
import { format } from "date-fns";
import { ReminderBasis } from "@prisma/client";


interface TaskOverviewProps {
  data: TaskData;
}

export const TaskOverview = ({ data }: TaskOverviewProps) => {
  const { open } = useUpdateTaskModal();

  // Helper to calculate reminder date
  const calculateReminderDate = (basisDate: Date | null, daysBefore: number) => {
    if (!basisDate) return null; // Handle null dates
    const date = new Date(basisDate);
    date.setDate(date.getDate() - daysBefore);
    //date.setUTCDate(date.getUTCDate() - daysBefore);
    return date // Convert back to string if needed
  };

  return (
    <div className="flex flex-col gap-y-2 col-span-1">
      <div className="bg-muted/50 rounded-lg p-4">
        <div className="flex flex-start items-center justify-between">
          <p className="text-lg font-semibold">Overview</p>
          <Button
            onClick={() => open(data.task.id)}
            className="h-8 ml-auto"
            variant="datePicker"
            size="sm"
          >
            <Pencil1Icon className="size-4" />
            Edit
          </Button>
        </div>
        <Separator className="my-2" />
        <div className="flex flex-row gap-x-6 2xl:gap-x-12">
          <div className="flex flex-col gap-y-4">
            <OverviewProperty label="Assignee">
              <MemberAvatar name={data.assignee.name!} />
              <p className="text-sm font-medium">{data.assignee.name!}</p>
            </OverviewProperty>
            <OverviewProperty label="Start Date">
              <TaskDate value={data.task?.startDate!} className="text-sm font-medium" />
            </OverviewProperty>
            <OverviewProperty label="Due Date">
              <TaskDate value={data.task.dueDate} className="text-sm font-medium" />
            </OverviewProperty>
            <OverviewProperty label="Status">
              <Badge variant={data.task.status}>
                {snakeCaseToTitleCase(data.task.status)}
              </Badge>
            </OverviewProperty>
          </div>

          {/* Render Reminders */}
          <div className="flex flex-col items-center gap-y-2">
            {data.reminders && data.reminders.map((reminder, index) => {
              const reminderDate = reminder.enabled
                ? reminder?.customDate ? reminder.customDate : // Use custom date if available
                  calculateReminderDate(
                    reminder.basis === ReminderBasis.START_DATE ? data.task.startDate : data.task.dueDate,
                    reminder.daysBefore
                  )
                : null;

              return (
                <div key={index} className="flex flex-col gap-y-4 border-x-4 border-cyan-600 rounded-lg px-4">
                  <div className="flex flex-row items-center gap-x-2">
                    <AlarmClock color="cyan" className="size-4" />
                    <p className="font-medium text-base">Remind Me</p>
                  </div>
                  <OverviewProperty label="Status">
                    <p className="text-sm font-medium">{reminder.enabled ? "Enabled" : "Disabled"}</p>
                  </OverviewProperty>
                  <OverviewProperty label="Remind Date">
                    {reminderDate ? (
                      <p className="text-sm font-medium">{format(reminderDate, "PPpp")}</p>
                    ) : (
                      <p className="text-sm text-muted-foreground">No reminder set</p>
                    )}
                  </OverviewProperty>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

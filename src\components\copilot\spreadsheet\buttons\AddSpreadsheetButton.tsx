import { SpreadsheetData } from "@/lib/types";
import { generateId } from "ai";
import { PlusCircle } from "lucide-react";

interface AddSpreadsheetButtonProps {
  currentSheetIndex: number;
  setCurrentSheetIndex: (fn: (i: number) => number) => void;
  setSpreadsheets: (fn: (spreadsheets: SpreadsheetData[]) => SpreadsheetData[]) => void;
}

export function AddSpreadsheetButton({
  currentSheetIndex,
  setCurrentSheetIndex,
  setSpreadsheets,
}: AddSpreadsheetButtonProps) {
  const now = new Date();
  const formattedDate = now.toISOString().split('T')[0]; // Extract date in YYYY-MM-DD format
  const hours = now.getHours();
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const period = hours >= 12 ? 'pm' : 'am';
  const formattedTime = `${hours % 12 || 12}${minutes}${period}`; // Convert to 12-hour format with am/pm
  const sheetName = `Sheet ${formattedDate}_${formattedTime}`;
  const handleAddSpreadsheet = () => {
    const newSpreadsheet: SpreadsheetData = {
      id: generateId(),
      title: sheetName,
      rows: Array(8).fill(Array(12).fill({ value: "" })),
    };

    setSpreadsheets((prevSheets) => {
      const updatedSheets = [...prevSheets, newSpreadsheet];
      setCurrentSheetIndex(() => updatedSheets.length - 1);
      return updatedSheets;
    });
  };

  return (
    <button
      onClick={handleAddSpreadsheet}
    >
      <PlusCircle className="h-5 w-5 text-black dark:text-white hover:text-orange-600" />
    </button>
  );
}
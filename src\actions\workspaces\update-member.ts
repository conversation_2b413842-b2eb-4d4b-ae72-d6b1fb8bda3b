'use server';

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { Role } from "@prisma/client";
import { revalidatePath } from "next/cache";

export async function updateMember(workspaceId: string, memberId: string, role: Role) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      };
    }

    // Check if the user has ADMIN privileges
    const adminMember = await prisma.member.findFirst({
      where: { userId, workspaceId },
    });
    if (!adminMember || adminMember.role !== Role.ADMIN) {
      return {
        error: "You are not authorized to update member.",
        success: false
      };
    }


    // Fetch the member to be updated
    const memberToUpdate = await prisma.member.findUnique({
      where: { id: memberId },
    });
    console.log("memberToUpdate=======>", memberToUpdate)
    if (!memberToUpdate) {
      return {
        error: "Member not found.",
        success: false
      };
    }
    if (memberToUpdate.role === Role.ADMIN && role === Role.ADMIN) {
      return {
        error: "You are already an administrator.",
        success: false
      };
    }
    if (memberToUpdate.id === adminMember.id && memberToUpdate.role === adminMember.role) {
      return {
        error: "You are not allowed to downgrade yourself.",
        success: false
      };
    }

    // Check if this is the last member in the workspace
    const allMembersInWorkspace = await prisma.member.count({
      where: { workspaceId: memberToUpdate.workspaceId },
    });

    if (allMembersInWorkspace <= 1) {
      return {
        error: "Cannot downgrade the only member in the workspace.",
        success: false
      };
    }

    // Proceed to update the member
    const updatedMember = await prisma.member.update({
      where: { id: memberId },
      data: { role: Role[role] }
    });

    revalidatePath('/workspaces');

    console.log("Update member:", updatedMember);

    return {
      message: `Member with ID ${memberId} has been updated successfully.`,
      success: true
    };
  } catch (error) {
    console.error("Error deleting member:", error);
    return {
      error: "Failed to delete the member.",
      success: false
    };
  }
}

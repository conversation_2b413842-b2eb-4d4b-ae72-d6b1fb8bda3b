"use client";

import { Loader } from "lucide-react";
import { DataTable } from "./data-table";
import { columns } from "./columns";

export type TimeDeposit = {
  id: string;
  amount: number;
  interestRate: number;
  date: Date;
  accountId: string;
  categoryId: string;
  certificateNo: string;
  period: string;
  type: "AVAILABLE" | "WITHDRAWN";
  description: string;
  currency: string;
}

interface AccountDepositsProps {
  timeDeposits: TimeDeposit[];
  onTimeDepositUpdate: () => void;
}

export default function AccountDeposits({
  timeDeposits,
  onTimeDepositUpdate
}: AccountDepositsProps) {
 
  return (
    <div className="space-y-1">
      <div className="rounded-md border">
        {!timeDeposits ? (
          <div className="w-full border rounded-lg h-[200px] flex flex-col items-center justify-center">
            <Loader className="size-5 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <DataTable<TimeDeposit, any>
            columns={columns} 
            data={timeDeposits} 
          />       
        )}
      </div>
    </div>
  )
}
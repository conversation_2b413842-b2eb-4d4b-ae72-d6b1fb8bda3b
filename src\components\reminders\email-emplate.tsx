import {
    Body,
    Container,
    Head,
    Heading,
    Html,
    Preview,
    Section,
    Text,
  } from "@react-email/components";
  import * as React from "react";
  import { formatDateTime } from "@/components/reminders/date-format";
  
  interface TaskAssignmentEmailProps {
    assigneeName: string;
    assignerName: string;
    assignerEmail: string;
    taskName: string;
    taskDescription?: string;
    startDate: Date | string | null;
    dueDate: Date | string;
    timezone: string;
  }
  
  export const TaskAssignmentEmail: React.FC<
    Readonly<TaskAssignmentEmailProps>
  > = ({
    assigneeName,
    assignerName,
    assignerEmail,
    taskName,
    taskDescription,
    startDate,
    dueDate,
    timezone
  }) => (
    <Html>
      <Head />
      <Preview>New Task Assigned: {taskName}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={h1}>Task Assignment Notification</Heading>
          </Section>
          <Section style={content}>
            <Text style={paragraph}>Dear {assigneeName},</Text>
            <Text style={paragraph}>
              {assignerName} (<a href={`mailto:${assignerEmail}`}>{assignerEmail}</a>) 
              has assigned you a new task: <strong>{taskName}</strong>.
            </Text>
            {taskDescription && (
              <Text style={paragraph}>
                <strong>Task Description:</strong> {taskDescription}
              </Text>
            )}
            {startDate && (
              <Text style={paragraph}>
                <strong>Start Date:</strong> {formatDateTime(startDate, timezone)}
              </Text>
            )}
            {dueDate && (
              <Text style={paragraph}>
                <strong>Due Date:</strong> {formatDateTime(dueDate, timezone)}
              </Text>
            )}
            <Text style={paragraph}>
              Please review the task details and proceed accordingly. If you have any questions or need assistance, feel free to reach out to {assignerName}.
            </Text>
          </Section>
          <Section style={footer}>
            <Text style={footerText}>
              This email is a notification from your task management platform. If you believe this was sent in error, please contact the support team.
            </Text>
            <Text style={footerText}>
              © {new Date().getFullYear()} Task Management Platform. All rights reserved.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
  
  const main = {
    fontFamily: "Arial, sans-serif",
    lineHeight: "1.6",
    color: "#333",
  };
  
  const container = {
    maxWidth: "600px",
    margin: "0 auto",
  };
  
  const header = {
    backgroundColor: "#007BFF",
    padding: "20px",
    textAlign: "center" as const,
    color: "#fff",
  };
  
  const h1 = {
    margin: "0",
    fontSize: "24px",
  };
  
  const content = {
    padding: "20px",
  };
  
  const paragraph = {
    margin: "0 0 15px",
  };
  
  const footer = {
    backgroundColor: "#f1f1f1",
    padding: "10px",
    textAlign: "center" as const,
  };
  
  const footerText = {
    fontSize: "12px",
    margin: "5px 0",
  };
  
  export default TaskAssignmentEmail;
  
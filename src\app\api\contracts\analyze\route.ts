import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@clerk/nextjs/server';
import { prisma } from '@/lib/db';
import { Risk, Opportunity } from '@prisma/client'
import { 
  createAssistant,
 } from "@/actions/canvas/assistant"
import { extractTextFromPDF, analyzeContractWithAI } from '@/ai/contract/ai.services';
import { updateEmbeddings } from './functions';
import { analysis_data, analysis_data_2 } from './constant'

export async function POST(req: NextRequest) {
  const user = await currentUser();
  const userId = user?.id
  const userEmail = user?.emailAddresses[0]?.emailAddress ?? userId

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const formData = await req.formData();
    const file = formData.get('contract') as File;
    const contractType = formData.get('contractType') as string;

    if (!file || !contractType) {
      return NextResponse.json({ error: 'Missing file or contract type' }, { status: 400 });
    }

    const buffer = await file.arrayBuffer();
    const pdfText = await extractTextFromPDF(Buffer.from(buffer));
    //console.log("pdfText============>", pdfText)
    const analysis = await analyzeContractWithAI(pdfText, "premium", contractType);
    //const analysis = analysis_data_2
    console.log("analysis============>", analysis)

    if (!analysis.summary || !analysis.risks || !analysis.opportunities) {
      throw new Error("Failed to analyze contract");
    }
  
    
    const savedAnalysis = await prisma.contractAnalysis.create({
      data: {
        userId,
        contractText: pdfText,
        contractType,
        risks: {
          create: analysis.risks.map((risk: Risk) => ({
            risk: risk.risk,
            explanation: risk.explanation,
            severity: risk.severity
          }))
        },
        opportunities: {
          create: analysis.opportunities.map((opportunity: Opportunity) => ({
            opportunity: opportunity.opportunity,
            explanation: opportunity.explanation,
            impact: opportunity.impact
          }))
        },
        summary: analysis.summary || '',
        recommendations: analysis.recommendations || [],
        keyClauses: analysis.keyClauses || [],
        legalCompliance: analysis.legalCompliance || '',
        negotiationPoints: analysis.negotiationPoints || [],
        contractDuration: analysis.contractDuration || '',
        terminationConditions: analysis.terminationConditions || '',
        financialTerms: analysis.financialTerms || {},
        performanceMetrics: analysis.performanceMetrics || [],
        specificClauses: analysis.specificClauses || '',
        overallScore: Number(analysis.overallScore) || 0,
        language: "zh-TW",
        filePath: `${userEmail}/${file.name}`
      },
      include: {
        risks: true,
        opportunities: true
      }
    });
 
    // Add additional logging
    if (!savedAnalysis) {
      console.error('No analysis was saved');
      return NextResponse.json({ error: 'Failed to save analysis' }, { status: 500 });
    }
    // Run embeddings update and assistant creation in parallel
    const [embeddingsResult, createdAssistant] = await Promise.all([
      updateEmbeddings({
        tableName: "ContractAnalysis",
        embeddingColumn: "embedding",
        filter: { userId, id: savedAnalysis.id },
        filePath: `${userEmail}/${file.name}`
      }),
      prisma.assistant.create({
        data: {
          assistant_id: savedAnalysis.id,
          graph_id: savedAnalysis.id,
          name: `${file.name}`,
          metadata: {
            user_id: userId,
            iconData: {
              iconName: "User",
              iconColor: "#000000",
            },
            description: "Contract assistant.",
            is_default: true,
          },
          config: {
            configurable: {
              systemPrompt: "",
            },
          },
        },
      })
    ]);

    if (!embeddingsResult.success) {
      console.error('Embeddings update failed', embeddingsResult.error);
      return NextResponse.json({ error: 'Failed to update embeddings' }, { status: 500 });
    }

    return NextResponse.json(savedAnalysis);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to analyze contract' }, { status: 500 });
  }
}
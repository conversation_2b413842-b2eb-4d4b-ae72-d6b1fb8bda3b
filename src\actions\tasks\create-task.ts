'use server'
import { after } from 'next/server'
import { currentUser } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { revalidatePath } from "next/cache"
import { z } from "zod"
import { createTaskSchema } from "@/components/tasks/schemas"
import { ReminderBasis } from "@prisma/client";
import { workflowClient } from "@/lib/workflow"
import { createOrUpdateWorkflowRun } from "@/actions/reminders/create-or-update-workflowrun";
import config from "@/lib/config/env";
import { sendEmail } from "@/lib/workflow";

export async function createTask(values: z.infer<typeof createTaskSchema>) {
  //console.log("values", values)
  try {
    const user = await currentUser();
    const userId = user?.id
    const assignerName = user?.firstName + " " + user?.lastName
    const assignerEmail = user?.emailAddresses[0].emailAddress

    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    const validatedFields = createTaskSchema.safeParse(values)
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false
      }
    }

    const { name, workspaceId, projectId, startDate, dueDate, assigneeId, status, reminders } = validatedFields.data

    // Check if creator is a member of the workspace
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId },
    });

    if (!member) {
      return {
        error: "Unauthorized",
        success: false
      }
    }

    // Verify assignee exists and is a member of the workspace
    const assigneeMember = await prisma.member.findFirst({
      where: {
        id: assigneeId,
        workspaceId,
      },
    });

    if (!assigneeMember) {
      return {
        error: "Assignee not found in workspace",
        success: false
      }
    }

    const highestPositionTask = await prisma.task.findFirst({
      where: {
        workspaceId,
        status
      },
      orderBy: {
        position: 'desc'
      }
    })

    const newPosition = highestPositionTask
      ? highestPositionTask.position + 1000
      : 1000

    const task = await prisma.task.create({
      data: {
        name,
        status,
        userId,
        workspaceId,
        projectId,
        assigneeId,
        startDate: startDate ? new Date(startDate).toISOString() : null,
        dueDate: new Date(dueDate).toISOString(),
        position: newPosition,
        reminders: {
          create: reminders?.map(reminder => ({
            enabled: reminder.enabled,
            basis: reminder.basis as ReminderBasis,
            daysBefore: reminder.daysBefore ?? 1,
            customDate: reminder.customDate ? reminder.customDate.toISOString() : null,
          })) ?? [],
        },
      },
      include: { reminders: true, assignee: { include: { user: true } } },
    })

    revalidatePath('/workspaces')

    after(async () => {
      // Immediate notification for task creation
      try {
        await sendEmail({
          subject: `Task Assigned: ${task.name}`,
          assignerName,
          assignerEmail: assignerEmail ?? "",
          assigneeName: task.assignee.user.name ?? "",
          assigneeEmail: task.assignee.user.email ?? "",
          taskName: task.name,
          startDate: task.startDate ?? task.dueDate,
          dueDate: task.dueDate,
          taskDescription: task.description ?? "No description provided",
          timezone: task.assignee.user.timezone ?? "UTC"
        });

        // Trigger workflows for reminders
        for (const reminder of task.reminders) {
          if (!reminder.enabled) continue;

          const timezone = task.assignee.user.timezone ?? "UTC";
          const { workflowRunId } = await workflowClient.trigger({
            url: `${config.env.prodApiEndpoint}/api/workflows/reminding`,
            body: { reminder, task, assignerName, assignerEmail, timezone },
          });
    
          await createOrUpdateWorkflowRun(reminder.id, workflowRunId);
        }
            
      } catch (error) {
        console.error('Failed to send assignment email:', error);
      }
    });


    return {
      data: task,
      success: true
    }
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to create task.",
      success: false,
      details: error instanceof Error ? error.stack : undefined
    }
  }
}


import { Workspace } from '@prisma/client';
'use server';

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { z } from "zod";
import { revalidatePath } from "next/cache";


const deleteSchema = z.object({
  workspaceId: z.string().min(1),
  memberId: z.string().min(1),
});


export async function deleteMember(input: z.infer<typeof deleteSchema>) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      };
    }

    const validatedFields = deleteSchema.safeParse(input);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false
      }
    }
    
    const { workspaceId, memberId } = validatedFields.data;

    // Check if the user has ADMIN privileges
    const adminMember = await prisma.member.findFirst({
      where: { userId, workspaceId },
    });
    console.log("adminMember=======>", adminMember)

    if (!adminMember || adminMember.role !== "ADMIN") {
      return {
        error: "You are not authorized to delete member.",
        success: false
      };
    }
    // Fetch the member to be deleted
    const memberToDelete = await prisma.member.findUnique({
      where: { id: memberId },
    });

    if (!memberToDelete) {
      return {
        error: "Member not found.",
        success: false
      };
    }
    if (memberToDelete.id === adminMember.id && memberToDelete.role === adminMember.role) {
      return {
        error: "You are not allowed to remove yourself.",
        success: false
      };
    }

    // Check if this is the last member in the workspace
    const allMembersInWorkspace = await prisma.member.count({
      where: { workspaceId: memberToDelete.workspaceId },
    });

    if (allMembersInWorkspace <= 1) {
      return {
        error: "Cannot delete the last member in the workspace.",
        success: false
      };
    }

    // Proceed to delete the member
    const deletedMember = await prisma.member.delete({
      where: { id: memberId },
    });

    revalidatePath('/workspaces');

    console.log("Deleted member:", deletedMember);

    return {
      message: `Member with ID ${memberId} has been deleted successfully.`,
      success: true
    };
  } catch (error) {
    console.error("Error deleting member:", error);
    return {
      error: "Failed to delete the member.",
      success: false
    };
  }
}

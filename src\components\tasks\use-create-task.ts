import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createTask } from '@/actions/tasks/create-task';
import { z } from 'zod';
import { createTaskSchema } from '@/components/tasks/schemas';
import { toast } from 'sonner';


export function useCreateTask() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof createTaskSchema>) => {
      console.log("values", values)
      const response = await createTask(values);
      console.log("response", response)
      if (!response?.success || !response?.data) {
        throw new Error(response.error || 'Failed to create task.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Task created!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['workspace-analytics'] });
      queryClient.invalidateQueries({ queryKey: ['project-analytics'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create task.');
    },
  });

  return mutation;
}

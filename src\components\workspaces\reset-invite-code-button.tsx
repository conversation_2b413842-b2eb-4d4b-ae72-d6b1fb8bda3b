'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { resetInviteCode } from "@/actions/workspaces/reset-invite-code";

interface UpdateInviteCodeButtonProps {
  workspaceId: string;
  workspaceName: string;
  isPending: boolean;
  startTransition: (callback: () => void) => void;
  onInviteCodeReset: (newInviteCode: string) => void;
}

export function ResetInviteCodeButton({
  workspaceId,
  workspaceName,
  isPending,
  startTransition,
  onInviteCodeReset,
}: UpdateInviteCodeButtonProps) {
  const router = useRouter();

  const onUpdateInviteCode = async () => {
    startTransition(async () => {
      try {
        const result = await resetInviteCode(workspaceId);
        console.log("result", result)

        if (result.success && result.data?.inviteCode) {
          toast.success("Invite code updated successfully");
          onInviteCodeReset(result.data.inviteCode);
        } else {
          toast.error(result.error || "Failed to update invite code");
        }
      } catch (error) {
        toast.error("Failed to update invite code");
        console.error("Error updating invite code:", error);
      }
    });
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" disabled={isPending}>
          {isPending ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Reseting...
            </>
          ) : (
            "Reset Invite Code"
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Reset Invite Code</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to generate a new invite code for the workspace "{workspaceName}"? The previous code will no longer be valid.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onUpdateInviteCode}>
            Reset Code
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

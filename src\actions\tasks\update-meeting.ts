"use server";

import { currentUser } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { google } from "googleapis";
import { calendarEventSchema } from '@/components/tasks/schemas';
import { z } from "zod";

type CreateMeetingResponse = {
  success?: boolean;
  data?: any;
  error?: string;
  message?: string;
};

const SCOPES = [
  "https://www.googleapis.com/auth/calendar",
  "https://www.googleapis.com/auth/calendar.events",
];

const calendarId = process.env.QF_GOOGLE_CALENDAR_ID;
const initGoogleCalendar = async () => {
  try {
    // Replace escaped newlines with actual newlines
    const privateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n');

    const credentials = {
      client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
      client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      project_id: process.env.QF_GOOGLE_PROJECT_ID,
      private_key: privateKey
    }

    const auth = new google.auth.GoogleAuth({
      credentials,
      scopes: SCOPES,
    });

    const calendar = google.calendar({ version: "v3", auth });
    if (!calendar) {
      throw new Error("Failed to initialize Google Calendar");
    }
    return calendar;
  } catch (error) {
    console.error("Error initializing Google Calendar API:", error);
    throw error;
  }
};


export const updateMeeting = async (
  newEvent: any,
  isUpdate: boolean
): Promise<CreateMeetingResponse> => {
  try {
    const user = await currentUser();
    const userId = user?.id;
  
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      };
    }
    if (!newEvent) {
      return {
        error: "No event data provided.",
        success: false
      }
    }

    const calendar = await initGoogleCalendar();


    if (isUpdate) {
      const updatedMeeting = (await calendar?.events.update({
        calendarId: calendarId,
        eventId: newEvent.id,
        requestBody: newEvent,
      }))?.data;

      if (!updatedMeeting) {
        return { 
          success: false,
          error: "Failed to create meeting"
        };
      }

      return { 
        data: updatedMeeting, 
        success: true,
        message: "Meeting has been rescheduled" 
      };

    } else {
      const createdMeeting = await calendar?.events.insert({
        calendarId,
        requestBody: newEvent,
      });

      if (!createdMeeting) {
        return { 
          success: false,
          error: "Failed to create meeting"
        };
      }

      return { 
        data: createdMeeting, 
        success: true,
        message: "Meeting has been created" 
      };
    }

  } catch (error) {
    console.error('Error creating meeting:', error);
    return { 
      success: false,
      error: error instanceof Error ? error.message : "Failed to create meeting"
    };
  } finally {
    revalidatePath("/");
  }
}

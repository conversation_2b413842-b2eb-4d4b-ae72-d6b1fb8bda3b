import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  flexRender,
  createColumnHelper,
  VisibilityState
} from "@tanstack/react-table";
import { ReactNode, useState, useMemo } from "react";
import { format } from "date-fns";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

interface Transaction {
  id: string;
  date: Date;
  description: string;
  incomeAmount: number;
  expenseAmount: number;
  count: number;
  type: string; // CREDIT or DEBIT
}

interface AccountBalanceProps {
  transactions: Transaction[];
}

const columnHelper = createColumnHelper<Transaction | { netAmount: number }>();

export default function AccountStatements({ transactions }: AccountBalanceProps) {
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const processedTransactions = useMemo(() => {
    // Separate incomes and expenses and calculate the net
    const incomes = transactions.filter((t) => t.type === "CREDIT");
    const expenses = transactions.filter((t) => t.type === "DEBIT");
    const totalIncome = incomes.reduce((sum, t) => sum + t.incomeAmount, 0);
    const totalExpense = expenses.reduce((sum, t) => sum + t.expenseAmount, 0);

    return [
      ...incomes,
      ...expenses,
      { netAmount: totalIncome - totalExpense }, // Add net record
    ];
  }, [transactions]);

  const columns = useMemo(() => {
    return [
      columnHelper.accessor("date", {
        header: "Date",
        cell: (info) =>
          <span className={""}>
            {info.getValue()
              ? format(new Date(info.getValue() as string | Date), "MMM d, yyyy")
              : ""}
          </span>
      }),
      columnHelper.accessor("description", {
        header: "Description",
        cell: (info) =>
          info.row.original.hasOwnProperty("netAmount") ? (
            ""
          ) : (
            info?.getValue()!
          ),
      }),
      columnHelper.accessor("incomeAmount", {
        header: "Income",
        cell: (info) =>
          info.row.original.hasOwnProperty("netAmount") ? (
            ""
          ) : (
            <span className="font-bold">
              {new Intl.NumberFormat("zh-TW", {
                style: "currency",
                currency: "TWD",
              }).format(Number(info.getValue()) || 0)}
            </span>
          ),
      }),
      columnHelper.accessor("expenseAmount", {
        header: "Expense",
        cell: (info) =>
          info.row.original.hasOwnProperty("netAmount") ? (
            ""
          ) : (
            <span className="font-bold">
              {new Intl.NumberFormat("zh-TW", {
                style: "currency",
                currency: "TWD",
              }).format(Number(info.getValue()) || 0)}
            </span>
          ),
      }),
      columnHelper.accessor("netAmount", {
        header: "Net",
        cell: (info) =>
          info.row.original.hasOwnProperty("netAmount") ? (
            <span className="font-bold">
              {new Intl.NumberFormat("zh-TW", {
                style: "currency",
                currency: "TWD",
              }).format(Number(info.getValue()) || 0)}
            </span>
          ) : (
            ""
          ),
      }),
    ];
  }, []);

  const table = useReactTable({
    data: processedTransactions as (Transaction | { netAmount: number })[],
    columns,
    state: {
      columnVisibility,
    },
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return (
    <div className="space-y-1">
      <div className="border-none">
        <Table className='text-base'>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

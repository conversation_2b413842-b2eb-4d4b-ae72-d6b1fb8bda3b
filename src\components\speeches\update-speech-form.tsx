"use client"

import type { SpeechData } from "@/components/speeches/types";
import { useUpdateSpeech } from "./use-update-speech";
import { updateSpeechSchema } from "./schemas";
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from "@/components/ui/textarea";
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";
import { ProjectAvatar } from "../projects/project-avatar";

interface UpdateSpeechFormProps {
  onCancel?: () => void;
  projectOptions: {id: string, name: string, imageUrl: string | null}[];
  initialValues: SpeechData;
}

type FormValues = z.infer<typeof updateSpeechSchema>;

export const UpdateSpeechForm = ({ 
  onCancel, 
  projectOptions,
  initialValues
}: UpdateSpeechFormProps) => {
  const { mutate, isPending } = useUpdateSpeech();

  const form = useForm<FormValues>({
    resolver: zodResolver(updateSpeechSchema),
    defaultValues: {
      id: initialValues.speech.id,
      title: initialValues.speech.title,
      projectId: initialValues.speech.projectId,
      description: initialValues.speech.description || undefined,
      workspaceId: initialValues.speech.workspaceId,
    },
  });

  const onSubmit = async (values: FormValues) => {
    mutate({
      ...values,
      id: initialValues.speech.id,
    }, {
      onSuccess: () => {
        form.reset();
        onCancel?.()
      }
    })
  }

  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7 py-4">
        <CardTitle className="text-xl font-bold">
          Update Speech
        </CardTitle>
      </CardHeader>
      <div className="px-7">
        <Separator />
      </div>
      <CardContent className="p-7">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Speech Title</FormLabel>
                    <FormControl>
                      <Input className="h-8" placeholder="Enter speech title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="projectId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project</FormLabel>
                    <Select
                      defaultValue={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select project" />
                        </SelectTrigger>
                      </FormControl>
                      <FormMessage />
                      <SelectContent>
                        {projectOptions.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            <div className="flex items-center gap-x-2">
                              <ProjectAvatar 
                                className="size-4" 
                                name={project?.name!}
                                image={project?.imageUrl!} 
                              />
                              {project.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <FormLabel>Description</FormLabel>
                    <Textarea 
                      placeholder="Add speech description (Optional)"
                      value={field.value || ""}
                      rows={3}
                      onChange={field.onChange}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Separator className="px-7" />
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" size="sm" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" size="sm" disabled={isPending}>
                {isPending ? (
                  <><Loader2 className="h-4 w-4 animate-spin" /> Update Speech</>
                ) : (
                  "Update Speech"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
"use server"

import { auth } from "@clerk/nextjs/server";
import {
  getCategories,
  suggestCategoryForTransaction,
} from "@/lib/config/categories";

export async function suggestCategory(
  description: string,
  amount: number,
  type: "CREDIT" | "DEBIT",
) {
  const { userId } = await auth()
  if (!userId) throw new Error("Unauthorized")
  
  const [suggestedId, categories] = await Promise.all([
    suggestCategoryForTransaction(description, type, userId),
    getCategories(userId),
  ]);
  
  const category = categories.find((cat) => cat.id === suggestedId);

  if (!category) {
    return {
      categoryId: "other",
      name: "OTHER",
      confidence: 0.3,
      icon: "Other",
    };
  }

  // Higher confidence for transactions matching the correct type
  if (type === "CREDIT" && category.type === "CREDIT") {
    return {
      categoryId: category.id,
      name: category.name,
      icon: category.icon,
      confidence: 0.9,      
    };
  }

  if (type === "DEBIT" && category.type === "DEBIT") {
    return {
      categoryId: category.id,
      name: category.name,
      icon: category.icon,
      confidence: 0.9,      
    };
  }

  // Lower confidence if transaction type doesn't match category type
  if (type !== category.type) {
    return {
      categoryId: category.id,
      name: category.name,
      icon: category.icon,
      confidence: 0.4,
    };
  }

  // Calculate confidence based on keyword match strength
  const confidence = category.keywords.some((k) =>
    description.toLowerCase().includes(k.toLowerCase())
  )
    ? 0.8
    : 0.5;

  return {
    categoryId: category.id,
    name: category.name,
    icon: category.icon,
    confidence,
  };
}

// Export a function to get categorization rules dynamically
export async function getCategorizationRules() {
  const { userId } = await auth()
  if (!userId) throw new Error("Unauthorized")
  return await getCategories(userId);
}

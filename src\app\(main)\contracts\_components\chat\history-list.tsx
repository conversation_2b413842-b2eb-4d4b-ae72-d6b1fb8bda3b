import React, {Dispatch, SetStateAction } from 'react'
import HistoryItem from './history-item'
import { Chat, Message } from '@prisma/client'
import { useDeleteContractChat } from "./use-delete-contract-chat"
import { SidebarActions } from './history-actions'
import { ClearHistory } from './clear-history'

type HistoryListProps = {
  assistantId: string;
  contractId: string;
  chats: (Chat & { messages: Message[] })[];
  chatId: string;
  setChatId: Dispatch<SetStateAction<string>>;
  filePath?: string;
  path: string;
}

// Start of Selection
export function HistoryList({ 
  assistantId, 
  contractId, 
  chats, 
  chatId, 
  setChatId, 
  filePath, 
  path 
}: HistoryListProps) {
  const deleteChatMutation = useDeleteContractChat();
  return (
    <div className="flex flex-col flex-1 space-y-3 h-full">
      <div className="flex flex-col space-y-0.5 flex-1 overflow-y-auto">
        {!chats?.length ? (
          <div className="text-foreground/30 text-sm text-center py-4">
            No search history
          </div>
        ) : (
          chats?.map((chat: Chat) =>
            chat ? (
              <div key={chat.id} className="flex items-center justify-between">
                <HistoryItem 
                  chat={chat} 
                  chatId={chatId}
                  setChatId={setChatId} 
                  filePath={filePath} 
                  path={`${path}`} 
                />
                <SidebarActions
                  chat={chat}
                  removeChatAction={async () => deleteChatMutation.mutate({ id: chat.id, contractId })}
                  path={path}
                />
              </div>
            ) : null
          )
        )}
      </div>
      <div className="mt-auto">
        <ClearHistory 
          assistantId={assistantId} 
          contractId={contractId} 
          empty={!chats?.length}
          path={path} 
        />
      </div>
    </div>
  );
}
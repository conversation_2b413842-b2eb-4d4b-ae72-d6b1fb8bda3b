'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

export async function getWorkspaceDetails(workspaceId?: string) {
  try {
    const { userId } = await auth();
    if (!userId || !workspaceId) {
      return {
        error: "Unauthorized.",
        success: false
      };
    }

    // Get a single workspace with its members (users)
    const workspace = await prisma.workspace.findUnique({
      where: { id: workspaceId },
      include: {
        members: {
          include: {
            user: true,
          },
        },
        projects: true,
        tasks: {
          include: {
            project: true,
          },
        },
      }        
    });

    if (!workspace) {
      return {
        error: "Workspace not found.",
        success: false
      };
    }

      //console.log("workspace", workspace);
      return {
        data: workspace, // workspace is a single object now
        success: true
      };

  } catch (error) {
    console.error('Error fetching workspace:', error);
    return {
      error: "Failed to fetch workspace.",
      success: false
    };
  }
}


export const LangSmithSVG = ({ className }: { className?: string }) => (
  <svg
    width="48"
    height="24"
    viewBox="0 0 48 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M48 12C48 18.6163 42.5277 24 35.8024 24H12.1976C5.47233 24 0 18.6173 0 12C0 5.38272 5.47233 0 12.1976 0H35.8024C42.5286 0 48 5.38368 48 12ZM19.7123 11.4817C20.7324 12.7095 21.5077 14.1636 22.1424 15.6141C22.246 15.8062 22.301 16.0576 22.3633 16.2666C22.4568 16.5804 22.5007 16.8897 22.7315 17.1366C22.7871 17.2094 22.964 17.3017 23.0997 17.4266C23.4252 17.7262 23.8302 18.1273 23.6888 18.2965C23.7324 18.3897 23.9035 18.477 24.057 18.5865C24.3119 18.7685 24.5388 18.9438 24.3515 19.094C23.9457 19.1763 23.4835 19.1965 23.1733 18.8765C23.0815 19.0852 22.9108 19.0662 22.7315 19.0215C22.6884 19.0108 22.6244 18.9558 22.5842 18.949C22.5649 19.0022 22.6025 19.0419 22.5842 19.094C21.8997 19.1392 21.3653 18.4557 21.0378 17.934C20.7232 17.7663 20.3441 17.6359 20.0068 17.4991C19.6484 17.3537 19.317 17.2506 18.9759 17.0641C18.9684 17.1804 18.9769 17.3031 18.9759 17.4266C18.9715 17.9366 18.9239 18.4206 18.4604 18.7315C18.4451 19.347 18.9923 19.3897 19.4914 19.384C19.9228 19.3791 20.3732 19.3498 20.4487 19.7465C20.4154 19.7499 20.3355 19.7463 20.3014 19.7465L20.2995 19.7465C20.2043 19.7472 20.1559 19.7476 20.0805 19.819C19.838 20.0519 19.5414 19.9964 19.2704 19.8915C19.021 19.795 18.792 19.6637 18.5341 19.819C18.3017 19.9347 18.0919 20.0121 17.9449 20.109C17.6359 20.3128 17.4456 20.4937 16.8404 20.544C16.7905 20.4708 16.7976 20.4431 16.8404 20.399C16.9237 20.3039 17.0059 20.2059 17.0613 20.109C17.1736 19.9126 17.215 19.6835 17.5031 19.6015C17.1157 19.5418 16.7986 19.7181 16.4722 19.8915C16.4257 19.9162 16.3712 19.9402 16.3249 19.964C16.1218 20.0461 16.0057 20.0315 15.883 19.964C15.7133 19.8707 15.5794 19.7611 15.1467 20.0365C15.0643 19.9703 15.0999 19.8702 15.1467 19.819C15.3358 19.5913 15.5287 19.5885 15.8094 19.6015C15.0767 19.1984 14.6179 19.442 14.1893 19.674C13.8095 19.8797 13.4585 20.0685 13.1584 19.674C13.0222 19.7096 12.9396 19.7958 12.8638 19.8915C12.8327 19.9309 12.8255 20.0014 12.7902 20.0365C12.7171 19.9566 12.6982 19.8402 12.7166 19.7465C12.7242 19.7073 12.789 19.7134 12.7902 19.674C12.7719 19.6657 12.7355 19.6092 12.7166 19.6015C12.6057 19.5563 12.4644 19.5519 12.4956 19.384C12.2637 19.3071 12.1513 19.4 11.9802 19.529C11.9704 19.5364 11.9163 19.5217 11.9065 19.529C11.7944 19.4432 11.8938 19.3437 11.9802 19.239C12.02 19.1908 12.0378 19.1352 12.0538 19.094C12.1332 18.9576 12.2888 18.9526 12.422 18.949C12.5346 18.946 12.6388 18.9597 12.7166 18.8765C12.9947 18.7205 13.3292 18.795 13.6739 18.8765C13.9253 18.936 14.1786 18.9803 14.4103 18.949C14.8334 19.0022 15.3602 18.5789 15.1467 18.1515C14.7592 17.6641 14.7757 17.0781 14.7785 16.4841C14.7789 16.3826 14.7799 16.2936 14.7785 16.1941C14.7462 15.9688 14.4418 15.6581 14.1157 15.3966C13.8667 15.1969 13.5741 15.0025 13.453 14.8166C13.1185 14.4429 12.9036 14.0108 12.6429 13.5841C12.6349 13.571 12.5773 13.5973 12.5693 13.5841C12.1276 12.7402 12.0105 11.7794 11.8329 10.8292C11.6195 9.68758 11.3966 8.58029 10.7283 7.63924C10.174 7.94118 9.43899 7.7545 8.96097 7.34925C8.71062 7.57283 8.68582 7.90832 8.66642 8.21923C8.04804 7.60834 8.09747 6.43438 8.59278 5.75428C8.79517 5.48645 9.05863 5.29539 9.32917 5.10179C9.38917 5.05866 9.40484 5.025 9.40281 4.95679C9.89201 2.78508 13.2073 3.20155 14.263 4.7393C14.6117 5.17006 14.9089 5.62293 15.1467 6.11677C15.431 6.70738 15.7051 7.30513 16.1776 7.78424C16.6349 8.27771 17.0947 8.76368 17.5767 9.23421C18.3264 9.96597 19.0561 10.6642 19.7123 11.4817ZM28.1808 15.7591C28.8714 15.1352 29.5728 14.4611 29.5799 14.4541C29.627 14.4149 30.4062 13.735 31.5682 12.4242L32.9673 14.2366C32.341 15.0057 31.9743 15.4912 31.6418 15.9766C30.642 17.4371 29.809 18.648 29.8008 18.659C29.7968 18.6671 29.146 19.6015 28.1808 19.6015C28.0343 19.6015 27.8996 19.5792 27.7389 19.529C27.2029 19.3605 26.8113 19.0763 26.6344 18.659C26.4045 18.1194 26.5591 17.573 26.6344 17.4266C26.9069 16.8969 27.5034 16.371 28.1808 15.7591ZM39.0793 17.7166C39.2584 17.9162 39.3739 18.1747 39.3739 18.4415V18.5865C39.3739 18.8313 39.3015 19.0535 39.153 19.239C38.9465 19.4918 38.5988 19.674 38.2693 19.674C38.1981 19.674 38.0794 19.645 38.0082 19.632L37.9011 19.6015C37.6265 19.5494 37.3315 19.3882 37.1647 19.1665C36.6623 18.4985 35.5739 17.0117 34.5874 15.6866C34.1687 15.124 33.8028 14.5934 33.4828 14.1641C33.3674 14.0094 33.2073 13.8535 33.1146 13.7291C32.9051 13.4483 32.1449 12.4745 31.9364 12.2067C30.842 10.7953 30.1035 9.96723 30.0954 9.9592C30.0079 9.85387 29.7361 9.6268 29.5063 9.59671C29.1411 9.61276 28.7135 9.38924 28.6962 9.37921C28.5355 9.29896 28.3891 9.23421 28.328 9.23421C28.2009 9.31446 28.1757 9.35769 28.1808 9.52421C28.1808 9.54161 28.1804 9.57814 28.1808 9.59671C28.1815 9.63594 28.1828 9.62291 28.1808 9.66921C28.1716 9.90594 27.9822 10.0861 27.9598 10.1042C27.9598 10.1042 27.7115 10.3539 27.3707 10.6117C26.9558 10.9247 26.6466 10.6942 26.6344 10.6842C26.6079 10.6621 26.0979 10.2217 25.8243 9.8867C25.5548 9.55568 25.4718 9.44705 25.1616 9.01672C24.8036 8.52069 25.0752 8.16153 25.0879 8.14673L25.677 7.63924L25.6797 7.63712C25.7699 7.56636 26.1769 7.24701 26.4134 7.42175C26.6107 7.55516 26.8471 7.42676 26.8553 7.42175C26.8634 7.41673 26.9716 7.32608 26.9289 7.05925C26.8628 6.64899 27.1386 6.34731 27.1498 6.33427C27.158 6.32825 27.953 5.67875 28.9172 4.8843C30.3918 3.92958 32.2223 4.29896 32.9423 4.44426L32.9673 4.4493C33.4779 4.55262 34.0277 4.79256 33.9983 5.02929C33.9922 5.07844 33.9909 5.25582 33.7773 5.24679C32.1429 5.17958 31.0419 5.94251 30.7581 6.18927C30.7429 6.20231 30.7602 6.24271 30.7581 6.26177V6.33427C30.7561 6.34731 30.7531 6.39473 30.7581 6.40677L31.1263 7.27675C31.2148 7.4854 31.2736 7.70254 31.2736 7.92924V8.50923L31.4209 8.87172C31.5399 9.02419 32.9397 10.8507 34.2192 12.2792C35.2383 13.4167 37.912 16.3585 38.9321 17.4991L39.0793 17.7166ZM40.0367 6.26177L40.2576 6.47927C40.3369 6.55751 40.3587 6.73443 40.3312 6.84176L39.963 8.29173C39.9427 8.36395 39.4308 10.017 37.9011 10.3942C37.3469 10.5311 36.9805 10.5302 36.7229 10.5392C36.3043 10.5532 36.1952 10.5667 35.692 11.1192C35.588 11.2338 35.4395 11.3591 35.3238 11.4817C35.1326 11.6841 34.9595 11.8755 34.7346 12.1342L33.1882 10.3942C33.232 10.3431 33.2963 10.2907 33.3355 10.2492C33.4064 10.174 33.4647 10.0839 33.5564 9.9592C33.8351 9.59207 34.0418 9.3523 34.1455 9.16171C34.2299 9.00824 34.2283 8.82575 34.2192 8.65422L34.2183 8.6388C34.1937 8.1989 34.1488 7.39707 34.2192 6.91426C34.3158 6.2472 34.8854 5.53112 35.692 5.10179C36.258 4.79926 37.0596 4.49896 37.3781 4.37962L37.3857 4.3768C37.5118 4.32966 37.6664 4.42049 37.7538 4.5218L37.9011 4.6668C38.0018 4.78517 38.0008 4.91093 37.9011 5.02929L36.5756 6.62426C36.54 6.66639 36.4999 6.71409 36.502 6.76926V7.13175C36.504 7.19595 36.5258 7.30712 36.5756 7.34925L37.3857 8.00174C37.4335 8.04086 37.4709 8.00776 37.5329 8.00174H38.0484C38.1074 7.99572 38.157 7.97438 38.1957 7.92924L39.5212 6.33427C39.5792 6.26606 39.6526 6.19328 39.7421 6.18927C39.8316 6.18225 39.9736 6.19958 40.0367 6.26177Z"
      fill="currentColor"
    ></path>
    <path
      d="M15.6621 18.5865C15.593 18.9527 15.8816 19.1389 16.1776 19.0215C16.4715 18.8881 16.6681 19.0738 16.7667 19.3115C17.2203 19.3767 17.8459 19.1887 17.8713 18.659C17.3315 18.3521 17.1396 17.8263 16.914 17.2816C16.899 17.2452 16.8553 17.173 16.8404 17.1366C16.806 17.0531 16.7286 17.0019 16.6931 16.9191C16.6878 16.9068 16.6984 16.8588 16.6931 16.8466C16.6654 16.9532 16.6451 17.0843 16.6194 17.2091C16.4924 17.8258 16.357 18.5607 15.6621 18.5865Z"
      fill="currentColor"
    ></path>
    <path
      d="M28.549 18.659C28.4096 18.8877 28.0223 18.9432 27.6653 18.7315C27.4822 18.6232 27.2967 18.459 27.2235 18.2965C27.1563 18.1491 27.1594 18.0394 27.2235 17.934C27.2967 17.8137 27.4863 17.7166 27.6653 17.7166C27.826 17.7166 28.0119 17.7612 28.1808 17.8616C28.5378 18.0732 28.6883 18.4303 28.549 18.659Z"
      fill="currentColor"
    ></path>
  </svg>
);

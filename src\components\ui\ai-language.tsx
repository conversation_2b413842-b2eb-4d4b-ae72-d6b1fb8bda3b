"use client"

import * as React from "react"

import { useMediaQuery } from "@/hooks/use-media-query"
import { useAILanguage } from '@/hooks/useAILanguage'
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
} from "@/components/ui/drawer"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export type Language = {
  value: string
  label: string
}

const languages: Language[] = [
  { value: "en-US", label: "English" },
  { value: "zh-TW", label: "繁體中文" },
  //{ value: "fr", label: "Français" },
  //{ value: "de", label: "Deutsch" },
  //{ value: "es", label: "Español" },
  //{ value: "pt", label: "Português" },
  //{ value: "ja", label: "日本語" },
  //{ value: "ko", label: "한국어" },
	//{ value: "cn", label: "简体中文" },
] as const

export const DEFAULT_LANGUAGE: Language = { value: "zh-TW", label: "繁體中文" };

export function LanguageSelection() {
  const [open, setOpen] = React.useState(false)
  const isDesktop = useMediaQuery("(min-width: 768px)")
  const [selectedLanguage, setSelectedLanguage] = useAILanguage()

  if (isDesktop) {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="ghost" className="absolute left-[-16px] top-[-4px] h-6 w-[100px] py-0 mt-1 justify-start font-bold text-sm hover:bg-gradient-to-l bg-gradient-to-r from-lime-400 to-cyan-400 inline-block text-transparent bg-clip-text">
            {selectedLanguage ? <>{selectedLanguage.label}</> : <>+ Language</>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[200px] p-0" align="start">
          <LanguageList setOpen={setOpen} setSelectedLanguage={setSelectedLanguage} />
        </PopoverContent>
      </Popover>
    )
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button variant="ghost" className="absolute left-1 top-[-22px] h-6 w-[100px] py-0 justify-start text-xs">
          {selectedLanguage ? <>{selectedLanguage.label}</> : <>+ Language</>}
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <div className="mt-4 border-t">
          <LanguageList setOpen={setOpen} setSelectedLanguage={setSelectedLanguage} />
        </div>
      </DrawerContent>
    </Drawer>
  )
}

function LanguageList({
  setOpen,
  setSelectedLanguage,
}: {
  setOpen: (open: boolean) => void
  setSelectedLanguage: React.Dispatch<React.SetStateAction<Language>>
}) {
  return (
    <Command>
      {/*<CommandInput placeholder="Filter language..." />*/}
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup>
          {languages.map((language) => (
            <CommandItem
              className="cursor-pointer hover:border"
              key={language.value}
              value={language.value}
              onSelect={(value) => {
                setSelectedLanguage(
                  languages.find((priority) => priority.value === value) ?? DEFAULT_LANGUAGE
                )
                setOpen(false)
              }}
            >
              {language.label}
            </CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </Command>
  )
}

import { prisma } from "@/lib/db";

export const getStoreItem = async (namespace: string[], key: string) => {
  return prisma.memoryStore.findUnique({
    where: { 
      namespace_key: { namespace, key } // Using composite unique key
    },
  });
};

export const setStoreItem = async (namespace: string[], key: string, value: any) => {
  // Upsert: Create or update the store item
  return prisma.memoryStore.upsert({
    where: { namespace_key: { namespace, key } },
    create: { namespace, key, value },
    update: { value },
  });
};

export const deleteStoreItem = async (namespace: string[], key: string) => {
  return prisma.memoryStore.delete({
    where: { namespace_key: { namespace, key } },
  });
};

"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { UpdateSpeechFormWrapper } from "./update-speech-form-wrapper";
import { useUpdateSpeechModal } from "@/hooks/use-update-speech-modal";

export const UpdateSpeechModal = () => {
  const { speechId, close } = useUpdateSpeechModal()
  return (
    <ResponsiveModal open={!!speechId} onOpenChange={close}>
      {speechId && (
        <UpdateSpeechFormWrapper onCancel={close} id={speechId} />
      )}
      
    </ResponsiveModal>
  )
}
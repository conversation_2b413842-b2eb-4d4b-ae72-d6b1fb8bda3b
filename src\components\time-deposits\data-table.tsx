"use client"

import { useState } from "react";
import {
  Row,
  ColumnDef,
  ColumnFiltersState,
  VisibilityState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableFooter
} from "@/components/ui/table"
import { TimeDeposit } from "./account-deposits";
import { Eye, Filter } from "lucide-react";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
}

export function DataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TimeDeposit, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = useState("");
  const [showAllTypes, setShowAllTypes] = useState(false);

  // Filter data based on showAllTypes
  const filteredData = showAllTypes 
  ? data 
  : data.filter(item => item.type === 'AVAILABLE');

  // Calculate totals
  const calculateTotals = (rows: Row<TimeDeposit>[]) => {
    return rows
      .filter(row => row.original.type === 'AVAILABLE')
      .reduce((acc, row) => {
        const amount = Number(row.original.amount || 0);
        const currency = row.original.currency || "TWD";
    
        if (!acc[currency]) {
          acc[currency] = 0;
        }
        acc[currency] += amount;
    
        return acc;
      }, {} as Record<string, number>);
  };

  const table = useReactTable({
    data: filteredData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize: 30,
      },
    },
  })

  return (
    <div>
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Search across all columns..."
          value={globalFilter ?? ""}
          onChange={(e) => setGlobalFilter(e.target.value)}
          className="h-8 max-w-sm"
        />
        <div className="flex items-center gap-2">
          {/* Type Filter Toggle */}
          <Button 
            variant={showAllTypes ? "default" : "outline"} 
            size="sm"
            onClick={() => setShowAllTypes(!showAllTypes)}
          >
            <Filter className="h-4 w-4" />
            {showAllTypes ? "Showing All Types" : "Show Only Available"}
          </Button>
        
          {/* Column Visibility Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4" /> View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>      

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="h-8 text-sm px-4 py-0">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="text-sm px-4 py-2">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          {/* Totals */}
          <TableFooter>
            {table.getFilteredRowModel().rows.length > 0 && (
              <TableRow className="font-bold bg-muted">
                <TableCell className="px-4 py-2">Totals</TableCell>
                <TableCell className="p-2" />
                <TableCell className="p-2" />
                <TableCell className="p-2" />
                <TableCell className="p-2" />
                <TableCell className="p-2" />
                <TableCell className="text-right px-4 py-2">
                  {(() => {
                    const totals = calculateTotals(table.getFilteredRowModel().rows);
                    return Object.entries(totals).map(([currency, total]) => (
                      <div key={currency}>
                        {total.toLocaleString("zh-TW", {
                          style: "currency",
                          currency,
                        })}
                      </div>
                    ));
                  })()}
                </TableCell>
                <TableCell className="p-2" />
              </TableRow>
            )}
          </TableFooter>
        </Table>
      </div>
      <div className="flex items-center justify-between p-2">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
        <div className="text-sm text-muted-foreground px-2">
          Page {table.getState().pagination.pageIndex + 1} of{' '}
          {table.getPageCount()}
        </div>
      </div>
    </div>
  )
}

import * as z from 'zod';

export const createWorkspaceSchema = z.object({
  name: z.string().trim().min(1, "Workspace name is required"),
  orgnr: z.number().optional(),
  address: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
});

export const updateWorkspaceSchema = z.object({
  name: z.string().trim().min(1, "Must be 1 or more characters").optional(),
  orgnr: z.number().optional(),
  address: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
});

export const codeSchema = z.object({
  code: z.string(),
});
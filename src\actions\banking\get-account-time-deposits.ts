"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

type TimeDeposit = {
  id: string;
  type: string;    
  date: Date;
  certificateNo: string | null;
  description: string | null;
  amount: number;
}

interface AccountDetails {
  account: {
    id: string;
    name: string;
    type: string;
    balance: number;
    accountNumber?: string;
    lastUpdated: Date;
  };
  timeDeposits: TimeDeposit[]
}

export async function getAccountTimeDeposits(
  accountId: string,
  startDate: Date,
  endDate: Date
): Promise<{ success: boolean; data?: AccountDetails; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Get account with latest balance
    const account = await prisma.bankAccount.findFirst({
      where: {
        id: accountId,
        userId,
      },
      include: {
        TimeDeposit: {
          //where: {
          //  date: {
          //    gte: startDate, lte: endDate
          //  },
          //},
          orderBy: { description: "desc" },
        },
        Balance: {
          //where: {
          //  date: {
          //    gte: startDate,
          //  },
          //},
          orderBy: { date: "desc" },
          take: 1,
        },
      },
    });

    if (!account) {
      throw new Error("Account not found");
    }

    return {
      success: true,
      data: {
        account: {
          id: account.id,
          name: account.name,
          type: account.accountType,
          balance:  Number(account.Balance[0]?.amount) || 0,
          accountNumber: account.originalId || undefined,
          lastUpdated: account.Balance[0]?.date || account.updatedAt,
        },
        timeDeposits: account.TimeDeposit.map((tx) => ({
          id: tx.id,
          type: tx.type,
          date: tx.date,
          certificateNo: tx.certificateNo,
          description: tx.description,
          amount: Number(tx.amount),
        })),
      },
    };
  } catch (error) {
    console.error("Error in getAccountDetails:", error);
    return {
      success: false,
      error: "Failed to fetch account details",
    };
  }
}

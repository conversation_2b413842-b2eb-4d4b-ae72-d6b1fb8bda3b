"use server";
import { currentUser } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { google } from "googleapis";
import { calendar_v3 as googleCalendar } from "@googleapis/calendar";
import { add, format, startOfDay, endOfDay, areIntervalsOverlapping, sub } from "date-fns";
import { fromZonedTime, toZonedTime } from 'date-fns-tz';
import { generateTimeSlots } from "./generate-timeslots";

type CreateMeetingResponse = {
  success?: boolean;
  data?: any;
  error?: string;
  message?: string;
};

const timezone = 'Asia/Taipei'
const SCOPES = [
  "https://www.googleapis.com/auth/calendar",
  "https://www.googleapis.com/auth/calendar.events",
];

const calendarId = process.env.QF_GOOGLE_CALENDAR_ID;

const availableSlots = ["08:00", "08:20", "08:40", "09:00", "09:20", "09:40"]

const initGoogleCalendar = async () => {
  try {
    // Replace escaped newlines with actual newlines
    const privateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n');

    const credentials = {
      client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
      client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      project_id: process.env.QF_GOOGLE_PROJECT_ID,
      private_key: privateKey
    }

    const auth = new google.auth.GoogleAuth({
      credentials,
      scopes: SCOPES,
    });

    const calendar = google.calendar({ version: "v3", auth });
    if (!calendar) {
      throw new Error("Failed to initialize Google Calendar");
    }
    return calendar;
  } catch (error) {
    console.error("Error initializing Google Calendar API:", error);
    throw error; // Re-throw to handle it in the calling function
  }
};

export const buildDateSlots = async (date: Date) => {
  /*
  return availableSlots.map(slot => {
    const [hours, minutes] = slot.split(':').map(Number);
    return new Date(date.getFullYear(), date.getMonth(), date.getDate(), hours, minutes);
  });
  */

  const dateSlots = availableSlots.map(slot => {
    const cetDateTime = new Date(
      date.getFullYear(), 
      date.getMonth(), 
      date.getDate(),
      +slot.slice(0, 2),
      +slot.slice(3, 5)
    );
    return fromZonedTime(cetDateTime, timezone);
  })
  return dateSlots
}

export const getAvailableSlots = async (date: Date, WORKING_HOURS?: any) => {
  console.log("date", date)
  try {
    const calendar = await initGoogleCalendar();
    //const dayDate = parse(date, 'yyyyMMdd', new Date());
    
    // Convert to local timezone for start/end of day
    //const zonedDate = toZonedTime(dayDate, WORKING_HOURS.timezone);
    const startTime = fromZonedTime(startOfDay(date), WORKING_HOURS.timezone);
    const endTime = fromZonedTime(endOfDay(date), WORKING_HOURS.timezone);
    //console.log("dayDate", dayDate)
    console.log("startTime", startTime)
    console.log("endTime", endTime)


    const response = await calendar?.events.list({
      calendarId: calendarId,
      timeMin: startTime.toISOString(),
      timeMax: endTime.toISOString(),
      timeZone: WORKING_HOURS.timezone,
      singleEvents: true,
      orderBy: 'startTime',
    });

    const events = response?.data?.items || [];
    const allPossibleSlots = generateTimeSlots(date, WORKING_HOURS);
    console.log("events=======>", events)
    console.log("allPossibleSlots=======>", allPossibleSlots)
    console.log("WORKING_HOURS=======>", WORKING_HOURS)

    // Filter out slots that overlap with existing events
    const availableSlots = allPossibleSlots.filter(slot => {
      const slotEnd = add(slot, { minutes: WORKING_HOURS.slotDuration });
      const slotWithBuffer = {
        start: sub(slot, { minutes: WORKING_HOURS.minGapBetweenMeetings }),
        end: add(slotEnd, { minutes: WORKING_HOURS.minGapBetweenMeetings })
      };
    
      return !events.some((event: googleCalendar.Schema$Event) => {
        // Parse the event times directly from the ISO string
        const eventStart = new Date(event.start?.dateTime || '');
        const eventEnd = new Date(event.end?.dateTime || '');
    
        // Convert to UTC for comparison
        return areIntervalsOverlapping(
          { start: slotWithBuffer.start, end: slotWithBuffer.end },
          { start: eventStart, end: eventEnd }
        );
      });
    });

    // Convert available slots to local timezone formatted strings
    return availableSlots.map(slot => {
      const localSlot = toZonedTime(slot, WORKING_HOURS.timezone);
      return format(localSlot, 'HH:mm');
    });

  } catch (error) {
    console.error('Error fetching available slots:', error);
    throw error;
  }
};

export const createMeeting = async (formData: FormData): Promise<CreateMeetingResponse> => {
  try {
    const user = await currentUser();
    const userId = user?.id;

    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      };
    }

    const calendar = await initGoogleCalendar();
    
    const dateString = formData.get("selectedCalendarDate") as string;
    const timeString = formData.get("timetable") as string;
    const description = formData.get("message") as string;
    const title = formData.get("title") as string;
    const duration = parseInt(formData.get("duration") as string);
    const timezone = formData.get("timezone") as string;

    // Extract workingHours data
    const workingHours: Record<string, string | number> = {};
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('workingHours[')) {
        const propertyName = key.match(/\[(.*?)\]/)?.[1];
        if (propertyName) {
          let parsedValue: string | number = value as string;
          // Convert numeric strings to numbers
          if (['start', 'end', 'slotDuration', 'minGapBetweenMeetings'].includes(propertyName)) {
            parsedValue = parseInt(parsedValue, 10);
          }
          workingHours[propertyName] = parsedValue;
        }
      }
    }

    // Get attendees from formData
    const attendees = Array.from(formData.entries())
      .filter(([key]) => key.startsWith('attendees['))
      .map(([_, value]) => ({ email: value as string }));

    if (!timeString || !dateString) {
      return { message: "No correct time slot selected" };
    }
    console.log("dateString", dateString)

    const [hours, minutes] = timeString.split(':');
    const startDate = new Date(dateString);
    startDate.setHours(parseInt(hours, 10), parseInt(minutes, 10), 0, 0);
    
    // Convert to UTC for Google Calendar
    const startDateTime = fromZonedTime(startDate, timezone);
    // Create end date
    const endDateTime = add(startDateTime, { minutes: duration });

    // Verify the slot is still available
    const WORKING_HOURS = {
      start: workingHours.start as number,
      end: workingHours.end as number,
      slotDuration: duration, // minutes
      minGapBetweenMeetings: workingHours.minGapBetweenMeetings as number,
      timezone: workingHours.timezone as string
    };

    const availableSlots = await getAvailableSlots(new Date(dateString), WORKING_HOURS);
    const isSlotAvailable = availableSlots.includes(timeString);
    console.log("availableSlots", availableSlots)

    if (!isSlotAvailable) {
      return { message: "Selected time slot is no longer available" };
    }

    const event = {
      summary: title,
      description: description,
      start: {
        dateTime: startDateTime.toISOString(),
        timeZone: timezone,
      },
      end: {
        dateTime: endDateTime.toISOString(),
        timeZone: timezone,
      },
      //attendees: attendees,
      //sendUpdates: 'all',
      conferenceData: {
        createRequest: {
          requestId: Math.random().toString(36).substring(7),
          conferenceSolutionKey: { type: "hangoutsMeet" },
        },
      },
      reminders: {
        useDefault: false,
        overrides: [{ method: "email", minutes: 30 }],
      },
    };

    const meeting = await calendar?.events.insert({
      calendarId: calendarId,
      requestBody: event,
      //sendUpdates: 'all',
      //conferenceDataVersion: 1,
    });

    if (!meeting?.data) {
      throw new Error("Failed to insert event: Calendar not initialized");
    }

    revalidatePath("/");
    return { 
      data: meeting.data, 
      success: true,
      message: "Meeting has been scheduled" };

  } catch (error) {
    console.error('Error creating meeting:', error);
    return { 
      success: false,
      error: error instanceof Error ? error.message : "Failed to create meeting"
    };
  }
};

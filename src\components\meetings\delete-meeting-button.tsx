'use client'

import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { useDeleteMeeting } from "@/components/meetings/use-delete-meeting"
import { Loader2 } from "lucide-react"
import { useUpdateMeetingModal } from "@/hooks/use-update-meeting-modal"


interface DeleteMeetingButtonProps {
  meetingId: string
  isAlertOpen: boolean;
  setIsAlertOpen: (open: boolean) => void;
}

export function DeleteMeetingButton({ 
  meetingId, 
  isAlertOpen,
  setIsAlertOpen,
}: DeleteMeetingButtonProps) {
  const { mutate, isPending } = useDeleteMeeting();
  const { close } = useUpdateMeetingModal();
  const onDelete = async () => {
    mutate(meetingId, {
      onSuccess: () => {
        setIsAlertOpen(false);
        close();
      }
    });
  }


  return (
    <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
      <AlertDialogTrigger asChild onClick={(e) => {
        e.preventDefault(); // Prevent dropdown from closing
        setIsAlertOpen(true);
      }}>
        <Button disabled={isPending} variant="destructive" size="xs" className="h-8">
          {isPending ? (
            <div className="flex flex-items items-center gap-1"><Loader2 className="h-4 w-4 animate-spin" />Deleting</div>
          ) : (
            "Delete schedule"
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the meeting
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={() => setIsAlertOpen(false)}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onDelete}>
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
import { google } from "googleapis";
import { announcementsSchema } from "@/app/api/webhook/schemas";

export async function fetchBulletinFromSheets() {
  try {
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
    });

    const sheets = google.sheets({ version: 'v4', auth });
    
    // First check if we can access the spreadsheet
    const spreadsheet = await sheets.spreadsheets.get({
      spreadsheetId: process.env.GOOGLE_BULLETIN_SHEETS_ID,
    });

    // Find the Bulletin sheet
    const bulletinSheet = spreadsheet.data.sheets?.find(
      sheet => sheet.properties?.title === 'Bulletin'
    );

    if (!bulletinSheet) {
      throw new Error("Bulletin sheet not found");
    }

    // Get the values from the sheet
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: process.env.GOOGLE_BULLETIN_SHEETS_ID,
      range: 'Bulletin!A2:H', // Assuming headers are in row 1, data starts from row 2
      valueRenderOption: 'UNFORMATTED_VALUE', // This will return 'true'/'false' for boolean values
    });

    const rows = response.data.values || [];    // Transform the data to match our schema
    const announcements = rows.map((row, index) => {
      // Parse and format the date
      let dateStr = String(row[5] || '');
      // Try to parse the date, expecting format like '2025/5/29' or '2025-5-29'
      const dateMatch = dateStr.match(/^(\d{4})[-/](\d{1,2})[-/](\d{1,2})$/);
      
      if (dateMatch) {
        // Convert to YYYY-MM-DD format
        const [_, year, month, day] = dateMatch;
        dateStr = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      } else {
        // Fallback to current date if format is invalid
        dateStr = new Date().toISOString().split('T')[0];
      }

      return {
        enable: Boolean(row[0]), // Convert to boolean
        id: Number(row[1]) || index + 1, // Use array index + 1 as fallback if id is missing
        title: String(row[2] || ''),
        topic: String(row[3] || ''), // Using title as topic if not specified
        content: String(row[4] || ''),
        date: dateStr,
        severity: String(row[6] || ''),
        image: String(row[7] || ''),
      };
    });

    // Validate the data against our schema
    const validatedAnnouncements = announcementsSchema.parse(announcements);

    // Only return enabled announcements, sorted by date
    return validatedAnnouncements
      .filter(announcement => announcement.enable)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  } catch (error) {
    console.error('Error fetching bulletin data:', error);
    throw new Error('Failed to fetch bulletin data');
  }
}

import { createAgent, createTool, anthropic, openai } from "@inngest/agent-kit";
import { z } from "zod";

export const receiptScanningAgent = createAgent({
  name: "Receipt Scanning Agent",
  description:
    "Processes receipt images and PDFs to extract key information such as vendor names, dates, amounts, and line items",
  system: `You are an AI-powered receipt scanning assistant. Your primary role is to accurately extract and structure
relevant information from scanned receipts. Your task includes recognizing and parsing details such as:
- Merchant Information: Store name, address, contact details
- Transaction Details: Date, time, receipt number, payment method
- Itemized Purchases: Product names, quantities, individual prices, discounts
- Total Amounts: Subtotal, taxes, total paid, and any applied discounts
- Ensure high accuracy by detecting OCR errors and correcting misread text when possible.
- Normalize dates, currency values, and formatting for consistency.
- If any key details are missing or unclear, return a structured response indicating incomplete data.
- Handle multiple formats, languages, and varying receipt layouts efficiently.
- Maintain a structured JSON output for easy integration with databases or expense tracking systems.
`,
  model: openai({
    model: "gpt-4o-mini",
    defaultParameters: {
      max_completion_tokens: 1000,
    }
  }),
  tools: [
    createTool({
      name: "parse-pdf",
      description: "Analyzes the given PDF file and extracts the text content",
      parameters: z.object({
        pdfUrl: z.string()
      }),
      handler: async ({ pdfUrl }, { step }) => {
        const result = await step?.ai.infer("parse-pdf", {
          model: openai({
            //model: "claude-3-5-sonnet-20240620",
            model: "gpt-4o-mini",
            defaultParameters: {
              //max_tokens: 3094,
              max_completion_tokens: 3094
            }
          }),
          body: {
            messages: [
              {
                role: "user",
                content: `Extract the data from the receipt PDF located at: ${pdfUrl}\n\nReturn the structured output as follows:
                    {
                      "merchant": {
                        "name": "string",
                        "address": "string",
                        "contact": "string"
                      },
                      "transaction": {
                        "date": "string",
                        "receipt_number": "string",
                        "payment_method": "string",
                        "items": [
                          {
                            "name": "string",
                            "quantity": "string",
                            "unit_price": "string",
                            "total_price": "string"
                          }
                        ],
                        "totals": {
                          "subtotal": "string",
                          "tax": "string",
                          "total": "string",
                          "currency": "string"
                        }
                      }
                    }`
              }
            ]
          }
        })
        return result
      }
    })
  ]
});
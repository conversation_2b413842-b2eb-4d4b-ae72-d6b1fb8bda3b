'use client';

import React, { useEffect } from 'react';
import { useWorkspaces } from '@/hooks/use-workspaces';
import { Member } from '@prisma/client';

interface WorkspaceProviderProps {
  children: React.ReactNode;
  initialWorkspaces: { id: string; name: string; members: Member[] }[];
}

export function WorkspaceProvider({
  children,
  initialWorkspaces,
}: WorkspaceProviderProps) {
  const {
    setWorkspaces,
    setWorkspaceId,
    hydrateOnClient,
    currentWorkspaceId,
    isHydrated
  } = useWorkspaces();

  // Single hydration effect
  useEffect(() => {
    hydrateOnClient();
    
    // Only set initial data after hydration
    if (isHydrated) {
      setWorkspaces(initialWorkspaces);
      if (!currentWorkspaceId && initialWorkspaces.length > 0) {
        setWorkspaceId(initialWorkspaces[0].id);
      }
    }
  }, [isHydrated, initialWorkspaces, currentWorkspaceId]);

  return <>{children}</>;
}

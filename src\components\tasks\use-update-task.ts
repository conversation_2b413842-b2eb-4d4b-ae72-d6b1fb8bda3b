import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateTask } from '@/actions/tasks/update-task';
import { z } from 'zod';
import { updateTaskSchema } from '@/components/tasks/schemas';
import { toast } from 'sonner';


export function useUpdateTask() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof updateTaskSchema>) => {
      console.log("values", values)
      const response = await updateTask(values);
      if (!response.success && response.data) {
        throw new Error(response.error || 'Failed to update task.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Task updated!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['workspace-analytics'] });
      queryClient.invalidateQueries({ queryKey: ['project-analytics'] });
      queryClient.invalidateQueries({ queryKey: ['task', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update task.');
    },
  });

  return mutation;
}

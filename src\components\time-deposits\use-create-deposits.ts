import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createTimeDeposits } from "@/actions/time-deposits/create-deposits";
import { z } from "zod";
import { createDepositSchema } from "./schemas";
import { toast } from "sonner";

export function useCreateDeposit(bankAccountId: string) {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof createDepositSchema>[]) => {
      console.log("useCreateDeposit values", values)
      const response = await createTimeDeposits({ 
        timeDeposits: values
      });
      if (!response?.success || !response?.timeDeposits) {
        throw new Error(response.error || "Failed to create deposit.");
      }
      return { data: response.timeDeposits };
    },
    onSuccess: ({ data }) => {
      toast.success("Deposit created successfully!");
      queryClient.invalidateQueries({ queryKey: ["banking", bankAccountId] });
      queryClient.invalidateQueries({ queryKey: ["deposites"] });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create deposit.");
    },
  });

  return mutation;
}

import { z } from "zod";
import { parse, isValid, startOfDay } from "date-fns";

export const createDepositSchema = z.object({
  type: z.enum(["AVAILABLE", "WITHDRAWN"], { message: "Please select a valid type" }),
  amount: z.number().min(1, "Amount is required"),
  certificateNo: z.string().min(2, "Certificate Number is required"),
  period: z.string(),
  interestRate: z.number(),
  categoryId: z.string().min(2, "Please select a category"),
  accountId: z.string().min(1, "Please select an account"),
  description: z.string(),
  transactionDate: z
    .union([
      z.date(),
      z.string().transform((val) => {
        if (val === '') return startOfDay(new Date()); // Default to today
        const parsedDate = parse(val, 'yyyy-MM-dd', new Date());
        if (isValid(parsedDate) && parsedDate <= new Date()) {
          return startOfDay(parsedDate);
        }
        throw new Error("Invalid date format");
      })
    ])
    .optional()
    .default(startOfDay(new Date())), // Set default to today
});

export const updateDepositSchema = z.object({
  id: z.string(),
  type: z.enum(["AVAILABLE", "WITHDRAWN"], { message: "Please select a valid type" }),
  amount: z.number().min(1, "Amount is required"),
  certificateNo: z.string().min(2, "Certificate Number is required"),
  period: z.string().optional(),
  interestRate: z.number().optional(),
  categoryId: z.string().min(2, "Please select a category"),
  accountId: z.string().min(1, "Please select an account"),
  description: z.string().optional(),
  date: z.date().default(startOfDay(new Date())),
});
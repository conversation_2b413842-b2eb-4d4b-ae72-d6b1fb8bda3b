import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateProject } from '@/actions/projects/update-project';
import { z } from 'zod';
import { updateProjectSchema } from '@/components/projects/schemas';
import { toast } from 'sonner';


export function useUpdateProject(projectId: string) {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof updateProjectSchema>) => {
      console.log("values@useUpdateProject", values)
      const response = await updateProject(projectId, values);
      
      if (!response?.success || !response?.data) {
        throw new Error(response.error || 'Failed to update projects.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Project updated!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project', data?.id] })
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update project.');
    },
  });

  return mutation;
}

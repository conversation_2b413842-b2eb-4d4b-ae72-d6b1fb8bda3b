"use client"

import Link from "next/link";
import Image from "next/image";
import { useRef } from "react";
import { Project } from "@prisma/client";
import { useUpdateProject } from "./use-update-project"
import { updateProjectSchema } from "./schemas";
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner"
import { DeleteProjectButton } from "./delete-project-button";
import { ChevronsLeft,ImageIcon, Loader2 } from "lucide-react";
import { Ava<PERSON>, AvatarFallback } from "@radix-ui/react-avatar";


interface UpdateProjectFormProps {
  project: Project;
  onCancel?: () => void;
}

type FormValues = z.infer<typeof updateProjectSchema>;

export const UpdateProjectForm = ({ project, onCancel }: UpdateProjectFormProps) => {
  const { mutate, isPending } = useUpdateProject(project.id);

  const inputRef = useRef<HTMLInputElement>(null);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(updateProjectSchema),
    defaultValues: {
      name: project.name,
      image: project?.imageUrl || "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    const finalValues = {
      name: values.name,
      image: values.image instanceof File ? values.image : undefined,
    };

    mutate(finalValues, {
      onSuccess: () => {
        form.reset();
        toast.success("Project updated successfully")
        onCancel?.()
        //router.push(`/projects/${data?.id}`)
      }
    })
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      form.setValue("image", file);
    }
  }

  return (
    <div className="flex flex-col gap-y-2">
      <Card className="w-full h-full border-none shadow-none">
        <CardHeader className="flex p-7 pt-2">
          <CardTitle className="flex items-center gap-x-2 text-xl font-bold">
            <Button variant="outline" size="sm" className="p-2">
              <Link href={`/workspaces/${project.id}/projects/${project.id}`}>
                <ChevronsLeft className="size-8" />
              </Link>
            </Button>
            Update Project
          </CardTitle>
        </CardHeader>
        <div className="px-7">
          <Separator />
        </div>
        <CardContent className="p-7">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter project name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="image"
                  render={({ field }) => (
                    <div className="flex felx-col gap-y-2">
                      <div className="flex items-center gap-x-5">
                        {field.value ? (
                          <div className="size-[72px] relative rounded-md overflow-auto">
                            <Image
                              alt="Logo"
                              fill
                              className="object-cover"
                              src={
                                field.value instanceof File
                                  ? URL.createObjectURL(field.value)
                                  : field.value
                              }
                            />
                          </div>
                        ) : (
                          <Avatar className="size-[72px]">
                            <AvatarFallback>
                              <ImageIcon className="size-[36px] text-neutral-400"/>
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <div className="flex flex-col">
                          <p className="text-sm">Project Icon</p>
                          <p className="text-sm text-muted-foreground">
                            JPG, PNG, SVG or JPEG, max 1MB
                          </p>
                          <input
                            className="hidden"
                            type="file"
                            accept=".jpg, .png, .jpeg, .svg"
                            onChange={handleImageChange}
                            ref={inputRef}
                            disabled={isPending}
                          />
                          {field.value ? (
                            <Button
                              type="button"
                              disabled={isPending}
                              variant="destructive"
                              size="xs"
                              onClick={()=> {
                                field.onChange(null);
                                if (inputRef.current) {
                                  inputRef.current.click();
                                }
                              }}
                            >
                              Remove Image
                            </Button>
                          ) : (
                            <Button
                              type="button"
                              disabled={isPending}
                              variant="default"
                              size="xs"
                              className="w-fit mt-2"
                              onClick={()=> inputRef.current?.click()}
          
                            >
                              Upload Image
                            </Button> 
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                />
              </div>
              <Separator className="px-7" />
              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={onCancel}>
                  <Link href={`/workspaces/${project.id}/projects/${project.id}`}>
                    Cancel
                  </Link>
                </Button>
                <Button type="submit" disabled={isPending}>
                  {isPending ? (
                    <><Loader2 className="h-4 w-4 animate-spin" />Update Project</>
                  ) : (
                    "Update Project"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
      <Card className="w-full h-full border-none shadow-none">
        <CardContent className="px-7">
          <div className="flex flex-col mb-4">
            <h3 className="font-bold">Danger Zone</h3>
            <p className="text-sm text-foreground/60">
              Deleting a project will remove all associated data
            </p>
          </div>
          <div className="flex justify-end space-x-4">
            <DeleteProjectButton
              projectId={project.id}
            />
          </div>
        </CardContent>
      </Card>
    </div>

  );
};
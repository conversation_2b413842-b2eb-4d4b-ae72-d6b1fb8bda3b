import axios from 'axios';
import { Message } from '@line/bot-sdk';

// LINE Messaging API endpoint
const LINE_API_URL = 'https://api.line.me/v2/bot/message/push';

export const sendMessage = async (userId: string, messages: Message | Message[]) => {
  const messagesToSend = Array.isArray(messages) ? messages : [messages];

  try {
    await axios.post(
      LINE_API_URL,
      {
        to: userId,
        messages: messagesToSend
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('訊息傳送成功');
  } catch (error) {
    console.error('訊息傳送失敗:', error);
    throw error;
  }
};

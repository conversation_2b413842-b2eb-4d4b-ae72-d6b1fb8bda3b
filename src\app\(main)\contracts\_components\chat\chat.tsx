"use client";

import cuid from "cuid";
import Textarea from "react-textarea-autosize";
import { Message, ToolInvocationUIPart } from "@ai-sdk/ui-utils";
import { Message as ChatMessage } from "@prisma/client";
import { useChat } from "@ai-sdk/react";
import { 
  useEffect, 
  useMemo, 
  useState, 
  useRef, 
  Dispatch, 
  SetStateAction 
} from "react";
import { AnimatePresence, motion } from "framer-motion";
import { MemoizedReactMarkdown } from '@/components/markdown';
import { MarkdownMessage } from "@/components/message";
import { saveChat, saveMessages } from "@/actions/chats/actions";
import { Button } from '@/components/ui/button';
import { IconSpinner } from '@/components/ui/icons';
import { ArrowUp, PlusIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

type ChatProps = {
  contractId: string;
  filePath: string;
  initMessages: ChatMessage[]
  chatId: string;
  setChatId: Dispatch<SetStateAction<string>>;
}
export default function Chat({
  contractId,
  filePath,
  initMessages,
  chatId,
  setChatId
}: ChatProps) {
  const [toolCall, setToolCall] = useState<string>();

  console.log("initialMessages: ", initMessages)

  // Function to start a new chat
  const startNewChat = () => {
    const newChatId = cuid();
    setChatId(newChatId);
    
    // Update localStorage with new chatId
    const storageKey = `chat_${contractId}_${filePath}`;
    localStorage.setItem(storageKey, newChatId);
    
    // Clear message history in your UI
    //setMessageHistory([]);
  };

  const { messages, input, handleInputChange, handleSubmit, status } =
    useChat({
      //initialMessages: chatMessages,
      api: `/api/contracts/chat`,
      body: {
        contractId,
        filePath,
      },
      maxSteps: 4,
      experimental_throttle: 50,
      onToolCall({ toolCall }) {
        setToolCall(toolCall.toolName);
      },
      
      onError: (error) => {
        toast.error(`You've been rate limited, please try again later!, ${error}`);
      },
      onFinish: async (message: Message) => {
        console.log("message====>", message)
        /*await saveChat({
          id: cuid(),
          assistantId: "cm5apqebj006fmm2vskl1p0dt",
          title: filePath?.split("/").pop()?.split(".")[0] || `Chat ${new Date()}`,
          messages: [{
            chatId: chatId,
            role: "user",
            content: typeof message.content === 'string' 
              ? { input } 
              : input || {}
          }]
        }) 
        try {
          // Extract simple text content
          let textContent = "";
          
          if (typeof message.content === 'string') {
            textContent = message.content;
          } else if (message.parts && Array.isArray(message.parts)) {
            const textParts = message.parts.filter(part => part.type === "text");
            textContent = textParts.map(part => part.text).join('\n');
          }
          
          // Save with a very simple content structure
          await saveChat({
            id: chatId,
            assistantId: "cm5apqebj006fmm2vskl1p0dt",
            title: filePath?.split("/").pop() || `Chat ${new Date()}`,
            messages: [{
              chatId: chatId,
              role: message.role,
              content: textContent, // Just pass the text string
              createdAt: new Date()
            }]
          });
        } catch (error) {
          console.error("Error saving chat:", error);
          toast.error("Failed to save chat. Please try again.");
        }*/
        try {
          if (message?.content && message?.content.length > 0) {
            await saveChat({
              id: chatId,
              assistantId: "cm5apqebj006fmm2vskl1p0dt",
              contractId,
              title: filePath?.split("/").pop()?.split(".")[0] || `Chat ${new Date()}`,
              messages: [{
                chatId: chatId,
                role: "user",
                content: input
              }]
            }) 
            await saveMessages({
              messages: [{
                id: cuid(),              
                chatId: chatId,
                role: message.role,
                content: message.content,
                createdAt: new Date()
              }]
            })          
          }
        } catch (error) {
          console.error("Error saving chat:", error);
          toast.error("Failed to save chat. Please try again.");
        }
      }
    });

  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const messageListRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    if (messages.length > 0) setIsExpanded(true);
    if (messageListRef.current) {
      requestAnimationFrame(() => {
        messageListRef.current!.scrollTop = messageListRef.current!.scrollHeight;
      });
    }
    console.log("messages===========>", messages)
  }, [messages]);

  /*const currentToolCall = useMemo(() => {
    const tools = messages?.slice(-1)[0]?.toolInvocations;
    if (tools && toolCall === tools[0].toolName) {
      return tools[0].toolName;
    } else {
      return undefined;
    }
  }, [toolCall, messages]);*/

  const currentToolCall = useMemo(() => {
    const lastMessage = messages?.slice(-1)[0];
    if (!lastMessage) return undefined;

    // Extract tool invocations from parts
    const toolParts = lastMessage.parts?.filter(
      (part) => part.type === 'tool-invocation'
    ) as ToolInvocationUIPart[];

    if (toolParts?.length && toolCall === toolParts[0].toolInvocation.toolName) {
      return toolParts[0].toolInvocation.toolName;
    }
    
    return undefined;
  }, [toolCall, messages]);

  const awaitingResponse = useMemo(() => {
    if (
      status != "ready" &&
      currentToolCall === undefined &&
      messages.slice(-1)[0].role === "user"
    ) {
      return true;
    } else {
      return false;
    }
  }, [status, currentToolCall, messages]);

  const userQuery: Message | undefined = messages
    .filter((m) => m.role === "user")
    .slice(-1)[0];

  const lastAssistantMessage: Message | undefined = messages
    .filter((m) => m.role !== "user")
    .slice(-1)[0];

  const disabled = input?.length === 0
  return (
    <div className="flex justify-center items-start min-h-screen w-full">
      <div className="flex flex-col items-center w-full max-w-[500px]">
        <motion.div
          animate={{
            minHeight: isExpanded ? 200 : 0,
            padding: isExpanded ? 12 : 0,
          }}
          transition={{
            type: "spring",
            bounce: 0.5,
          }}
          className={cn(
            "rounded-lg w-full",
            isExpanded
              ? "bg-neutral-100/0 dark:bg-neutral-800/0"
              : "bg-transparent",
          )}
        >
          <div ref={messageListRef} className="flex flex-col w-full justify-between gap-1 pb-140 max-h-[calc(100vh-120px)] overflow-y-auto">
            <motion.div
              transition={{
                type: "spring",
              }}
              className="min-h-fit flex flex-col"
            >
              <AnimatePresence>
                {initMessages.map((message) => (
                  <div key={message.id}>
                    {message.role === "user" && (
                      <div className="w-fit max-w-[90%] flex justify-end ml-auto dark:text-neutral-400 text-neutral-600 text-sm mt-4 mb-2 text-right p-2 rounded-2xl rounded-br-none bg-lime-200/20">
                        {message.content && typeof message.content === "object" && "text" in message.content
                          ? String(message.content.text)
                          : String(message.content ?? "")}
                      </div>
                    )}
                    {message.role === "assistant" && (
                      <div className="w-fit max-w-[90%] flex justify-start mr-auto dark:text-neutral-400 text-neutral-600 text-sm mt-4 mb-2 text-left p-2 rounded-2xl rounded-tl-none bg-lime-200/20">
                        {message.content && 
                          <MarkdownMessage content={String(message.content)} />
                        }
                      </div>
                    )}
                  </div>
                ))}

                {messages.map((message, index) => (
                  <div key={message.id} className="">
                    {message.role === "user" ? (
                      <div className="w-fit max-w-[90%] flex justify-end ml-auto dark:text-neutral-400 text-neutral-600 text-sm mt-4 mb-2 text-right p-2 rounded-2xl rounded-br-none bg-lime-200/20">
                        {message.content}
                      </div>
                    ) : (
                      <AssistantMessage message={message} />
                    )}
                  </div>
                ))}
                
                {(awaitingResponse || currentToolCall) && !lastAssistantMessage && (
                  <div className="min-h-12">
                    <Loading tool={currentToolCall} />
                  </div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
        </motion.div>
        <form ref={formRef} onSubmit={handleSubmit} className="absolute bottom-0 w-full sm:max-w-[420px] min-h-[60px] flex items-center bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 px-1 shadow-lg">
          {/* New Chat button at the top */}
          <div className="flex justify-center">
            <Button 
              onClick={startNewChat}
              variant="outline"
              size="xs"
              className="w-fit flex items-center rounded-full p-1"
            >
              <PlusIcon className="size-4" />
            </Button>
          </div>
          <Textarea
            className={`w-full resize-none p-2 bg-transparent text-white focus-within:outline-none`}
            minLength={3}
            required
            rows={1}
            value={input}
            placeholder={"Ask me anything..."}
            onChange={handleInputChange}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey && !disabled) {
                formRef.current?.requestSubmit();
                e.preventDefault();
              }
            }}
          />
          <button
            className={cn(
              "absolute right-2 bottom-3 my-auto flex size-8 items-center justify-center rounded-full transition-all",
              disabled
                ? "cursor-not-allowed bg-transparent"
                : "bg-green-600 hover:bg-green-700",
            )}
            disabled={disabled}
          >
            {status != "ready" ? (
              <IconSpinner />
            ) : (              
              <ArrowUp size={18}
                className={cn(
                  "",
                  disabled ? "text-gray-300/0" : "text-white",
                )}
              />
            )}
            </button>
        </form>        
      </div>

    </div>
  );
}

const AssistantMessage = ({ message }: { message: Message | undefined }) => {
  if (message === undefined) return "HELLO";

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={message.id}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="whitespace-pre-wrap font-mono anti text-sm text-neutral-800 dark:text-neutral-200 overflow-hidden"
        id="markdown"
      >
        <MemoizedReactMarkdown
          className={"max-h-100 overflow-y-scroll no-scrollbar-gutter"}
        >
          {message.content}
        </MemoizedReactMarkdown>
      </motion.div>
    </AnimatePresence>
  );
};

const Loading = ({ tool }: { tool?: string }) => {
  const toolName =
    tool === "getInformation"
      ? "Getting information"
      : tool === "addResource"
        ? "Adding information"
        : "Thinking";

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ type: "spring" }}
        className="overflow-hidden flex justify-start items-center"
      >
        <div className="flex flex-row gap-1 items-center">
          <div className="animate-spin dark:text-neutral-400 text-neutral-500">
            <IconSpinner />
          </div>
          <div className="text-neutral-500 dark:text-neutral-400 text-sm">
            {toolName}...
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

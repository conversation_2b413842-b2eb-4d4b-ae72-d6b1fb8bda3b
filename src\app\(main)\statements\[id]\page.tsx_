/*"use client";

import { Income } from "@prisma/client"
import { getAccountDetails } from "@/actions/statements/get-account-details";
import { notFound } from "next/navigation";
import AccountTransactions from "@/components/statements/account-transactions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatDistanceToNow, subYears, subMonths } from "date-fns";
import { BalanceTooltip } from "@/components/transactions/balance-tooltip";
import { BarChartCard } from "@/components/bar-chart-statements";
import { PieChartCard } from "@/components/pie-chart-statements";
import { use, useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";

interface BankingDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

interface BaseTransaction extends Income {
  category?: {
    id: string;
    name: string;
    icon: string;
  };
}

// Then define the Account type
interface Account {
  name: string;
  balance: number;
  lastUpdated: Date;
}

// Define the AccountDetails type that matches your API response
interface AccountDetails {
  account: Account;
  transactions: Income[];
}

// Define the API response type
interface AccountDetailsResponse {
  success?: boolean;
  data?: AccountDetails;
  error?: string;
}


interface ProcessedMonthData {
  monthYear: string; // e.g. "Jan 2024" for display
  sortKey: string; // e.g. "2024-01" for sorting
  income: number;
  expense: number;
}

const chartConfig = {
  income: {
    label: "Income",
    color: "hsl(var(--chart-1))",
  },
  expense: {
    label: "Expense",
    color: "hsl(var(--chart-2))",
  },
};
interface ChartData {
  data: ProcessedMonthData[];
  trending: {
    percentage: string;
    direction: 'up' | 'down';
  };
}

const timeRanges = {
  "2y": { label: "Last 2 years", duration: subYears(new Date(), 2) },
  "1y": { label: "Last 1 year", duration: subYears(new Date(), 1) },
  "6m": { label: "Last 6 months", duration: subMonths(new Date(), 6) },
};

type TimeRangeKey = keyof typeof timeRanges;

export default function BankingDetailsPage(props: BankingDetailsPageProps) {
  const queryClient = useQueryClient();
  const params = use(props.params)
  const { id } = params;
  const [timeRange, setTimeRange] = useState<TimeRangeKey>("2y");

  const startDate = timeRanges[timeRange]?.duration || subYears(new Date(), 1);

  // Fetch account details using @tanstack/react-query
  const { data, isLoading } = useQuery({
    queryKey: ["accountDetails", id, startDate],
    queryFn: () => getAccountDetails(id, startDate),
    placeholderData: (previousData) => previousData,
  });
  
  // Use useEffect for side effects that should run when data changes
  useEffect(() => {
    if (timeRange === "6m") {
      queryClient.prefetchQuery({
        queryKey: ["accountDetails", id, subYears(new Date(), 1)],
        queryFn: () => getAccountDetails(id, subYears(new Date(), 1)),
      });
    }
  }, [data, timeRange, id, queryClient]);
  
  // Need to check if data and data.data exist before destructuring
  if (!data?.success || !data?.data) {
    notFound();
    return null;
  }
  
  const { account, transactions } = data.data;
  
  // Fix the processTransactionData return type and usage
  const chartData = processTransactionData(transactions);

  return (
    <div className="flex flex-1 flex-col gap-2 p-2 pt-0">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">{account.name}</h2>
      </div>

      <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Balance</CardTitle>
            <BalanceTooltip
              balance={account.balance}
              availableBalance={account.balance}
              pendingBalance={0}
              lastUpdated={account.lastUpdated}
            />
            <Select 
              value={timeRange} 
              onValueChange={(value: TimeRangeKey) => setTimeRange(value)}
            >
              <SelectTrigger
                className="w-[160px] rounded-lg sm:ml-auto"
                aria-label="Select a value"
              >
                <SelectValue placeholder="Last 1 year" />
              </SelectTrigger>
              <SelectContent className="rounded-xl">
                {Object.entries(timeRanges).map(([key, range]) => (
                  <SelectItem key={key} value={key} className="rounded-lg">
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {account.balance.toLocaleString("zh-TW", {
                style: "currency",
                currency: "TWD",
              })}
            </div>
            <p className="text-xs text-muted-foreground">
              Last updated{" "}
              {formatDistanceToNow(account.lastUpdated, { addSuffix: true })}
            </p>
          </CardContent>
        </Card>
        <BarChartCard
          title="Statements"
          description="Income vs Expense"
          data={chartData.data}
          xAxisKey="month"
          chartConfig={chartConfig}
        />
        <PieChartCard
          title="Statements"
          description="Income vs Expense"
          data={chartData}
          xAxisKey="month"
          chartConfig={chartConfig}
        />
      </div>

      <AccountTransactions
        transactions={transactions}
        onTransactionUpdate={() => 
          queryClient.invalidateQueries({ 
            queryKey: ["accountDetails", id] 
          })
        }
      />
    </div>
  );
}


function processTransactionData(transactions: BaseTransaction[]): ChartData {
  // Group by year-month and calculate totals
  const monthlyData = transactions.reduce((acc: Record<string, ProcessedMonthData>, transaction) => {
    const date = new Date(transaction.date);
    const sortKey = format(date, 'yyyy-MM');
    const monthYear = format(date, 'MMM yyyy');
    const amount = parseFloat(transaction.amount.toString()) / 100;

    if (!acc[sortKey]) {
      acc[sortKey] = {
        monthYear,
        sortKey,
        income: 0,
        expense: 0
      };
    }

    if (transaction.type === 'CREDIT') {
      acc[sortKey].income += amount;
    } else if (transaction.type === 'DEBIT') {
      acc[sortKey].expense += amount;
    }

    return acc;
  }, {});

  // Convert to array and sort by date
  const sortedMonths = Object.values(monthlyData)
    .sort((a, b) => a.sortKey.localeCompare(b.sortKey));

  // Step 1: Get the last 6 months and previous 6 months
  const last6Months = sortedMonths.slice(-6);
  const previous6Months = sortedMonths.slice(-12, -6);

  // Step 2: Calculate the net income for both periods
  const currentNet = last6Months.reduce((acc, month) => acc + (month.income - month.expense), 0);
  const previousNet = previous6Months.reduce((acc, month) => acc + (month.income - month.expense), 0);

  // Step 3: Calculate trending percentage and direction
  let trendingPercentage = 0;
  let trendDirection: 'up' | 'down' = 'up';

  if (previousNet !== 0) {
    const percentageChange = ((currentNet - previousNet) / Math.abs(previousNet)) * 100;
    trendingPercentage = Math.abs(percentageChange);
    trendDirection = percentageChange >= 0 ? 'up' : 'down';
  }

  // Logging for debugging purposes
  console.log("Last 6 months:", last6Months);
  console.log("Previous 6 months:", previous6Months);
  console.log("Current net income:", currentNet);
  console.log("Previous net income:", previousNet);
  console.log("Trending percentage:", trendingPercentage);
  console.log("Trend direction:", trendDirection);

  return {
    data: sortedMonths,
    trending: {
      percentage: trendingPercentage.toFixed(1),
      direction: trendDirection
    }
  };
}*/
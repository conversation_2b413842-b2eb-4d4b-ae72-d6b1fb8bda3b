generator client {
  provider        = "prisma-client-js"
  output          = "app/generated/prisma/client"
  previewFeatures = ["driverAdapters", "fullTextSearchPostgres", "postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  directUrl  = env("DATABASE_URL_UNPOOLED")
  extensions = [vector(schema: "public")]
}

model User {
  id                  String  @id
  name                String?
  email               String? @unique
  phone               String?
  plan                String  @default("basic")
  credits             Int     @default(3)
  image               String?
  language            String? @default("english")
  timezone            String  @default("Asia/Taipei") // Store timezone in IANA format
  onboardingEmailSent Boolean @default(false)

  bankAccounts      BankAccount[]
  budgets           Budget[]
  transactions      Transaction[]
  incomes           Income[]
  expenses          Expense[]
  budgetRollovers   BudgetRollover[]
  budgetPreferences BudgetPreferences[]

  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @default(now()) @map(name: "updated_at")

  stripeCustomerId       String?       @unique @map(name: "stripe_customer_id")
  stripeSubscriptionId   String?       @unique @map(name: "stripe_subscription_id")
  stripePriceId          String?       @map(name: "stripe_price_id")
  stripeCurrentPeriodEnd DateTime?     @map(name: "stripe_current_period_end")
  //workspaceId            String?
  members                Member[]
  projects               Project[]
  tasks                  Task[]
  Category               Category[]
  assets                 Asset[]
  investments            Investment[]
  liabilities            Liability[]
  savingsGoals           SavingsGoal[]
  chats                  Chat[]
  speeches               Speech[]
  banners                Banner[]

  @@map(name: "users")
}

model Workspace {
  id         String    @id @default(cuid())
  name       String
  orgnr      Int?
  address    String?
  postalCode String?
  city       String?
  inviteCode String?
  //bank_accounts BankAccount[]
  members    Member[]
  projects   Project[]
  tasks      Task[]
  speeches   Speech[]

  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @default(now()) @map(name: "updated_at")
}

model Member {
  id            String  @id @default(cuid())
  userId        String
  bankAccountId String?
  workspaceId   String
  role          Role    @default(MEMBER) // Workspace-specific role

  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignees   Task[]
  bankAccount BankAccount? @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)
  workspace   Workspace    @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@unique([userId, workspaceId]) // Ensure a user can't have multiple roles for the same workspace
}

model Project {
  id          String    @id @default(cuid())
  name        String
  userId      String
  workspaceId String
  imageUrl    String?
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  createdAt   DateTime  @default(now()) @map(name: "created_at")
  updatedAt   DateTime  @default(now()) @map(name: "updated_at")
  tasks       Task[]
  speeches    Speech[]
}

model Task {
  id          String     @id @default(cuid())
  name        String
  userId      String
  workspaceId String
  projectId   String
  assigneeId  String
  description String?
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace   Workspace  @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  assignee    Member     @relation(fields: [assigneeId], references: [id])
  startDate   DateTime?
  dueDate     DateTime
  status      TaskStatus
  position    Int        @default(1000)

  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now()) @map(name: "created_at")
  updatedAt DateTime @default(now()) @map(name: "updated_at")

  reminders Reminder[] // One-to-many relationship with reminders
}

model Reminder {
  id            String        @id @default(cuid())
  taskId        String
  task          Task          @relation(fields: [taskId], references: [id], onDelete: Cascade)
  enabled       Boolean       @default(false) // Whether the reminder is active
  basis         ReminderBasis // START_DATE or DUE_DATE
  daysBefore    Int // Days before the basis date to send a reminder
  customDate    DateTime? // Custom reminder date
  workflowRunId String? // Track workflow runs for dynamic updates
  workflowRuns  WorkflowRun[] // One-to-many relationship with workflow runs
  createdAt     DateTime      @default(now()) @map(name: "created_at")
  updatedAt     DateTime      @default(now()) @map(name: "updated_at")
}

enum ReminderBasis {
  START_DATE
  DUE_DATE
}

model WorkflowRun {
  id            String   @id @default(cuid())
  reminderId    String
  reminder      Reminder @relation(fields: [reminderId], references: [id], onDelete: Cascade)
  workflowRunId String   @unique // Workflow ID from Upstash
  status        String // Workflow status (e.g., "active", "completed")
  createdAt     DateTime @default(now()) @map(name: "created_at")
  updatedAt     DateTime @default(now()) @map(name: "updated_at")
}

model BankAccount {
  id              String           @id @default(cuid())
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @default(now())
  resourceId      String?
  originalId      String?
  orgId           String?
  userId          String?
  name            String
  originalPayload Json?
  initialAmount   Int?             @default(0)
  Balance         Balance[]
  TimeDeposit     TimeDeposit[]
  Transaction     Transaction[]
  Income          Income[]
  Expense         Expense[]
  Budget          Budget[]
  BudgetRollover  BudgetRollover[]
  accountType     AccountType      @default(BANK)
  resource        Resource?        @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  //workspaceId     String?          
  //workspace       Workspace?       @relation(fields: [workspaceId], references: [id])
  members         Member[]
  user            User?            @relation(fields: [userId], references: [id])

  @@map(name: "bank_accounts")
}

model Balance {
  id              String       @id @default(cuid())
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @default(now())
  accountId       String?
  assetId         String?
  currencyIso     String
  amount          Int?
  date            DateTime
  description     String?
  type            BalanceType  @default(AVAILABLE)
  originalPayload Json?
  bankAccount     BankAccount? @relation(fields: [accountId], references: [id], onDelete: Cascade)
  asset           Asset?       @relation(fields: [assetId], references: [id], onDelete: Cascade)
  currency        Currency     @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)
}

model TimeDeposit {
  id                  String          @id @default(cuid())
  createdAt           DateTime        @default(now())
  updatedAt           DateTime        @default(now())
  accountId           String?
  assetId             String?
  categoryId          String?
  currencyIso         String
  amount              Int?
  date                DateTime
  certificateNo       String?
  period              String?
  interestRate        Decimal?
  description         String?
  type                TimeDepositType @default(AVAILABLE)
  originalPayload     Json?
  bankAccount         BankAccount?    @relation(fields: [accountId], references: [id], onDelete: Cascade)
  asset               Asset?          @relation(fields: [assetId], references: [id], onDelete: Cascade)
  currency            Currency        @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)
  category            Category?       @relation(fields: [categoryId], references: [id])
  categoryValidated   Boolean         @default(false)
  suggestedCategoryId String?
}

model Currency {
  iso         String        @id
  symbol      String        @default("$")
  name        String        @default("新台幣")
  numericCode Int?
  Balance     Balance[]
  Transaction Transaction[]
  TimeDeposit TimeDeposit[]
}

model Asset {
  id              String             @id @default(cuid())
  name            String
  originalPayload Json?
  Balance         Balance[]
  TimeDeposit     TimeDeposit[]
  Transaction     Transaction[]
  type            AssetType
  value           Decimal
  purchaseDate    DateTime?
  description     String?
  userId          String
  user            User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions    AssetTransaction[]
  valuations      AssetValuation[]

  @@index([id, userId])
  @@map(name: "assets")
}

model AssetValuation {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  value     Decimal
  date      DateTime
  assetId   String
  asset     Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@map(name: "asset_valuations")
}

model AssetTransaction {
  id          String          @id @default(cuid())
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  type        TransactionType
  amount      Decimal
  date        DateTime
  description String?
  assetId     String
  asset       Asset           @relation(fields: [assetId], references: [id], onDelete: Cascade)

  @@map(name: "asset_transactions")
}

model Transaction {
  id                  String       @id @default(cuid())
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @default(now())
  accountId           String?
  assetId             String?
  userId              String
  user                User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  currencyIso         String
  categoryId          String?
  amount              Decimal
  date                DateTime
  description         String
  originalPayload     Json?
  review              Boolean      @default(false)
  bankAccount         BankAccount? @relation(fields: [accountId], references: [id], onDelete: Cascade)
  asset               Asset?       @relation(fields: [assetId], references: [id], onDelete: Cascade)
  category            Category?    @relation(fields: [categoryId], references: [id])
  currency            Currency     @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)
  type                String
  categoryValidated   Boolean      @default(false)
  suggestedCategoryId String?

  @@index([id, userId, accountId, categoryId, type])
}

model Income {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  amount      Decimal
  description String?
  date        DateTime
  categoryId  String
  category    Category @relation(fields: [categoryId], references: [id])

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  accountId           String?
  bankAccount         BankAccount? @relation(fields: [accountId], references: [id])
  type                String
  categoryValidated   Boolean      @default(false)
  suggestedCategoryId String?
  importHash          String?      @unique

  @@unique([userId, accountId, categoryId, date, amount, description])
  @@index([id, userId, accountId, categoryId, importHash])
}

model Expense {
  id          String   @id @default(cuid())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  amount      Decimal
  description String?
  date        DateTime
  categoryId  String
  category    Category @relation(fields: [categoryId], references: [id])

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  accountId           String?
  bankAccount         BankAccount? @relation(fields: [accountId], references: [id])
  type                String
  categoryValidated   Boolean      @default(false)
  suggestedCategoryId String?
  importHash          String?      @unique

  @@unique([userId, accountId, categoryId, date, amount, description])
  @@index([id, userId, accountId, categoryId, importHash])
}

model SpreadsheetImport {
  id         String   @id @default(cuid())
  userId     String
  importedAt DateTime @default(now())
  fileName   String?
  sheetName  String
  rowCount   Int
  type       String // "Income", "Expense", or "Both"
  importHash String

  @@unique([userId, sheetName, importHash])
}

enum CategoryType {
  CREDIT
  DEBIT
}

model Category {
  id              String           @id @default(cuid())
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  name            String
  icon            String
  color           String           @default("hsl(var(--chart-1))")
  keywords        String[]         @default([])
  type            CategoryType     @default(DEBIT)
  userId          String
  description     String?
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions    Transaction[]
  budgets         CategoryBudget[]
  incomes         Income[]
  expenses        Expense[]
  budgetRollovers BudgetRollover[]
  TimeDeposit     TimeDeposit[]

  @@unique([name, userId])
  @@index([id, userId])
  @@map(name: "categories")
}

model BudgetPreferences {
  id                   String  @id @default(cuid())
  userId               String  @unique
  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  autoCreateNextBudget Boolean @default(true)
  autoApplyRollovers   Boolean @default(true)
  rolloverThreshold    Decimal @default(0)
}

model Budget {
  id                String           @id @default(cuid())
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  name              String?
  startDate         DateTime?
  endDate           DateTime?
  amount            Decimal?
  userId            String
  user              User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  rolloversPrevious BudgetRollover[] @relation("PreviousBudget")
  rolloversNext     BudgetRollover[] @relation("NextBudget")
  categories        CategoryBudget[]
  accountId         String?
  account           BankAccount?     @relation(fields: [accountId], references: [id])

  @@index([id, userId, accountId])
  @@map(name: "budgets")
}

model CategoryBudget {
  id         String   @id @default(cuid())
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  amount     Decimal
  budgetId   String
  categoryId String
  budget     Budget   @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@unique([budgetId, categoryId])
  @@index([id, budgetId])
  @@map(name: "category_budgets")
}

model BudgetRollover {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  categoryId String
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  previousBudgetId String?
  previousBudget   Budget? @relation("PreviousBudget", fields: [previousBudgetId], references: [id], onDelete: SetNull)

  nextBudgetId String?
  nextBudget   Budget? @relation("NextBudget", fields: [nextBudgetId], references: [id], onDelete: SetNull)

  amount             Decimal // Rolled over amount
  createdAt          DateTime     @default(now())
  periodStart        DateTime
  periodEnd          DateTime
  rolloverPercentage Decimal?
  accountId          String?
  account            BankAccount? @relation(fields: [accountId], references: [id])

  @@index([id, userId, accountId, categoryId])
}

model ConnectorConfig {
  id          String       @id @default(cuid())
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  orgId       String
  secret      Json?
  env         ConnectorEnv
  connectorId String
  connector   Connector    @relation(fields: [connectorId], references: [id], onDelete: Cascade)
}

model Connector {
  id              String            @id @default(cuid())
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  name            String
  logoUrl         String?
  status          ConnectorStatus
  type            ConnectorType
  connectorConfig ConnectorConfig[]
  Integration     Integration[]
}

model Integration {
  id                  String     @id @default(cuid())
  createdAt           DateTime   @default(now())
  updatedAt           DateTime   @updatedAt
  name                String
  logoUrl             String?
  connectorProviderId String?    @unique
  connectorId         String
  connector           Connector  @relation(fields: [connectorId], references: [id], onDelete: Cascade)
  Resource            Resource[]
}

model Resource {
  id            String        @id @default(cuid())
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  integrationId String
  originalId    String
  userId        String
  integration   Integration   @relation(fields: [integrationId], references: [id], onDelete: Cascade)
  bankAccounts  BankAccount[]

  @@map(name: "resources")
}

model Stats {
  id                     String   @id @default(cuid())
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  totalUsers             Int      @default(0)
  activeUsers            Int      @default(0)
  totalAccounts          Int      @default(0)
  totalTransactions      Int      @default(0)
  totalAssets            Int      @default(0)
  totalLiabilities       Int      @default(0)
  totalInvestments       Int      @default(0)
  avgAccountsPerUser     Decimal  @default(0)
  avgTransactionsPerUser Decimal  @default(0)
  dailyActiveUsers       Int      @default(0)
  weeklyActiveUsers      Int      @default(0)
  monthlyActiveUsers     Int      @default(0)
  operatingSystem        Json? // Store counts for different OS
  browser                Json? // Store counts for different browsers
  country                Json? // Store counts for different countries
  lastUpdated            DateTime @default(now())

  @@map(name: "stats")
}

model StatsHistory {
  id                 String   @id @default(cuid())
  createdAt          DateTime @default(now())
  totalUsers         Int
  activeUsers        Int
  totalAccounts      Int
  totalTransactions  Int
  totalAssets        Int
  totalLiabilities   Int
  totalInvestments   Int
  dailyActiveUsers   Int
  weeklyActiveUsers  Int
  monthlyActiveUsers Int
  snapshot           Json // Store full stats snapshot

  @@map(name: "stats_history")
}

model Investment {
  id            String                  @id @default(cuid())
  createdAt     DateTime                @default(now())
  updatedAt     DateTime                @updatedAt
  name          String
  type          InvestmentType
  amount        Decimal
  shares        Decimal?
  purchasePrice Decimal?
  currentPrice  Decimal?
  description   String?
  userId        String
  user          User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions  InvestmentTransaction[]
  valuations    InvestmentValuation[]

  @@index([id, userId])
  @@map(name: "investments")
}

model InvestmentValuation {
  id           String     @id @default(cuid())
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  value        Decimal
  date         DateTime
  investmentId String
  investment   Investment @relation(fields: [investmentId], references: [id], onDelete: Cascade)

  @@index([id, investmentId])
  @@map(name: "investment_valuations")
}

model InvestmentTransaction {
  id           String                    @id @default(cuid())
  createdAt    DateTime                  @default(now())
  updatedAt    DateTime                  @updatedAt
  type         InvestmentTransactionType
  amount       Decimal
  shares       Decimal?
  price        Decimal
  date         DateTime
  description  String?
  investmentId String
  investment   Investment                @relation(fields: [investmentId], references: [id], onDelete: Cascade)

  @@index([id, investmentId])
  @@map(name: "investment_transactions")
}

model Liability {
  id             String             @id @default(cuid())
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  name           String
  type           LiabilityType
  amount         Decimal
  interestRate   Decimal
  monthlyPayment Decimal
  startDate      DateTime?
  endDate        DateTime?
  description    String?
  userId         String
  user           User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments       LiabilityPayment[]

  @@index([id, userId])
  @@map(name: "liabilities")
}

model LiabilityPayment {
  id          String      @id @default(cuid())
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  amount      Decimal
  date        DateTime
  type        PaymentType
  description String?
  liabilityId String
  liability   Liability   @relation(fields: [liabilityId], references: [id], onDelete: Cascade)

  @@map(name: "liability_payments")
}

model SavingsGoal {
  id          String            @id @default(cuid())
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  name        String
  target      Decimal
  current     Decimal           @default(0)
  deadline    DateTime?
  description String?
  isDefault   Boolean           @default(false) // To distinguish between system defaults and user goals
  type        GoalType          @default(CUSTOM)
  priority    Int               @default(0) // For ordering goals
  userId      String
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  progress    SavingsProgress[]

  @@index([id, userId])
  @@map(name: "savings_goals")
}

model SavingsProgress {
  id        String      @id @default(cuid())
  createdAt DateTime    @default(now())
  amount    Decimal
  date      DateTime
  goalId    String
  goal      SavingsGoal @relation(fields: [goalId], references: [id], onDelete: Cascade)

  @@index([id, goalId])
  @@map(name: "savings_progress")
}

model ContractAnalysis {
  id                          String                 @id @default(cuid())
  userId                      String
  contractText                String
  summary                     String
  recommendations             String[]
  keyClauses                  String[]
  legalCompliance             String
  negotiationPoints           String[]
  contractDuration            String
  terminationConditions       String
  overallScore                Float
  performanceMetrics          String[]
  intellectualPropertyClauses Json?
  createdAt                   DateTime               @default(now())
  version                     Int                    @default(1)
  userFeedback                Json?
  customFields                Json?
  expirationDate              DateTime?
  language                    String                 @default("en")
  filePath                    String?
  contractType                String
  financialTerms              Json?
  specificClauses             String
  compensationStructure       CompensationStructure?
  opportunities               Opportunity[]
  risks                       Risk[]
  chats                       Chat[]

  @@index([userId])
}

model Risk {
  id          String           @id @default(cuid())
  contractId  String
  risk        String
  explanation String
  severity    String
  contract    ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)
}

model Opportunity {
  id          String           @id @default(cuid())
  contractId  String
  opportunity String
  explanation String
  impact      String
  contract    ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)
}

model CompensationStructure {
  id            String           @id @default(cuid())
  contractId    String           @unique
  baseSalary    String
  bonuses       String
  equity        String
  otherBenefits String
  contract      ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)
}

model Speech {
  id             String          @id @default(cuid())
  userId         String
  workspaceId    String
  projectId      String
  title          String
  description    String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace      Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  project        Project         @relation(fields: [projectId], references: [id], onDelete: Cascade)
  recordings     Recording[]
  transcriptions Transcription[]
  analyses       Analysis[]
}

model Recording {
  id        String   @id @default(cuid())
  speechId  String
  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)
  url       String // URL to the stored audio file
  duration  Int // Duration in seconds
  createdAt DateTime @default(now())
}

model Transcription {
  id        String   @id @default(cuid())
  speechId  String
  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)
  text      String // Full transcribed text
  language  String // Language code (e.g., "en", "zh-TW")
  createdAt DateTime @default(now())
}

model Analysis {
  id        String   @id @default(cuid())
  speechId  String
  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)
  summary   String? // AI-generated summary
  keyPoints Json? // Key points or action items extracted by AI
  sentiment String? // Sentiment analysis (e.g., "Positive", "Neutral", "Negative")
  createdAt DateTime @default(now())
}

model VectorStore {
  id           String                      @id @default(uuid())
  documentId   String
  documentType String
  content      String
  embedding    Unsupported("vector(384)")?
  metadata     Json?
  filePath     String
  createdAt    DateTime                    @default(now())

  @@index([documentId])
  @@index([filePath])
  @@index([documentType])
}

model Assistant {
  assistant_id String   @id @default(cuid())
  graph_id     String
  config       Json
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  metadata     Json?
  version      Int?
  name         String
  description  String?
  runs         Run[]    @relation("AssistantRuns")
  chats        Chat[]   @relation("AssistantChats")

  @@unique([graph_id, name]) // Ensure uniqueness of graph_id + name
}

model Thread {
  thread_id         String             @id @default(cuid())
  created_at        DateTime           @default(now())
  updated_at        DateTime           @updatedAt
  metadata          Json?
  status            ThreadStatus
  values            Json
  checkpoints       Checkpoint[]
  checkpoint_blobs  CheckpointBlobs[]
  checkpoint_writes CheckpointWrites[]
  runs              Run[]              @relation("ThreadRuns")
}

model Run {
  run_id             String             @id @default(cuid())
  thread             Thread             @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade, name: "ThreadRuns")
  thread_id          String
  assistant          Assistant          @relation(fields: [assistant_id], references: [assistant_id], onDelete: Cascade, name: "AssistantRuns")
  assistant_id       String
  created_at         DateTime           @default(now())
  updated_at         DateTime           @updatedAt
  status             RunStatus
  metadata           Json?
  multitask_strategy MultitaskStrategy?
}

model Checkpoint {
  thread               Thread  @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)
  thread_id            String
  checkpoint_ns        String
  checkpoint_id        String
  parent_checkpoint_id String?
  type                 String?
  checkpoint           Json
  metadata             Json?   @default("{}")

  @@id([thread_id, checkpoint_ns, checkpoint_id])
}

model CheckpointBlobs {
  thread        Thread @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)
  thread_id     String
  checkpoint_ns String @default("")
  channel       String
  version       String
  type          String
  blob          Bytes?

  @@id([thread_id, checkpoint_ns, channel, version])
  @@map("checkpoint_blobs")
}

model CheckpointWrites {
  thread        Thread  @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)
  thread_id     String
  checkpoint_ns String  @default("")
  checkpoint_id String
  task_id       String
  idx           Int
  channel       String
  type          String?
  blob          Bytes

  @@id([thread_id, checkpoint_ns, checkpoint_id, task_id, idx])
  @@map("checkpoint_writes")
}

model CheckpointMigrations {
  v Int @id

  @@map("checkpoint_migrations")
}

model Chat {
  id          String     @id @default(cuid())
  assistantId String
  contractId  String?
  assistant   Assistant  @relation(fields: [assistantId], references: [assistant_id], onDelete: Cascade, name: "AssistantChats")
  createdAt   DateTime   @default(now())
  title       String
  userId      String
  visibility  Visibility @default(PRIVATE) // Equivalent to varchar('visibility', { enum: ['public', 'private'] }).notNull().default('private')

  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  contracts ContractAnalysis? @relation(fields: [contractId], references: [id], onDelete: Cascade)
  messages  Message[]

  @@map("Chat")
}

model Message {
  id        String   @id @default(cuid())
  chatId    String
  role      String
  content   Json
  createdAt DateTime @default(now())

  chat Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)

  @@map("Message")
}

model MemoryStore {
  id         String   @id @default(cuid())
  namespace  String[]
  key        String
  value      Json
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@unique([namespace, key])
}

model Banner {
  id          String   @id @default(cuid())
  userId      String?
  user        User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  title       String
  type        String?  @default("title")
  size        String?  @default("text-base")
  fontWeight  String?  @default("font-normal")
  color       String
  description String?
  shared      Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
}

enum Role {
  ADMIN
  MEMBER
}

enum AccountType {
  BANK
  SAVINGS
  INVESTMENT
  CRYPTO
  VIRTUAL
}

enum BalanceType {
  AVAILABLE
  BOOKED
  EXPECTED
}

enum TimeDepositType {
  AVAILABLE
  WITHDRAWN
}

enum ConnectorEnv {
  DEVELOPMENT
  SANDBOX
  PRODUCTION
}

enum ConnectorStatus {
  ACTIVE
  BETA
  DEV
  INACTIVE
}

enum ConnectorType {
  DIRECT
  AGGREGATED
}

enum AssetType {
  REAL_ESTATE
  VEHICLE
  PRECIOUS_METALS
  OTHER
}

enum TransactionType {
  PURCHASE
  SALE
  MAINTENANCE
  IMPROVEMENT
  DEPRECIATION
  APPRECIATION
}

enum InvestmentType {
  STOCKS
  CRYPTO
  ETF
  OTHER
}

enum InvestmentTransactionType {
  BUY
  SELL
  DIVIDEND
  SPLIT
  MERGE
}

enum LiabilityType {
  MORTGAGE
  CREDIT_CARD
  CAR_LOAN
  STUDENT_LOAN
}

enum PaymentType {
  REGULAR
  EXTRA
  INTEREST
  PRINCIPAL
}

enum GoalType {
  EMERGENCY_FUND
  RETIREMENT
  DOWN_PAYMENT
  CUSTOM
}

enum TaskStatus {
  BACKLOG
  TODO
  IN_PROGRESS
  IN_REVIEW
  DONE
}

enum RunStatus {
  pending
  running
  error
  success
  timeout
  interrupted
}

enum ThreadStatus {
  idle
  busy
  interrupted
}

enum MultitaskStrategy {
  reject
  interrupt
  rollback
  enqueue
}

enum Visibility {
  PUBLIC
  PRIVATE
}

// if we need full reset : npx prisma db push --force-reset

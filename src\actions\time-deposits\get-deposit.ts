"use server";

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";

export async function getDeposit(depositId: string) {
  const { userId } = await auth();
  if (!userId) {
    return { error: "Unauthorized", success: false };
  }

  try {
    const deposit = await prisma.timeDeposit.findUnique({
      where: { id: depositId },
      include: {
        bankAccount: true,
        asset: true,
        currency: true,
        category: true,
      },
    });

    if (!deposit) {
      return { error: "Deposit not found", success: false };
    }

    return { 
      success: true, 
      data: { 
        deposit: {
          id: deposit.id,
          amount: deposit.amount ?? 0,
          accountId: deposit.accountId ?? "",
          categoryId: deposit.categoryId ?? "",
          date: deposit.date ? new Date(deposit.date) : new Date(),
          type: deposit.type as "AVAILABLE" | "WITHDRAWN",
          certificateNo: deposit.certificateNo ?? "",
          period: deposit.period ?? undefined, 
          interestRate: Number(deposit.interestRate) ?? undefined,
          description: deposit.description ?? undefined,
        }
      } 
    };
  } catch (error) {
    console.error("Error fetching deposit:", error);
    return { error: "Failed to fetch deposit", success: false };
  }
}

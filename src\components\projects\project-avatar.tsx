import Image from "next/image";

import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

interface ProjectAvatarProps {
  image?: string;
  name: string;
  className?: string;
  fallbackClassName?: string;
}

export function ProjectAvatar({
  image,
  name,
  className,
  fallbackClassName
}: ProjectAvatarProps) { 
  if (image) {
    return (
      <div className={cn(
        "size-3.5 relative rounded-md overflow-hidden",
        className
      )}>
        <Image
          alt={name}
          fill
          className="object-cover"
          src={image}
        />
      </div>        
    )
  }

  return (
    <Avatar className={cn("size-3 rounded-md", className)}>
      <AvatarFallback className={cn("text-white bg-blue-600 font-semibold text-[8px]",
        fallbackClassName,
      )}>
        {name[0]}
      </AvatarFallback>
    </Avatar>
  )
}

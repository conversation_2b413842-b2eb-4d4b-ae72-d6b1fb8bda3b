"use client";

import Link from "next/link";
import React from "react";
import { Shield } from "lucide-react";
import { SignedIn } from "@clerk/nextjs";
import { But<PERSON> } from "@/components/ui/button";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

const Header = () => {
  const pathname = usePathname();
  const isHomePage = pathname === "/";
  return (
    <div className={cn("flex items-center justify-between px-4 py-2", isHomePage ? "bg-blue-50" : "bg-white border-b border-blue-50")}>
      <Link href="/" className="flex items-center">
        <Shield className="w-6 h-6 text-blue-600" />
        <h1 className="text-xl font-semibold">Expensio</h1>
      </Link>
      <div className="flex items-center space-x-4">
        <SignedIn>
          <Link href="/receipts">
            <Button variant="outline" className="hover:cursor-pointer">My receipts</Button>
          </Link>

          <Link href="/manage-plan">
            <Button className="hover:cursor-pointer">Manage plan</Button>
          </Link>
        </SignedIn>
      </div>
    </div>
  );
};

export default Header;
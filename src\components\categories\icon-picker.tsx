import { CATEGORY_ICONS } from "@/lib/config/categories";
import { cn } from "@/lib/utils";

export function IconPicker({ selectedIcon, onIconSelect }: { 
  selectedIcon: string; 
  onIconSelect: (icon: string) => void;
}) {
  return (
    <div className="grid grid-cols-4 gap-2">
      {Object.entries(CATEGORY_ICONS).map(([name, Icon]) => (
        <div
          key={name}
          className={cn(
            "cursor-pointer flex flex-col items-center justify-center p-4 border rounded",
            selectedIcon === name ? "border-primary" : "border-muted"
          )}
          onClick={() => onIconSelect(name)}
        >
          <Icon className="w-6 h-6 text-foreground" />
          <span className="text-xs whitespace-nowrap mt-1">{name}</span>
        </div>
      ))}
    </div>
  );
}

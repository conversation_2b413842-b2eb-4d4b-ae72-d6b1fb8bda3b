"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { getIconByName } from "@/lib/config/categories";
import {
  Settings,
  Loader2,
  Trash,
  MoreHorizontal,
} from "lucide-react";
import { updateCategoryBudget } from "@/actions/budgets/update-category-budget";
import { getCategories } from "@/actions/categories/manage-categories";
import { deleteCategory } from "@/actions/categories/delete-category";

const budgetSchema = z.object({
  monthlyLimit: z
    .string()
    .min(1, "Monthly limit is required")
    .refine((val) => !isNaN(Number(val)), "Must be a valid number")
    .transform((val) => Number(val)),
  categoryId: z.string().min(1, "Category is required"),
  description: z.string().optional(),
});

type BudgetFormValues = z.infer<typeof budgetSchema>;

interface Category {
  id: string;
  name: string;
  icon: string;
  description?: string;
}

export function EditBudgetComponent() {
  const [open, setOpen] = useState(false);
  const [step, setStep] = useState<1 | 2>(1);
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  
  useEffect(() => {
    async function loadCategories() {
      const response = await getCategories();
      if (response.success && response.data) {
        setCategories(response.data);
      }
    }
    loadCategories();
  }, []);

  const handleDelete = async (categoryId: string) => {
    try {
      setIsLoading(true);
      const result = await deleteCategory({ id: categoryId });
      if (result.success) {
        toast.success("Category deleted successfully");
        setOpen(false);
        // Refresh categories
        const response = await getCategories();
        if (response.success && response.data) {
          setCategories(response.data);
        }
      } else {
        throw new Error("Unknown error");
      }
    } catch (error) {
      toast.error("Failed to delete category");
    } finally {
      setIsLoading(false);
    }
  };

  const form = useForm<BudgetFormValues>({
    resolver: zodResolver(budgetSchema),
    defaultValues: {
      monthlyLimit: 0,
      description: "",
    },
  });

  async function onSubmit(data: BudgetFormValues) {
    try {
      setIsLoading(true);

      // Convert monthlyLimit to string for API
      const result = await updateCategoryBudget({
        ...data,
        monthlyLimit: data.monthlyLimit.toString(),
      });

      if (!result.success) {
        throw new Error("Failed to update budget");
      }

      toast.success("Budget updated successfully");
      setOpen(false);
      form.reset();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update budget");
    } finally {
      setIsLoading(false);
    }
  }

  function handleCategorySelect(categoryId: string) {
    form.setValue("categoryId", categoryId);
    setStep(2);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="w-4 h-4 mr-2" /> Edit Budgets
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] md:max-w-[700px]">
        {isLoading && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="flex flex-col items-center gap-2">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">
                Updating budget settings...
              </p>
            </div>
          </div>
        )}
        <DialogHeader>
          <DialogTitle>
            {step === 1 ? "Select Category" : "Budget Settings"}
          </DialogTitle>
        </DialogHeader>

        {step === 1 ? (
          <div className="grid grid-cols-3 sm:grid-cols-5 gap-3">
            {categories &&
              categories.map((category, index) => {
                const Icon = getIconByName(category.icon) || MoreHorizontal;
                return (
                  <Card
                    key={category.id}
                    className={`group relative cursor-pointer transition-all ${
                      form.watch("categoryId") === category.id
                        ? "border-primary shadow-md"
                        : "hover:border-primary hover:shadow-sm"
                    }`}
                    onClick={() => handleCategorySelect(category.id)}
                  >
                    <CardContent className="flex flex-col items-center text-center p-4">
                      <div
                        className="rounded-full p-3 mb-3"
                        style={{ backgroundColor: `hsl(var(--chart-${index % 11}))` }}
                      >
                        <Icon className="h-6 w-6 text-background" />
                      </div>
                      <h3 className="text-base font-semibold mb-1">{category.name}</h3>
                      <p className="text-xs text-muted-foreground">
                        {category.description || "Set budget limit"}
                      </p>
                    </CardContent>
                    {/* Delete Button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 right-2 opacity-0 group-hover:opacity-100"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent triggering category selection
                        handleDelete(category.id);
                      }}
                    >
                      <Trash className="h-4 w-4 text-destructive" />
                    </Button>
                  </Card>
                );
              })}
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormField
                control={form.control}
                name="monthlyLimit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Monthly Budget Limit</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter amount"
                        type="number"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Add a note about this budget"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setStep(1)}
                >
                  Back
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Budget"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}

import { useQuery } from "@tanstack/react-query";
import { getDeposit } from "@/actions/time-deposits/get-deposit";
import { TimeDepositFormValues } from "./update-deposit-form";

interface UseDepositProps {
  depositId: string;
}

export const useGetDeposit = ({ depositId }: UseDepositProps) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["deposit", depositId],
    queryFn: async () => {
      const response = await getDeposit(depositId);
      
      if (!response.success || !response.data) {
        throw new Error(response.error || "Failed to fetch deposit");
      }

      const deposit = response.data.deposit;

      return {
        ...deposit,
        date: new Date(deposit.date),
      } as TimeDepositFormValues;
    },
    enabled: !!depositId,
  });

  return {
    data,
    isLoading,
    error,
  };
};

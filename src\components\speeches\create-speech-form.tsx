"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useCreateSpeech } from "./use-create-speech";
import { createSpeechSchema } from "./schemas";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { ProjectAvatar } from "../projects/project-avatar";

interface SpeechFormProps {
  onCancel?: () => void;
  projectOption: {id: string, name: string, imageUrl: string | null}[]
}

type FormValues = z.infer<typeof createSpeechSchema>;

export const CreateSpeechForm = ({ onCancel, projectOption }: SpeechFormProps) => {
  const workspaceId = useWorkspaceId();
  const { mutate, isPending } = useCreateSpeech();

  const form = useForm<FormValues>({
    resolver: zodResolver(createSpeechSchema),
    defaultValues: {
      workspaceId,
      title: "",
      description: "",
      recordings: [],
      transcriptions: [],
      analyses: []
    },
  });

  const onSubmit = async (values: FormValues) => {
    mutate(values, {
      onSuccess: () => {
        form.reset();
        onCancel?.();
      },
    });
  };

  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7 py-4">
        <CardTitle className="text-xl font-bold">Create a new speech</CardTitle>
      </CardHeader>
      <div className="px-7">
        <Separator />
      </div>
      <CardContent className="p-7">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Speech Title</FormLabel>
                  <FormControl>
                    <Input className="h-8" placeholder="Enter speech title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea className="h-8" placeholder="Enter speech description" {...field} />
                </FormControl>
                <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="projectId"
              render={({ field }) => (
                <FormItem>
                <FormLabel>Project</FormLabel>
                  <Select
                    defaultValue={field.value}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder="Select project" />
                      </SelectTrigger>
                    </FormControl>
                    <FormMessage />
                    <SelectContent>
                      {projectOption.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          <div className="flex items-center gap-x-2">
                            <ProjectAvatar 
                              className="size-4" 
                              name={project?.name!}
                              image={project?.imageUrl!} 
                              />
                            {project.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            <Separator className="px-7" />
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" size="sm" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" size="sm" disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" /> Creating...
                  </>
                ) : (
                  "Create Speech"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

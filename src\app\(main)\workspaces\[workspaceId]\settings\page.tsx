import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { getWorkspace } from "@/actions/workspaces/get-workspace";
import { UpdateWorkspaceForm } from "@/components/workspaces/update-workspace-form";

interface WorkspaceIdSettingsPageProps {
  params: Promise<{
    workspaceId: string;
  }>;
}

const WorkspaceIdSettingsPage = async (props: WorkspaceIdSettingsPageProps) => {
  const params = await props.params;
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const response = await getWorkspace(params.workspaceId);
  if (!response.success || !response.data) redirect("/dashboard");
  const workspace = Array.isArray(response.data) ? response.data[0] : response.data


  return (
    <div className="w-full lg:max-w-xl mx-auto">
      <UpdateWorkspaceForm workspace={workspace}/>
    </div>
  )
}

export default WorkspaceIdSettingsPage
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';


export function useDeleteMeeting() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (eventId: string) => {
      const url = `/api/calendar/${eventId}/delete`;
      //const url = eventId ? `/api/calendar/`;
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      });

      const responseData = await response.json();
      console.log("responseData", responseData)

      if (!response.ok || !responseData.success) {
        throw new Error(`Failed to delete schedule.`);
      }
      
      return responseData;
    },
    onSuccess: ({ data }) => {
      toast.success('Meeting deleted!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['calendar-events'] });
      queryClient.invalidateQueries({ queryKey: ['calendar-events', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete meeting.');
    },
  });

  return mutation;
}

import { getCurrentUser } from "@/actions/user/get-current-user";
import { getAccountIds } from "@/actions/account/get-accountIds"
import { getSelectCategories } from "@/actions/categories/get-select-categories";
import { redirect } from "next/navigation";
import { EmptyPlaceholder } from "@/components/empty-placeholder";
import { Wallet } from "lucide-react";
import { Spreadsheet } from "./_components/client";
import "./_components/styles.css";

export default async function SpreadsheetPage() {
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const { success, accounts, error } = await getAccountIds();
  const categoryResponse = await getSelectCategories();

  if (!success || !accounts || accounts.length === 0) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Accounts</h2>
        </div>
        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon icon={Wallet} />
          <EmptyPlaceholder.Title>No accounts yet</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            Connect your accounts to start tracking your finances.
          </EmptyPlaceholder.Description>
        </EmptyPlaceholder>
      </div>
    );
  }

  return (
    <Spreadsheet bankAccounts={accounts} categories={categoryResponse?.categories!} />
  )
}

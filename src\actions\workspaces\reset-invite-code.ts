'use server';

import { prisma } from "@/lib/db";
import { Role } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { generateInviteCode } from "@/lib/utils";

export async function resetInviteCode(workspaceId: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false,
      };
    }

    // Check if user exists and has ADMIN role
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId },
      select: { role: true }
    });
    console.log("member", member)

    if (!member || member.role !== Role.ADMIN) {
      return {
        error: "Only administrators can delete workspaces.",
        success: false
      }
    }
    
    // Generate a new invite code
    const newInviteCode = generateInviteCode(6);

    // Update the invite code in the database
    const updatedWorkspace = await prisma.workspace.update({
      where: { id: workspaceId },
      data: { inviteCode: newInviteCode },
    });

    revalidatePath('/workspaces');
    return {
      data: updatedWorkspace,
      success: true,
    };
  } catch (error) {
    console.error("Error updating invite code:", error);
    return {
      error: "Failed to update invite code.",
      success: false,
    };
  }
}

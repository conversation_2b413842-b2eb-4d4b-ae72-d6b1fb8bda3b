import { authClient } from "@/lib/api";
import config from "@/store/config.json";

export interface FileResult {
  id: string;
  name: string;
  mimeType: string;
}

export const driveApi = {
  // Existing method to list folders
  listFolders: async (folderId: string) => {
    const teamDriveId = config.directory.team_drive;
    const corpora = teamDriveId ? "teamDrive" : "allDrives";

    const response = await authClient.get("https://www.googleapis.com/drive/v3/files", {
      params: {
        corpora,
        includeTeamDriveItems: true,
        supportsAllDrives: true,
        teamDriveId,
        q: `mimeType='application/vnd.google-apps.folder' and trashed = false and parents in '${folderId}'`,
      },
    });

    return response.data.files as FileResult[];
  },

  // New method to fetch a folder's name
  getFolderName: async (folderId: string): Promise<string> => {
    const response = await authClient.get(`https://www.googleapis.com/drive/v3/files/${folderId}`, {
      params: {
        fields: "name",
        supportsAllDrives: true,
      },
    });

    return response.data.name;
  },
  getParentFolder: async (folderId: string): Promise<string | undefined> => {
    try {
      const response = await authClient.get(
        `https://www.googleapis.com/drive/v3/files/${folderId}`,
        {
          params: {
            fields: "parents",
            supportsAllDrives: true,
          },
        }
      );
      // Returns the parent folder ID (first item in the parents array)
      return response.data.parents?.[0];
    } catch (error) {
      console.error("Failed to fetch parent folder:", error);
      return undefined;
    }
  },
  searchFiles: async (query: string, folderIds: string[]) => {
    const teamDriveId = config.directory.team_drive;
    const corpora = teamDriveId ? "teamDrive" : "allDrives";

    const response = await authClient.get("https://www.googleapis.com/drive/v3/files", {
      params: {
        corpora,
        includeTeamDriveItems: true,
        supportsAllDrives: true,
        teamDriveId,
        q: `mimeType!='application/vnd.google-apps.folder' and trashed = false and parents in '${folderIds.join(
          "','"
        )}' and (name contains '${query}' or fullText contains '${query}')`,
      },
    });

    return response.data.files as FileResult[];
  },
  resolveShortcut: async (fileId: string): Promise<string> => {
    const response = await authClient.get(
      `https://www.googleapis.com/drive/v3/files/${fileId}`,
      {
        params: {
          supportsAllDrives: true,
          fields: "shortcutDetails/targetId",
        },
      }
    );

    // Return the target ID of the shortcut
    return response.data.shortcutDetails?.targetId;
  },
};

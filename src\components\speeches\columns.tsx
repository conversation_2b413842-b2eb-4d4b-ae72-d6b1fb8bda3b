"use client";

import Link from "next/link";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreVertical } from "lucide-react";
import { SpeechWithRelations } from "./types";
import { Button } from "@/components/ui/button";
import { ProjectAvatar } from "../projects/project-avatar";
import { SpeechDate } from "./speech-date";
import { SpeechAction } from "./speech-actions";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";


export const columns: ColumnDef<SpeechWithRelations>[] = [
  // Name column
  {
    accessorKey: "title",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => { 
      const title = row.original.title 
      const id = row.original.id;
      const workspaceId = useWorkspaceId();
      return (
        <Link
          href={`/workspaces/${workspaceId}/speeches/${id}`}
          className="text-primary hover:underline line-clamp-1"
        >
          {title}
        </Link>
      );
    },
  },
  // Project column
  {
    accessorKey: "project",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Project
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => { 
      const project = row.original.project 
      return (
        <div className="flex items-center gap-x-1 text-sm">
          <ProjectAvatar
            className="size-4"
            name={project?.name!}
            image={project?.imageUrl!}
          />
          <p className="line-clamp-1">{project?.name}</p>
        </div>
      )
    },
  },
  // Start Date column
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Created Date
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
        const createdAt = row.original.createdAt;
        return <SpeechDate value={createdAt ? new Date(createdAt) : null} />
      }
  },
  // Updated Date column
  {
    accessorKey: "updatedAt",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Updated Date
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const updatedAt = row.original.updatedAt;
      return <SpeechDate value={updatedAt ? new Date(updatedAt) : null} />
    }
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const id = row.original.id;
      const projectId = row.original.projectId;
      return <SpeechAction id={id} projectId={projectId}>
        <Button variant={"ghost"} size="xs">
          <MoreVertical className="size-4" />
        </Button>
      </SpeechAction>
    }
  }
];

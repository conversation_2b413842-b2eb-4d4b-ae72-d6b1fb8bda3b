import { google } from "googleapis";
import { NextResponse } from "next/server";
import { authenticateGoogleOAuth2 } from "../authenticate";

export async function POST(request: Request) {
  try {
    const { fileId, email, role = 'reader' } = await request.json();

    if (!fileId || !email) {
      return NextResponse.json(
        { error: "File ID and email are required" },
        { status: 400 }
      );
    }

    const auth = await authenticateGoogleOAuth2();
    if (!auth) {
      throw new Error('Authentication failed');
    }

    const drive = google.drive({ version: "v3", auth });

    const permission = {
      type: 'user',
      role: role,
      emailAddress: email,
    };

    const file = await drive.permissions.create({
      fileId: fileId,
      requestBody: permission,
      fields: 'id, share, permission',
      // Enable email notifications to the shared user
      sendNotificationEmail: true,
      // Optionally include a custom message
      // emailMessage: 'Custom message to the recipient',
    });
    console.log("file share", file)

    return NextResponse.json({ 
      file: file.data,
      success: true,
      message: `Successfully shared with ${email}`
    });

  } catch (error: any) {
    console.error("Error sharing file:", error);
    
    const errorMessage = error.response?.data?.error?.message || error.message || "Failed to share file";
    
    return NextResponse.json(
      { error: errorMessage },
      { status: error.response?.status || 500 }
    );
  }
}
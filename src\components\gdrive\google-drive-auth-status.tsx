'use client';

import { useState, useEffect } from 'react';
import { generateOAuthState, getGoogleOAuthURL } from './oauth';
import { Button } from '@/components/ui/button';
import { LiaGoogleDrive } from "react-icons/lia";

interface GoogleDriveAuthStatusProps {
  onCancel?: () => void;
}
export function GoogleDriveAuthStatus({ onCancel }: GoogleDriveAuthStatusProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // Call your API endpoint to check if access token exists and is valid
      const response = await fetch('/api/gdrive/auth-status');
      const data = await response.json();
      setIsAuthenticated(data.isAuthenticated);
    } catch (error) {
      console.error('Failed to check auth status:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnect = async () => {
    setIsAuthenticating(true);
    try {
      const state = generateOAuthState();
      
      // Set the state via API endpoint
      const response = await fetch('/api/auth/set-state', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ state }),
      });
  
      if (!response.ok) {
        throw new Error('Failed to set state');
      }
  
      // After successful state setting, redirect to Google OAuth
      window.location.href = getGoogleOAuthURL(state);
    } catch (error) {
      console.error('Authentication error:', error);
      setIsAuthenticating(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      const response = await fetch('/api/gdrive/disconnect', {
        method: 'POST',
      });
      if (response.ok) {
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Failed to disconnect:', error);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {isAuthenticated ? (
        <Button
          onClick={handleDisconnect}
          variant={"ghost"}
          size={"sm"}
          className="h-8 text-red-500 gap-x-2 p-0"
        >
          <LiaGoogleDrive />
          Disconnect Google Drive
        </Button>
      ) : (
        <Button
          onClick={handleConnect}
          disabled={isAuthenticating}
          variant={"ghost"}
          size={"sm"}
          className="h-8 gap-x-2 p-0"
        >
          <LiaGoogleDrive />
          {isAuthenticating ? 'Connecting...' : 'Connect Google Drive'}
        </Button>
      )}
    </div>
  );
}
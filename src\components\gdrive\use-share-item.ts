"use client"
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useShareItem() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({ values }: {
      values: { 
        action: string; 
        fileId: string; 
        permissionId?: string; 
        email?: string;
        role?: string;
        type?: string;
      }
    }) => {
      console.log("values", values)
      const response = await fetch(`/api/gdrive/share`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });
      const responseData = await response.json();
      console.log("share responseData", responseData)

      if (!response.ok) {
        throw new Error(`Failed to share file.`);
      }
      
      return responseData.data;
    },
    onSuccess: ({ data }) => {
      queryClient.invalidateQueries({ queryKey: ['googledrive'] });
      queryClient.invalidateQueries({ queryKey: ['googledrive', data?.id] });
    },
    onError: (error: any) => {
      toast.error(`Failed to share file: ${error.message}`);
    },
  });

  return mutation;
}

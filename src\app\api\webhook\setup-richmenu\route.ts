import { NextRequest, NextResponse } from 'next/server';
import { setupRichMenu, deleteRichMenu } from '../richMenu';
import { RichMenuType } from '../richMenu';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function POST(req: NextRequest) {
  if (!process.env.CHANNEL_ACCESS_TOKEN || !process.env.CHANNEL_SECRET) {
    return NextResponse.json({ error: 'LINE Bot 憑證未設定' }, { status: 500 });
  }

  try {
    const { menuType = 'message' } = (await req.json()) as {
      menuType?: RichMenuType;
    };
    //const richMenuId = await setupRichMenu(menuType);
    const deleteRichMenuId = 'richmenu-376b3b532cf217021786c644cf4eb43c'
    console.log("deletedRichMenuId", deleteRichMenuId)
    const richMenuId = await deleteRichMenu(deleteRichMenuId);
     console.log("richMenuId", richMenuId)
    return NextResponse.json({
      success: true,
      richMenuId,
      message: `成功建立 ${menuType} Rich Menu`,
    });
  } catch (error) {
    console.error('設定 Rich Menu 失敗:', error);
    return NextResponse.json(
      {
        success: false,
        error: '設定 Rich Menu 失敗',
        details: error instanceof Error ? error.message : String(error),
      },
      {
        status: 500,
      }
    );
  }
}

// For testing the endpoint
export async function GET() {
  return NextResponse.json(
    {
      message: 'Please use POST method to setup Rich Menu',
      example: {
        method: 'POST',
        body: { menuType: 'message' },
      },
    },
    {
      status: 400,
    }
  );
}

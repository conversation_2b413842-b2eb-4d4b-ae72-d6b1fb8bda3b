'use client'

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { deleteWorkspace } from "@/actions/workspaces/delete-workspace"
import { But<PERSON> } from "@/components/ui/button"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

import { Loader2 } from "lucide-react"
import { toast } from "sonner"

interface DeleteWorkspaceButtonProps {
  workspaceId: string
  workspaceName: string
  isPending: boolean
  startTransition: (callback: () => void) => void;
}

export function DeleteWorkspaceButton({ workspaceId, workspaceName, isPending, startTransition }: DeleteWorkspaceButtonProps) {
  const router = useRouter()

  const onDelete = async () => {
    startTransition(async () => {
      try {
        const result = await deleteWorkspace({ workspaceId })

        if (result.success) {
          toast.success("Workspace deleted successfully")
          router.push('/workspaces')
        } else {
          toast.error(result.error || "Something went wrong")
        }
      } catch (error) {
        toast.error("Failed to delete workspace")
      }
    })
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" disabled={isPending}>
          {isPending ? (
            <><Loader2 className="h-4 w-4 animate-spin" />Delete Workspace</>
          ) : (
            "Delete Workspace"
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the workspace
            "{workspaceName}" and all associated data including bank accounts.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onDelete}>
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
import { getIconByName } from "@/lib/config/categories"

interface CategoryListProps {
  data: {
    name: string;
    icon: string;
    amount: number;
    budget: number;
    color: string;
    percentage: number;
    progress: number;
  }[];
}

export function CategoryList({ data }: CategoryListProps) {
  return (
    <div className="space-y-4">
      {data.map((category) => {
        // Get icon from our centralized icon mapping
        const Icon = getIconByName(category.icon);
        //const Icon = data.icon //CATEGORY_ICONS[category.name] || MoreHorizontal;

        return (
          <div key={category.name} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div
                  className="rounded-full p-2"
                  style={{ backgroundColor: category.color }}
                >
                  <Icon className="h-4 w-4 text-background" />
                </div>
                <span className="text-sm font-medium">{category.name}</span>
              </div>
              <div className="text-sm text-muted-foreground">
                ${Math.abs(category.amount).toLocaleString()} / $
                {category.budget.toLocaleString()}
              </div>
            </div>
            <div className="h-2 rounded-full bg-muted">
              <div
                className="h-full rounded-full transition-all"
                style={{
                  width: `${Math.min(Math.abs(category.progress), 100)}%`,
                  backgroundColor: category.color,
                }}
              />
            </div>
            <div className="text-right text-xs text-muted-foreground">
              {Math.abs(category.progress).toFixed(1)}% of budget used
            </div>
          </div>
        );
      })}
    </div>
  );
}

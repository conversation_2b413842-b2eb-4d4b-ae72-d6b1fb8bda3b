import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { cookies } from 'next/headers';

export async function POST() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized', success: false }, { status: 401 });
    }

    const cookieStore = await cookies();
    
    // Remove all Google Drive related cookies
    cookieStore.delete('access_token');
    cookieStore.delete('refresh_token');
    cookieStore.delete('session_id');

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error disconnecting:', error);
    return NextResponse.json(
      { error: 'Failed to disconnect' },
      { status: 500 }
    );
  }
}
"use client"


import { useGetProjectAnalytics } from "@/components/projects/use-get-project-analytics";
import { Analytics } from "@/components/analytics";
import { Loader } from "lucide-react";

interface ProjectIdClientProps {
  projectId: string
}

export const ProjectIdClient = ({
    projectId,
}: ProjectIdClientProps) => { 
  const { data, isLoading } =  useGetProjectAnalytics({
    projectId,
  })
  
  if (isLoading) return (
    <div className="w-full flex items-center justify-center"><Loader className="h-4 w-4 animate-spin" /></div>
  )

  if (!data) return null;
  return (
    <div className="h-full flex flex-col space-y-4">
      <Analytics  data={data} />
    </div>
  )
}
'use server';

import { Role } from "@prisma/client";
import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { updateProjectSchema } from "@/components/projects/schemas";

export async function updateProject(projectId: string, values: z.infer<typeof updateProjectSchema>) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    // Check if project exists
    const existingProject = await prisma.project.findUnique({
      where: { id: projectId },
    });

    const member = await prisma.member.findFirst({
      where: { userId, workspaceId: existingProject?.workspaceId! },
    });
  
    if (!member) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }
    
    // Validate input data
    const validatedFields = updateProjectSchema.safeParse(values);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        success: false
      }
    }

    const { name, image } = validatedFields.data;

    // Update project
    const project = await prisma.project.update({
      where: { id: projectId },
      data: {
        name,
        //imageUrl: image,
        updatedAt: new Date(),
      }
    });

    revalidatePath('/workspaces');
    
    return {
      data: project,
      success: true
    }

  } catch (error) {
    console.error('Error updating project:', error);
    return {
      error: "Failed to update project.",
      success: false
    }
  }
}

import axios, { AxiosError } from 'axios';
import fs from 'fs';

// Rich Menu 類型定義
export type RichMenuType = 'message' | 'flex' | 'carousel';

// Rich Menu 設定
const messageMenuData = {
  size: {
    width: 2500,
    height: 843
  },
  selected: false,
  name: "訊息選單",
  chatBarText: "選單",
  areas: [
    {
      bounds: {
        x: 11,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        data: "action=change_menu&action_type=message",
        label: "文字訊息"
      }
    },
    {
      bounds: {
        x: 860,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        label: "彈性訊息",
        data: "action=change_menu&action_type=flex"
      }
    },
    {
      bounds: {
        x: 1686,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        data: "action=change_menu&action_type=carousel",
        label: "輪播訊息"
      }
    },
    {
      bounds: {
        x: 11,
        y: 390,
        width: 2453,
        height: 563
      },
      action: {
        type: "postback",
        data: "action=send_message&type=message",
        label: "傳送文字訊息"
      }
    }
  ]
};

const flexMenuData = {
  size: {
    width: 2500,
    height: 843
  },
  selected: false,
  name: "彈性訊息選單",
  chatBarText: "選單",
  areas: [
    {
      bounds: {
        x: 11,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        data: "action=change_menu&action_type=message",
        label: "文字訊息"
      }
    },
    {
      bounds: {
        x: 860,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        label: "彈性訊息",
        data: "action=change_menu&action_type=flex"
      }
    },
    {
      bounds: {
        x: 1686,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        data: "action=change_menu&action_type=carousel",
        label: "輪播訊息"
      }
    },
    {
      bounds: {
        x: 11,
        y: 390,
        width: 2453,
        height: 563
      },
      action: {
        type: "postback",
        data: "action=send_message&type=flex",
        label: "傳送彈性訊息"
      }
    }
  ]
};

const carouselMenuData = {
  size: {
    width: 2500,
    height: 843
  },
  selected: false,
  name: "輪播訊息選單",
  chatBarText: "選單",
  areas: [
    {
      bounds: {
        x: 11,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        data: "action=change_menu&action_type=message",
        label: "文字訊息"
      }
    },
    {
      bounds: {
        x: 860,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        label: "彈性訊息",
        data: "action=change_menu&action_type=flex"
      }
    },
    {
      bounds: {
        x: 1686,
        y: 17,
        width: 820,
        height: 280
      },
      action: {
        type: "postback",
        data: "action=change_menu&action_type=carousel",
        label: "輪播訊息"
      }
    },
    {
      bounds: {
        x: 11,
        y: 390,
        width: 2453,
        height: 563
      },
      action: {
        type: "postback",
        data: "action=send_message&type=carousel",
        label: "傳送輪播訊息"
      }
    }
  ]
};

// 建立 Rich Menu
export const createRichMenu = async (menuType: RichMenuType = 'message') => {
  const menuData = {
    message: messageMenuData,
    flex: flexMenuData,
    carousel: carouselMenuData
  }[menuType];

  try {
    const response = await axios.post(
      'https://api.line.me/v2/bot/richmenu',
      menuData,
      {
        headers: {
          'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log(`${menuType} Rich Menu 建立成功:`, response.data);
    return response.data.richMenuId;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error(`建立 ${menuType} Rich Menu 失敗:`, axiosError.response?.data);
    throw error;
  }
};

// 上傳 Rich Menu 圖片
export const uploadRichMenuImage = async (richMenuId: string, imagePath: string) => {
  try {
    const imageBuffer = fs.readFileSync(imagePath);
    
    const response = await axios.post(
      `https://api-data.line.me/v2/bot/richmenu/${richMenuId}/content`,
      imageBuffer,
      {
        headers: {
          'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`,
          'Content-Type': 'image/png'
        }
      }
    );

    console.log('Rich Menu 圖片上傳成功');
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error('上傳 Rich Menu 圖片失敗:', axiosError.response?.data);
    throw error;
  }
};

// 設定為預設 Rich Menu（對所有用戶顯示）
export const setDefaultRichMenu = async (richMenuId: string) => {
  try {
    const response = await axios.post(
      `https://api.line.me/v2/bot/user/all/richmenu/${richMenuId}`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`
        }
      }
    );

    console.log('設定預設 Rich Menu 成功');
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error('設定預設 Rich Menu 失敗:', axiosError.response?.data);
    throw error;
  }
};

// 針對特定用戶設定 Rich Menu
export const setUserRichMenu = async (userId: string, richMenuId: string) => {
  try {
    const response = await axios.post(
      `https://api.line.me/v2/bot/user/${userId}/richmenu/${richMenuId}`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`
        }
      }
    );

    console.log('設定用戶 Rich Menu 成功');
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error('設定用戶 Rich Menu 失敗:', axiosError.response?.data);
    throw error;
  }
};

// 刪除 Rich Menu
export const deleteRichMenu = async (richMenuId: string) => {
  try {
    const response = await axios.delete(
      `https://api.line.me/v2/bot/richmenu/${richMenuId}`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`
        }
      }
    );

    console.log('刪除 Rich Menu 成功');
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error('刪除 Rich Menu 失敗:', axiosError.response?.data);
    throw error;
  }
};

// 取得所有 Rich Menu 列表
export const getRichMenuList = async () => {
  try {
    const response = await axios.get(
      'https://api.line.me/v2/bot/richmenu/list',
      {
        headers: {
          'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`
        }
      }
    );

    console.log('取得 Rich Menu 列表成功');
    return response.data.richmenus;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error('取得 Rich Menu 列表失敗:', axiosError.response?.data);
    throw error;
  }
};

export const setupRichMenu = async (menuType: RichMenuType = 'message') => {
  try {
    // 1. 建立 Rich Menu
    const richMenuId = await createRichMenu(menuType);
    
    // 2. 生成並上傳圖片
    const { generateRichMenuImage } = await import('./server-image-generator');
    const imagePath = await generateRichMenuImage(menuType);
    await uploadRichMenuImage(richMenuId, imagePath);
    
    // 3. 設定為預設 Rich Menu
    await setDefaultRichMenu(richMenuId);
    
    console.log('Rich Menu 設定完成！');
    return richMenuId;
  } catch (error) {
    console.error('Rich Menu 設定失敗:', error);
    throw error;
  }
};
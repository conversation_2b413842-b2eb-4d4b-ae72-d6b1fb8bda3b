import { Action } from "@copilotkit/shared";
import { researchWithLangGraph } from "../research";
import { NextRequest } from "next/server";
import {
  CopilotRuntime,
  copilotRuntimeNextJSAppRouterEndpoint,
  LangChainAdapter,
} from "@copilotkit/runtime";
import { ChatGroq } from "@langchain/groq";
import { BaseLanguageModelInput } from "@langchain/core/language_models/base";

const UNSPLASH_ACCESS_KEY_ENV = "UNSPLASH_ACCESS_KEY";
const UNSPLASH_ACCESS_KEY = process.env[UNSPLASH_ACCESS_KEY_ENV];

//export const runtime = "edge";

const researchAction: Action<any> = {
  name: "research",
  description:
    "Call this function to conduct research on a certain topic. Respect other notes about when to call this function",
  parameters: [
    {
      name: "topic",
      type: "string",
      description: "The topic to research. 5 characters or longer.",
    },
  ],
  handler: async ({ topic }) => {
    console.log("Researching topic: ", topic);
    return await researchWithLangGraph(topic);
  },
};

const actions: Action<any>[] = [];
if (process.env["TAVILY_API_KEY"] && process.env["TAVILY_API_KEY"] !== "NONE") {
  actions.push(researchAction);
}

const model = new ChatGroq({
  model: "llama-3.3-70b-versatile",
  temperature: 0,
  //apiKey: process.env.GROQ_API_KEY,
});

export const POST = async (req: NextRequest) => {
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime: new CopilotRuntime({
      actions: actions,
    }),
    serviceAdapter: new LangChainAdapter({
      chainFn: async ({ messages, tools }) => {
        return model.bindTools(tools).stream(messages as BaseLanguageModelInput);
      },
    }),
    endpoint: req.nextUrl.pathname,
  });
  return handleRequest(req);
};
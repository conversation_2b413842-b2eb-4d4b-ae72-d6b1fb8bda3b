import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import * as XLSX from "xlsx";
import { generateId } from "ai"

interface Spreadsheet {
  id: string;
  title: string;
  rows: Array<Array<{ value: string }>>;
}

interface UseFileDropzoneOptions {
  onDataExtracted: (data: Spreadsheet[]) => void;
  accept?: Record<string, string[]>;
  maxFiles?: number;
  multiple?: boolean;
}

export const UseFileDropzone = ({
  onDataExtracted,
  accept = {
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
    "application/vnd.ms-excel": [".xls"],
  },
  maxFiles = 1,
  multiple = false,
}: UseFileDropzoneOptions) => {
  const [error, setError] = useState<string | null>(null);

  const handleFileUpload = useCallback(
    (file: File) => {
      try {
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target && e.target.result) {
            const data = new Uint8Array(e.target.result as ArrayBuffer);
            const workbook = XLSX.read(data, { type: "array" });
  
            // Get the first sheet
            const sheetName = workbook.SheetNames[0];
            const sheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
  
            // Transform the data
            const rows = (jsonData as Array<Array<string>>)
              .filter((row) => row.some((cell) => cell)) // Remove empty rows
              .map((row) => row.map((cell) => ({ value: cell || "" })));
  
            // Ensure all rows have the same number of columns
            const maxColumns = Math.max(...rows.map((row) => row.length));
            rows.forEach((row) => {
              while (row.length < maxColumns) {
                row.push({ value: "" });
              }
            });
  
            // Call the callback with the extracted data
            const spreadsheetData: Spreadsheet = {
              id: generateId(),
              title: sheetName || "Untitled Spreadsheet",
              rows,
            };
            onDataExtracted([spreadsheetData]);
            setError(null);
          }
        };
        reader.readAsArrayBuffer(file);
      } catch (err) {
        console.error("Error reading file:", err);
        setError("Failed to read the file. Please try again.");
      }
    },
    [onDataExtracted]
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        setError(null); // Reset any previous errors
        handleFileUpload(acceptedFiles[0]); // Process the first file
      } else {
        setError("No file selected or unsupported file type.");
      }
    },
    [handleFileUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    maxFiles,
    multiple,
  });

  return {
    getRootProps,
    getInputProps,
    isDragActive,
    error,
  };
};


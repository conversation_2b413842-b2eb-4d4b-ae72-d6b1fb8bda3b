import { useMutation, useQueryClient } from '@tanstack/react-query';
import { saveSpeechResult } from '@/actions/speeches/save-speech-result';
import { toast } from 'sonner';

export function useSaveSpeechResult() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      speechId,
      text,
      language,
    }: {
      speechId: string;
      text: string;
      language: string;
    }) => {
      const response = await saveSpeechResult({ speechId, text, language });

      if (!response.success) {
        throw new Error(response.error);
      }

      return { data: response.data };;
    },
    onSuccess: ({ data }) => {
      toast.success('Transcription saved!');
      queryClient.invalidateQueries({ queryKey: ['transcriptions', data?.speechId] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to save transcription.');
    },
  });
}
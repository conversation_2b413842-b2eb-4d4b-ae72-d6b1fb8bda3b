/*import { getCurrentUser } from "@/actions/user/get-current-user";
import { getWorkspaces } from "@/actions/workspaces/get-workspaces";
import { WorkspaceProvider } from '@/components/workspaces/workspace-provider';
import { WorkspaceSwitcher } from "@/components/workspaces/workspace-switcher";

export default async function WorkspacesLayout({
  children
}: {
  children: React.ReactNode
}) {
  const user = await getCurrentUser();
  
  if (!user) {
    return null;
  }

  const response = await getWorkspaces();
  if (!response.success) {
    return null;
  }

  const workspaces = response.data || [];

  return (
    <WorkspaceProvider initialWorkspaces={workspaces} >
      <WorkspaceSwitcher initialWorkspaces={workspaces} />
      {children}
    </WorkspaceProvider>
  );
}*/
"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { Balance } from "@prisma/client";

export interface AccountDetails {
  account: {
    id: string;
    name: string;
    type: string;
    balance: number;
    accountNumber?: string;
    lastUpdated: Date;
  };
  transactions: Array<{
    description: string;
    incomeAmount: number;
    expenseAmount: number;
    count: number;
    type: string; // CREDIT or DEBIT
    id: string;
    date: Date;
  }>;
  balances?: Balance[];
}

export async function getVirtualAccountDetails(
  accountId: string,
  startDate: Date,
  endDate: Date
): Promise<{ success: boolean; data?: AccountDetails; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }
    /*const bankAccountId =
      accountId === "cm8r4wi73000dcg7g1t3uhw0c"
        ? "cm8r4rx1o0001cg7gu9y160k0"
        : accountId === "cm8r4xj7w000fcg7g8wsqv1db"
          ? "cm8r4t3fw0004cg7gl7u6nn4d"
          : accountId;
    const oneMonthBefore = new Date(startDate);
    oneMonthBefore.setDate(1); // Set to first day of month
    oneMonthBefore.setMonth(oneMonthBefore.getMonth() - 1);
    oneMonthBefore.setHours(oneMonthBefore.getHours() + 8);
    //console.log("oneMonthBefore", oneMonthBefore);*/

    //const [account, balances] = await Promise.all([
    const [account] = await Promise.all([
      prisma.bankAccount.findFirst({
        where: { id: accountId, userId },
        include: {
          Income: {
            where: { date: { gte: startDate, lte: endDate } },
            orderBy: { date: "desc" },
          },
          Expense: {
            where: { date: { gte: startDate, lte: endDate } },
            orderBy: { date: "desc" },
          },
          Balance: {
            where: { date: { gte: startDate, lte: endDate } },
            orderBy: { date: "desc" },
          },
        },
      }),
      /*prisma.bankAccount.findMany({
        where: { id: bankAccountId, userId },
        include: {
          Balance: {
            where: {
              date: {
                gte: new Date(oneMonthBefore),
                lte: new Date(endDate),
              },
            },
            orderBy: { date: "desc" },
          },
        },
      }),*/
    ]);

    if (!account) {
      throw new Error("Account not found");
    }

    const incomeEntries = account.Income.map((income) => ({
      description: income.description || "Unknown",
      incomeAmount: Number(income.amount) || 0,
      expenseAmount: 0,
      count: 1,
      type: "CREDIT",
      id: income.id,
      date: income.date,
    }));

    const expenseEntries = account.Expense.map((expense) => ({
      description: expense.description || "Unknown",
      incomeAmount: 0,
      expenseAmount: Number(expense.amount) || 0,
      count: 1,
      type: "DEBIT",
      id: expense.id,
      date: expense.date,
    }));

    const combinedTransactions = [...incomeEntries, ...expenseEntries].sort(
      (a, b) =>
        a.type.localeCompare(b.type) || b.date.getTime() - a.date.getTime()
    );

    return {
      success: true,
      data: {
        account: {
          id: account.id,
          name: account.name,
          type: account.accountType,
          balance: 0, // Calculate or add logic to fetch balance if required
          accountNumber: account.originalId || undefined,
          lastUpdated: account.updatedAt,
        },
        transactions: combinedTransactions,
        //balances: balances[0]?.Balance || [],
      },
    };
  } catch (error) {
    console.error("Error in getVirtualAccountDetails:", error);
    return {
      success: false,
      error: "Failed to fetch account details",
    };
  }
}

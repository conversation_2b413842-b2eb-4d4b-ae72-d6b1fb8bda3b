import { Workspace } from "@prisma/client";
import { useQuery } from "@tanstack/react-query";
import { getWorkspace } from "@/actions/workspaces/get-workspace";


export const useGetWorkspace = (workspaceId: string) => {
    const { data, isLoading, error } = useQuery({
    queryKey: ["workspace"],
    queryFn: async () => {
      const response = await getWorkspace(workspaceId);
      if (!response.success || !response.data) throw new Error("Failed to fetch workspaces");
      return response?.data as Workspace;
    },
    enabled: !!workspaceId,
  })

  return { data, isLoading, error }
}
'use client'

import React, { useRef } from "react";
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { MemoizedReactMarkdown } from '@/components/markdown'
import { Prism as Syntax<PERSON><PERSON>lighter } from 'react-syntax-highlighter'
import { prism } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { cn } from '@/lib/utils'

import 'katex/dist/katex.min.css'

type MessageProps = {
  content: string;
}

export function MarkdownMessage({ content }: MessageProps): JSX.Element {
  const codeBlockRef = useRef<SyntaxHighlighter>(null);
  //console.log('Original content:', content); // Logging

  const containsLaTeX = /\\\[([\s\S]*?)\\\]|\\\(([\s\S]*?)\\\)/.test(content || '');
  const processedContent = containsLaTeX ? preprocessLaTeX(content || '') : content;

  return (
    <>
      <MemoizedReactMarkdown
        className="prose max-w-[100ch] break-words text-secondary-text/80 dark:prose-invert prose-headings:text-secondary-text prose-strong:text-secondary-text prose-p:leading-relaxed prose-pre:py-0 prose-pre:px-[0.4rem] prose-pre:bg-white/10 prose-pre:text-pink-600 prose-code:p-2 prose-ol:whitespace-normal prose-ul:whitespace-normal prose-a:text-indigo-600 hover:prose-a:text-sky-500"
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex, rehypeRaw]}
        components={{
          h1: ({ node: _node, className, ...props }) => (
            <h1
              className={cn(
                "mb-8 scroll-m-20 text-4xl font-extrabold tracking-tight last:mb-0",
                className,
              )}
              {...props}
            />
          ),
          h2: ({ node: _node, className, ...props }) => (
            <h2
              className={cn(
                "mb-4 mt-8 scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0 last:mb-0",
                className,
              )}
              {...props}
            />
          ),
          h3: ({ node: _node, className, ...props }) => (
            <h3
              className={cn(
                "mb-4 mt-6 scroll-m-20 text-2xl font-semibold tracking-tight first:mt-0 last:mb-0",
                className,
              )}
              {...props}
            />
          ),
          h4: ({ node: _node, className, ...props }) => (
            <h4
              className={cn(
                "mb-4 mt-6 scroll-m-20 text-xl font-semibold tracking-tight first:mt-0 last:mb-0",
                className,
              )}
              {...props}
            />
          ),
          h5: ({ node: _node, className, ...props }) => (
            <h5
              className={cn(
                "my-4 text-lg font-semibold first:mt-0 last:mb-0",
                className,
              )}
              {...props}
            />
          ),
          h6: ({ node: _node, className, ...props }) => (
            <h6
              className={cn("my-4 font-semibold first:mt-0 last:mb-0", className)}
              {...props}
            />
          ),
          p: ({ node: _node, className, ...props }) => (
            <p
              className={cn(
                "mb-5 mt-5 leading-7 first:mt-0 last:mb-0",
                className,
              )}
              {...props}
            />
          ),
          a: ({ node: _node, className, ...props }) => (
            <a
              target="_blank"
              className={cn(
                "text-primary font-medium underline underline-offset-4",
                className,
              )}
              {...props}
            />
          ),
          blockquote: ({ node: _node, className, ...props }) => (
            <blockquote
              className={cn("border-l-2 pl-6 italic", className)}
              {...props}
            />
          ),
          ul: ({ node: _node, className, ...props }) => (
            <ul
              className={cn("my-1 ml-6 list-disc [&>li]:mt-2", className)}
              {...props}
            />
          ),
          ol: ({ node: _node, className, ...props }) => (
            <ol
              className={cn("my-1 ml-6 list-decimal [&>li]:mt-2", className)}
              {...props}
            />
          ),
          hr: ({ node: _node, className, ...props }) => (
            <hr className={cn("my-5 border-b", className)} {...props} />
          ),
          table: ({ node: _node, className, ...props }) => (
            <table
              className={cn(
                "my-5 w-full border-separate border-spacing-0 overflow-y-auto",
                className,
              )}
              {...props}
            />
          ),
          th: ({ node: _node, className, ...props }) => (
            <th
              className={cn(
                "bg-muted/50 px-4 py-2 text-left font-bold first:rounded-tl-lg last:rounded-tr-lg [&[align=center]]:text-center [&[align=right]]:text-right",
                className,
              )}
              {...props}
            />
          ),
          td: ({ node: _node, className, ...props }) => (
            <td
              className={cn(
                "border-b border-l px-4 py-2 text-left last:border-r [&[align=center]]:text-center [&[align=right]]:text-right",
                className,
              )}
              {...props}
            />
          ),
          tr: ({ node: _node, className, ...props }) => (
            <tr
              className={cn(
                "m-0 border-b p-0 first:border-t [&:last-child>td:first-child]:rounded-bl-lg [&:last-child>td:last-child]:rounded-br-lg",
                className,
              )}
              {...props}
            />
          ),
          sup: ({ node: _node, className, ...props }) => (
            <sup
              className={cn("[&>a]:text-xs [&>a]:no-underline", className)}
              {...props}
            />
          ),
          pre: ({ node: _node, className, ...props }) => (
            <pre
              className={cn(
                "overflow-x-auto rounded-b-lg bg-black p-4 text-white",
                className,
              )}
              {...props}
            />
          ),   
          code(props) {
            const {children, className, node, ...rest} = props
            const match = /language-(\w+)/.exec(className || '')
            return match ? (
              <SyntaxHighlighter
                {...rest}
                ref={codeBlockRef}
                PreTag="div"
                children={String(children).replace(/\n$/, '')}
                language={match[1]}
                style={prism}
              />
            ) : (
              <code {...rest} className={className}>
                {children}
              </code>
            )
          }
        }}
      >
        {processedContent}
      </MemoizedReactMarkdown>
    </>
  );
}

// Preprocess LaTeX equations to be rendered by KaTeX
const preprocessLaTeX = (content: string): string => {
  const blockProcessedContent = content.replace(
    /\\\[([\s\S]*?)\\\]/g,
    (_, equation) => `$$${equation}$$`
  )
  const inlineProcessedContent = blockProcessedContent.replace(
    /\\\(([\s\S]*?)\\\)/g,
    (_, equation) => `$${equation}$`
  )
  return inlineProcessedContent
}
import { createResource, findRelevantContent } from "./functions";
import { auth } from '@clerk/nextjs/server';
import { groq } from '@ai-sdk/groq';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { generateObject, smoothStream, streamText, tool } from "ai";
import { z } from "zod";
import { systemDetailPrompt } from "./prompt";

export const maxDuration = 30;

const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
});

export async function POST(req: Request) {
  const { messages, contractId, filePath } = await req.json();
  const { userId } = await auth();
  if (!userId) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const prompt = messages
  .filter((message: { role: string }) => message.role === 'user')
  .pop()
  const userPrompt: string | undefined = prompt?.content

  console.log("chat messages", JSON.stringify(messages.slice(-4), null, 2))
  const result = streamText({
    //model: groq('llama-3.3-70b-versatile'),
    model: google("gemini-2.5-flash-preview-04-17"),
    experimental_transform: smoothStream(),
    messages: messages.slice(-4),
    system: systemDetailPrompt,
    tools: {
      addResource: tool({
        description: `add a resource to your knowledge base.
          If the user provides a random piece of knowledge unprompted, use this tool without asking for confirmation.`,
        parameters: z.object({
          content: z
            .string()
            .describe("the content or resource to add to the knowledge base"),
        }),
        execute: async ({ content }) => createResource(userId, contractId, content, filePath),
      }),
      getInformation: tool({
        description: `get information from your knowledge base to answer questions.`,
        parameters: z.object({
          question: z.string().describe("the users question"),
          similarQuestions: z.array(z.string()).describe("keywords to search"),
        }),
        execute: async ({ similarQuestions }) => {
          const results = await Promise.all(
            similarQuestions.map(
              async (question) => await findRelevantContent(question, contractId, filePath),
            ),
          );
          // Flatten the array of arrays and remove duplicates based on 'name'
          const uniqueResults = Array.from(
            new Map(results.flat().map((row: any) => [row.content, row])).values(),
          );
          console.log("uniqueResults: ",  uniqueResults)

          // Find the result with the highest similarity
          /*const highestResult = uniqueResults.reduce((max, current) =>
            current.similarity > max.similarity ? current : max
          );
          console.log("highestResult: ",  highestResult)*/
          return uniqueResults;
        },
      }),
      understandQuery: tool({
        description: `Analyze the user query to identify the most relevant tools and similar questions.`,
        parameters: z.object({
          query: z.string().describe("the users query"),
          toolsToCallInOrder: z
            .array(z.string())
            .describe(
              "The tools to call in order to respond effectively.",
            ),
        }),
        execute: async ({ query }) => {
          const { object } = await generateObject({
            model: groq('qwen-qwq-32b', {
            // model: google("gemini-2.0-flash-exp", {
            // structuredOutputs: true,
            }),
            system:
              "You are a query understanding assistant. Analyze the user query and generate similar questions.",
            schema: z.object({
              questions: z
                .array(z.string())
                .max(3)
                .describe("similar questions to the user's query. be concise."),
            }),
            prompt: `Analyze this query: "${query}". Provide the following:
                    3 similar questions in user's language that could help answer the user's query`,
          });
          return object.questions;
        },
      }),
    },
  });

  return result.toDataStreamResponse();
}
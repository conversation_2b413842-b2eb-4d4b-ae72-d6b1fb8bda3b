'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

export async function getProject(projectId: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return null
    }
    
    // Get project from a projectId
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    })

    if (!project) {
      return null
    }
    
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId: project?.workspaceId },
    });
  
    if (!member) {
      return null
    }

         
    return project
    
  } catch (error) {
    console.error('Error getting project', error)
    return null
  }
}
"use client";

import "./styles.css";
import { CopilotKit } from "@copilotkit/react-core";
import { CopilotKitCSSProperties, CopilotSidebar } from "@copilotkit/react-ui";
import { Presentation } from "@/components/copilot/presentation/Presentation";
import { useState } from "react";
import { ArrowUp } from "lucide-react";

export default function AIPresentation() {
  const [performResearch, setPerformResearch] = useState(false);

  return (
    <CopilotKit
      //publicApiKey={process.env.NEXT_PUBLIC_COPILOT_CLOUD_API_KEY}
      // Alternatively, you can use runtimeUrl to host your own CopilotKit Runtime
      runtimeUrl="/api/copilotkit/presentation"
      //transcribeAudioUrl="/api/transcribe"
      //textToSpeechUrl="/api/tts"
    >
      <div
        style={
          {
            height: `100vh`,
            "--copilot-kit-primary-color": "#222222",
          } as CopilotKitCSSProperties
        }
      >
        <CopilotSidebar
          icons={{sendIcon: <ArrowUp size={20}/>}}
          instructions={
            "Help the user create and edit a powerpoint-style presentation." +
            (!performResearch
              ? " No research is needed. Do not perform any research."
              : " Perform research on the topic.")
          }
          defaultOpen={false}
          labels={{
            title: "Presentation Copilot",
            initial:
              "Hi you! 👋 I can help you create a presentation on any topic.",
          }}
          clickOutsideToClose={false}
        >
          <Presentation
            performResearch={performResearch}
            setPerformResearch={setPerformResearch}
          />
        </CopilotSidebar>
      </div>
    </CopilotKit>
  );
}
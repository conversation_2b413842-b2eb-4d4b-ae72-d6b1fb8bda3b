"use server"
import { generateObject } from 'ai';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { 
  autoPayResidentSchema, 
} from '@/app/api/webhook/schemas';

const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
});

export const analyzeAutoPayResidentImage = async (base64Image: string) => {
  const { object } = await generateObject({
    model: google('gemini-2.0-flash-exp'),
    schema: autoPayResidentSchema,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `請分析這張國泰世華商業銀行代扣繳費授權和款項案內容清單，並提取以下資訊：

1. 列印日期 (printDate) - 格式：YYYY年MM月DD日
2. 委託單位統編 (organizationId) - 數字編號
3. 交易記錄列表 (records)，每筆記錄包含：
   - 用戶代號 (userId) - 格式通常為 XX-XX-X
   - 金額 (amount) - 數字金額

請仔細識別表格中的每一筆交易記錄，確保：
- 正確提取用戶代號（在最右側欄位）
- 準確識別金額數字（移除逗號轉為數字）
- 計算或驗證合計金額是否正確
- 統計借方交易總筆數

若某項資訊無法清楚辨識，請根據上下文合理推測。`
          },
          {
            type: 'image',
            image: base64Image,
          }
        ],
      }
    ],
  });
  
  return object;
};
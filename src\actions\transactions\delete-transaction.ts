"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";

const deleteSchema = z.object({
  id: z.string().min(1, { message: "Transaction ID is required" }),
});

export async function deleteTransaction(input: { id: string }): Promise<{
  success: boolean;
  error?: string;
}> {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Unauthorized" };
  }

  const validation = deleteSchema.safeParse(input);
  if (!validation.success) {
    return { success: false, error: "Invalid input" };
  }

  try {
    const transaction = await prisma.transaction.findUnique({
      where: {
        id: validation.data.id,
        userId: userId, // Ensure user owns the transaction
      },
    });

    if (!transaction) {
      return { success: false, error: "Transaction not found or access denied" };
    }

    // Calculate balance adjustment
    // When deleting a CREDIT transaction, we need to subtract from the balance
    // When deleting a DEBIT transaction, we need to add to the balance
    const balanceAdjustment = transaction.type === "CREDIT" 
      ? -Number(transaction.amount) 
      : Number(transaction.amount);

    // Use a transaction to ensure both operations succeed or fail together
    await prisma.$transaction(async (tx) => {
      // Delete the transaction
      await tx.transaction.delete({
        where: {
          id: validation.data.id,
        },
      });

      // Get the latest balance for the account
      const latestBalance = await tx.balance.findFirst({
        where: { accountId: transaction.accountId },
        orderBy: { date: 'desc' },
      });

      // Calculate new balance
      const currentAmount = latestBalance?.amount ?? 0;
      const newBalanceAmount = Number(currentAmount) + balanceAdjustment;

      // Create a new balance record
      await tx.balance.create({
        data: {
          amount: newBalanceAmount,
          date: new Date(),
          bankAccount: {
            connect: { id: transaction.accountId! },
          },
          currency: {
            connect: { iso: "TWD" }, // 使用預設貨幣
          },
        },
      });
    });

    // Revalidate relevant paths
    revalidatePath("/transactions");
    revalidatePath(`/accounts/${transaction.accountId}`);
    revalidatePath("/banking");
    revalidatePath("/dashboard");

    return { success: true };
  } catch (error) {
    console.error("Error deleting transaction:", error);
    return { success: false, error: "Failed to delete transaction" };
  }
}
import { useState } from "react";
import { SpeechData } from "./types";
import { Pencil1Icon } from "@radix-ui/react-icons";
import { useUpdateSpeechDescription} from "./use-update-speech-description";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { XIcon } from "lucide-react";

interface SpeechDescriptionProps {
  data: SpeechData;
}

export const SpeechDescription = ({ 
  data 
}: SpeechDescriptionProps) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [value, setValue] = useState<string>(data?.speech.description ?? "");

  const { mutate, isPending } = useUpdateSpeechDescription();

  const handleSave = () => {
    mutate({
      speechId: data.speech.id,
      data: { description: value }
    });
    setIsEditing(false )
  }

  return (
    <div className="p-4 border rounded-log">
      <div className="flex items-center justify-between">
        <p className="text-lg font-semibold">Description</p>
        <Button
          onClick={() => setIsEditing((prev) => !prev)}
          className="h-8 ml-auto"
          variant="datePicker"
          size="sm"
        >        
          {isEditing ? (
            <XIcon className="size-4" />
          ) : (
            <Pencil1Icon className="size-4" /> 
          )}
          {isEditing ? "Cancel" : "Edit"}
        </Button>
      </div>
      <Separator className="my-2" />
      {isEditing ? (
        <div className="flex flex-col gap-y-4">
          <Textarea 
            placeholder="Add Speech description"
            value={value}
            rows={4}
            onChange={(e) => setValue(e.target.value)}
          />
          <Button
            onClick={handleSave}
            disabled={isPending}
            className="h-8 w-fit ml-auto"
            variant="datePicker"
            size="sm"
          >
            {isPending ? "Saving..." : "Save"}
          </Button>
        </div>
      ) : (
        <div className="flex flex-col gap-y-4">
          {data?.speech.description || (
            <span className="text-muted-foreground">No description set.</span>
          )}
        </div>
      )}
    </div>
  )
}
.container {
    min-height: 100vh;
    padding: 0 0.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  
  .main {
    padding: 5rem 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width:50vw;
    max-width:50vw;
  }
  
  .footer {
    width: 100%;
    height: 100px;
    border-top: 1px solid #eaeaea;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .footer img {
    margin-left: 0.5rem;
  }
  
  .footer a {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .title a {
    color: #0070f3;
    text-decoration: none;
  }
  
  .title a:hover,
  .title a:focus,
  .title a:active {
    text-decoration: underline;
  }
  
  .title {
    margin: 0;
    line-height: 1.15;
    font-size: 4rem;
  }
  
  .title,
  .description {
    text-align: center;
  }
  
  .description {
    line-height: 1.5;
    font-size: 1.5rem;
  }
  
  .code {
    background: #fafafa;
    border-radius: 5px;
    padding: 0.75rem;
    font-size: 1.1rem;
    font-family: <PERSON>lo, Monaco, <PERSON>, Liberation Mono, <PERSON><PERSON><PERSON>,
      Bitstream Vera Sans Mono, Courier New, monospace;
  }
  
  .grid {
    display: flex;
    align-items: center;
    justify-content: left;
    flex-wrap: wrap;
    max-width: 800px;
  }
  
  .card {
    margin-right: 5px;
    margin-bottom: 20px;
    flex-basis: 45%;
    padding: 1.5rem;
    text-align: left;
    color: inherit;
    text-decoration: none;
    border: 1px solid #eaeaea;
    border-radius: 10px;
    transition: color 0.15s ease, border-color 0.15s ease;
    color: #165260;
    border-color: #165260;
  }
  
  .card:hover,
  .card:focus,
  .card:active {
    color: #63bfb0;
    border-color: #63bfb0;
  }
  
  .card h3 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
  }
  
  .card p {
    margin: 0;
    font-size: 1.25rem;
    line-height: 1.5;
  }
  
  .logo {
    height: 1em;
  }
  
  @media (max-width: 600px) {
    .grid {
      width: 100%;
      flex-direction: column;
    }
  }
  
  .headerContainer {
    display: 'flex';
    justify-content: 'center'; 
    align-items: 'center';
    margin-bottom:20px;
  }
  
  .searchInput {
     width: 44vw; 
     text-align: left; 
     padding-left : 12px; 
     padding-right : 12px;
     padding-top : 10px;
     padding-bottom : 12px;
  }
  
  .searchContainer {
    margin-left: 0px;
    margin-bottom: 13px;
    padding-left: 0px;
    margin-top: 0px;
  }
  
  .searchResult {
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 19px;
    list-style: none;
    margin-left: 0px;
    border-left: 1px solid #d1d1d1;
    border-right: 1px solid #d1d1d1;
    vertical-align: middle;
    cursor: pointer;
  }
  
  .searchResult:last-child {
     border-bottom: 1px solid #d1d1d1;
  }
  
  .searchResult:nth-child(even) {
    background-color: #f7fdfe;
  }
  
  .searchResultLink {
    cursor: pointer;
    display:block;
  }
  
  .FolderHeader {
    width: 100%;
    border-bottom: 1px solid #efefef;
    border-top: 1px solid #efefef;
    background: #9e9e9e;
    padding-left: 9px;
    margin-bottom: -1px;
    color: #fff;
    text-align : "left" ;
  }
  
  .BackButton {
    width: 28px;
    margin-right: 10px;
    cursor: pointer;
    background: #9e9e9e;
    color: #fff;
    border: 0;
    top: 4px;
    position: relative;
  }
  
  
  .filesContainer {
    margin-left: 0px;
    margin-bottom: 13px;
    padding-left: 0px;
    margin-top: 0px;
  }
  
  .fileResult {
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 19px;
    list-style: none;
    margin-left: 0px;
    border-left: 1px solid #d1d1d1;
    border-right: 1px solid #d1d1d1;
    vertical-align: middle;
    cursor: pointer;
  }
  
  .fileResult:hover,
  .fileResult:focus,
  .fileResult:active {
    background:#eff8ff !important;
  }
  
  .fileResult:last-child {
     border-bottom: 1px solid #d1d1d1;
  }
  
  .fileResult:nth-child(even) {
    background-color: #f4ffee;
  }
'use server'
import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache"
import { z } from "zod"
import { bulkUpdateTasksSchema } from "@/components/tasks/schemas";

export async function bulkUpdateTasks(values: z.infer<typeof bulkUpdateTasksSchema>) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    const validatedFields = bulkUpdateTasksSchema.safeParse(values)
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false
      }
    }

    const { tasks } = validatedFields.data

    const tasksToUpdate =  await prisma.task.findMany({
      where: {
        id: {
          in: tasks.map(task => task.id)
        }
      }
    })

    // Check if all tasks belong to the same workspace
    const workspaceIds = tasksToUpdate.map(task => task.workspaceId)
    const uniqueWorkspaceIds = new Set(workspaceIds);
    if (uniqueWorkspaceIds.size !== 1) {
      return {
        error: "All tasks must be in the same workspace",
        success: false,
      };
    }

    const workspaceId = Array.from(uniqueWorkspaceIds)[0];

    // Check if creator is a member of the workspace
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId },
    });

    if (!member) {
      return {
        error: "Unauthorized",
        success: false
      }
    }

    const updatedTasks = await Promise.all(
      tasks.map((task) => {
        return prisma.task.update({
          where: {
            id: task.id,
          },
          data: {
            status: task.status,
            position: task.position,
          },
        });
      })
    );

    if (!updatedTasks) {
      return {
        error: "Failed to update tasks.",
        success: false
      }
    }

    revalidatePath('/workspaces')

    return {
      data: updatedTasks,
      success: true
    }
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to update tasks.",
      success: false,
      details: error instanceof Error ? error.stack : undefined
    }
  }
}
'use client';

import React, { useState, useEffect } from 'react';
import { useGetFolders } from './use-get-folders';
import { useCreateFolder } from './use-create-folder';
import { useUpdateItem } from './use-update-item';
import { useUploadFile } from './use-upload-file';
import { useDeleteItem } from './use-delete-item';
import { GoogleDriveAuthStatus } from './google-drive-auth-status'; 
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loading } from '@/components/ui/loader';
import { Search, Pencil, Trash2, Loader } from "lucide-react";
import { toast } from "sonner";

interface Item {
  id: string;
  name: string;
  type: 'folder' | 'file';
  extension?: string;
  children?: Item[];
  size?: number;
  icon?: string;
  owner?: string;
  modifiedTime?: string;
}

export default function GoogleDriveComponent() {
  const [structure, setStructure] = useState<Item[]>([]);
  const [selectedItems, setSelectedItems] = useState<Item[]>([]);
  const [folderId, setFolderId] = useState<string>("");
  const [folderName, setFolderName] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [itemToRename, setItemToRename] = useState<Item | null>(null);
  const [newName, setNewName] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const driveId = "none" //process.env.NEXT_PUBLIC_SHARED_DRIVE_ID;

  const { data, isLoading: isFetchFoldersLoading } = useGetFolders();
  const { mutate: createFolderMutate, isPending: isCreateFolderLoading } = useCreateFolder();
  const { mutate: updateItemMutate, isPending: isUpdateItemLoading } = useUpdateItem();
  const { mutate: uploadFileMutate, isPending: isUploadFileLoading }= useUploadFile();
  const { mutate: deleteItemMutate, isPending: isDeleteItemLoading } = useDeleteItem();


  useEffect(() => {
    if (data) {
      setStructure(data.structure);;
    }
  }, [data]);

  const filterItems = (items: Item[], query: string): Item[] => {
    if (!query) return items;
    
    return items.map(item => {
      if (item.name.toLowerCase().includes(query.toLowerCase())) {
        // If this item matches, return it with all its children
        return item;
      } else if (item.children) {
        // If this item doesn't match, check its children
        const filteredChildren = filterItems(item.children, query);
        if (filteredChildren.length > 0) {
          // If any children match, return this item with only matching children
          return { ...item, children: filteredChildren };
        }
      }
      return null;
    }).filter(Boolean) as Item[];
  };

  const toggleItemSelection = (item: Item) => {
    setSelectedItems((prev) => {
      const index = prev.findIndex((i) => i.id === item.id);
      if (index > -1) {
        return prev.filter((i) => i.id !== item.id);
      } else {
        return [...prev, item];
      }
    });
  };

  const getAllFolders = (items: Item[]): Item[] => {
    let folders: Item[] = [];
    
    items.forEach(item => {
      if (item.type === 'folder') {
        folders.push(item);
        if (item.children) {
          folders = [...folders, ...getAllFolders(item.children)];
        }
      }
    });
    
    return folders;
  };

  const createFolder = () => {
    try {
      if (!folderName) {
        console.error('Folder name is required');
        return;
      }
  
      const values = {
        folderId: folderId === 'root' ? null : folderId,
        folderName,
      };

      createFolderMutate({ values }, {
        onSuccess: ({ data }) => {
          if (data.webViewLink) {
            const link = data.webViewLink;
          }
          setFolderName("");
          setFolderId(data.id);
        }
      });
  
    } catch (error) {
      console.error('Error creating folder:', error);
    }
  };
  const uploadFile = () => {
    if (!file) return;

    const formData = new FormData();

    formData.append("folderId", folderId);
    formData.append("file", file);
    formData.append("driveId", driveId!);

    uploadFileMutate(formData, {
      onSuccess(data) {
        const link = data.file.webViewLink;
      },
    })
  };

  const handleDelete = (item: Item) => {
    try {
      deleteItemMutate(item.id, {
        onSuccess: () => {
          setSelectedItems([]);
          setItemToRename(null);
          setNewName("");
          setSearchQuery("")
        }
      });
    } catch (error) {
      console.error('Error deleting item:', error);
      toast.error('Failed to delete item');
    }
  };
  
  const handleRename = (item: Item) => {
    try {
      if (!newName) return;

      const values = { 
        fileId: item.id,
        newName: newName 
      }
      updateItemMutate({ values }, {
        onSuccess: ({ data }) => {
          setItemToRename(null);
          setNewName("");
        }
      });
  
    } catch (error) {
      console.error('Error renaming item:', error);
      toast.error('Failed to rename item');
    }
  };

  const renderItem = (item: Item) => (
    <li key={item.id} className="mb-1">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={selectedItems.some((i) => i.id === item.id)}
            onCheckedChange={() => toggleItemSelection(item)}
          />
          <span className="text-sm font-medium">{item.name}</span>
        </div>
        <div className="flex items-center">
          <Dialog open={itemToRename?.id === item.id} onOpenChange={(open) => {
            if (!open) setItemToRename(null);
          }}>
            <DialogTrigger asChild>
              <Button
                className='h-8 p-0'
                variant="ghost"
                size="icon"
                onClick={() => {
                  setItemToRename(item);
                  setNewName(item.name);
                }}
              >
                <Pencil className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Rename Item</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  value={newName}
                  onChange={(e) => setNewName(e.target.value)}
                  placeholder="New name"
                />
                <div className="flex flex-item items-center">
                  {isUpdateItemLoading && <Loader className="size-4 mr-1" />}
                  <Button className='h-8' onClick={() => handleRename(item)}>
                    Save
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button
            className='h-8 p-0'
            variant="ghost"
            size="icon"
            onClick={() => handleDelete(item)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {item.type === 'folder' && item.children && (
        <ul className="pl-6 mt-1">
          {item.children.map((child) => renderItem(child))}
        </ul>
      )}
    </li>
  );

  const disabled = isFetchFoldersLoading || isCreateFolderLoading || isUploadFileLoading || isDeleteItemLoading;

  if (isFetchFoldersLoading && structure.length === 0) {
    return <Loading />;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-item items-center justify-between">
          <CardTitle>Select Items from Google Drive</CardTitle>
          <div className="">
            <div className="relative">
              <Input
                placeholder="Search items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[300px] w-full border rounded-md p-4 mb-3">
          <ul>{filterItems(structure, searchQuery).map((item) => renderItem(item))}</ul>
          {structure?.length === 0 && (
            <div className='flex items-center justify-center'><GoogleDriveAuthStatus /></div>
          )}
        </ScrollArea>
        <div className="flex flex-item items-end gap-x-8">
          <div className="flex flex-item items-center mt-4">
            {isLoading && <Loader className="size-4 mr-1" />}
            <Button disabled={disabled || selectedItems.length === 0} size="sm" onClick={() => console.log("selectedItems",selectedItems)}>
              Process Selected Files {selectedItems?.length > 0 && `(${selectedItems.length})`}
            </Button>
          </div>
          <div className="flex flex-item items-center gap-8">
            <div className="form-group space-y-2">
              <h1>Create Folder</h1>
              <Select onValueChange={setFolderId} value={folderId || "root"}>
                <SelectTrigger className="h-8 w-[200px]">
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="root">Root (My Drive)</SelectItem>
                  {getAllFolders(structure).map((folder) => (
                    <SelectItem key={folder.id} value={folder.id}>
                      {folder.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                className='h-8'
                type="text"
                placeholder="Folder Name"
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
              />
              <div className="flex flex-item items-center">
                {isCreateFolderLoading && <Loader className="size-4 mr-1" />}
                <Button disabled={disabled || !folderName} size="sm" onClick={createFolder}>Create Folder</Button>
              </div>
              
            </div>

            <div className="form-group space-y-2">
              <h1>Upload File</h1>
              <div className="flex flex-col items-start gap-2">
                <Select onValueChange={setFolderId} value={folderId}>
                  <SelectTrigger className="h-8 w-[200px]">
                    <SelectValue placeholder="Select folder" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAllFolders(structure).map((folder) => (
                      <SelectItem key={folder.id} value={folder.id}>
                        {folder.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <input
                  type="file"
                  placeholder="File"
                  onChange={(e) => setFile(e.target.files![0])}
                  className='hover:cursor-pointer'
                />
              </div>
              <div className="flex flex-item items-center">
                {isUploadFileLoading && <Loader className="size-4 mr-1" />}
                <Button disabled={disabled || !file} size="sm" onClick={uploadFile}>Upload File</Button>
              </div>
            </div>
          </div>
        </div>

      </CardContent>
    </Card>
  );
}

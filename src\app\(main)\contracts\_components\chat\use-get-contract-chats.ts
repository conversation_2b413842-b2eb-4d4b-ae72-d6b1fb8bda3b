import { useQuery } from "@tanstack/react-query";
import { getChatsByContractId } from "@/actions/chats/actions";

type UseChatProps = {
  contractId: string
  assistantId: string
}

export const useGetContractChats = ({ contractId, assistantId }: UseChatProps) => {
  const { data, isLoading, refetch, error } = useQuery({
    queryKey: [
      "contracts", contractId,
    ],
    queryFn: async () => {
      const data = await getChatsByContractId({contractId, assistantId});

      console.log("chat by contract", data)

      if (!data) {
        throw new Error(`Failed to get chats.`);
      }
      
      return data;
    },
    enabled: <PERSON><PERSON><PERSON>(contractId && assistantId),
  });

  return {
    data,
    isLoading,
    refetch,
    error,
  };
};
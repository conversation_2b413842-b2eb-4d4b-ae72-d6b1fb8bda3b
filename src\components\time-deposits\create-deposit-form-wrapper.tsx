"use client"

import { CreateDepositForm } from "./create-deposit-form";
import { useBankAccountId } from "./use-bank-id";
interface CreateDepositFormWrapperProps {
  onCancel?: () => void;
}

export const CreateDepositFormWrapper = ({ onCancel }: CreateDepositFormWrapperProps) => {
  const bankAccountId = useBankAccountId()
  return (
    <CreateDepositForm 
      onCancel={onCancel}
      bankAccountId={bankAccountId}
    />
  );
};
"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { UpdateDepositFormWrapper } from "./update-deposit-form-wrapper";
import { useUpdateDepositModal } from "@/hooks/use-update-deposit-modal";

export const UpdateDepositModal = () => {
  const { depositId, close } = useUpdateDepositModal()
  return (
    <ResponsiveModal open={!!depositId} onOpenChange={close}>
      {depositId && (
        <UpdateDepositFormWrapper onCancel={close} id={depositId} />
      )}
      
    </ResponsiveModal>
  )
}
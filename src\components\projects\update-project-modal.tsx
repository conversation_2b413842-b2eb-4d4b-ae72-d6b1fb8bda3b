"use client";

import React, { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { Project } from "@prisma/client";
import { ResponsiveModal } from "@/components/responsive-modal";
import { UpdateProjectForm } from "./update-project-form";
import { useUpdateProjectModal } from "@/hooks/use-update-project-modal";
import { getProject } from "@/actions/projects/get-project";


export const UpdateProjectModal = () => {
  const { isOpen, close } = useUpdateProjectModal();
  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  // Get projectId from URL only when needed
  const projectId = isOpen ? pathname.match(/\/projects\/([^/?]+)/)?.[1] : null;

  // Fetch project data only once when modal opens
  const fetchProject = async (id: string) => {
    setIsLoading(true);
    try {
      const response = await getProject(id);
      if (response) {
        setProject(Array.isArray(response) ? response[0] : response);
      }
    } catch (err) {
      console.error("Failed to fetch project:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle modal opening
  useEffect(() => {
    if (isOpen && projectId && !project) {
      fetchProject(projectId);
    }
  }, [isOpen]); // Only depend on isOpen

  // Handle modal closing
  const handleClose = () => {
    close();
    setProject(null);
    router.replace(`/projects/${projectId}`);
  };

  return (
    <ResponsiveModal 
      open={isOpen} 
      onOpenChange={(open) => {
        if (!open) handleClose();
      }}
    >
      {isLoading ? (
        <div>Loading project details...</div>
      ) : (
        project && (
          <UpdateProjectForm 
            project={project} 
            onCancel={handleClose}
          />
        )
      )}
    </ResponsiveModal>
  );
};
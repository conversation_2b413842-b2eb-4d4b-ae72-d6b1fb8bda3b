"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { Prisma } from "@prisma/client"; // Import Prisma namespace

// Schema defining the fields that can be updated
// Only include fields you want to allow updating via this action
const updateTransactionSchema = z.object({
  id: z.string().min(1, { message: "Transaction ID is required" }),
  // Optional fields - adjust based on your requirements
  amount: z.coerce.number().positive({ message: "金額必須為正數" }).optional(),
  type: z.string().optional(), //(z.enum(["CREDIT", "DEBIT"]).optional(),
  categoryId: z.string().min(1, "請選擇類別").optional(),
  date: z.date().optional(),
  description: z.string().max(255, "描述過長").optional(), // Example max length
  // Add other fields like `categoryValidated` if they should be updatable
  // categoryValidated: z.boolean().optional(),
});

// Type for the input based on the schema
type UpdateTransactionInput = z.infer<typeof updateTransactionSchema>;

// Define the return type for better type safety
interface UpdateTransactionResult {
  success: boolean;
  error?: string;
  data?: Prisma.TransactionGetPayload<{}>; // Return the updated transaction data
}

export async function updateTransaction(input: UpdateTransactionInput): Promise<UpdateTransactionResult> {
  const { userId } = await auth();
  if (!userId) {
    return { success: false, error: "Unauthorized: User not logged in" };
  }

  // Validate the input using the Zod schema
  const validation = updateTransactionSchema.safeParse(input);
  if (!validation.success) {
    // Log detailed validation errors for debugging
    console.error("Update Transaction Validation Error:", validation.error.flatten().fieldErrors);
    return { success: false, error: "Invalid input data provided" };
  }

  // Destructure the validated data
  const { id, ...dataToUpdate } = validation.data;

  try {
    // 1. Verify the user owns the transaction before attempting to update
    const existingTransaction = await prisma.transaction.findFirst({
      where: {
        id: id,
        userId: userId,
      },
      select: {
        accountId: true,
        amount: true,
        type: true,
      }
    });

    if (!existingTransaction) {
      return { success: false, error: "Transaction not found or access denied" };
    }

    // Calculate balance adjustment if amount or type changes
    let balanceAdjustment = 0;
    if (dataToUpdate.amount !== undefined || dataToUpdate.type !== undefined) {
      // Reverse the effect of the old transaction
      const oldEffect = existingTransaction.type === "CREDIT" 
        ? Number(existingTransaction.amount) 
        : -Number(existingTransaction.amount);

      // Calculate the effect of the new transaction
      const newAmount = dataToUpdate.amount !== undefined 
        ? dataToUpdate.amount 
        : Number(existingTransaction.amount);
      const newType = dataToUpdate.type || existingTransaction.type;
      const newEffect = newType === "CREDIT" ? newAmount : -newAmount;

      // The adjustment is the difference between the new and old effects
      balanceAdjustment = newEffect - oldEffect;
    }

    // Verify categoryId exists if it's being updated
    if (dataToUpdate.categoryId) {
      //const categoryName = dataToUpdate.categoryId.charAt(0).toUpperCase() + dataToUpdate.categoryId.slice(1).toLowerCase();
      const categoryExists = await prisma.category.findFirst({
        where: {
          id: dataToUpdate.categoryId,
          //name: categoryName,
          userId: userId,
        },
      });

      if (!categoryExists) {
        return { success: false, error: "Invalid category selected" };
      }
      
      // Update the categoryId to use the properly cased name
      dataToUpdate.categoryId = categoryExists.id;
    }

    // 2. Prepare the payload for Prisma update
    // Convert amount to Decimal if your Prisma schema uses Decimal
    const updatePayload: Prisma.TransactionUpdateArgs['data'] = {
      ...dataToUpdate,
      // Conditionally include amount only if it was provided and is valid
      ...(dataToUpdate.amount !== undefined && { amount: new Prisma.Decimal(dataToUpdate.amount) }),
      // Ensure date is passed correctly if provided
      date: dataToUpdate.date ? dataToUpdate.date : undefined,
      // Handle description potentially being an empty string if you want to allow clearing it
      // description: dataToUpdate.description === undefined ? undefined : dataToUpdate.description,
    };

    // Use a transaction to ensure both operations succeed or fail together
    const updatedTransaction = await prisma.$transaction(async (tx) => {
      // Update the transaction
      const updated = await tx.transaction.update({
        where: { id },
        data: updatePayload,
      });

      // If there's a balance adjustment, update the balance
      if (balanceAdjustment !== 0) {
        // Get the latest balance
        const latestBalance = await tx.balance.findFirst({
          where: { accountId: existingTransaction.accountId },
          orderBy: { date: 'desc' },
        });

        // Calculate new balance
        const currentAmount = latestBalance?.amount ?? 0;
        const newBalanceAmount = Number(currentAmount) + balanceAdjustment;

        // Create a new balance record
        await tx.balance.create({
          data: {
            amount: newBalanceAmount,
            date: new Date(),
            bankAccount: {
              connect: { id: existingTransaction.accountId! },
            },
            currency: {
              connect: { iso: "TWD" },
            },
          },
        });
      }

      return updated;
    });

    // Revalidate paths
    revalidatePath("/transactions");
    revalidatePath(`/accounts/${updatedTransaction.accountId}`);
    revalidatePath("/banking");
    revalidatePath("/dashboard");

    return { success: true, data: updatedTransaction };

  } catch (error) {
    console.error("Error updating transaction:", error);
    // Handle potential Prisma-specific errors (e.g., unique constraint violation) if needed
    // if (error instanceof Prisma.PrismaClientKnownRequestError) { ... }
    return { success: false, error: "Database error: Failed to update transaction" };
  }
}
"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { MAX_DATE_RANGE_DAYS } from "@/constants";
import { 
  format,
  parse,
  startOfMonth,
  endOfMonth,
  differenceInDays
} from "date-fns";
import {
  createColumnHelper,
} from '@tanstack/react-table'
import { Input } from "@/components/ui/input"
import { DateRange } from "react-day-picker";
import { DateRangePicker } from  "@/components/ui/date-range-picker";
import { toast } from "sonner";
import { useEffect, useState, useMemo, useCallback } from "react";
import { cn } from "@/lib/utils";

interface MonthlyData {
  [key: string]: number;
}
  
interface TableDataItem {
  description: string;
  monthlyData: MonthlyData;
  sum: number;
   percentage: number;
}

interface Transaction {
  incomes: {
    tableData: TableDataItem[];
    monthlySums: MonthlyData;
    total: number;
  };
  expenses: {
    tableData: TableDataItem[];
    monthlySums: { [key: string]: number };
    total: number;
  };
  dateRange: string[];
}

interface AccountTransactionsProps {
  dataRange: any
  transactions: Transaction;
  onTransactionUpdate: () => Promise<void>;
}

const columnHelper = createColumnHelper<Transaction>()

export default function AccountTransactionsMatrix({
  dataRange,
  transactions,
  onTransactionUpdate,
}: AccountTransactionsProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: dataRange.from,
    to: dataRange.to,
  });
  const [searchFilter, setSearchFilter] = useState("");
  const [displayMode, setDisplayMode] = useState<string[]>(["both"]);

  //console.log("VIRTULAL transactions==============", transactions)

  const months = useMemo(() => {
    return transactions.dateRange.map((dateString: string) => {
      const date = parse(dateString, 'yyyy-MM', new Date());
      return format(date, 'MMM/yy');
    });
  }, [transactions.dateRange]);

  const filterData = useCallback((tableData: TableDataItem[], transaction: Transaction) => {
    const normalizedSearch = searchFilter.toLowerCase().trim();
    const searchNumber = Number(normalizedSearch);
    const isNumericSearch = !isNaN(searchNumber);  

    return tableData.map(row => {
      // Reset and rebuild monthlyData completely for each filter operation
      const filteredMonthlyData: { [key: string]: number } = {};
      
      // Always start fresh from the original data
      Object.entries(row.monthlyData).forEach(([monthStr, value]) => {
        const monthDate = parse(monthStr, 'yyyy-MM', new Date());
        
        if (!dateRange || 
            (dateRange.from && dateRange.to && 
            monthDate >= startOfMonth(dateRange.from) && 
            monthDate <= endOfMonth(dateRange.to))) {
          filteredMonthlyData[monthStr] = value;
        }
        // Important: Don't include months outside the range at all
      });

      const newSum = Object.values(filteredMonthlyData).reduce((sum, value) => sum + (value || 0), 0);
      
      return {
        ...row,
        monthlyData: filteredMonthlyData,
        sum: newSum,
        percentage: newSum ? (newSum / transaction.incomes.total) * 100 : 0
      };
    }).filter(row => {
      const descriptionMatch = row.description.toLowerCase().includes(normalizedSearch);
      const numericMatch = isNumericSearch && Object.values(row.monthlyData).some(value => 
        value === searchNumber
      );
      const sumMatch = isNumericSearch && row.sum === searchNumber;
      const percentageMatch = isNumericSearch && (
        Math.abs(row.percentage - searchNumber) < 0.1 ||
        row.percentage.toFixed(1) === searchNumber.toFixed(1)
      );

      const hasDataInRange = Object.values(row.monthlyData).some(value => value !== 0);

      return (descriptionMatch || numericMatch || sumMatch || percentageMatch) && hasDataInRange;
    });
  }, [searchFilter, dateRange]); // Add useCallback with proper dependencies
    const filteredIncomes = useMemo(
      () => filterData(transactions.incomes.tableData, transactions),
      [transactions, searchFilter, dateRange, transactions.incomes.tableData]
    );

    const filteredExpenses = useMemo(
      () => filterData(transactions.expenses.tableData, transactions),
      [transactions, searchFilter, dateRange, transactions.expenses.tableData]
  );

  const showIncomes = displayMode.includes("both") || displayMode.includes("incomes");
  const showExpenses = displayMode.includes("both") || displayMode.includes("expenses");
  
  useEffect(() => {
    setDateRange({
      from: dataRange.from,
      to: dataRange.to,
    });
  }, [dataRange.from, dataRange.to]);

  useEffect(() => {
    console.log('Search Filter Changed:', searchFilter);
    console.log('Filtered Incomes:', filteredIncomes);
    console.log('Filtered Expenses:', filteredExpenses);
    console.log('Display Mode:', displayMode);
  }, [searchFilter, filteredIncomes, filteredExpenses, displayMode]);

  const handleDateRangeChange = useCallback((values: { range: DateRange | undefined }) => {
    if (!values.range) return;
    if (!values.range.from || !values.range.to) return;
    if (
      differenceInDays(values.range.to, values.range.from) > MAX_DATE_RANGE_DAYS
    ) {
      toast.error(
        `The selected date range is too big. Max allowed range is ${MAX_DATE_RANGE_DAYS} days!`
      );
      return;
    }
    
    // Update the date range - this will trigger the useMemo recalculation
    setDateRange(values.range);
    
    // Call the upstream handler if needed
    if (onTransactionUpdate) {
      onTransactionUpdate();
    }
  }, [onTransactionUpdate]);
 
  return (
    <div className="space-y-1">
      <div id="no-print" className="flex gap-1">
        <DateRangePicker
          key={`${dataRange?.from}-${dataRange?.to}-${dateRange?.from}-${dateRange?.to}`}
          initialDateFrom={dataRange?.from!}
          initialDateTo={dataRange?.to!}
          onUpdate={handleDateRangeChange}
          
        />
        <Input
          placeholder="Search descriptions or amounts..."
          value={searchFilter}
          onChange={(e) => setSearchFilter(e.target.value)}
          className="h-8 max-w-sm"
        />
        <ToggleGroup className="h-8" type="multiple" value={displayMode} onValueChange={setDisplayMode}>
          <ToggleGroupItem className="h-7" value="both" aria-label="Show both incomes and total">
            顯示收入與支出
          </ToggleGroupItem>
          <ToggleGroupItem className="h-7" value="incomes" aria-label="Show only incomes">
            顯示收入
          </ToggleGroupItem>
          <ToggleGroupItem className="h-7" value="expenses" aria-label="Show only expenses">
            顯示支出
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div id="print-area" className="rounded-md border-none">
        <Table>
          <TableHeader className="bg-white">
          {showIncomes && (
            <TableRow>
              <TableHead className="h-8 min-w-[12rem] text-xs text-black font-bold px-2 py-0">收入明細</TableHead>
              {months.map((month) => (
                <TableHead key={month} className="h-8 text-right text-xs text-black font-bold p-0 px-[0.12rem]">
                  {month}
                </TableHead>
              ))}
              <TableHead className="h-8 text-right text-xs text-black font-bold p-0 px-1">分項合計</TableHead>
              <TableHead className="h-8 text-right text-xs text-black font-bold pr-2 py-0">%</TableHead>
            </TableRow>
          )}
          </TableHeader>
          <TableBody className="text-black bg-white">
            {/* Income Section */}
            {showIncomes && (
              <>
                {filteredIncomes.map((row, i) => (
                  <TableRow key={i}>
                    <TableCell 
                      className="sticky left-0 text-xs pl-2 py-1"
                    >
                      {row.description}
                    </TableCell>
                    {transactions.dateRange.map((month) => {
                      const value = row.monthlyData[month];
                      const isMatch = !isNaN(Number(searchFilter)) && 
                        value != Number("") && value === Number(searchFilter);
                      
                      return (
                        <TableCell 
                          key={month} 
                          className={`text-right text-xs p-1 ${
                            isMatch ? 'bg-yellow-100 font-bold' : ''
                          }`}
                        >
                          {formatCurrency(value)}
                        </TableCell>
                      );
                    })}
                    <TableCell 
                      className={`min-w-[4rem] text-right text-xs font-bold px-1 py-[0.12rem] ${
                        !isNaN(Number(searchFilter)) && 
                        row.sum === Number(searchFilter) ? 'bg-yellow-100 font-bold' : ''
                      }`}
                    >
                      {formatCurrency(row.sum)}
                    </TableCell>
                    <TableCell 
                      className={`w-8 text-right text-xs font-bold px-0 py-1 ${
                        !isNaN(Number(searchFilter)) && searchFilter != "" &&
                        (Math.abs(row.percentage - Number(searchFilter)) < 0.01 || 
                        row.percentage.toFixed(1) === Number(searchFilter).toFixed(1)) 
                          ? 'bg-yellow-100 font-bold' 
                          : ''
                      }`}
                    >
                      {row.percentage.toFixed(1)}%
                    </TableCell>
                  </TableRow>
                ))}
  
                {/* Income Total Row */}
                <TableRow className="font-bold text-xs border-t border-b bg-muted/30">
                  <TableCell className="underline sticky left-0 p-2">收入合計</TableCell>
                  {transactions.dateRange.map((month) => (
                    <TableCell 
                      key={month} 
                      className={`text-right text-xs p-1 ${
                        !isNaN(Number(searchFilter)) && 
                        transactions.incomes.monthlySums[month] != Number("") && 
                        transactions.incomes.monthlySums[month] === Number(searchFilter)
                          ? 'bg-yellow-100'
                          : ''
                      }`}
                    >
                      {formatCurrency(transactions.incomes.monthlySums[month])}
                    </TableCell>
                  ))}
                  <TableCell 
                    className={`text-right text-xs p-1 ${
                      !isNaN(Number(searchFilter)) && 
                      transactions.incomes.total != Number("") && 
                      transactions.incomes.total === Number(searchFilter)
                        ? 'bg-yellow-100'
                        : ''
                    }`}
                  >
                    {formatCurrency(transactions.incomes.total)}
                  </TableCell>
                  <TableCell />
                </TableRow>
              </>
            )}

            {/* Expense Section */}
            {showExpenses && (
              <>
                <TableRow className="text-xs font-bold py-0">
                  <TableCell className="font-bold sticky left-0 p-2">
                    支出明細
                  </TableCell>
                  {months.map((month) => (
                    <TableHead key={month} className="h-8 text-right text-xs text-black font-bold p-0 px-[0.12rem]">
                      {month}
                    </TableHead>
                  ))}
                <TableHead className="h-8 text-right text-xs text-black font-bold p-0 px-1">分項合計</TableHead>
                <TableHead className="h-8 text-right text-xs text-black font-bold pr-2 py-0">%</TableHead>
                </TableRow>
                {filteredExpenses.map((row, i) => (
                  <TableRow key={i}>
                    <TableCell 
                      className="sticky left-0 text-xs px-0 py-[0.12rem]"
                    >
                      {row.description}
                    </TableCell>
                    {transactions.dateRange.map((month) => {
                      const value = row.monthlyData[month];
                      const isMatch = !isNaN(Number(searchFilter)) && 
                        value != Number("") && value === Number(searchFilter);
                      
                      return (
                        <TableCell 
                          key={month} 
                          className={`text-right text-xs px-0 py-[0.12rem] ${
                            isMatch ? 'bg-yellow-100 font-bold' : ''
                          }`}
                        >
                          {formatCurrency(value)}
                        </TableCell>
                      );
                    })}
                    <TableCell 
                      className={`min-w-[4rem] text-right text-xs font-bold px-1 py-[0.12rem] ${
                        !isNaN(Number(searchFilter)) && 
                        row.sum === Number(searchFilter) ? 'bg-yellow-100 font-bold' : ''
                      }`}
                    >
                      {formatCurrency(row.sum)}
                    </TableCell>
                    <TableCell 
                      className={`w-8 text-right text-xs font-bold pr-2 py-[0.12rem] ${
                        !isNaN(Number(searchFilter)) && searchFilter != "" &&
                        (Math.abs(row.percentage - Number(searchFilter)) < 0.01 || 
                        row.percentage.toFixed(1) === Number(searchFilter).toFixed(1)) 
                          ? 'bg-yellow-100 font-bold' 
                          : ''
                      }`}
                    >
                      {row.percentage.toFixed(1)}%
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow className="font-bold text-xs bg-muted/30">
                  <TableCell className="underline sticky left-0 p-2">支出合計</TableCell>
                  {transactions.dateRange.map((month) => (
                    <TableCell 
                      key={month} 
                      className={`text-right text-xs px-0 py-[0.12rem] ${
                        !isNaN(Number(searchFilter)) && 
                        transactions.expenses.monthlySums[month] != Number("") && 
                        transactions.expenses.monthlySums[month] === Number(searchFilter)
                          ? 'bg-yellow-100'
                          : ''
                      }`}
                    >
                      {formatCurrency(transactions.expenses.monthlySums[month])}
                    </TableCell>
                  ))}
                  <TableCell 
                    className={`text-right text-xs px-1 py-[0.12rem] ${
                      !isNaN(Number(searchFilter)) && 
                      transactions.expenses.total != Number("") && 
                      transactions.expenses.total === Number(searchFilter)
                        ? 'bg-yellow-100'
                        : ''
                    }`}
                  >
                    {formatCurrency(transactions.expenses.total)}
                  </TableCell>
                  <TableCell />
                </TableRow>
              </>
            )}

            {/* Net Total Row */}
            {showIncomes && (
              <TableRow className="font-bold text-xs">
                <TableCell className="underline decoration-double sticky left-0 p-2">收支結算</TableCell>
                  {transactions.dateRange.map((month) => (
                    <TableCell 
                      key={month} 
                      className={cn(
                        "text-right text-xs px-0 py-1",
                        {
                          'bg-yellow-100': !isNaN(Number(searchFilter)) && 
                          (transactions.incomes.monthlySums[month] - transactions.expenses.monthlySums[month]) != Number("") && 
                            (transactions.incomes.monthlySums[month] - transactions.expenses.monthlySums[month]) === Number(searchFilter),
                          'text-destructive': 
                            (transactions.incomes.monthlySums[month] - transactions.expenses.monthlySums[month]) < 0,
                        }
                      )}
                    >
                      {formatCurrency(transactions.incomes.monthlySums[month] - transactions.expenses.monthlySums[month])}
                    </TableCell>
                  ))}
                  <TableCell 
                    className={`text-right text-xs px-1 py-1 ${
                      !isNaN(Number(searchFilter)) && 
                      (transactions.incomes.total - transactions.expenses.total) != Number("") && 
                      (transactions.incomes.total - transactions.expenses.total) === Number(searchFilter)
                        ? 'bg-yellow-100'
                        : ''
                    }`}
                  >
                    {formatCurrency(transactions.incomes.total - transactions.expenses.total)}
                  </TableCell>
                <TableCell />
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

const formatCurrency = (value: number) => {
  if (value === 0) return null;
  return new Intl.NumberFormat("zh-TW", {
    //style: "currency",
    //currency: "TWD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

// Helper functions remain the same
const checkTotalMatch = (value: number, searchNumber: number): boolean => {
  return value === searchNumber;
};

const checkMonthlySumsMatch = (monthlySums: MonthlyData, searchNumber: number): boolean => {
  return Object.values(monthlySums).some(sum => sum === searchNumber);
};

const checkNetTotalMatch = (
  incomeSums: MonthlyData,
  expenseSums: MonthlyData,
  searchNumber: number
): boolean => {
  return Object.keys(incomeSums).some(month => {
    const netTotal = incomeSums[month] - expenseSums[month];
    return netTotal === searchNumber;
  });
};
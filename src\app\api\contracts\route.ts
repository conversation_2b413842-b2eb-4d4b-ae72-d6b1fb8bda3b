import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/db';

export async function GET(req: NextRequest) {
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const contracts = await prisma.contractAnalysis.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });
    //console.log("contracts: ====>", contracts)

    return NextResponse.json(contracts);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to fetch contracts' }, { status: 500 });
  }
}
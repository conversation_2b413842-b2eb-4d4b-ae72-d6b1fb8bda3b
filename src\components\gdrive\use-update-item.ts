"use client"
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateItem() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({ values }: {
      values: { fileId: string | null; newName: string }
    }) => {
      //console.log("values", values)
      const response = await fetch(`/api/gdrive/update-item`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });
      const responseData = await response.json();
      //console.log("responseData", responseData)

      if (!response.ok || !responseData.file) {
        throw new Error(`Failed to update item.`);
      }
      
      return responseData.file;
    },
    onSuccess: ({ data }) => {
      toast.success('Item updated!');
      queryClient.invalidateQueries({ queryKey: ['googledrive'] });
      queryClient.invalidateQueries({ queryKey: ['googledrive', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update item.');
    },
  });

  return mutation;
}

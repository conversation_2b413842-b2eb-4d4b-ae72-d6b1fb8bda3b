"use client"

import React, { useState, useEffect } from 'react';
import config from '@/store/config.json';
import { getGoogleToken } from "@/actions/get-google-token";

const SimpleSignOn = ({ children }: { children: React.ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState<{message: string} | null>(null);
  const [currentURL, setCurrentURL] = useState('');

  useEffect(() => {
    if (!currentURL) {
      setCurrentURL(window.location.href);
    } 
  }, [currentURL]);
 
  useEffect(() => {
    const fetchData = async () => {
      const { accessToken } = await getGoogleToken();
      if (accessToken) {
        setIsAuthenticated(true);
       }
    }
    fetchData();
  }, []);

  const handleSignOn = async () => {
    try {
      // Redirect the user to the Google authorization endpoint
      window.location.href = 'https://accounts.google.com/o/oauth2/v2/auth?access_type=offline&prompt=consent&response_type=code&client_id='+config.api.client_id+'&redirect_uri='+ currentURL +'login&scope='+config.api.scopes;
    } catch (err: any) {
      setError(err);
    }
  };


  if (isAuthenticated) {
      console.log('is authenticated')
    return (
      <>
        {children}
      </>
    );
  }

  return (
    <div>
      {error && <div>An error occurred: {error.message}</div>}
      <button onClick={handleSignOn}>Sign On with Google</button>
    </div>
  );
};

export default SimpleSignOn;

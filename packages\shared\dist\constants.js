"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PROGRAMMING_LANGUAGES = exports.DEFAULT_INPUTS = exports.CONTEXT_DOCUMENTS_NAMESPACE = exports.OC_WEB_SEARCH_RESULTS_MESSAGE_KEY = exports.OC_HIDE_FROM_UI_KEY = exports.OC_SUMMARIZED_MESSAGE_KEY = void 0;
exports.OC_SUMMARIZED_MESSAGE_KEY = "__oc_summarized_message";
exports.OC_HIDE_FROM_UI_KEY = "__oc_hide_from_ui";
exports.OC_WEB_SEARCH_RESULTS_MESSAGE_KEY = "__oc_web_search_results_message";
exports.CONTEXT_DOCUMENTS_NAMESPACE = ["context_documents"];
exports.DEFAULT_INPUTS = {
    highlightedCode: undefined,
    highlightedText: undefined,
    next: undefined,
    language: undefined,
    artifactLength: undefined,
    regenerateWithEmojis: undefined,
    readingLevel: undefined,
    addComments: undefined,
    addLogs: undefined,
    fixBugs: undefined,
    portLanguage: undefined,
    customQuickActionId: undefined,
    webSearchEnabled: undefined,
    webSearchResults: undefined,
};
exports.PROGRAMMING_LANGUAGES = [
    {
        language: "typescript",
        label: "TypeScript",
    },
    {
        language: "javascript",
        label: "JavaScript",
    },
    {
        language: "cpp",
        label: "C++",
    },
    {
        language: "java",
        label: "Java",
    },
    {
        language: "php",
        label: "PHP",
    },
    {
        language: "python",
        label: "Python",
    },
    {
        language: "html",
        label: "HTML",
    },
    {
        language: "sql",
        label: "SQL",
    },
    {
        language: "json",
        label: "JSON",
    },
    {
        language: "rust",
        label: "Rust",
    },
    {
        language: "xml",
        label: "XML",
    },
    {
        language: "clojure",
        label: "Clojure",
    },
    {
        language: "csharp",
        label: "C#",
    },
    {
        language: "other",
        label: "Other",
    },
];

import { TaskStatus } from "@prisma/client";
import { useQuery } from "@tanstack/react-query";
import { getTasks } from "@/actions/tasks/get-tasks";

interface UseTasksProps {
  workspaceId: string
  projectId?: string | null
  assigneeId?: string | null
  status?: TaskStatus | null
  search?: string | null
  startDate?: string | null
  dueDate?: string | null
}

export const useGetTasks = ({ 
  workspaceId,
  projectId,
  assigneeId,
  status,
  search,
  startDate,
  dueDate, 
}: UseTasksProps) => {
  const { data, isLoading, error } = useQuery({
    queryKey: [
      "tasks", 
      workspaceId, 
      projectId, 
      assigneeId, 
      status, 
      search, 
      startDate,
      dueDate,
    ],
    queryFn: async () => {
      const response = await getTasks({ 
        workspaceId,
        projectId: projectId || undefined,
        assigneeId: assigneeId || undefined,
        status: status || undefined,
        search: search || undefined,
        startDate: startDate || undefined,
        dueDate: dueDate || undefined,
      });
      console.log("Raw response:", response);
      
      if (!response.data) {
        throw new Error(response.error || "Failed to fetch tasks");
      }
      
      return response.data;
    },
    enabled: !!workspaceId,
  });

  return {
    tasks: data?.tasks || [],
    populatedTasks: data?.populatedTasks || [],
    isLoading,
    error,
  };
};
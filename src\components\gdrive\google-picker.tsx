"use client";
import GoogleP<PERSON> from "react-google-picker";
import { FaGoogle } from "react-icons/fa";
import { But<PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";

export default function GoogleDrive() {
  const CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!;
  const API_KEY = process.env.NEXT_PUBLIC_GOOGLE_DRIVE_PICKER!;
  const APP_ID = "comfymindslab";
  const scope = [
    "https://www.googleapis.com/auth/drive.readonly",
    "https://www.googleapis.com/auth/drive.photos.readonly",
  ];
  const [oauthToken, setOauthToken] = useState<string | null>(null);

  // Ensure gapi is loaded
  useEffect(() => {
    const loadGoogleAPI = () => {
      if (window.gapi) {
        window.gapi.load("auth", () => {
          // Use gapi.auth for the new authentication system
          window.gapi.auth.authorize(
            {
              client_id: CLIENT_ID,
              scope: scope.join(" "),
              immediate: false, // true means silent login
            },
            (authResult: any) => {
              if (authResult && !authResult.error) {
                setOauthToken(authResult.access_token); // Store the OAuth token
              } else {
                console.error("Error authenticating:", authResult);
              }
            }
          );
        });
      }
    };
    loadGoogleAPI();
  }, [CLIENT_ID]);

  const handleAuth = async () => {
    if (!window.gapi) return;
    try {
      window.gapi.auth.authorize(
        {
          client_id: CLIENT_ID,
          scope: scope.join(" "),
          immediate: false, // true means silent login
        },
        (authResult: any) => {
          if (authResult && !authResult.error) {
            setOauthToken(authResult.access_token); // Store the OAuth token
          } else {
            console.error("Google Auth failed:", authResult);
          }
        }
      );
    } catch (error) {
      console.error("Error during Google Auth:", error);
    }
  };

  return (
    <div className="text-center">
      {!oauthToken ? (
        <Button variant={"outline"} size={"sm"} onClick={handleAuth}>
          <FaGoogle color="red" /> Continue with Google
        </Button>
      ) : (
        <GooglePicker
          clientId={CLIENT_ID}
          developerKey={API_KEY}
          scope={scope}
          navHidden
          mimeTypes={["image/png", "image/jpeg", "image/jpg"]}
          createPicker={(google) => {
            const picker = new google.picker.PickerBuilder()
              .addView(new google.picker.View(google.picker.ViewId.DOCS_IMAGES))
              .addView(new google.picker.DocsUploadView())
              .setOAuthToken(oauthToken) // Use OAuth token
              .setDeveloperKey(API_KEY)
              .setAppId(APP_ID)
              .setCallback((data: { action: any; docs: { id: any }[] }) => {
                if (data.action === google.picker.Action.PICKED) {
                  var fileId = data.docs[0].id;
                  alert("The user selected: " + fileId);
                }
              })
              .enableFeature(google.picker.Feature.NAV_HIDDEN)
              .enableFeature(google.picker.Feature.MULTISELECT_ENABLED);

            picker.build().setVisible(true);
          }}
        />
      )}
    </div>
  );
}

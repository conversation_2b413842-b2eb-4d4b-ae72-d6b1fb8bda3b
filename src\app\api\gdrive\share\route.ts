import { google } from 'googleapis';
import { NextResponse } from 'next/server';
import { authenticateGoogleOAuth2 } from '../authenticate';

export async function POST(request: Request) {
  try {
    const { action, fileId, permissionId, email, role, type } = await request.json();

    if (!fileId) {
      return NextResponse.json(
        { error: "File ID is required" },
        { status: 400 }
      );
    }

    const auth = await authenticateGoogleOAuth2();
    if (!auth) {
      throw new Error("Authentication failed");
    }

    const drive = google.drive({ version: "v3", auth });

    let response;

    switch (action) {
      case "fetch-permissions":
        response = await drive.permissions.list({
          fileId,
          fields: "permissions(id, displayName, type, role, photoLink, emailAddress, role)",
        });
        break;
      case "add-permission":
        if (!email || !role) throw new Error("Email and role are required");
        response = await drive.permissions.create({
          fileId,
          requestBody: { type: "user", role, emailAddress: email },
          fields: 'id, share, permission, webViewLink',
          sendNotificationEmail: true,
        });
        break;
      case "update-permission":
        if (!permissionId || !role) throw new Error("Permission ID and role are required");
        response = await drive.permissions.update({
          fileId,
          permissionId,
          requestBody: { role },
        });
        break;
      case "access-permission":
        if (!permissionId || !role || !type) throw new Error("Permission ID, role and type are required");
        if (type === "user") {
          // Delete the "anyone" permission
          response = await drive.permissions.delete({
            fileId: fileId,
            permissionId: permissionId
          }); 

          break;
        } else if (type === "anyone") {
          response = await drive.permissions.create({
            fileId: fileId,
            requestBody: {
              role: 'reader',
              type: 'anyone'
            }
          });

          break;
        } else {
          throw new Error("Invalid type");
        }
        break;
      case "delete-permission":
        if (!permissionId) throw new Error("Permission ID is required");
        response = await drive.permissions.delete({ fileId, permissionId });
        break;
      case "transfer-ownership":
        if (!permissionId) throw new Error("Permission ID is required");
        response = await drive.permissions.update({
          fileId,
          permissionId,
          transferOwnership: true, 
          requestBody: { role: "owner" },
        });
        break;
      default:
        throw new Error("Invalid action");
    }

    return NextResponse.json({ success: true, data: response.data });
  } catch (error: any) {
    console.error("Error:", error);
    const errorMessage = error.response?.data?.error?.message || error.message;
    return NextResponse.json(
      { error: errorMessage },
      { status: error.response?.status || 500 }
    );
  }
}

import { SpreadsheetData } from "@/lib/types";
import { Trash2 } from "lucide-react";

interface DeleteSpreadsheetButtonProps {
  currentSheetIndex: number;
  setCurrentSheetIndex: (fn: (i: number) => number) => void;
  spreadsheets: SpreadsheetData[];
  setSpreadsheets: (fn: (spreadsheets: SpreadsheetData[]) => SpreadsheetData[]) => void;
}

export function DeleteSpreadsheetButton({
  currentSheetIndex,
  setCurrentSheetIndex,
  spreadsheets,
  setSpreadsheets,
}: DeleteSpreadsheetButtonProps) {
  const handleDeleteSpreadsheet = () => {
    if (spreadsheets.length <= 1) {
      alert("Cannot delete the last remaining sheet.");
      return;
    }

    setSpreadsheets((prevSpreadsheets) => {
      const updatedSheets = prevSpreadsheets.filter((_, index) => index !== currentSheetIndex);
      setCurrentSheetIndex((prevIndex) =>
        prevIndex > 0 ? prevIndex - 1 : 0 // Adjust index after deletion
      );
      return updatedSheets;
    });
  };

  return (
    <button
      onClick={handleDeleteSpreadsheet}
    >
      <Trash2 className="h-5 w-5 p-[0.04rem] text-black dark:text-white hover:text-red-600" />
    </button>
  );
}

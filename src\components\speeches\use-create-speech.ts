import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createSpeech } from "@/actions/speeches/create-speech";
import { z } from "zod";
import { createSpeechSchema } from "@/components/speeches/schemas";
import { toast } from "sonner";

export function useCreateSpeech() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof createSpeechSchema>) => {
      const response = await createSpeech(values);
      if (!response?.success || !response?.data) {
        throw new Error(response.error || "Failed to create speech.");
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success("Speech created successfully!");
      queryClient.invalidateQueries({ queryKey: ["speeches"] });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create speech.");
    },
  });

  return mutation;
}

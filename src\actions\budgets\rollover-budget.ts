"use server";

import { prisma } from "@/lib/db";
import { Prisma } from '@prisma/client';

type BudgetRolloverWithRelations = Prisma.BudgetRolloverGetPayload<{
  include: {
    category: true;
    previousBudget: true;
  }
}>;

export async function processBudgetRollover(userId: string, accountId: string) {
  try {
    const now = new Date();
    
    const endedBudgets = await prisma.budget.findMany({
      where: {
        userId,
        endDate: { lt: now },
        rolloversPrevious: { none: {} }
      },
      include: {
        categories: {
          include: {
            category: true,
          }
        }
      }
    });

    const rollovers: BudgetRolloverWithRelations[][] = [];

    for (const budget of endedBudgets) {
      const budgetRollovers: BudgetRolloverWithRelations[] = [];

      for (const catBudget of budget.categories) {
        const budgetedAmount = Number(catBudget.amount);
        
        const totalExpenses = await prisma.expense.aggregate({
          where: {
            userId,
            categoryId: catBudget.categoryId,
            date: {
              gte: budget?.startDate!,
              lte: budget?.endDate!
            }
          },
          _sum: {
            amount: true
          }
        });

        const rolloverAmount = budgetedAmount - Number(totalExpenses._sum.amount || 0);
        const rolloverPercentage = (rolloverAmount / budgetedAmount) * 100;

        const significantThreshold = 0.01; // 1% threshold
        if (Math.abs(rolloverPercentage) > significantThreshold) {
          const rollover = await prisma.budgetRollover.create({
            data: {
              userId,
              categoryId: catBudget.categoryId,
              previousBudgetId: budget.id,
              amount: rolloverAmount,
              periodStart: budget?.startDate!,
              periodEnd: budget?.endDate!,
              rolloverPercentage,
              accountId
            },
            include: {
              category: true,
              previousBudget: true
            }
          });

          budgetRollovers.push(rollover);
        }
      }

      if (budgetRollovers.length > 0) {
        rollovers.push(budgetRollovers);
      }
    }

    return rollovers;
  } catch (error) {
    console.error('Budget rollover process failed:', error);
    throw error;
  }
}

// Automatically creates the next budget period
export async function createNextBudgetPeriod(
  userId: string, 
  options?: {
    autoCreateBudget?: boolean;
    budgetTemplate?: Partial<Prisma.BudgetCreateInput>;
    accountId?: string;
  }
) {
  // Check user preferences or default setting
  const autoCreateBudget = options?.autoCreateBudget ?? true;
  
  if (!autoCreateBudget) {
    return null;
  }

  // Find a default account if no account is specified
  const accountId = options?.accountId ?? 
  (await prisma.bankAccount.findFirst({
    where: { 
      userId,
      //isDefault: true 
    }
  }))?.id;

  if (!accountId) {
    throw new Error('No account found for budget creation');
  }

  const lastBudget = await prisma.budget.findFirst({
    where: { userId },
    orderBy: { endDate: 'desc' }
  });

  const nextStartDate = lastBudget ? 
    new Date(lastBudget?.endDate!.getTime() + 24 * 60 * 60 * 1000) : // Day after last budget
    new Date(); // Or current date

  return prisma.budget.create({
    data: {
      userId,
      startDate: nextStartDate,
      endDate: new Date(nextStartDate.getFullYear(), nextStartDate.getMonth() + 1, 0),
      accountId: accountId,
      //...options?.budgetTemplate, // Allow custom budget creation
    }
  });
}

export async function applyRolloversToNextBudget(
  rollovers: BudgetRolloverWithRelations[][], 
  nextBudget: Prisma.BudgetGetPayload<{}> | null,
  options?: {
    autoApplyRollovers?: boolean;
    rolloverThreshold?: number;
  }
) {
  const autoApplyRollovers = options?.autoApplyRollovers ?? true;
  const rolloverThreshold = options?.rolloverThreshold ?? 0; // $10 minimum

  if (!autoApplyRollovers || !nextBudget) {
    return;
  }

  // Fetch the previous budget to get original category budget amounts
  const previousBudget = await prisma.budget.findFirst({
    where: { id: rollovers[0][0].previousBudgetId! },
    include: {
      categories: true
    }
  });

  for (const rolloverGroup of rollovers) {
    for (const rollover of rolloverGroup) {
      if (Math.abs(Number(rollover.amount)) >= rolloverThreshold) {
        // Find the original budget amount for this category
        const originalCategoryBudget = previousBudget?.categories.find(
          cat => cat.categoryId === rollover.categoryId
        );

        const originalAmount = Number(originalCategoryBudget?.amount || 0);

        // Create or update category budget
        const categoryBudget = await prisma.categoryBudget.upsert({
          where: {
            budgetId_categoryId: {
              budgetId: nextBudget.id,
              categoryId: rollover.categoryId
            }
          },
          update: {
            amount: originalAmount  // Refill to original budget amount
          },
          create: {
            budgetId: nextBudget.id,
            categoryId: rollover.categoryId,
            amount: originalAmount
          }
        });

        // Create an expense to represent the budget rollover
        await prisma.expense.create({
          data: {
            userId: rollover.userId,
            categoryId: rollover.categoryId,
            accountId: rollover?.accountId!,
            amount: rollover.amount,
            date: nextBudget?.startDate!,
            type: 'BUDGET_ROLLOVER',
            description: `Budget rollover from previous period`
          }
        });

        // Link rollover to next budget
        await prisma.budgetRollover.update({
          where: { id: rollover.id },
          data: { 
            nextBudgetId: nextBudget.id 
          }
        });
      }
    }
  }
}

// Updated scheduled job
export async function scheduledBudgetRollover(accountId: string) {
  const users = await prisma.user.findMany();
  
  for (const user of users) {
    try {
      // Process rollovers
      const rollovers = await processBudgetRollover(user.id, accountId);
      
      // Optionally create next budget period
      const options = {
        autoCreateBudget: true,
        accountId: "" // to be add
      }
      const nextBudget = await createNextBudgetPeriod(user.id);
      
      // Apply rollovers to next budget
      if (rollovers && nextBudget) {
        await applyRolloversToNextBudget(rollovers, nextBudget);
      }
    } catch (error) {
      console.error(`Budget rollover failed for user ${user.id}:`, error);
      // Optionally send notification to admin or user
    }
  }
}

export async function getBudgetRolloverHistory(userId: string) {
  return prisma.budgetRollover.findMany({
    where: { userId },
    include: {
      category: true,
      previousBudget: true,
      nextBudget: true
    },
    orderBy: { createdAt: 'desc' }
  });
}
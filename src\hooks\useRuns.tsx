export function useRuns() {
  /**
   * Generates a public shared run ID for the given run ID.
   */
   /*const shareRun = async (runId: string): Promise<string | undefined> => {
   const res = await fetch("/api/runs/share", {
      method: "POST",
      body: JSON.stringify({ runId }),
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      return;
    }

    const { sharedRunURL } = await res.json();
    return sharedRunURL;
  };*/
  
  /**
   * Simulated version of generating a public shared run URL for the given run ID.
   */
  const shareRun = async (runId: string): Promise<string | undefined> => {
    // Simulate a network delay of 500ms, then return a fake URL.
    return new Promise((resolve) => {
      setTimeout(() => {
        const fakeURL = `https://smith.langchain.com/public/${runId}/shared`;
        resolve(fakeURL);
      }, 500);
    });
  };

  return {
    shareRun,
  };
}
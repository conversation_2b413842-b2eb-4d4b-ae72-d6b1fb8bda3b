const { CohereClient } = require("cohere-ai");

const apiKey = process.env.COHERE_API_KEY;

if (!apiKey) {
  throw Error("COHERE_API_KEY is not set");
}

const cohere = new CohereClient({
  token: api<PERSON>ey,
});

export default cohere;

export async function getEmbedding(
  textOrTexts: string | string[],
  model: string = "embed-multilingual-v3.0"
): Promise<number[] | number[][]> {
  // Normalize input to an array
  const texts = Array.isArray(textOrTexts) ? textOrTexts : [textOrTexts];

  if (!texts.every((text) => typeof text === "string")) {
    throw new Error("Input must be a string or an array of strings.");
  }

  // Call Cohere's embedding API
  const embed = await cohere.embed({
    texts,
    model,
    inputType: "search_query",
  });

  const embeddings = embed.embeddings;

  if (!embeddings || embeddings.length !== texts.length) {
    throw new Error("Error generating embeddings for some or all inputs.");
  }

  // Return single embedding if input was a single string
  return Array.isArray(textOrTexts) 
    ? (embeddings as number[][]) 
    : (embeddings[0] as number[]);
}

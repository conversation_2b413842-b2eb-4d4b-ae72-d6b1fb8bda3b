import { NextRequest, NextResponse } from "next/server";
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/db';

// Get all banners or a specific banner
export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      // If no ID is provided, return all banners for the user
      const banners = await prisma.banner.findMany({
        where: {
          OR: [
            { userId },
            { shared: true }
          ]
        }
      });
      return NextResponse.json({ banners });
    }

    const banner = await prisma.banner.findFirst({
      where: {
        id,
        OR: [
          { userId },
          { shared: true }
        ]
      }
    });

    if (!banner) {
      return NextResponse.json({ error: "Banner not found" }, { status: 404 });
    }

    return NextResponse.json({ banner });
  } catch (error) {
    return NextResponse.json({ error: error?.toString() || "Unknown error" }, { status: 500 });
  }
}

// Create a new banner
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await req.json();
    const { title, type, size, fontWeight, color, description, shared } = data;
    if (!title || !color) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }
    const banner = await prisma.banner.create({
      data: {
        userId,
        title,
        type,
        size,
        fontWeight,
        color,
        description: description || null,
        shared: shared ?? false,
      },
    });
    return NextResponse.json({ success: true, banner });
  } catch (error) {
    return NextResponse.json({ error: error?.toString() || "Unknown error" }, { status: 500 });
  }
}

// Update an existing banner
export async function PUT(req: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    if (!id) {
      return NextResponse.json({ error: "Banner ID is required" }, { status: 400 });
    }

    // Check if banner exists and belongs to user
    const existingBanner = await prisma.banner.findFirst({
      where: {
        id,
        userId,
      },
    });

    if (!existingBanner) {
      return NextResponse.json({ error: "Banner not found or unauthorized" }, { status: 404 });
    }    const data = await req.json();
    const { title, color, description, shared, type, size, fontWeight } = data;

    const updatedBanner = await prisma.banner.update({
      where: { id },
      data: {
        title: title || existingBanner.title,
        type: type || existingBanner.type,
        size: size || existingBanner.size,
        fontWeight: fontWeight || existingBanner.fontWeight,
        color: color || existingBanner.color,
        description: description ?? existingBanner.description,
        shared: shared ?? existingBanner.shared,
      },
    });

    return NextResponse.json({ success: true, banner: updatedBanner });
  } catch (error) {
    return NextResponse.json({ error: error?.toString() || "Unknown error" }, { status: 500 });
  }
}

// Delete a banner
export async function DELETE(req: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    if (!id) {
      return NextResponse.json({ error: "Banner ID is required" }, { status: 400 });
    }

    // Check if banner exists and belongs to user
    const existingBanner = await prisma.banner.findFirst({
      where: {
        id,
        userId,
      },
    });

    if (!existingBanner) {
      return NextResponse.json({ error: "Banner not found or unauthorized" }, { status: 404 });
    }

    await prisma.banner.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: error?.toString() || "Unknown error" }, { status: 500 });
  }
}


import { TaskStatus } from "@prisma/client";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { useGetProjects } from "@/components/projects/use-get-projects";
import { useGetMembers } from "@/components/workspaces/use-get-members";
import { useTaskFilters } from "@/components/tasks/use-task-filters";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectSeparator,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select"
import { Frame, ListCheckIcon, UserIcon } from "lucide-react";


interface DataFiltersProps {
  hideProjectFilter?: boolean;
}

export const DataFilters = ({
  hideProjectFilter
}: DataFiltersProps) => {
  const workspaceId = useWorkspaceId();
  
  const { projects, isLoading: isLoadingProjects } = useGetProjects({
    workspaceId,
  });
  const { members, isLoading: isLoadingMembers } = useGetMembers({
    workspaceId,
  });

  const isLoading = isLoadingProjects || isLoadingMembers || !workspaceId;

  const projectOptions = projects?.map((project) => ({
    value: project.id,
    label: project.name,
  }));

  const memberOptions = members?.map((member) => ({
    value: member.id,
    label: member.user.name,
  }));

  const [{
    status,
    assigneeId,
    projectId,
    startDate,
    dueDate,
  }, setFilters] = useTaskFilters();

  const onStatusChange = (value: string) => {
    setFilters({ status: value === "all" ? null: value as TaskStatus });
  }
  const onAssigneeChange = (value: string) => {
    setFilters({ assigneeId: value === "assigneeId" ? null: value as string });
  }
  const onProjectChange = (value: string) => {
    setFilters({ projectId: value === "projectId" ? null : value as string });
  }

  if (isLoading) return null;


  return (
    <div className="flex flex-col lg:flex-row gap-2">
      <Select
        defaultValue={status ?? "all"}
        onValueChange={(value) => {onStatusChange(value)}}
      >
        <SelectTrigger className="w-full lg:w-auto h-8">
          <div className="flex items-center pr-2">
            <ListCheckIcon className="size-4 mr-2" />
            <SelectValue placeholder="All statuses" />
          </div>
        </SelectTrigger>
        <SelectContent className="max-w-[160px]">
          <SelectItem value="all">All statuses</SelectItem>
          <SelectSeparator />
          <SelectItem value={TaskStatus.BACKLOG}>Backlog</SelectItem>
          <SelectItem value={TaskStatus.IN_PROGRESS}>In Progress</SelectItem>
          <SelectItem value={TaskStatus.IN_REVIEW}>In Review</SelectItem>
          <SelectItem value={TaskStatus.TODO}>Todo</SelectItem>
          <SelectItem value={TaskStatus.DONE}>Done</SelectItem>
        </SelectContent>
      </Select>
      <Select
        defaultValue={assigneeId ?? "assigneeId"}
        onValueChange={(value) => {onAssigneeChange(value)}}
      >
        <SelectTrigger className="w-full lg:w-auto h-8">
          <div className="flex items-center pr-2">
            <UserIcon className="size-4 mr-2" />
            <SelectValue placeholder="All assignees" />
          </div>
        </SelectTrigger>
        <SelectContent className="max-w-[160px]">
          <SelectItem value="assigneeId">All assignee</SelectItem>
          <SelectSeparator />
          {memberOptions?.map((member) => (
            <SelectItem key={member.value} value={member.value}>
              {member.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {!hideProjectFilter && (
        <Select
          defaultValue={projectId ?? "projectId"}
          onValueChange={(value) => {onProjectChange(value)}}
        >
          <SelectTrigger className="w-full lg:w-auto h-8">
            <div className="flex items-center pr-2">
              <Frame className="size-3 mr-2" />
              <SelectValue placeholder="All projects" />
            </div>
          </SelectTrigger>
          <SelectContent className="max-w-[160px]">
            <SelectItem value="projectId">All projects</SelectItem>
            <SelectSeparator />
            {projectOptions?.map((project) => (
              <SelectItem key={project.value} value={project.value}>
                {project.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      <div className="flex flex-row items-center gap-x-2">
        <label className='text-sm'>startDate</label>
        <Input
          id="startDate"
          className="h-8"
          type="date"
          name="startDate"
          onChange={(e) => setFilters((prevFilters) => ({
            ...prevFilters,
            startDate: e.target.value || null
          }))}
          value={startDate || ''}
        />
      </div>
      <div className="flex flex-row items-center gap-x-2">
        <label className='text-sm'>dueDate</label>
        <Input
          id="dueDate"
          className="h-8"
          type="date"
          name="dueDate"
          onChange={(e) => setFilters((prevFilters) => ({
            ...prevFilters,
            dueDate: e.target.value || null
          }))}
          value={dueDate || ''}
        />
      </div>
    </div>
  )
}
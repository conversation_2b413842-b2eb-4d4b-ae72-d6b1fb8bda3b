import * as z from "zod";

export const createSpeechSchema = z.object({
  title: z.string().min(1, "Title is required."),
  description: z.string().optional(),
  workspaceId: z.string().trim().min(1, "Workspace ID is required"),
  projectId: z.string().trim().min(1, "Project ID is required"),
  recordings: z.array(z.object({
    url: z.string().url("Invalid URL."),
    duration: z.number().positive("Duration must be positive."),
  })).optional(),
  transcriptions: z.array(z.object({
    text: z.string().min(1, "Text is required."),
    language: z.string().min(2, "Language code is required."),
  })).optional(),
  analyses: z.array(z.object({
    summary: z.string().optional(),
    keyPoints: z.any().optional(),
    sentiment: z.string().optional(),
  })).optional(),
});

export const updateSpeechSchema = z.object({
  id: z.string().min(1, "Speech ID is required."),
  title: z.string().min(1, "Title is required.").optional(),
  description: z.string().optional(),
  workspaceId: z.string().trim().min(1, "Workspace ID is required").optional(),
  projectId: z.string().trim().min(1, "Project ID is required").optional(),
  recordings: z.array(z.object({
    id: z.string().optional(), // For existing recordings
    url: z.string().url("Invalid URL."),
    duration: z.number().positive("Duration must be positive."),
  })).optional(),
  transcriptions: z.array(z.object({
    id: z.string().optional(), // For existing transcriptions
    text: z.string().min(1, "Text is required."),
    language: z.string().min(2, "Language code is required."),
  })).optional(),
  analyses: z.array(z.object({
    id: z.string().optional(), // For existing analyses
    summary: z.string().optional(),
    keyPoints: z.any().optional(),
    sentiment: z.string().optional(),
  })).optional(),
});
"use client"

import { useState, useEffect } from "react"
import { getAvailableSlots } from "@/actions/tasks/schedule-meeting"
import { useGetAvailableSlots } from "./use-get-available-slots"
import { useUpdateCalendarEvents } from "./use-update-calendar-events"
import { DeleteMeetingButton } from "./delete-meeting-button"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, <PERSON>, Clock, <PERSON>, Calendar as CalendarIcon } from 'lucide-react'
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"


interface Attendee {
  email: string;
}

interface FormState {
  title: string;
  message: string;
  attendees: Attendee[];
  selectedDate: Date | undefined;
}

interface CalendarEvent {
  id: string;
  summary?: string;
  description?: string;
  start?: {
    dateTime: string;
    timeZone: string;
  };
  end?: {
    dateTime: string;
    timeZone: string;
  };
  attendees?: Attendee[];
  status?: string;
}

interface UpdateMeetingFormProps {
  onCancel?: () => void;
  initialValues: CalendarEvent;
}

export const UpdateMeetingForm = ({ initialValues, onCancel }: UpdateMeetingFormProps) => {
  const [formState, setFormState] = useState<FormState>({
    title: initialValues?.summary || "",
    message: initialValues?.description || "",
    attendees: initialValues?.attendees || [],
    selectedDate: initialValues?.start ? new Date(initialValues.start.dateTime) : undefined,
  });
  const calculateDuration = () => {
    if (initialValues?.start?.dateTime && initialValues?.end?.dateTime) {
      const start = new Date(initialValues.start.dateTime);
      const end = new Date(initialValues.end.dateTime);
      const duration = (end.getTime() - start.getTime()) / (1000 * 60); // Convert ms to minutes
      return Math.max(0, duration); // Ensure non-negative duration
    }
    return 20; // Default duration
  };
  const [slots, setAvailableSlots] = useState<string[]>()
  const [timetableError, setTimetableError] = useState<string>("")
  const [isTimeTableLoading, setIsTimeTableLoading] = useState(false)
  const [showMessage, setShowMessage] = useState(false)
  const [newAttendee, setNewAttendee] = useState("")
  const [duration, setDuration] = useState<number>(calculateDuration()); // Duration in minutes
  const [minGapBetweenMeetings, setMinGapBetweenMeetings] = useState(5) // Default 5 minutes gap
  const [workingHours, setWorkingHours] = useState({
    start: 9,
    end: 17,
    slotDuration: 20,
    minGapBetweenMeetings: 5,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  })
  const [isAlertOpen, setIsAlertOpen] = useState<boolean>(false);

  useEffect(() => {
    if (initialValues) {
      try {
        const startDate = initialValues.start?.dateTime ? 
          new Date(initialValues.start.dateTime) : undefined;
  
        setFormState((prevState) => ({
          ...prevState,
          title: initialValues.summary || "",
          message: initialValues.description || "",
          attendees: initialValues.attendees || [],
          selectedDate: startDate
        }));
        
        if (initialValues.start?.dateTime && initialValues.end?.dateTime) {
          const start = new Date(initialValues.start.dateTime);
          const end = new Date(initialValues.end.dateTime);
          const durationInMinutes = Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
          setDuration(durationInMinutes);
        }
      } catch (error) {
        console.error("Error parsing initial values:", error);
      }
      console.log("formState", formState)
      console.log("initialValues", initialValues)
      console.log("duration", duration)
    }
  }, [initialValues]);


  useEffect(() => {
    setWorkingHours(prevState => ({
      ...prevState,
      slotDuration: duration,
      minGapBetweenMeetings: minGapBetweenMeetings,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }))
  }, [duration, minGapBetweenMeetings])
 
  const { availableSlots, isLoading: isGetAvailableSlotsLoading } = useGetAvailableSlots(workingHours);

  useEffect(() => {
    if (availableSlots) {
      setAvailableSlots(availableSlots);
    }
  }, [availableSlots]);

  const { mutate, isPending: isUpdateMeetingPending } = useUpdateCalendarEvents();

  // Update the handlers to work with the proper types
  const handleAddAttendee = () => {
    if (newAttendee && /\S+@\S+\.\S+/.test(newAttendee)) {
      setFormState(prevState => ({
        ...prevState,
        attendees: [...prevState.attendees, { email: newAttendee }]
      }));
      setNewAttendee("");
    }
  };

  const handleRemoveAttendee = (emailToRemove: string) => {
    setFormState(prevState => ({
      ...prevState,
      attendees: prevState.attendees.filter(attendee => attendee.email !== emailToRemove)
    }));
  };

  // For handling date selection
  const handleDayPickerSelect = async (date: Date | undefined) => {
    setTimetableError("");
    setShowMessage(false);

    if (!date) {
      setFormState(prevState => ({
        ...prevState,
        selectedDate: undefined
      }));
      setAvailableSlots([]);
      return;
    }

    // Update a new date object for today, with time set to 00:00:00
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // weekends (Sunday and Saturday, Past date)
    if (date.getDay() === 0 || date.getDay() === 6 || date < today) {
      setFormState(prevState => ({
        ...prevState,
        selectedDate: undefined
      }));
      setAvailableSlots([]);
      return;
    }

    setFormState(prevState => ({
      ...prevState,
      selectedDate: date
    }));
    setIsTimeTableLoading(true);

    try {
      const availableSlots = await getAvailableSlots(date, workingHours);
      setAvailableSlots(availableSlots);
    } catch (error) {
      console.error('Error fetching slots:', error);
      setTimetableError("Failed to fetch available slots. Please try again.");
    } finally {
      setIsTimeTableLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormState((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const form = event.currentTarget;
    const formData = new FormData(form);
    
    if (!formData.get("timetable") || !formState.selectedDate) {
      setTimetableError("Please select a date and time slot");
      return;
    }

    try {
      formData.set('selectedCalendarDate', formState.selectedDate.toLocaleDateString());
      mutate({ 
        eventId: initialValues.id,
        values: {
        summary: formState.title,
        description: formState.message,
        status: initialValues.status,
        start: { dateTime: formState.selectedDate?.toISOString(), timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone },
        end: { dateTime: new Date(formState.selectedDate?.getTime() + duration * 60000).toISOString(), timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone },
        //attendees: attendees.map(attendee => ({ email: attendee })),
      }}, {
        onSuccess: ({ data: meeting }) => {
          console.log("Meeting data:", meeting);
          setFormState((prevState) => ({
            ...prevState,
            message: meeting?.message ?? "Meeting scheduled!",
            title: meeting?.title ?? prevState.title,
            attendees: meeting?.attendees ?? prevState.attendees,
          }));
          setShowMessage(true);
          setTimetableError("");
          setAvailableSlots([]);

          form.reset();
          if (meeting.data) {
            onCancel?.()            
          }
        }
      });

    } catch (error) {
      console.error('Form submission error:', error);
      setTimetableError("Failed to update meeting. Please try again.");
    }
  };

  const resetForm = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault()
    const form = event.currentTarget.form
    if (form) {
      form.reset()
    }
  }

  const isLoading = isUpdateMeetingPending || isGetAvailableSlotsLoading

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl flex items-center gap-2">
          <CalendarIcon className="h-6 w-6" />
          Schedule a Meeting
        </CardTitle>
        <CardDescription>
          Select a date, time, and add attendees to schedule your meeting
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-8">
          {showMessage && formState.message && (
            <Alert variant="default">
              <AlertDescription>{formState.message}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left Column - Calendar and Time Selection */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                {formState.selectedDate ? format(formState.selectedDate, 'MMMM d, yyyy') : "Select a Date"}
              </h3>
              {/* Calendar */}
              <div className="w-fit bg-muted rounded-lg">
                <Calendar
                  mode="single"
                  selected={formState.selectedDate}
                  onSelect={handleDayPickerSelect}
                  className="rounded-md border"
                />
              </div>

              <div className="space-y-4">   
                {/* Duration selector */}
                <div className="space-y-2">
                  <Label>Duration</Label>
                  <Select
                    value={duration.toString()}
                    onValueChange={(value) => setDuration(parseInt(value))}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Select project" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={'15'}>
                        15 minutes
                      </SelectItem>
                      <SelectItem value={'20'}>
                        20 minutes
                      </SelectItem>
                      <SelectItem value={'30'}>
                        30 minutes
                      </SelectItem>
                      <SelectItem value={'45'}>
                        45 minutes
                      </SelectItem>
                      <SelectItem value={'60'}>
                        1 hour
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Minimum gap between meetings */}
                <div className="space-y-2">
                  <Label>Mini Gap Between Meetings</Label>
                  <Select
                    value={minGapBetweenMeetings.toString()}
                    onValueChange={(value) => setMinGapBetweenMeetings(parseInt(value, 10))}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Select minimum gap" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">No gap</SelectItem>
                      <SelectItem value="5">5 minutes</SelectItem>
                      <SelectItem value="10">10 minutes</SelectItem>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Right Column - Meeting Details */}
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Attendees
                </h3>
                
                <div className="flex gap-2">
                  <Input
                    type="email"
                    value={newAttendee}
                    onChange={(e) => setNewAttendee(e.target.value)}
                    placeholder="Add attendee email"
                    className="h-8 flex-1"
                  />
                  <Button 
                    type="button"
                    size={newAttendee ? "sm" : "xs"}
                    onClick={handleAddAttendee}
                    variant="secondary"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {formState.attendees.map((attendee) => (
                    <Badge 
                      key={attendee.email} 
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {attendee.email}
                      <button
                        onClick={() => handleRemoveAttendee(attendee.email)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="space-y-1">
                  <Label htmlFor="title">Meeting Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formState.title}
                    onChange={handleInputChange}
                    placeholder="Enter meeting title"
                    required
                  />
                </div>

                <div className="space-y-1">
                  <Label htmlFor="message">Agenda</Label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formState.message}
                    onChange={handleInputChange}
                    placeholder="Describe the meeting agenda..."
                    className="h-32"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {isTimeTableLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="time-slots-grid">
              {slots && slots.length > 0 ? (
                <RadioGroup name="timetable" className="grid grid-cols-5 gap-1">
                  {slots.map((slot) => (
                    <div key={slot}>
                      <RadioGroupItem
                        value={slot}
                        id={slot}
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor={slot}
                        className="h-8 flex items-center justify-center rounded-md border-2 border-muted text-sm bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                      >
                        {slot}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <p className="text-muted-foreground">No Available Slots</p>
                </div>
              )}
            </div>
          )}
          {timetableError && (
            <Alert variant="destructive">
              <AlertDescription>{timetableError}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end space-x-4">
            <DeleteMeetingButton 
              meetingId={initialValues.id} 
              isAlertOpen={isAlertOpen}
              setIsAlertOpen={setIsAlertOpen}
            />
            <Button type="button" variant="outline" onClick={resetForm}>
              Reset
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Scheduling...
                </>
              ) : (
                'Schedule Meeting'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}


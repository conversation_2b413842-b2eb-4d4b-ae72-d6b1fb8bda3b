"use client"
import { useState, useEffect } from "react"
import { ResponsiveModal } from "@/components/responsive-modal"
import { useShareItem } from "./use-share-item"
import type { GoogleDriveItem } from "./columns"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { toast } from "sonner"
import { Loader, MoreVertical, LinkIcon, Copy, Globe, GlobeLock } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

type Permission = {
  id: string
  displayName: string
  type: string
  kind: string
  photoLink: string
  emailAddress: string
  role: string
  deleted: boolean
  pendingOwner: boolean
}

interface ShareModalProps {
  isOpen: boolean
  onClose: () => void
  item: GoogleDriveItem
}

export const ShareGdriveItemModal = ({ isOpen, onClose, item }: ShareModalProps) => {
  const { mutate: shareItemMutate, isPending } = useShareItem()
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [email, setEmail] = useState("")
  const [permissionType, setPermissionType] = useState("")
  const [role, setRole] = useState<"reader" | "writer" | "commenter">("reader")

  useEffect(() => {
    if (isOpen && item.id) {
      shareItemMutate(
        { values: { action: "fetch-permissions", fileId: item.id } },
        {
          onSuccess: (data) => {
            setPermissions(data.permissions || [])
            if (data && data?.permissions) {
              const anyonePermission = data.permissions.find((p: Permission) => p.type === 'anyone');
              if (anyonePermission && anyonePermission?.type === "anyone") {
                setPermissionType("anyone")
              } else {
                setPermissionType("user")
              }
            }
          },
        },
      )
    }
  }, [isOpen, item.id])

  const handleAddPermission = () => {
    //e.preventDefault()

    if (!email?.trim()) return

    shareItemMutate(
      {
        values: {
          action: "add-permission",
          fileId: item.id,
          email: email.trim(),
          role,
        },
      },
      {
        onSuccess: (data) => {
          setPermissions((prev) => [...prev, data.permission])
          setEmail("")
          toast.success(`Successfully shared with ${email}`)
        },
      },
    )
  }

  const handleUpdatePermission = (permissionId: string, newRole: string) => {
    shareItemMutate(
      {
        values: {
          action: "update-permission",
          fileId: item.id,
          permissionId,
          role: newRole,
        },
      },
      {
        onSuccess: () => {
          setPermissions((prev) => prev.map((perm) => (perm.id === permissionId ? { ...perm, role: newRole } : perm)))
          toast.success("Permission updated")
        },
      },
    )
  }

  const handleAccessPermission = (permissionId: string, newType: string, emailAddress: string, newRole: string) => {
    shareItemMutate(
      {
        values: {
          action: "access-permission",
          fileId: item.id,
          permissionId,
          role: newRole,
          type: newType,
          email: emailAddress
        },
      },
      {
        onSuccess: () => {
          setPermissions((prev) => prev.map((perm) => (perm.id === permissionId ? { ...perm, role: newRole, type: newType } : perm)))
          setPermissionType(newType)
          toast.success("Permission updated")
        },
      },
    )
  }

  const handleTransferOwnership = (permissionId: string) => {
    shareItemMutate(
      {
        values: {
          action: "transfer-ownership",
          fileId: item.id,
          permissionId,
	        role: "owner",
        },
      },
      {
        onSuccess: () => {
          setPermissions((prev) => prev.map((perm) => (perm.id === permissionId ? { ...perm, role: "owner" } : perm)))
          toast.success("Ownership transfered")
        },
      },
    )
  }

  const handleRemovePermission = (permissionId: string, email: string) => {
    shareItemMutate(
      {
        values: {
          action: "delete-permission",
          fileId: item.id,
          permissionId,
        },
      },
      {
        onSuccess: () => {
          setPermissions((prev) => prev.filter((perm) => perm.id !== permissionId))
          toast.success(`Removed access for ${email}`)
        },
      },
    )
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case "reader":
        return "Viewer"
      case "writer":
        return "Editor"
      case "commenter":
        return "Commenter"
      case "owner":
        return "Owner"
      default:
        return role
    }
  }

  return (
    <ResponsiveModal open={isOpen} onOpenChange={onClose} className="sm:max-w-lg">
      <div className="p-6 pt-2 pace-y-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Share 「{item.name}」</h2>
        </div>

        <div className="space-y-6">
          <div className="space-y-4">
            <div className="flex gap-4">
              <Input
                type="email"
                placeholder="Add people or groups"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="h-8 flex-1"
              />
              <Select value={role} onValueChange={(value) => setRole(value as any)}>
                <SelectTrigger className="h-8 w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="reader">Viewer</SelectItem>
                  <SelectItem value="commenter">Commenter</SelectItem>
                  <SelectItem value="writer">Editor</SelectItem>
                </SelectContent>
              </Select>
              <Button type="button" size="sm" className="h-8" onClick={handleAddPermission} disabled={!email.trim() || isPending}>
                {isPending && <Loader className="h-4 w-4 animate-spin" />}
                Share
              </Button>
            </div>

            <Separator />

            <div className="space-y-1">
              <h3 className="text-sm font-medium">People with access</h3>
              <div className="space-y-2">
                {permissions.map((permission) => (
                  <div key={permission.id} className="flex items-center justify-between p-1">
                    {permission.type != "anyone" && (
                      <>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={permission?.photoLink} />
                            <AvatarFallback>
                              {permission.displayName?.charAt(0).toUpperCase() || permission.emailAddress?.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">{permission?.displayName!}</span>
                            <span className="text-xs text-muted-foreground">{permission.emailAddress}</span>
                            {permission.type === "user" && permission.role === "owner" && (
                              <span className="text-xs text-muted-foreground"></span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {permission.role !== "owner" ? (
                            <>
                              <Select
                                value={permission.role}
                                onValueChange={(value) => handleUpdatePermission(permission.id, value)}
                              >
                                <SelectTrigger className="h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="reader">Viewer</SelectItem>
                                  <SelectItem value="commenter">Commenter</SelectItem>
                                  <SelectItem value="writer">Editor</SelectItem>
                                </SelectContent>
                              </Select>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreVertical className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => handleRemovePermission(permission.id, permission.emailAddress)}
                                    className="text-red-500"
                                  >
                                    Remove access
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </>
                          ) : (
                            <Badge variant="default">Owner</Badge>
                          )}
                        </div>
                      </>
                    )}

                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Then render the General access section once */}
            <div className="flex flex-col items-start justify-between gap-y-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">General access</span>
              </div>
              
              <div className="flex items-center justify-between gap-2 p-1">
                <Select
                  value={permissions.find(p => p.type === 'anyone') ? 'anyone' : 'user'}
                  onValueChange={(value) => {
                    const anyonePermission = permissions.find(p => p.type === 'anyone');
                    if (value === 'anyone') {
                      // If switching to anyone, use first non-owner user's permission ID
                      const firstUserPermission = permissions.find(p => p.type === 'user' && p.role !== 'owner');
                      if (firstUserPermission) {
                        handleAccessPermission(firstUserPermission.id, 'anyone', '', 'reader');
                      }
                    } else if (anyonePermission) {
                      // When switching back to restricted, find the first non-owner user to get their email
                      const firstNonOwnerUser = permissions.find(p => p.type === 'user' && p.role !== 'owner');
                      if (firstNonOwnerUser) {
                        handleAccessPermission(
                          anyonePermission.id, 
                          'user', 
                          firstNonOwnerUser.emailAddress,
                          'reader'
                        );
                      } else {
                        // If there's no non-owner user, show an error
                        toast.error("Cannot restrict access - no user to transfer permission to");
                      }
                    }
                  }}
                >
                  <div className="rounded-full p-[0.4rem] bg-muted-foreground/30">
                    {permissionType === "anyone" ? <Globe className="size-4" /> : <GlobeLock className="size-4" />}
                  </div>
                  <SelectTrigger className="h-8 text-nowrap gap-x-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">Restricted</SelectItem>
                    <SelectItem value="anyone">Anyone with link</SelectItem>
                  </SelectContent>
                </Select>

                {permissions.find(p => p.type === 'anyone') && (
                  <Select
                    value={permissions.find(p => p.type === 'anyone')?.role || 'reader'}
                    onValueChange={(value) => {
                      const anyonePermission = permissions.find(p => p.type === 'anyone');
                      if (anyonePermission) {
                        handleUpdatePermission(anyonePermission.id, value);
                      }
                    }}
                  >
                    <SelectTrigger className="h-8 gap-x-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="reader">Viewer</SelectItem>
                      <SelectItem value="commenter">Commenter</SelectItem>
                      <SelectItem value="writer">Editor</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            <div className="space-y-3">
              {item.webViewLink && (
                <div className="flex gap-2">
                  <Input readOnly value={item.webViewLink} className="h-8 flex-1 bg-muted" />
                  <Button
                    className="h-8"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(item.webViewLink!)
                      toast.success("Link copied to clipboard")
                    }}
                  >
                    <Copy className="h-4 w-4" />
                    Copy link
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ResponsiveModal>
  )
}



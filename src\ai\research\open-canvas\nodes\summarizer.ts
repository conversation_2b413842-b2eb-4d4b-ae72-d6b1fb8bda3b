//import { Client } from "@langchain/langgraph-sdk";
import { prisma } from "@/lib/db";
import { OpenCanvasGraphAnnotation } from "../state";
import { LangGraphRunnableConfig } from "@langchain/langgraph";
import { graph } from '../../summarizer/index';

export async function summarizer(
  state: typeof OpenCanvasGraphAnnotation.State,
  config: LangGraphRunnableConfig
) {
  if (!config.configurable?.thread_id) {
    throw new Error("Missing thread_id in summarizer config.");
  }

  /*const client = new Client({
    apiUrl: `http://localhost:${process.env.PORT}`,
  });

  const { thread_id } = await client.threads.create();
  await client.runs.create(thread_id, "summarizer", {
    input: {
      messages: state._messages,
      threadId: config.configurable.thread_id,
    },
  });*/

  /*const newThread = await prisma.thread.create({
    data: {
      status: "idle", // Provide a valid ThreadStatus value
      values: {},
      metadata: {},
    },
  });*/
  const newRun = await prisma.run.create({
    data: {
      thread_id: config.configurable?.thread_id,//newThread.thread_id,
      assistant_id: config.configurable?.assistant_id!, 
      status: "pending",
      metadata: {}
    },
  });

  const summarizerInput = {
    messages: state._messages,
    threadId: config.configurable.thread_id,
  }

  const summarizerGraphOutput = await graph.invoke(summarizerInput)
  const jsonResult = JSON.parse(JSON.stringify(summarizerGraphOutput));
  await prisma.run.update({
    where: { run_id: newRun.run_id },
    data: {
      status: "success",
      metadata: {
        ...((typeof newRun.metadata === "object" && newRun.metadata !== null)
          ? (newRun.metadata as Record<string, unknown>)
          : {}),
        result: jsonResult,
      },
    },
  });

  return {};
}

import React, {Dispatch, SetStateAction } from 'react'
import { Chat, Message } from "@prisma/client"
import { History } from './history'
import { HistoryList } from './history-list'

type HistoryContainerProps = {
  assistantId: string
  contractId: string
  chats: (Chat & { messages: Message[] })[];
  chatId: string;
  setChatId: Dispatch<SetStateAction<string>>;
  location: 'sidebar' | 'header'
  filePath?: string
  path: string
}

const HistoryContainer: React.FC<HistoryContainerProps> = ({
  assistantId,
  contractId,
  chats,
  chatId,
  setChatId,
  location,
  filePath,
  path
}) => {
  return (
    <div className=''>
      <History location={location}>
        <HistoryList 
          assistantId={assistantId}
          contractId={contractId}
          chats={chats} 
          chatId={chatId}
          setChatId={setChatId}
          filePath={filePath} 
          path={path} 
        />
      </History>
    </div>
  )
}

export default HistoryContainer
'use server'
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { Project, Task, TaskStatus, User, Member } from "@prisma/client"
import { revalidatePath } from "next/cache"

export type TaskWithRelations = Task & {
  project: Project | null;
  assignee: (Member & {
    user: {
      id: string;
      name: string | null;
      email: string | null;
      image: string | null;
    }
  }) | null;
  user: User | null;
};

export type GetTasksResponse = {
  data?: {
    tasks?: Task[];
    populatedTasks?: TaskWithRelations[];
  };
  error?: string;
  success?: boolean;
};

export type TaskQueryParams = {
  workspaceId: string
  projectId?: string
  assigneeId?: string
  status?: TaskStatus
  search?: string
  startDate?: string
  dueDate?: string
}

export async function getTasks({
  workspaceId,
  projectId,
  assigneeId,
  status,
  search,
  startDate,
  dueDate,
}: TaskQueryParams): Promise<GetTasksResponse> {
  try {
    const { userId } = await auth();
    if (!userId) {
      return { error: "Unauthorized", success: false }
    }

    const member = await prisma.member.findFirst({
      where: {
        userId: userId,
        workspaceId: workspaceId,
      },
    })

    if (!member) {
      return { error: "Unauthorized", success: false }
    }

    const whereClause: any = {
      workspaceId: workspaceId,
    }

    if (projectId) {
      whereClause.projectId = projectId
    }
    if (status) {
      whereClause.status = status
    }
    if (assigneeId) {
      whereClause.assigneeId = assigneeId
    }

    if (startDate && dueDate) {
      const formattedStartDate = new Date(startDate).toISOString(); // Format as ISO string
      const formattedDueDate = new Date(dueDate).toISOString(); // Format as ISO string
    
      // Apply range condition for both startDate and dueDate
      whereClause.startDate = {
        gte: new Date(formattedStartDate), // Start on or after startDate
      };
      whereClause.dueDate = {
        lt: new Date(new Date(formattedDueDate).getTime() + 24 * 60 * 60 * 1000), // End before or on dueDate
      };
    } else if (startDate) {
      const formattedStartDate = new Date(startDate).toISOString();
      whereClause.startDate = {
        gte: new Date(formattedStartDate),
      };
    } else if (dueDate) {
      const formattedDueDate = new Date(dueDate).toISOString();
      whereClause.dueDate = {
        lt: new Date(new Date(formattedDueDate).getTime() + 24 * 60 * 60 * 1000),
      };
    }

    if (search) {
      whereClause.name = {
        contains: search,
        mode: 'insensitive'
      }
    }

    //console.log("whereClause", whereClause)

    // Single database query with includes
    const tasks = await prisma.task.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        project: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        assignee: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              }
            }
          }
        }
      }
    })

    // Map to create populated tasks with all relations included
    const populatedTasks = tasks.map(task => ({
      ...task,
      project: task.project,
      assignee: task.assignee,
      user: task.user
    }))

    // Map to create basic tasks without relations
    const basicTasks = tasks.map(({ project, user, assignee, ...task }) => task)

    revalidatePath('/workspaces')
    //console.log("tasks", tasks)

    return {
      data: {
        tasks: basicTasks,
        populatedTasks: populatedTasks as TaskWithRelations[],
      }
    }
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Internal Server Error",
      success: false,
      data: { tasks: [], populatedTasks: [] }
    };
  }
}
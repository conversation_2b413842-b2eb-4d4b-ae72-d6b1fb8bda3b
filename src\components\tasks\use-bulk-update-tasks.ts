import { useMutation, useQueryClient } from '@tanstack/react-query';
import { bulkUpdateTasks } from '@/actions/tasks/bulk-update-tasks';
import { z } from 'zod';
import { bulkUpdateTasksSchema } from '@/components/tasks/schemas';
import { toast } from 'sonner';


export function usebulkUpdateTasks() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof bulkUpdateTasksSchema>) => {
      const response = await bulkUpdateTasks(values);
      if (!response?.success || !response?.data) {
        throw new Error(response.error || 'Failed to update tasks.');
      }
      return { data: response.data };
    },
    onSuccess: () => {
      toast.success('Tasks updated!');
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['workspace-analytics'] });
      queryClient.invalidateQueries({ queryKey: ['project-analytics'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update tasks.');
    },
  });

  return mutation;
}

import { useCallback, useState } from "react"
import { useDropzone } from "react-dropzone"
import { motion, AnimatePresence } from "framer-motion"
import { FileText, Trash, Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"

interface FileUploadProps {
  onFileSelect: (file: File | null) => void
  onAnalyze: () => void
  isProcessing: boolean
}

export function FileUpload({ onFileSelect, onAnalyze, isProcessing }: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null)

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        setFile(acceptedFiles[0])
        onFileSelect(acceptedFiles[0])
      }
    },
    [onFileSelect],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    maxFiles: 1,
    multiple: false,
  })

  return (
    <AnimatePresence>
      <motion.div>
        <div
          {...getRootProps()}
          className={cn(
            "border-2 border-dashed rounded-lg p-8 mt-8 mb-4 text-center transition-colors",
            isDragActive ? "border-primary bg-primary/10" : "border-gray-300 hover:border-gray-400",
          )}
        >
          <input {...getInputProps()} />
          <motion.div>
            <FileText className="mx-auto size-16 text-primary" />
          </motion.div>
          <p className="mt-4 text-sm text-gray-600">
            Drag &apos;n&apos; drop some files here, or click to select files
          </p>
          <p className="bg-yellow-500/30 border border-yellow-500 border-dashed text-yellow-700 p-2 rounded mt-2">
            Note: Only PDF files are accepted
          </p>
        </div>
        {file && (
          <div className="mt-4 bg-green-500/30 border border-green-500 border-dashed text-green-700 p-2 rounded flex items-center justify-between">
            <span>
              {file.name} <span className="text-sm text-gray-600">({file.size} bytes)</span>
            </span>
            <Button
              variant="ghost"
              size="sm"
              className="hover:bg-green-500"
              onClick={() => {
                setFile(null)
                onFileSelect(null)
              }}
            >
              <Trash className="size-5 hover:text-green-900" />
            </Button>
          </div>
        )}
        {file && !isProcessing && (
          <Button className="mt-4 w-full mb-4" onClick={onAnalyze}>
            <Sparkles className="mr-2 size-4" />
            Analyze Contract With AI
          </Button>
        )}
      </motion.div>
    </AnimatePresence>
  )
}


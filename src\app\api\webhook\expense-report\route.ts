{
  /** 
  掃描社區每月支出明細圖檔，
  並上傳圓檔及辨識結果Google Drive/Sheets，
  如物業、清潔、零用金、各項維修費用等。
*/
}

import { NextRequest, NextResponse } from "next/server";
import { webhook, messagingApi, validateSignature } from "@line/bot-sdk";
import { uploadImageToExpenseReportDrive } from "@/lib/google/upload-image-to-expense-report-drive";
import { appendExpenseReportToSheet } from "@/lib/google/append-expense-report-to-sheet";
import { analyzeExpenseReportImage } from "../functions";
import { streamToBuffer, getMessageContentWithRetry } from "../functions";

export const maxDuration = 300;

const client = new messagingApi.MessagingApiClient({
  channelAccessToken: process.env.CHANNEL_ACCESS_TOKEN!,
});
const blobClient = new messagingApi.MessagingApiBlobClient({
  channelAccessToken: process.env.CHANNEL_ACCESS_TOKEN!,
});

const eventHandler = async (event: webhook.Event) => {
  if (event.type !== "message" || !event.replyToken) return;
  if (event.message.type === "image") {
    try {
      const imageStream = await getMessageContentWithRetry(
        blobClient,
        event.message.id
      );
      const buffer = await streamToBuffer(imageStream);
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const filename = `expense_report_${timestamp}.jpg`;
      const base64Image = buffer.toString("base64");
      const [driveUploadResult, analyzedExpenseReport] = await Promise.all([
        uploadImageToExpenseReportDrive(buffer, filename),
        analyzeExpenseReportImage(base64Image),
      ]);
      const expenseReportData = {
        ...analyzedExpenseReport,
        googleDriveLink: driveUploadResult.webViewLink || undefined,
      };
      const appendResult = await appendExpenseReportToSheet(expenseReportData);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `收支明細表已上傳！\n預覽試算表：${appendResult.spreadsheetUrl}\nDrive連結：${appendResult.googleDriveLink}`,
          },
        ],
      });
      return;
    } catch (error) {
      console.error("Error processing expense report image:", error);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { type: "text", text: "收支明細表圖片處理失敗，請稍後再試。" },
        ],
      });
      return;
    }
  }
};

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = req.headers.get("x-line-signature") || "";
  const isValid = validateSignature(
    body,
    process.env.CHANNEL_SECRET!,
    signature
  );
  if (!isValid) {
    return new Response("Invalid signature", { status: 401 });
  }
  const events: webhook.Event[] = JSON.parse(body).events;
  try {
    await eventHandler(events[0]);
    return NextResponse.json({ status: "success" });
  } catch (err) {
    return NextResponse.json({ status: "error" }, { status: 500 });
  }
}

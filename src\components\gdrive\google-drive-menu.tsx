"use client";

import { useState, useRef, useCallback } from "react";
import { GoogleDriveItem } from "./columns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ResponsiveModal } from "@/components/responsive-modal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  ChevronDown,
  FolderPlus,
  CloudUpload,
  Loader,
} from "lucide-react";
import { toast } from "sonner";

interface GoogleDriveMenuProps {
  disabled?: boolean;
  currentFolder?: GoogleDriveItem | null;
  data?: { structure?: GoogleDriveItem[] };
  file?: File | null;
  folderName?: string;
  driveId?: string;
  createFolderMutate: (
    data: { values: { folderId: string; folderName: string } },
    options?: any
  ) => void;
  uploadFileMutate: (data: FormData, options?: any) => void;
  isCreateFolderLoading?: boolean;
  setCurrentFolder: (folder: GoogleDriveItem) => void;
  setItems: (items: GoogleDriveItem[]) => void;
  setFile: (file: File | null) => void;
  setFolderName: (name: string) => void;
  refetch: () => void;
}

export const GoogleDriveMenu = ({
  disabled,
  currentFolder,
  data,
  file,
  folderName,
  driveId,
  createFolderMutate,
  uploadFileMutate,
  isCreateFolderLoading,
  setCurrentFolder,
  setItems,
  setFile,
  setFolderName,
  refetch,
}: GoogleDriveMenuProps) => {
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const createFolder = useCallback(() => {
    if (!folderName) {
      toast.error("Folder name is required");
      return;
    }

    const values = {
      folderId: currentFolder?.id || "root",
      folderName,
    };

    createFolderMutate(
      { values },
      {
        onSuccess: ({
          data: newFolder,
          existingFolder,
        }: {
          data: GoogleDriveItem;
          existingFolder?: boolean;
        }) => {
          setFolderName("");
          if (!existingFolder) {
            if (currentFolder) {
              setCurrentFolder({
                ...currentFolder,
                children: (currentFolder.children || []).concat(newFolder),
              });
              setItems((currentFolder.children || []).concat(newFolder));
            } else {
              setItems((data?.structure || []).concat(newFolder));
            }
          }

          refetch();
          setIsCreateFolderOpen(false);
        },
        onError: (error: any) => {
          console.error("Error creating folder:", error);
        },
      }
    );
  }, [folderName, currentFolder, createFolderMutate, data, refetch]);

  const uploadFile = useCallback(() => {
    if (!file) return;

    const formData = new FormData();
    formData.append("folderId", currentFolder?.id || "root");
    formData.append("file", file);
    formData.append("driveId", driveId!);

    uploadFileMutate(formData, {
      onSuccess: ({ data: newFile }: { data: GoogleDriveItem }) => {
        setFile(null);

        if (currentFolder) {
          setCurrentFolder({
            ...currentFolder,
            children: (currentFolder.children || []).concat(newFile),
          });
          setItems((currentFolder.children || []).concat(newFile));
        } else {
          setItems((data?.structure || []).concat(newFile));
        }

        refetch();
      },
      onError: (error: any) => {
        console.error("Error uploading file:", error);
      },
    });
  }, [file, currentFolder, uploadFileMutate, data, refetch]);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger className="h-8" asChild>
          <Button className="font-semibold" disabled={disabled}>
            <Plus />
            New
            <ChevronDown />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => setIsCreateFolderOpen(true)}>
            <FolderPlus />
            Create Folder
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => fileInputRef.current?.click()}>
            <CloudUpload />
            Upload File
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={(e) => {
          const selectedFile = e.target.files?.[0];
          if (selectedFile) {
            setFile(selectedFile);
            uploadFile();
          }
        }}
      />

      <ResponsiveModal
        open={isCreateFolderOpen}
        onOpenChange={setIsCreateFolderOpen}
      >
        <div className="p-6 space-y-4">
          <h2 className="text-lg font-semibold">Create Folder</h2>
          <Input
            type="text"
            placeholder="Folder Name"
            value={folderName}
            onChange={(e) => setFolderName(e.target.value)}
          />
          <Button disabled={disabled || !folderName} onClick={createFolder}>
            {isCreateFolderLoading && (
              <Loader className="h-4 w-4 animate-spin" />
            )}
            Create
          </Button>
        </div>
      </ResponsiveModal>
    </>
  );
};

'use client';

import { memo, useCallback, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RiAddCircleFill, RiEditLine, RiGroupLine } from "react-icons/ri";
import { usePathname, useRouter } from "next/navigation";
import { useWorkspaces } from "@/hooks/use-workspaces";
import { Member } from "@prisma/client";
import { useCreateWorkspaceModal } from "@/hooks/use-create-workspace-modal";
import { useUpdateWorkspaceModal } from "@/hooks/use-update-workspace-modal";
import { LayoutTemplate } from "lucide-react";

interface WorkspaceWithMembers {
  id: string;
  name: string;
  members: Member[];
}
interface WorkspaceSwitcherProps {
  initialWorkspaces: WorkspaceWithMembers[];
  initialWorkspaceId: string | null;
}

export function WorkspaceSwitcher({ initialWorkspaces, initialWorkspaceId }: WorkspaceSwitcherProps) {
  const pathname = usePathname();
  const { setWorkspaceId } = useWorkspaces();
  const { open: createOpen } = useCreateWorkspaceModal();
  const router = useRouter();
  console.log("initialWorkspaceId", initialWorkspaceId)
   
  useEffect(() => {
    if (initialWorkspaceId && pathname === '/workspaces') {
      router.replace(`/workspaces/${initialWorkspaceId}`);
    }
  }, [initialWorkspaceId, pathname, router]);

  const onWorkspaceChange = useCallback((workspaceId: string) => {
    setWorkspaceId(workspaceId);
    
    // Check if we're in any workspace path
    if (pathname.startsWith('/workspaces/')) {
      // Extract the current path segments after the workspace ID
      const pathSegments = pathname.split('/');
      if (pathSegments.length > 3) {
        // Preserve the sub-path when switching workspaces
        const subPath = pathSegments.slice(3).join('/');
        router.push(`/workspaces/${workspaceId}/${subPath}`);
      } else {
        // If no sub-path, just go to the workspace root
        router.push(`/workspaces/${workspaceId}`);
      }
    } else if (pathname === '/workspaces') {
      router.push(`/workspaces/${workspaceId}`);
    }
  }, [pathname, router, setWorkspaceId]);

  // Safeguard against undefined workspaces
  if (!Array.isArray(initialWorkspaces)) {
    return <div>Loading workspaces...</div>;
  }

  const disabled = true;


  return (
    <div className="flex flex-col items-start gap-x-2 ml-4">
      <div className="flex flex-row items-center gap-x-2">
        <LayoutTemplate size={"16"} className="mb-[0.1rem] rounded-md" />
        <p className="text-xs uppercase text-primary font-semibold">Workspaces</p>
        <RiAddCircleFill
          className="size-5 text-neutral-500 cursor-pointer hover:text-neutral-400"
          onClick={createOpen}
        />
      </div>
      <WorkspaceSelect 
        workspaces={initialWorkspaces}
        workspaceId={initialWorkspaceId}
        onWorkspaceChange={onWorkspaceChange}
      />
    </div>
  );
}

const WorkspaceSelect = memo(function WorkspaceSelect({ 
  workspaces, 
  workspaceId,
  onWorkspaceChange 
}: {
  workspaces: WorkspaceWithMembers[];
  workspaceId: string | null;
  onWorkspaceChange: (id: string) => void;
}) {
  const router = useRouter();
  const { open: updateOpen } = useUpdateWorkspaceModal()
  return (
    <Select
    onValueChange={onWorkspaceChange}
    value={workspaceId || undefined}
  >
    <SelectTrigger className="h-7 max-w-fit bg-neutral-200/10 dark:bg-neutral-900 text-xs font-medium border-0 p-1 px-3 ml-4 gap-x-1 rounded-xl focus:ring-offset-0 focus:ring-0">
      <SelectValue placeholder="No workspace selected" />
    </SelectTrigger>
    <SelectContent>
      {workspaces.length > 0 ? (
        workspaces.map((workspace) => (
          <div key={workspace.id} className="flex items-center justify-between">
            <SelectItem value={workspace.id} className="text-sm">
              {workspace.name}
            </SelectItem>
            {workspaceId && 
              <button
                onClick={(e) => {
                  //e.stopPropagation();
                  e.preventDefault();
                  //updateOpen();
                  // Only update URL after modal opens
                  //router.replace(`/workspaces/${workspace.id}?update-workspace=true`);
                  router.push(`/workspaces/${workspace.id}/settings`);
                }}
                className="p-1 hover:text-primary"
              >
                <RiEditLine className="h-4 w-4" />
              </button>
            }
            {workspace?.id && 
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  router.push(`/workspaces/${workspace.id}/members`);
                }}
                className="p-1 hover:text-primary"
              >
                <RiGroupLine className="h-4 w-4" />
              </button>
            }
          </div>
        ))
      ) : (
        <div>No workspaces available</div>
      )}
    </SelectContent>
  </Select>
  );
});
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { extractTextFromPDF, detectContractType } from '@/ai/contract/ai.services';

export async function POST(req: NextRequest) {
  const { userId } = await auth();

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const formData = await req.formData();
    const file = formData.get('contract') as File;
    //console.log("formData============>", formData)

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    const buffer = await file.arrayBuffer();
    const pdfText = await extractTextFromPDF(Buffer.from(buffer));

    const detectedType = await detectContractType(pdfText);
    //const detectedType = "Grant"
    //console.log("detectedType============>", detectedType)
    return NextResponse.json({ detectedType });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to detect contract type' }, { status: 500 });
  }
}
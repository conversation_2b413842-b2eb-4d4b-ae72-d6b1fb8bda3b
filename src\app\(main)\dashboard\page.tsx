import { getCurrentUser } from "@/actions/user/get-current-user";
import { getDashboardOverview } from "@/actions/dashboard/get-dashboard-overview";
import { getAssetsOverview } from "@/actions/dashboard/get-assets-overview"
import { getInvestmentsOverview } from "@/actions/investments/get-investments-overview";
import { AddBankAccountComponent } from "@/components/account-connection";
import { MetricCards } from "@/components/dashboard/metric-cards";
import { TopCharts } from "@/components/dashboard/top-charts";
import { redirect } from "next/navigation";

export default async function DashboardPage() {
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const [overviewResult, assetsResult, investmentsResult] = await Promise.all([
    getDashboardOverview(user.id), // Existing data for MetricCards
    getAssetsOverview(user.id), // Real asset data for TopCharts
    getInvestmentsOverview()
  ]);

  const { success: overviewSuccess, data: overviewData } = overviewResult;
  const { success: assetsSuccess, data: assetsData } = assetsResult;

  console.log("Overview Data:", JSON.stringify(overviewData, null, 2));
  console.log("Assets Data:", assetsData);

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">
          Hey {user?.name?.split(" ")[0]}, Welcome back 👋
        </h2>
        <div className="hidden items-center space-x-2 md:flex">
          <AddBankAccountComponent />
        </div>
      </div>
      <TopCharts data={assetsData} />

      <MetricCards overview={overviewData} assets={assetsData} />
    </div>
  );
}

import { NextResponse } from 'next/server';
import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";

export async function GET(request: Request) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const sheetName = searchParams.get('sheetName');

    if (!sheetName) {
      return NextResponse.json({ error: "Sheet name is required" }, { status: 400 });
    }

    const lastImport = await prisma.spreadsheetImport.findFirst({
      where: { userId, sheetName },
      orderBy: { importedAt: 'desc' },
      select: { importedAt: true, rowCount: true, type: true },
    });
    console.log("lastImport", lastImport)

    if (!lastImport) {
      return NextResponse.json({ message: "No spreadsheet imports found for this sheet" }, {});
    }

    return NextResponse.json({
      lastUpdated: lastImport.importedAt,
      rowCount: lastImport.rowCount,
      type: lastImport.type,
    });
  } catch (error) {
    console.error('Error fetching last spreadsheet import:', error);
    return NextResponse.json({ error: 'Failed to fetch last spreadsheet import' }, { status: 500 });
  }
}


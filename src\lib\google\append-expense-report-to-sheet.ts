import { google } from 'googleapis';

export type ExpenseReportData = {
  title: string;
  document_period_minguo: string;
  table_headers: string[];
  items: Array<{
    serial_no: string;
    account_item: string;
    expense_summary: string;
    month_year: string;
    amount: number;
    payment_method: '匯款' | '領現' | '轉帳' | '自動扣款';
  }>;
  total: { label: string; amount: number };
  approvers: Array<{ role: string; name: string | null; stamp_present: boolean }>;
  googleDriveLink?: string;
};

export const appendExpenseReportToSheetDetail = async (data: ExpenseReportData) => {
  try {
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });
    const sheets = google.sheets({ version: 'v4', auth });
    const spreadsheetId = process.env.GOOGLE_EXPENSE_REPORT_SHEETS_ID;
    if (!spreadsheetId) throw new Error('GOOGLE_EXPENSE_REPORT_SHEETS_ID not set');

    // Prepare rows: headers, items, total, approvers, sum check
    const headers = [
      '序號', '會計科目', '摘要', '月份', '金額', '付款方式'
    ];
    const itemRows = data.items.map((item, idx) => [
      item.serial_no,
      item.account_item,
      item.expense_summary,
      item.month_year,
      item.amount,
      item.payment_method
    ]);
    const totalRow = ['合計', '', '', '', data.total.amount, ''];
    const sumCheck = [
      '加總檢核', '', '', '', data.items.reduce((sum, i) => sum + i.amount, 0) === data.total.amount ? '正確' : '錯誤', ''
    ];
    const approverRows = data.approvers.map(a => [a.role, a.name ?? '', a.stamp_present ? '有章' : '無章']);

    // Compose all rows
    const rows = [
      [data.title],
      ['期間', data.document_period_minguo],
      headers,
      ...itemRows,
      totalRow,
      sumCheck,
      ['簽核'],
      ...approverRows,
      ['收據圖片連結', data.googleDriveLink || ''],
    ];

    // Append to sheet (append as new sheet or to a fixed sheet)
    const sheetTitle = `${data.document_period_minguo}_${Date.now()}`;
    await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [{
          addSheet: { properties: { title: sheetTitle } }
        }]
      }
    });
    await sheets.spreadsheets.values.update({
      spreadsheetId,
      range: `${sheetTitle}!A1`,
      valueInputOption: 'USER_ENTERED',
      requestBody: { values: rows }
    });
    const spreadsheetUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit#gid=0`;
    return {
      success: true,
      spreadsheetUrl,
      googleDriveLink: data.googleDriveLink || undefined,
      rowCount: rows.length
    };
  } catch (error) {
    console.error('Error uploading expense report to Google Sheets:', error);
    throw error;
  }
};


// Assuming ExpenseReport is the type generated by the AI and validated by Zod
// It should be similar to your previous `ExpenseReport` type.
// Let's redefine it here for clarity based on the AI output and desired sheet structure.
export type AiExpenseReportOutput = {
  title: string;
  document_period_minguo: string; // e.g., "114年05月"
  table_headers: string[]; // Original table headers, might not be directly used for sheet
  items: Array<{
    serial_no: string;
    account_item: string;
    expense_summary: string;
    month_year: string; // e.g., "114/05"
    amount: number;
    payment_method: '匯款' | '領現' | '轉帳' | '自動扣款';
  }>;
  total: { label: string; amount: number };
  approvers: Array<{ role: string; name: string | null; stamp_present: boolean }>;
};

// Data structure we'll use to pass to the sheet appending function
export type ExpenseReportToSheetData = AiExpenseReportOutput & {
  googleDriveLink?: string; // Optional link to the receipt image
};

const getGregorianYearFromMinguo = (minguoPeriodOrMonth: string): string | null => {
  if (!minguoPeriodOrMonth) return null;
  // Regex to capture the Minguo year from "YYY年MM月" or "YYY/MM"
  const match = minguoPeriodOrMonth.match(/^(\d{2,3})(?:年|\/)/);
  if (match && match[1]) {
    const minguoYear = parseInt(match[1], 10);
    if (!isNaN(minguoYear)) {
      return (minguoYear + 1911).toString();
    }
  }
  return null;
};

const formatApprovers = (approvers: AiExpenseReportOutput['approvers']): string => {
  return approvers
    .map(a => `${a.role}：${a.name || ''}${a.name && a.stamp_present ? ' ' : ''}${a.stamp_present ? '有章' : (a.name ? ' 無章' : '無章')}`)
    .join('、');
};

export const appendExpenseReportToSheet = async (data: ExpenseReportToSheetData) => {
  try {
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });
    const sheets = google.sheets({ version: 'v4', auth });
    const spreadsheetId = process.env.GOOGLE_EXPENSE_REPORT_SHEETS_ID;

    if (!spreadsheetId) {
      console.error('GOOGLE_EXPENSE_REPORT_SHEETS_ID not set');
      throw new Error('GOOGLE_EXPENSE_REPORT_SHEETS_ID not set');
    }
    if (!data.items || data.items.length === 0) {
        console.warn('No items to append for expense report:', data.title);
        return {
            success: false,
            message: 'No items to append.',
            spreadsheetUrl: `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit`,
        };
    }

    // Determine sheet name (Gregorian Year)
    const gregorianYear = 
      getGregorianYearFromMinguo(data.document_period_minguo) || 
      (data.items.length > 0 ? getGregorianYearFromMinguo(data.items[0].month_year) : null);

    if (!gregorianYear) {
      console.error('Could not determine year for sheet name from data:', data);
      throw new Error('Could not determine year for sheet name.');
    }
    const sheetTitle = gregorianYear;

    // Check if sheet exists, create if not
    let sheetExists = false;
    try {
      const spreadsheet = await sheets.spreadsheets.get({ spreadsheetId });
      sheetExists = spreadsheet.data.sheets?.some(s => s.properties?.title === sheetTitle) || false;
    } catch (err) {
        console.error(`Error fetching spreadsheet details: ${err instanceof Error ? err.message : String(err)}`);
        throw new Error(`Error fetching spreadsheet details: ${err instanceof Error ? err.message : String(err)}`);
    }

    if (!sheetExists) {
      try {
        await sheets.spreadsheets.batchUpdate({
          spreadsheetId,
          requestBody: {
            requests: [{ addSheet: { properties: { title: sheetTitle } } }],
          },
        });
        console.log(`Sheet "${sheetTitle}" created.`);
        // Add headers to the new sheet
        const headerRowValues = [['序號', '會計科目', '摘要', '月份', '金額', '付款方式', '加總檢核', '簽核', '收據圖片連結']];
        await sheets.spreadsheets.values.append({
            spreadsheetId,
            range: `${sheetTitle}!A1`,
            valueInputOption: 'USER_ENTERED',
            requestBody: { values: headerRowValues },
        });

      } catch (err) {
        console.error(`Error creating sheet "${sheetTitle}": ${err instanceof Error ? err.message : String(err)}`);
        // If it's because the sheet already exists (race condition), we can often ignore
        if (!(err instanceof Error && err.message.includes('already exists'))) {
            throw new Error(`Error creating sheet "${sheetTitle}": ${err instanceof Error ? err.message : String(err)}`);
        }
        console.warn(`Sheet "${sheetTitle}" likely created by a concurrent process.`);
      }
    } else {
      console.log(`Sheet "${sheetTitle}" already exists. Appending data.`);
    }
    
    // Prepare rows to append (one row per item)
    const sumOfItems = data.items.reduce((sum, i) => sum + i.amount, 0);
    const sumCheckValue = sumOfItems === data.total.amount;
    const approversString = formatApprovers(data.approvers);

    const rowsToAppend = data.items.map(item => [
      item.serial_no,
      item.account_item,
      item.expense_summary,
      item.month_year,
      item.amount,
      item.payment_method,
      sumCheckValue, // This will be TRUE/FALSE in sheets
      approversString,
      data.googleDriveLink || '',
    ]);

    // Append item rows to the sheet
    const appendResult = await sheets.spreadsheets.values.append({
      spreadsheetId,
      range: `${sheetTitle}!A:I`, // Append to the first available row in columns A to I
      valueInputOption: 'USER_ENTERED',
      insertDataOption: 'INSERT_ROWS', // Important to insert new rows
      requestBody: {
        values: rowsToAppend,
      },
    });

    const updatedRange = appendResult.data.updates?.updatedRange;
    const spreadsheetUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit#gid=0`;


    console.log(`Data appended to sheet "${sheetTitle}". Range: ${updatedRange}`);

    return {
      success: true,
      spreadsheetUrl,
      sheetTitle,
      rowCount: rowsToAppend.length,
      googleDriveLink: data.googleDriveLink || undefined,
    };
  } catch (error) {
    console.error('Error appending expense report to Google Sheets:', error instanceof Error ? error.message : String(error));
    if (error instanceof Error && error.stack) {
        console.error(error.stack);
    }
    // Re-throw or return a structured error
    throw error;
  }
};

'use server'

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { Decimal } from "@prisma/client/runtime/library";

interface BulkUploadItem {
  bankAccountId: string;
  amount: number;
  description: string;
  year: string;
  month: string;
  day?: string;
  type: 'CREDIT' | 'DEBIT';
  categoryId: string; 
}

interface DataItem { 
  accountId: string;
  amount: Decimal; 
  description: string; 
  date: Date; 
  userId: string; 
  categoryId: string; 
  type: 'CREDIT' | 'DEBIT';
}

interface ValidationError {
  index: number;
  item: BulkUploadItem;
  error: string;
}

interface UploadResult {
  success: boolean;
  data?: any;
  validationErrors?: ValidationError[];
  error?: string;
}

export async function uploadIncomeExpense(items: BulkUploadItem[]): Promise<UploadResult> {
  console.log("items: ", items)
  try {
    const { userId } = await auth();
    if (!userId) return { success: false, error: "Unauthorized" };

    // Validate all items first
    const validationErrors: ValidationError[] = [];
    const validItems = items.map((item, index) => {
      try {
        validateAmount(item.amount);
        const date = getDateFromFields(item.year, item.month, item.day);
        return { ...item, date, isValid: true };
      } catch (error) {
        validationErrors.push({
          index,
          item,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        return { ...item, isValid: false };
      }
    });

    if (validationErrors.length > 0) {
      return { success: false, validationErrors };
    }

    // Separate income and expense items
    const incomeItems: DataItem[] = [];
    const expenseItems: DataItem[] = [];
    
    validItems.forEach(item => {
      const date = getDateFromFields(item.year, item.month, item.day);
      const processedItem = {
        amount: new Decimal(item.amount),
        description: item.description || '',
        date: date,
        userId,
        accountId: item.bankAccountId,
        categoryId: item.categoryId,
        type: item.type,
        categoryValidated: true
      };

      if (item.type === 'CREDIT') {
        incomeItems.push(processedItem);
      } else {
        expenseItems.push(processedItem);
      }
    });

    // Process in transaction with separate batches for income and expense
    const result = await prisma.$transaction(async (tx) => {
      const batchSize = 100;
      const results = [];

      // Process income batches
      for (let i = 0; i < incomeItems.length; i += batchSize) {
        const batch = incomeItems.slice(i, i + batchSize);
        const result = await tx.income.createMany({ data: batch });
        results.push(result);
      }

      // Process expense batches
      for (let i = 0; i < expenseItems.length; i += batchSize) {
        const batch = expenseItems.slice(i, i + batchSize);
        const result = await tx.expense.createMany({ data: batch });
        results.push(result);
      }

      return results;
    });

    return {
      success: true,
      data: result
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

function processYear(yearStr: string): number {
  const yearNum = parseInt(yearStr);
  if (yearNum < 1000) {
    return yearNum + 1911;
  }
  return yearNum;
}

function getDateFromFields(year: string, month: string, day?: string): Date {
  const processedYear = processYear(year);
  const processedMonth = parseInt(month) - 1; // JS months are 0-based
  
  // Validate month
  if (processedMonth < 0 || processedMonth > 11) {
    throw new Error('Invalid month');
  }
  
  // If no day provided, use last day of month
  return day 
    ? new Date(processedYear, processedMonth, parseInt(day))
    : new Date(processedYear, processedMonth + 1, 0);
}
  
function validateAmount(amount: number): boolean {
  // Amount validations:
  // 1. Must be a number
  if (typeof amount !== 'number' || isNaN(amount)) {
    throw new Error('Amount must be a valid number');
  }
  
  // 2. Must be positive
  if (amount <= 0) {
    throw new Error('Amount must be greater than 0');
  }
  
  // 3. Check for reasonable limits (e.g., prevent unreasonably large numbers)
  if (amount > 999999999) {
    throw new Error('Amount exceeds maximum limit');
  }
  
  // 4. Check decimal places (max 2)
  if (amount.toString().split('.')[1]?.length > 2) {
    throw new Error('Amount cannot have more than 2 decimal places');
  }

  return true;
}
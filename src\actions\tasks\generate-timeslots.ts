import { fromZonedTime, toZonedTime } from 'date-fns-tz';
export const generateTimeSlots = (
  date: Date,
  WORKING_HOURS: {
    start: number;
    end: number;
    slotDuration: number;
    minGapBetweenMeetings: number;
    timezone: string;
  },
): Date[] => {
  const slots: Date[] = [];
  const { start, end, slotDuration, minGapBetweenMeetings, timezone } = WORKING_HOURS;

  // Convert the input date to the local timezone
  const zonedDate = toZonedTime(date, timezone);

  // Start at the beginning of the working hours
  let currentTime = new Date(zonedDate);
  currentTime.setHours(start, 0, 0, 0);

  // Calculate the end time
  const endTime = new Date(zonedDate);
  endTime.setHours(end, 0, 0, 0);

  // Generate slots
  while (currentTime < endTime) {
    slots.push(fromZonedTime(currentTime, timezone)); // Convert back to the specified timezone

    // Increment time by slot duration and minimum gap
    currentTime = new Date(currentTime.getTime() + (slotDuration + minGapBetweenMeetings) * 60 * 1000);
  }

  return slots;
};

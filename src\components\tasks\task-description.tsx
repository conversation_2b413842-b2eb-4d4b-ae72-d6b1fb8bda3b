import { useState } from "react";
import { TaskData } from "@/actions/tasks/get-task";
import { Pencil1Icon } from "@radix-ui/react-icons";
import { useUpdateTaskDescription} from "./use-update-task-description";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { XIcon } from "lucide-react";

interface TaskDescriptionProps {
  data: TaskData;
}

export const TaskDescription = ({ 
  data 
}: TaskDescriptionProps) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [value, setValue] = useState<string>(data.task?.description ?? "");

  const { mutate, isPending } = useUpdateTaskDescription();

  const handleSave = () => {
    mutate({
      taskId: data.task.id,
      data: { description: value }
    });
    setIsEditing(false )
  }

  return (
    <div className="p-4 border rounded-log">
      <div className="flex items-center justify-between">
        <p className="text-lg font-semibold">Description</p>
        <Button
          onClick={() => setIsEditing((prev) => !prev)}
          className="h-8 ml-auto"
          variant="datePicker"
          size="sm"
        >        
          {isEditing ? (
            <XIcon className="size-4" />
          ) : (
            <Pencil1Icon className="size-4" /> 
          )}
          {isEditing ? "Cancel" : "Edit"}
        </Button>
      </div>
      <Separator className="my-2" />
      {isEditing ? (
        <div className="flex flex-col gap-y-4">
          <Textarea 
            placeholder="Add task description"
            value={value}
            rows={4}
            onChange={(e) => setValue(e.target.value)}
          />
          <Button
            onClick={handleSave}
            disabled={isPending}
            className="h-8 w-fit ml-auto"
            variant="datePicker"
            size="sm"
          >
            {isPending ? "Saving..." : "Save"}
          </Button>
        </div>
      ) : (
        <div className="flex flex-col gap-y-4">
          {data.task?.description || (
            <span className="text-muted-foreground">No description set.</span>
          )}
        </div>
      )}
    </div>
  )
}
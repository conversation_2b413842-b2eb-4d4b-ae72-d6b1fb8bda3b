"use server";

import { auth } from "@clerk/nextjs/server"; // For server-side auth
import { google } from "googleapis";
import { parse } from "csv-parse/sync";
import { prisma } from "@/lib/db";
import { Prisma } from "@prisma/client";
import { storeMonthlyTotals } from "@/actions/account/store-monthly-totals";
import { storeLastDayBalances } from "@/actions/balances/store-last-day-balances";
import { suggestCategory } from "@/lib/utils/transaction-categorization";
import { 
  calculateTransactionFullHash
 } from "@/lib/utils/hash-calculation";
 import { csvMetadataExtraction } from "@/lib/utils/csv-metadata-extraction";
 import { getEndOfMonthDate } from "@/lib/utils/get-end-of-month-date";

{
  /** 
  掃描社區銀行帳戶明細表(CSV檔)，
  並上傳分析結果至Google Sheets。
  1. 收入
    - 管理費收入：depositsMaintenanceFee: 
        - 依照交易資訊(轉帳末6碼),備註(住戶編號)，每住戶管理費按年月塡入表單： 管理費收支帳[管理費收入]。
    - 其他收入：
        - depositsSmallAmount: 小於974元收入，如臨停、汽機車清潔費收入。  
        - depositsUnmatched: 974
  3. 支出
    - withdrawals: 依交易資訊和備註欄之關鍵字(specialKeywords)列出支出項目。
    - withdrawalsUnmatched：交易資訊和備註欄為空白，如清潔人員薪資、零用金等。
  4. 每月累計餘額：
    - lastDayBalances：每個月的最後一筆餘額。
  5. 每月收入和支出總合
    - monthlyDeposits: 每個月所有存入加總金額。
    - monthlyWithdrawals: 每個月所有提出加總金額。
*/
}

interface MonthBalance {
  date: string;
  balance: number;
  fullDateTime: string;
  rawData: CsvRow;
  isLastDayOfMonth: boolean;
}

interface MonthlyTotals {
  [yearMonth: string]: {
    deposits: number;
    withdrawals: number;
  };
}

function normalizeFullWidthChars(text: string | null | undefined): string {
  if (text === null || text === undefined) {
    return "";
  }
  const fullWidthMap: { [key: string]: string } = {
    "０": "0",
    "１": "1",
    "２": "2",
    "３": "3",
    "４": "4",
    "５": "5",
    "６": "6",
    "７": "7",
    "８": "8",
    "９": "9",
    "－": "-",
    "＿": "_",
    "／": "/",
    "（": "(",
    "）": ")",
    "　": " ",
    "Ｆ": "F",
    "ｆ": "f",
  };
  return text
    .split("")
    .map((char) => fullWidthMap[char] || char)
    .join("");
}

function parseIdFromRemark(
  remarkStr: string | null | undefined,
  allCanonicalIdsSet: Set<string>,
  amount?: number
): string | null {
  if (!remarkStr) return null;
  let normalizedRemark = normalizeFullWidthChars(remarkStr);
  // Remove all whitespace for robust matching
  const normalizedRemarkNoSpace = normalizedRemark.replace(/\s+/g, "");
  // Normalize search strings as well
  const search711 = normalizeFullWidthChars("７１１建中門市").replace(
    /\s+/g,
    ""
  );
  const searchJianzhong = normalizeFullWidthChars("建中").replace(/\s+/g, "");
  // Regex for full word '建中' (not part of a longer word)
  const jianzhongRegex = new RegExp(`(^|[^\w])${searchJianzhong}([^\w]|$)`);
  // Debug log for special condition
  if (
    (normalizedRemarkNoSpace.includes(search711) ||
      jianzhongRegex.test(normalizedRemarkNoSpace)) &&
    typeof amount === "number"
  ) {
    console.log("[parseIdFromRemark] Special condition check:", {
      normalizedRemark,
      normalizedRemarkNoSpace,
      amount,
    });
  }
  // Special condition: if remark contains '７１１建中門市' or '建中' (as a word) and amount > 6000, assign to 06-01-1
  if (
    (normalizedRemarkNoSpace.includes(search711) ||
      jianzhongRegex.test(normalizedRemarkNoSpace)) &&
    typeof amount === "number" &&
    amount > 6000
  ) {
    return "06-01-1";
  }
  let match = normalizedRemark.match(/(\d{1,2})[-_ /](\d{1,2})[-_ /](\d)/);
  if (match) {
    const b = match[1].padStart(2, "0");
    const f = match[2].padStart(2, "0");
    const u = match[3];
    const candidateId = `${b}-${f}-${u}`;
    if (allCanonicalIdsSet.has(candidateId)) return candidateId;
  }
  const chiNumMap = new Map<string, string>([
    ["一", "1"],
    ["二", "2"],
    ["三", "3"],
    ["四", "4"],
    ["五", "5"],
    ["六", "6"],
    ["七", "7"],
    ["八", "8"],
    ["九", "9"],
    ["十", "10"],
    ["0", "0"],
    ["1", "1"],
    ["2", "2"],
    ["3", "3"],
    ["4", "4"],
    ["5", "5"],
    ["6", "6"],
    ["7", "7"],
    ["8", "8"],
    ["9", "9"],
  ]);
  const textPattern =
    /([0-9０-９一二三四五六七八九十]+)\s*號\s*([0-9０-９一二三四五六七八九十]+)\s*(?:樓(?:之)?|[Ff])\s*[-－]?\s*([0-9０-９一二三四五六七八九十])/;
  const textMatch = normalizedRemark.match(textPattern);

  // Debug logging for specific case
  if (normalizedRemark.includes("69") && normalizedRemark.includes("4")) {
    console.log("[parseIdFromRemark] Debug for 69-04 pattern:", {
      originalRemark: remarkStr,
      normalizedRemark,
      textPattern: textPattern.toString(),
      textMatch,
    });
  }

  if (textMatch) {
    const bText = textMatch[1];
    const fText = textMatch[2];
    const uText = textMatch[3];
    try {
      const bDigit = normalizeFullWidthChars(bText)
        .split("")
        .map((char) => chiNumMap.get(char) || char)
        .join("");
      const fDigit = normalizeFullWidthChars(fText)
        .split("")
        .map((char) => chiNumMap.get(char) || char)
        .join("");
      const uDigit = normalizeFullWidthChars(uText)
        .split("")
        .map((char) => chiNumMap.get(char) || char)
        .join("");
      if (
        /^\d+$/.test(bDigit) &&
        /^\d+$/.test(fDigit) &&
        /^\d+$/.test(uDigit)
      ) {
        const candidateId = `${bDigit.padStart(2, "0")}-${fDigit.padStart(2, "0")}-${uDigit}`;

        // Debug logging for specific case
        if (bDigit === "69" && fDigit === "4") {
          console.log("[parseIdFromRemark] Generated candidate ID for 69-04:", {
            bDigit, fDigit, uDigit, candidateId,
            hasInSet: allCanonicalIdsSet.has(candidateId),
            setSize: allCanonicalIdsSet.size
          });
        }

        if (allCanonicalIdsSet.has(candidateId)) return candidateId;
      }
    } catch (e) {
      /* Ignore */
    }
  }

  // Special cases for 6-1 and 8-1 patterns
  const specialPattern = /\b([68])-1\b/;
  const specialMatch = normalizedRemark.match(specialPattern);
  if (specialMatch) {
    const building = specialMatch[1];
    const candidateId = building === "6" ? "06-01-1" : "08-01-1";

    // Debug logging for special cases
    console.log("[parseIdFromRemark] Special case match:", {
      originalRemark: remarkStr,
      normalizedRemark,
      building,
      candidateId,
      hasInSet: allCanonicalIdsSet.has(candidateId)
    });

    if (allCanonicalIdsSet.has(candidateId)) return candidateId;
  }

  // 6-digit typo: e.g. 711001 → 71-10-1 (user added extra 0 to last digit)
  const sixDigitPattern = /\b(\d{6})\b/g;
  let sixMatch;
  while ((sixMatch = sixDigitPattern.exec(normalizedRemark)) !== null) {
    const numStr = sixMatch[1];
    // Try to match 71-10-1 pattern with extra 0
    // e.g. 711001 → 71-10-1 (if 5th digit is 0, drop it)
    if (
      (numStr.startsWith("69") || numStr.startsWith("71")) &&
      numStr[4] === "0"
    ) {
      // 71 10 01 → 71-10-1
      const b = numStr.substring(0, 2);
      const f = numStr.substring(2, 4);
      const u = numStr.substring(5, 6); // last digit
      const candidateId = `${b}-${f}-${u}`;
      if (allCanonicalIdsSet.has(candidateId)) return candidateId;
    }
  }
  const numPattern = /\b(\d{3,5})\b/g;
  let numMatches;
  while ((numMatches = numPattern.exec(normalizedRemark)) !== null) {
    const numStr = numMatches[1];
    let candidateId: string | null = null;
    if (numStr.length === 3) {
      const b = numStr.charAt(0);
      const f = numStr.charAt(1);
      const u_val = numStr.charAt(2);
      if (b === "6" || b === "8") candidateId = `0${b}-0${f}-${u_val}`;
    } else if (numStr.length === 4) {
      const b_2d = numStr.substring(0, 2);
      const f_1d = numStr.charAt(2);
      const u_val = numStr.charAt(3);
      if (b_2d === "69" || b_2d === "71")
        candidateId = `${b_2d}-0${f_1d}-${u_val}`;
      else {
        const b_1d = numStr.charAt(0);
        const f_2d = numStr.substring(1, 3);
        if (b_1d === "6" || b_1d === "8")
          candidateId = `0${b_1d}-${f_2d}-${u_val}`;
      }
    } else if (numStr.length === 5) {
      const b = numStr.substring(0, 2);
      const f = numStr.substring(2, 4);
      const u_val = numStr.charAt(4);
      if (b === "69" || b === "71") candidateId = `${b}-${f}-${u_val}`;
    }
    if (candidateId && allCanonicalIdsSet.has(candidateId)) return candidateId;
  }
  return null;
}

// --- Hardcoded Resident IDs ---
const allResidentIds = new Set([
  "06-01-1",  "06-02-1",  "06-02-2",  "06-03-1",  "06-03-2",  "06-04-1",  "06-04-2",  "06-05-1",  "06-05-2",  "06-06-1",
  "06-06-2",  "06-07-1",  "06-07-2",  "06-08-1",  "06-08-2",  "06-09-1",  "06-09-2",  "06-10-1",  "06-10-2",  "06-11-1",
  "06-11-2",  "06-12-1",  "08-01-1",  "08-02-1",  "08-02-2",  "08-03-1",  "08-03-2",  "08-04-1",  "08-04-2",  "08-05-1",
  "08-05-2",  "08-06-1",  "08-06-2",  "08-07-1",  "08-07-2",  "08-08-1",  "08-08-2",  "08-09-1",  "08-09-2",  "08-10-1",
  "08-10-2",  "08-11-1",  "08-11-2",  "69-02-1",  "69-02-2",  "69-03-1",  "69-03-2",  "69-04-1",  "69-04-2",  "69-05-1",
  "69-05-2",  "69-06-1",  "69-06-2",  "69-07-1",  "69-07-2",  "69-08-1",  "69-08-2",  "69-09-1",  "69-09-2",  "69-10-1",
  "69-10-2",  "69-11-1",  "69-11-2",  "71-02-1",  "71-02-2",  "71-02-3",  "71-02-5",  "71-02-6",  "71-02-8",  "71-03-1",
  "71-03-2",  "71-03-3",  "71-03-5",  "71-03-6",  "71-03-8",  "71-04-1",  "71-04-2",  "71-04-3",  "71-04-5",  "71-04-6",
  "71-04-8",  "71-05-1",  "71-05-2",  "71-05-3",  "71-05-5",  "71-05-6",  "71-05-8",  "71-06-1",  "71-06-2",  "71-06-3",
  "71-06-5",  "71-06-6",  "71-06-8",  "71-07-1",  "71-07-2",  "71-07-3",  "71-07-5",  "71-07-6",  "71-07-8",  "71-08-1",
  "71-08-2",  "71-08-3",  "71-08-5",  "71-08-6",  "71-08-8",  "71-09-1",  "71-09-2",  "71-09-3",  "71-09-5",  "71-09-6",
  "71-09-8",  "71-10-1",  "71-10-2",  "71-10-3",  "71-10-5",  "71-10-6",  "71-10-8",  "71-11-1",  "71-11-2",  "71-11-3",
  "71-11-5",  "71-11-6",  "71-11-8",
]);

const RESIDENT_PATTERNS = new Map([
  ['06-01-1', [/438530/]],
  ['06-02-1', [/108087/]],
  ['06-03-1', [/024312/]],
  ['06-04-2', [/077204/]],
  ['06-05-2', [/095111/]],
  ['08-02-1', [/009729/]],
  ['08-03-1', [/851891/]],
  ['08-04-1', [/940206/]],
  ['08-07-2', [/192300/]],
  ['08-09-1', [/008449/]],
  ['69-03-2', [/602805/]],
  ['69-05-1', [/122528/]],
  ['69-07-1', [/141612/]],
  ['69-07-2', [/200074/]],
  ['69-11-1', [/510297/]],
  ['69-11-2', [/510297/]],
  ['71-03-6', [/000115/, /074001/]], // Multiple patterns for same resident
  ['71-04-1', [/363291/]],
  ['71-05-1', [/173203/]],
  ['71-05-5', [/499934/, /040578/]], // Multiple patterns for same resident
  ['71-06-3', [/790712/]],
  ['71-07-6', [/035458/]],
  ['71-09-2', [/402654/]],
  ['71-10-1', [/008494/]],
  ['71-10-2', [/231291/, /056874/]], // Multiple patterns for same resident
  ['71-10-3', [/029559/]],
  ['71-10-6', [/222616/]],
  ['71-11-2', [/075385/]],
  ['71-11-3', [/885119/]],
  ['71-11-6', [/998228/]],
  ['71-11-8', [/231742/]],
]);

const SPECIAL_WITHDRAWAL_KEYWORDS = [
  "大昆保全", "社區清潔", "冠福機電保養", "社區公共意外險",
  "存款息", "信義房屋", "永慶房屋", "全國數位", "電話費",
  "電費", "水費", "區權會出席費", "0000025035077777"
];

type CsvRow = {
  交易日期: string;
  交易資訊: string;
  帳務日期: string;
  說明: string;
  提出: string;
  存入: string;
  餘額: string;
  備註: string;
};

const MONTH_ABBREVIATIONS = [
  "01",  "02",  "03",  "04",  "05",  "06",  "07",  "08",  "09",  "10",  "11",  "12",
];

// Extend globalThis to allow depositsUnmatched property
declare global {
  // eslint-disable-next-line no-var
  var depositsUnmatched: any[] | undefined;
  var depositsSmallAmount: any[] | undefined;
  var withdrawals: any[];
  var withdrawalsUnmatched: any[] | undefined;
  var monthlyTotals: MonthlyTotals | undefined;
  var motorBikeParkingLot: Array<{
    transactionDate: string;
    type: "CREDIT";
    amount: number;
    remark: string;
    row: any;
  }> | undefined;

  var lastDayBalances: { [monthYear: string]: MonthBalance } | undefined;
  var depositsMaintenanceFee: { [residentId: string]: { [monthYear: string]: number } } | undefined;
  var specialWithdrawals: Array<{
    transactionDate: string;
    type: "DEBIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }> | undefined;
}

interface UploadResult {
  success: boolean;
  message: string;
  spreadsheetUrl?: string;
  depositsMaintenanceFee?: (string | number | null)[][];
  depositsUnmatched?: any[];
  depositsSmallAmount?: any[];
  motorBikeParkingLot?: any[];
  withdrawals?: any[];
  withdrawalsUnmatched?: any[];
}

export async function uploadDepositToSheets(
  csvContent: string,
  enableUpdate: boolean
): Promise<UploadResult> {
  const { userId: clerkUserId } = await auth();
  if (!clerkUserId) {
    return { success: false, message: "Unauthorized: Please sign in." };
  }

  // Extract the accountId once at the start for reuse
  const [accountId, endDate] = csvMetadataExtraction(csvContent)
  const monthEndInfo = getEndOfMonthDate(endDate);
  console.log("Processing account:", accountId);
  console.log("Month end safety check:", {
    endDate,
    isAlreadyEndOfMonth: monthEndInfo.isAlreadyEndOfMonth,
    isSafeToUpdate: monthEndInfo.isSafeToUpdate,
    currentDate: monthEndInfo.currentDate,
    endOfMonthDate: monthEndInfo.endOfMonthDate
  });

  const spreadsheetId = process.env.GOOGLE_MANAGEMENT_SHEETS_ID!;
  if (!spreadsheetId) {
    console.error(
      "Google Sheets Spreadsheet ID (GOOGLE_MANAGEMENT_SHEETS_ID) not configured."
    );
    return {
      success: false,
      message: "Server configuration error. Please contact support.",
    };
  }

  // Reset global variables to avoid duplicates across runs
  globalThis.withdrawals = [];
  globalThis.depositsUnmatched = [];
  globalThis.depositsSmallAmount = [];
  globalThis.withdrawalsUnmatched = [];
  globalThis.motorBikeParkingLot = [];
  globalThis.monthlyTotals = {};

  try {
    const googleAuth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY!?.replace(
          /\\n/g,
          "\n"
        ),
      },
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });

    const sheets = google.sheets({ version: "v4", auth: googleAuth });

    // Clean and prepare CSV content for parsing
    function prepareCsvContent(content: string): string {
      // Split into lines but preserve newlines within quoted fields
      const lines: string[] = [];
      let currentLine = "";
      let insideQuotes = false;

      for (let i = 0; i < content.length; i++) {
        const char = content[i];

        if (char === '"') {
          insideQuotes = !insideQuotes;
        }

        if (char === "\n" && !insideQuotes) {
          if (currentLine.trim()) {
            lines.push(currentLine);
          }
          currentLine = "";
        } else {
          currentLine += char;
        }
      }
      if (currentLine.trim()) {
        lines.push(currentLine);
      }

      // Find the header line
      const headerIndex = lines.findIndex(
        (line) =>
          line.includes("交易日期") &&
          line.includes("帳務日期") &&
          line.includes("說明") &&
          line.includes("提出") &&
          line.includes("存入") &&
          line.includes("餘額") &&
          line.includes("交易資訊") &&
          line.includes("備註")
      );

      if (headerIndex === -1) return "";

      // Keep only the header and data lines
      const relevantLines = lines.slice(headerIndex);

      // Clean each line
      const cleanedLines = relevantLines
        .map((line) => {
          // Normalize spaces and handle quotes
          return line
            .replace(/\s+/g, " ") // normalize multiple spaces
            .replace(/""/g, '"') // handle escaped quotes
            .trim();
        })
        .join("\n");

      return cleanedLines;
    }

    // Use the new prepareCsvContent function to clean and normalize the CSV
    const cleanCsvContent = prepareCsvContent(csvContent);
    if (!cleanCsvContent) {
      return {
        success: false,
        message:
          "Could not find valid CSV headers. Expected: 交易日期, 帳務日期, 說明, 提出, 存入, 餘額, 交易資訊, 備註",
      };
    }

    // Parse the cleaned content
    const records: CsvRow[] = parse(cleanCsvContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
      quote: '"',
      escape: '"',
      relax_quotes: true,
      relax_column_count: true,
      skip_records_with_error: true,
    });

    console.log("Parsed records count:", records.length);
    if (records.length > 0) {
      console.log("First record:", records[0]);
      console.log("Record keys:", Object.keys(records[0]));
    }

    const allCanonicalIds: string[] = Array.from(allResidentIds).sort();
    const allCanonicalIdsSet = new Set(allCanonicalIds);

    // Set up data structures for parallel processing results
    // Note: depositsMaintenanceFee data will be stored in globalThis.depositsMaintenanceFee

    let targetYear = new Date().getFullYear();
    if (records.length > 0 && records[0]["交易日期"]) {
      const dateStr = String(records[0]["交易日期"]).split(/[\s\n]/)[0];
      const dateParts = dateStr.split("/");
      if (dateParts.length >= 2) {
        const year = parseInt(dateParts[0]);
        if (!isNaN(year)) {
          targetYear = year;
        }
      }
    }

    console.log("Target year:", targetYear);

    // Declare withdrawalsData to collect special withdrawal rows for reporting
    let withdrawalsData:
      | Array<{
          transactionDate: string;
          type: "DEBIT" | "CREDIT";
          remark: string;
          amount: string | number;
          row: CsvRow;
        }>
      | undefined;
    // Declare lastDayBalances to store the last day balance for each month
    const lastDayBalances: { [monthYear: string]: MonthBalance } = {};
   
    // Process calculations in parallel
    /*await Promise.all([
      // Calculate monthly totals
      (async () => {
        const totals = calculateMonthlyTotals(records);
        globalThis.monthlyTotals = totals;
        console.log("Monthly totals calculated:", globalThis.monthlyTotals);
      })(),

      // Process others
      (async () => {
        for (const row of records) {
          const withdrawAmountStr = row["提出"];
          const depositAmountStr = row["存入"];
          const remark = row["備註"];
          const transactionInfo = row["交易資訊"];
          const transactionDateStr = row["交易日期"];

          // Check if this row is the last day of the month for its month
          if (transactionDateStr) {
            const fullDateTimeStr = String(transactionDateStr); // Keep full datetime
            const dateStr = fullDateTimeStr.split(/[\s\n]/)[0];
            const dateParts = dateStr.split("/");
            if (dateParts.length === 3) {
              const year = parseInt(dateParts[0]);
              const month = parseInt(dateParts[1]);
              const day = parseInt(dateParts[2]);
              console.log("transactionDateStr", transactionDateStr);
              console.log("fullDateTime", { year, month, day });
              if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
                const monthYearKey = `${year}/${String(month).padStart(2, "0")}`;
                const lastDay = new Date(year, month, 0).getDate();
                const balance = row["餘額"];
                const isLastDayOfMonth = day === lastDay;

                const currentDate = new Date(year, month - 1, day);
                const currentTime = fullDateTimeStr.split(/[\s\n]/)[1];
                console.log("currentTime", currentTime);
                if (currentTime) {
                  const [hours, minutes] = currentTime.split(":");
                  currentDate.setHours(parseInt(hours), parseInt(minutes));
                }

                const existingBalance = lastDayBalances[monthYearKey];
                const updateBalance =
                  !existingBalance || // No existing balance
                  (existingBalance && // Or this record is newer
                    new Date(existingBalance.fullDateTime) <= currentDate);

                if (updateBalance) {
                  const balanceNum =
                    typeof balance === "string"
                      ? parseFloat(balance.replace(/,/g, ""))
                      : balance;
                  if (!isNaN(balanceNum)) {
                    lastDayBalances[monthYearKey] = {
                      date: dateStr,
                      fullDateTime: fullDateTimeStr,
                      balance: balanceNum,
                      isLastDayOfMonth,
                      rawData: row, // Store the complete row data
                    };
                  }
                }
              }
            }
          }

          if (
            depositAmountStr &&
            depositAmountStr.trim() !== "−" &&
            transactionDateStr
          ) {
            try {
              const amount = parseFloat(depositAmountStr.replace(/,/g, ""));
              if (isNaN(amount) || amount < 974) {
                // Collect small deposit rows (amount <= 974) for reporting
                if (!globalThis.depositsSmallAmount)
                  globalThis.depositsSmallAmount = [];
                globalThis.depositsSmallAmount.push({
                  transactionDate:
                    String(transactionDateStr).split(/[\s\n]/)[0],
                  type: "CREDIT",
                  amount,
                  remark,
                  row,
                  reason: "amount < 974",
                });
                continue;
              }

              const dateStr = String(transactionDateStr).split(/[\s\n]/)[0];
              const dateParts = dateStr.split("/");
              if (dateParts.length !== 3) continue;

              const year = parseInt(dateParts[0]);
              const monthNum = parseInt(dateParts[1]);

              if (year === targetYear && monthNum >= 1 && monthNum <= 12) {
                const monthAbbr = MONTH_ABBREVIATIONS[monthNum - 1];
                const monthYearKey = `${year}/${monthAbbr}`;
                let parsedRid = parseIdFromRemark(
                  remark,
                  allCanonicalIdsSet,
                  amount
                );
                // 新增例外條件：交易資訊或備註包含「173203」時，指定給 71-05-1
                const resident_06_01_1 = /438530/;
                const resident_06_02_1 = /108087/;
                const resident_06_03_1 = /024312/;
                const resident_06_04_2 = /077204/;
                const resident_06_05_2 = /095111/;
                const resident_08_02_1 = /009729/;
                const resident_08_04_1 = /940206/;
                const resident_08_07_2 = /192300/;
                const resident_08_09_1 = /008449/;
                const resident_69_03_2 = /602805/;
                const resident_69_05_1 = /122528/;
                const resident_69_07_2 = /200074/;
                const resident_69_11_1 = /510297/;
                const resident_69_11_2 = /510297/;
                const resident_71_03_6_2024 = /074001/;
                const resident_71_03_6 = /000115/;
                const resident_71_04_1 = /363291/;
                const resident_71_05_1 = /173203/;
                const resident_71_05_5_2024 = /040578/;
                const resident_71_05_5 = /499934/;
                const resident_71_06_3 = /790712/;
                const resident_71_07_6 = /035458/;
                const resident_71_09_2 = /402654/;
                const resident_71_10_1 = /008494/;
                const resident_71_10_2_2024 = /056874/;
                const resident_71_10_2 = /231291/;
                const resident_71_10_3 = /029559/;
                const resident_71_10_6 = /222616/;
                const resident_71_11_2 = /075385/;
                const resident_71_11_3 = /885119/;
                const resident_71_11_6 = /998228/;
                const resident_71_11_8 = /231742/;
                //
                // Assign by pattern: 173203 → 71-05-1, 035458 → 71-07-6
                if (!parsedRid) {
                  if (
                    (transactionInfo &&
                      resident_06_01_1.test(transactionInfo)) ||
                    (remark && resident_06_01_1.test(remark))
                  ) {
                    parsedRid = "06-01-1";
                  } else if (
                    (transactionInfo &&
                      resident_06_02_1.test(transactionInfo)) ||
                    (remark && resident_06_02_1.test(remark))
                  ) {
                    parsedRid = "06-02-1";
                  } else if (
                    (transactionInfo &&
                      resident_06_03_1.test(transactionInfo)) ||
                    (remark && resident_06_03_1.test(remark))
                  ) {
                    parsedRid = "06-03-1";
                  } else if (
                    (transactionInfo &&
                      resident_06_04_2.test(transactionInfo)) ||
                    (remark && resident_06_04_2.test(remark))
                  ) {
                    parsedRid = "06-04-2";
                  } else if (
                    (transactionInfo &&
                      resident_06_05_2.test(transactionInfo)) ||
                    (remark && resident_06_05_2.test(remark))
                  ) {
                    parsedRid = "06-05-2";
                  } else if (
                    (transactionInfo &&
                      resident_08_02_1.test(transactionInfo)) ||
                    (remark && resident_08_02_1.test(remark))
                  ) {
                    parsedRid = "08-02-1";
                  } else if (
                    (transactionInfo &&
                      resident_08_04_1.test(transactionInfo)) ||
                    (remark && resident_08_04_1.test(remark))
                  ) {
                    parsedRid = "08-04-1";
                  } else if (
                    (transactionInfo &&
                      resident_08_07_2.test(transactionInfo)) ||
                    (remark && resident_08_07_2.test(remark))
                  ) {
                    parsedRid = "08-07-2";
                  } else if (
                    (transactionInfo &&
                      resident_08_09_1.test(transactionInfo)) ||
                    (remark && resident_08_09_1.test(remark))
                  ) {
                    parsedRid = "08-09-1";
                  } else if (
                    (transactionInfo &&
                      resident_69_03_2.test(transactionInfo)) ||
                    (remark && resident_69_03_2.test(remark))
                  ) {
                    parsedRid = "69-03-2";
                  } else if (
                    (transactionInfo &&
                      resident_69_05_1.test(transactionInfo)) ||
                    (remark && resident_69_05_1.test(remark))
                  ) {
                    parsedRid = "69-05-1";
                  } else if (
                    (transactionInfo &&
                      resident_69_07_2.test(transactionInfo)) ||
                    (remark && resident_69_07_2.test(remark))
                  ) {
                    parsedRid = "69_07_2";
                  } else if (
                    (transactionInfo &&
                      resident_69_11_1.test(transactionInfo)) ||
                    (remark && resident_69_11_1.test(remark) && amount >= 1288)
                  ) {
                    parsedRid = "69-11-1";
                  } else if (
                    (transactionInfo &&
                      resident_69_11_2.test(transactionInfo)) ||
                    (remark && resident_69_11_2.test(remark) && amount >= 1396)
                  ) {
                    parsedRid = "69-11-2";
                  } else if (
                    (transactionInfo &&
                      resident_71_03_6.test(transactionInfo)) ||
                    (remark && resident_71_03_6.test(remark))
                  ) {
                    parsedRid = "71-03-6";
                  } else if (
                    (transactionInfo &&
                      resident_71_04_1.test(transactionInfo)) ||
                    (remark && resident_71_04_1.test(remark))
                  ) {
                    parsedRid = "71-04-1";
                  } else if (
                    (transactionInfo &&
                      resident_71_05_1.test(transactionInfo)) ||
                    (remark && resident_71_05_1.test(remark))
                  ) {
                    parsedRid = "71-05-1";
                  } else if (
                    (transactionInfo &&
                      resident_71_05_5_2024.test(transactionInfo)) ||
                    (remark && resident_71_05_5_2024.test(remark))
                  ) {
                    parsedRid = "71-05-5";
                  } else if (
                    (transactionInfo &&
                      resident_71_05_5.test(transactionInfo)) ||
                    (remark && resident_71_05_5.test(remark))
                  ) {
                    parsedRid = "71-05-5";
                  } else if (
                    (transactionInfo &&
                      resident_71_06_3.test(transactionInfo)) ||
                    (remark && resident_71_06_3.test(remark))
                  ) {
                    parsedRid = "71-06-3";
                  } else if (
                    (transactionInfo &&
                      resident_71_07_6.test(transactionInfo)) ||
                    (remark && resident_71_07_6.test(remark))
                  ) {
                    parsedRid = "71-07-6";
                  } else if (
                    (transactionInfo &&
                      resident_71_09_2.test(transactionInfo)) ||
                    (remark && resident_71_09_2.test(remark))
                  ) {
                    parsedRid = "71-09-2";
                  } else if (
                    (transactionInfo &&
                      resident_71_10_1.test(transactionInfo)) ||
                    (remark && resident_71_10_1.test(remark))
                  ) {
                    parsedRid = "71-10-1";
                  } else if (
                    (transactionInfo &&
                      resident_71_10_2_2024.test(transactionInfo)) ||
                    (remark && resident_71_10_2_2024.test(remark))
                  ) {
                    parsedRid = "71-10-2";
                  } else if (
                    (transactionInfo &&
                      resident_71_10_2.test(transactionInfo)) ||
                    (remark && resident_71_10_2.test(remark))
                  ) {
                    parsedRid = "71-10-2";
                  } else if (
                    (transactionInfo &&
                      resident_71_10_3.test(transactionInfo)) ||
                    (remark && resident_71_10_3.test(remark))
                  ) {
                    parsedRid = "71-10-3";
                  } else if (
                    (transactionInfo &&
                      resident_71_10_6.test(transactionInfo)) ||
                    (remark && resident_71_10_6.test(remark))
                  ) {
                    parsedRid = "71-10-6";
                  } else if (
                    (transactionInfo &&
                      resident_71_11_2.test(transactionInfo)) ||
                    (remark && resident_71_11_2.test(remark))
                  ) {
                    parsedRid = "71-11-2";
                  } else if (
                    (transactionInfo &&
                      resident_71_11_3.test(transactionInfo)) ||
                    (remark && resident_71_11_3.test(remark))
                  ) {
                    parsedRid = "71-11-3";
                  } else if (
                    (transactionInfo &&
                      resident_71_11_6.test(transactionInfo)) ||
                    (remark && resident_71_11_6.test(remark))
                  ) {
                    parsedRid = "71-11-6";
                  } else if (
                    (transactionInfo &&
                      resident_71_11_8.test(transactionInfo)) ||
                    (remark && resident_71_11_8.test(remark))
                  ) {
                    parsedRid = "71-11-8";
                  }
                }
                if (parsedRid) {
                  if (!depositsMaintenanceFee[parsedRid]) {
                    depositsMaintenanceFee[parsedRid] = {};
                  }
                  depositsMaintenanceFee[parsedRid][monthYearKey] =
                    (depositsMaintenanceFee[parsedRid][monthYearKey] || 0) +
                    amount;
                  console.log(
                    "Added deposit from resident:",
                    parsedRid,
                    monthYearKey,
                    amount
                  );
                } else {
                  // Collect unmatched deposit rows
                  if (!globalThis.depositsUnmatched)
                    globalThis.depositsUnmatched = [];
                  globalThis.depositsUnmatched.push({
                    transactionDate: dateStr,
                    type: "CREDIT",
                    amount,
                    remark,
                    row,
                  });
                }
              }
            } catch (error) {
              console.error("Error processing row:", row, error);
            }
          }

          // Collect special withdrawal rows for reporting (separate from depositsMaintenanceFee)
          if (
            withdrawAmountStr &&
            withdrawAmountStr.trim() !== "−" &&
            transactionDateStr
          ) {
            try {
              // Extract special remark rows for reporting
              const specialKeywords = [
                "大昆保全",
                "社區清潔",
                "冠福機電保養",
                "社區公共意外險",
                "存款息",
                "信義房屋",
                "永慶房屋",
                "全國數位",
                "電話費",
                "電費",
                "水費",
                "區權會出席費",
                "0000025035077777",
              ];
              if (
                (remark &&
                  specialKeywords.some((keyword) =>
                    remark.includes(keyword)
                  )) ||
                (transactionInfo &&
                  specialKeywords.some((keyword) =>
                    transactionInfo.includes(keyword)
                  ))
              ) {
                if (!withdrawalsData) withdrawalsData = [];

                const dateStr = String(transactionDateStr).split(/[\s\n]/)[0];
                const dateParts = dateStr.split("/");
                if (dateParts.length === 3) {
                  const year = parseInt(dateParts[0]);
                  const monthNum = parseInt(dateParts[1]);
                  if (!isNaN(year) && monthNum >= 1 && monthNum <= 12) {
                    const amount = parseFloat(
                      withdrawAmountStr.replace(/,/g, "")
                    );

                    // Push directly to array (no date grouping)
                    // Find the first matching keyword in remark
                    const matchedKeyword = specialKeywords.find(
                      (keyword) =>
                        (remark && remark.includes(keyword)) ||
                        (transactionInfo && transactionInfo.includes(keyword))
                    );
                    withdrawalsData.push({
                      transactionDate: dateStr, // Match the key name from depositsUnmatched
                      type: "DEBIT",
                      remark: matchedKeyword || "", // e.g., "水費",
                      amount: isNaN(amount) ? withdrawAmountStr : amount,
                      row,
                    });
                    // Also push to globalThis.withdrawals for client access
                    if (!globalThis.withdrawals) globalThis.withdrawals = [];
                    globalThis.withdrawals.push({
                      transactionDate: dateStr,
                      type: "DEBIT",
                      remark: matchedKeyword || "",
                      amount: isNaN(amount) ? withdrawAmountStr : amount,
                      row,
                    });
                  }
                }
              } else {
                // Collect unmatched withdrawal rows
                if (!globalThis.withdrawalsUnmatched)
                  globalThis.withdrawalsUnmatched = [];
                const dateStr = String(transactionDateStr).split(/[\s\n]/)[0];
                const amount = parseFloat(withdrawAmountStr.replace(/,/g, ""));
                globalThis.withdrawalsUnmatched.push({
                  transactionDate: dateStr,
                  type: "DEBIT",
                  remark: remark || "",
                  amount: isNaN(amount) ? withdrawAmountStr : amount,
                  row,
                });
              }
            } catch {
              // Ignore errors for special extraction
            }
          }
        }
      })(),
    ]);*/

    // Main processing function
    async function processRecordsInParallel() {
      // Set up data structures
      // Note: lastDayBalances is already declared in the outer scope
      
      // Get target year
      let targetYear = new Date().getFullYear();
      if (records.length > 0 && records[0]["交易日期"]) {
        const dateStr = String(records[0]["交易日期"]).split(/[\s\n]/)[0];
        const dateParts = dateStr.split("/");
        if (dateParts.length >= 2) {
          const year = parseInt(dateParts[0]);
          if (!isNaN(year)) {
            targetYear = year;
          }
        }
      }

      console.log("Target year:", targetYear);

      // Initialize globalThis arrays to ensure they're clean
      globalThis.depositsUnmatched = [];
      globalThis.depositsSmallAmount = [];
      globalThis.withdrawals = [];
      globalThis.withdrawalsUnmatched = [];
      globalThis.motorBikeParkingLot = [];

      // Pre-filter records by type
      const depositRecords = records.filter(row =>
        row["存入"] && row["存入"].trim() !== "−"
      );
      const withdrawalRecords = records.filter(row =>
        row["提出"] && row["提出"].trim() !== "−"
      );

      console.log("[DEBUG] Records summary before processing:", {
        totalRecords: records.length,
        depositRecords: depositRecords.length,
        withdrawalRecords: withdrawalRecords.length,
        sampleRecord: records[0],
        lastRecord: records[records.length - 1]
      });

      // Process in parallel
      const [monthlyTotals, balances, deposits, withdrawals, motorBikeParkingLot] = await Promise.all([
        // Calculate monthly totals
        (async () => {
          const totals = calculateMonthlyTotals(records);
          console.log("Monthly totals calculated:", totals);
          return totals;
        })(),

        // Process last day balances
        processLastDayBalances(records),

        // Process deposits
        processDeposits(depositRecords, allCanonicalIdsSet, targetYear),

        // Process withdrawals
        processWithdrawals(withdrawalRecords),

        // Process motor bike parking lot
        processMotorBikeParkingLot(depositRecords)
      ]);

      // Assign results to global variables and local variables
      globalThis.monthlyTotals = monthlyTotals;
      globalThis.lastDayBalances = balances;
      globalThis.depositsMaintenanceFee = deposits;
      globalThis.specialWithdrawals = withdrawals;
      // motorBikeParkingLot is already assigned in the processMotorBikeParkingLot function
      //console.log("depositsMaintenaceFee", deposits);
      //console.log("globalThis.depositsMaintenanceFee", globalThis.depositsMaintenanceFee);

      Object.assign(lastDayBalances, balances);

      // depositsMaintenanceFee is now stored in globalThis.depositsMaintenanceFee
      
      // Note: globalThis arrays are already populated by the individual processing functions
      // But you can also access them here if needed for additional processing
      
      {/*console.log("All processing completed in parallel");
      console.log("Results summary:");
      console.log("- Monthly totals:", Object.keys(monthlyTotals).length, "months");
      console.log("- Last day balances:", Object.keys(balances).length, "months");
      console.log("- Deposits matched:", Object.keys(deposits).length, "residents");
      console.log("- Deposits unmatched:", globalThis.depositsUnmatched?.length || 0);
      console.log("- Deposits small amount:", globalThis.depositsSmallAmount?.length || 0);
      console.log("- Special withdrawals:", globalThis.withdrawals?.length || 0);
      console.log("- Withdrawals unmatched:", globalThis.withdrawalsUnmatched?.length || 0);
      */}

      //console.log("- Monthly totals calculated:", globalThis.monthlyTotals);
      //console.log("////////////////////Final deposits data:////////////////////", depositsMaintenanceFee);
      //console.log("////////////////////depositsSmallAmount////////////////////", globalThis.depositsSmallAmount);
      //console.log("////////////////////Final withdrawals data:////////////////////", globalThis.withdrawals);
      //console.log("////////////////////globalThis.withdrawalsUnmatched////////////////////", globalThis.withdrawalsUnmatched);
      //console.log("////////////////////globalThis.depositsUnmatched////////////////////", globalThis.depositsUnmatched);

      // Store monthly totals:
      if (enableUpdate) {
        const storeResult = await storeMonthlyTotals({
          originalId: accountId,
          monthlyTotals: globalThis.monthlyTotals!
        });

        if (!storeResult.success) {
          console.error("Failed to store monthly totals:", storeResult.error);
        } else {
          console.log("Monthly totals stored successfully");
        }        
      }
    }
    await processRecordsInParallel();
    
   
    const sheetName =`管理費收入${targetYear}`;
    const getResp = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range: `${sheetName}!A:Z`,
    });
    const values = getResp.data.values as
      | (string | number | null)[][]
      | undefined;
    if (!values || values.length < 2) {
      return { success: false, message: "Sheet data not found or empty." };
    }
    const headers = values[0];
    const rows = values.slice(1);

    // Collect all cell updates for batch update
    const batchUpdates: Array<{ range: string; values: any[][] }> = [];
    const updatedCells: Array<{
      residentId: string;
      monthYear: string;
      amount: number;
    }> = [];

    // Check if "扣除機踏車位" column already exists
    const motorBikeDeductionColName = "扣除機踏車位";
    const existingMotorBikeColIdx = headers.findIndex((h) => h === motorBikeDeductionColName);

    console.log("Motor bike deduction column check:", {
      columnName: motorBikeDeductionColName,
      existingIndex: existingMotorBikeColIdx,
      exists: existingMotorBikeColIdx !== -1
    });

    // Calculate motor bike parking deductions by resident
    const motorBikeDeductions: { [residentId: string]: number } = {};

    {/*console.log("[DEBUG] Motor bike parking lot processing:", {
      hasMotorBikeParkingLot: !!globalThis.motorBikeParkingLot,
      motorBikeParkingLotLength: globalThis.motorBikeParkingLot?.length || 0,
      motorBikeParkingLotData: globalThis.motorBikeParkingLot
    });*/}

    if (globalThis.motorBikeParkingLot) {
      //console.log("[DEBUG] Processing motor bike parking entries...");

      for (const entry of globalThis.motorBikeParkingLot) {
        // Extract resident ID from the transaction data
        const remark = entry.remark || "";
        const transactionInfo = entry.row?.交易資訊 || "";

        {/*console.log("[DEBUG] Processing motor bike entry:", {
          entry,
          remark,
          transactionInfo,
          amount: entry.amount
        });*/}

        // Try to parse resident ID from the motor bike parking entry
        const parsedRid = parseIdFromRemark(remark, allCanonicalIdsSet, entry.amount) ||
                         findResidentByPattern(transactionInfo, remark, entry.amount);

        {/*console.log("[DEBUG] Parsed resident ID:", {
          remark,
          transactionInfo,
          amount: entry.amount,
          parsedRid
        });*/}

        if (parsedRid) {
          motorBikeDeductions[parsedRid] = (motorBikeDeductions[parsedRid] || 0) + entry.amount;
          {/*console.log("[DEBUG] Added to motorBikeDeductions:", {
            residentId: parsedRid,
            amount: entry.amount,
            totalForResident: motorBikeDeductions[parsedRid]
          });*/}
        } else {
          console.log("[DEBUG] Failed to parse resident ID for motor bike entry:", {
            remark,
            transactionInfo,
            amount: entry.amount
          });
        }
      }
    } else {
      console.log("[DEBUG] No motorBikeParkingLot data found in globalThis");
    }

    //console.log("[DEBUG] Final motorBikeDeductions:", motorBikeDeductions);

    //console.log("depositsMaintenanceFee: ==================>", globalThis.depositsMaintenanceFee);
    for (const residentId of Object.keys(globalThis.depositsMaintenanceFee || {})) {
      const residentPayments = globalThis.depositsMaintenanceFee![residentId];
      const rowIdx = rows.findIndex((r) => r[1] === residentId);
      if (rowIdx === -1) {
        console.log("Resident not found in sheet:", residentId);
        continue;
      }
      const sheetRowIdx = rowIdx + 2;

      // Update maintenance fee columns
      for (const monthYear of Object.keys(residentPayments)) {
        const colIdx = headers.findIndex((h) => h === monthYear);
        if (colIdx === -1) {
          console.log("Month column not found:", monthYear);
          continue;
        }
        const sheetColIdx = colIdx + 1;
        const cellRange = `${sheetName}!${String.fromCharCode(64 + sheetColIdx)}${sheetRowIdx}`;
        const amount = residentPayments[monthYear];

        // Apply update to local rows array
        rows[rowIdx][colIdx] = amount;

        batchUpdates.push({ range: cellRange, values: [[amount]] });
        updatedCells.push({ residentId, monthYear, amount });
      }

      // Add motor bike parking deduction for this resident (only if column exists)
      {/*console.log(`[DEBUG] Checking motor bike deduction for resident ${residentId}:`, {
        hasDeduction: !!motorBikeDeductions[residentId],
        deductionAmount: motorBikeDeductions[residentId],
        columnExists: existingMotorBikeColIdx !== -1,
        columnIndex: existingMotorBikeColIdx
      });*/}

      if (motorBikeDeductions[residentId] && existingMotorBikeColIdx !== -1) {
        const deductionAmount = motorBikeDeductions[residentId] * -1; // Negative amount for deduction
        const deductionCellRange = `${sheetName}!${String.fromCharCode(65 + existingMotorBikeColIdx)}${sheetRowIdx}`;

        batchUpdates.push({ range: deductionCellRange, values: [[deductionAmount]] });
        //console.log(`[DEBUG] SUCCESS: Adding motor bike deduction for ${residentId}: ${deductionAmount} at ${deductionCellRange}`);
      } else {
        {/*if (!motorBikeDeductions[residentId]) {
          console.log(`[DEBUG] SKIP: No motor bike deduction for resident ${residentId}`);
        }*/}
        if (existingMotorBikeColIdx === -1) {
          console.log(`[DEBUG] SKIP: Motor bike deduction column does not exist`);
        }
      }
    }

    // (Disable for test)
    // Batch update all cells in one request
    console.log("[DEBUG] Batch update summary:", {
      totalUpdates: batchUpdates.length,
      enableUpdate,
      motorBikeDeductionsCount: Object.keys(motorBikeDeductions).length,
      batchUpdates: batchUpdates.map(update => ({
        range: update.range,
        value: update.values[0][0]
      }))
    });

    if (batchUpdates.length > 0 && enableUpdate) {
      console.log("[DEBUG] Executing batch update to Google Sheets...");
      await sheets.spreadsheets.values.batchUpdate({
        spreadsheetId,
        requestBody: {
          valueInputOption: "USER_ENTERED",
          data: batchUpdates,
        },
      });
      console.log("[DEBUG] Batch update completed successfully");
    } else {
      if (batchUpdates.length === 0) {
        console.log("[DEBUG] No batch updates to perform");
      }
      if (!enableUpdate) {
        console.log("[DEBUG] Updates disabled (enableUpdate = false)");
      }
    }
    // Process withdrawals data if available this year
    if (
      globalThis.depositsUnmatched &&
      globalThis.depositsUnmatched.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()
    ) {
      const result = await createIncomeFromDeposits(
        globalThis.depositsUnmatched,
        accountId
      );
      if (!result.success) {
        console.error("Failed to create income records:", result.error);
      }
    }

    // Process small amount deposits this year
    if (
      globalThis.depositsSmallAmount &&
      globalThis.depositsSmallAmount.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()
    ) {
      const result = await createIncomeFromSmallDeposits(
        globalThis.depositsSmallAmount,
        accountId
      );
      console.log("results===============>")
      if (!result.success) {
        console.error("Failed to create income records from small deposits:", result.error);
      }
    }

    // Process unmatched withdrawals if available this year
    if (
      globalThis.withdrawalsUnmatched &&
      globalThis.withdrawalsUnmatched.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()
    ) {
      const result = await createExpenseFromWithdrawals(
        globalThis.withdrawalsUnmatched,
        accountId
      );
      if (!result.success) {
        console.error("Failed to create expense records from unmatched withdrawals:", result.error);
      }
    }

    if (
      withdrawalsData &&
      withdrawalsData.length > 0 &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      enableUpdate && targetYear === new Date().getFullYear()

    ) {
      const result = await createExpenseFromWithdrawals(
        withdrawalsData,
        accountId
      );
      if (!result.success) {
        console.error("Failed to create expense records:", result.error);
      }
    }

    // Store last day balances with only relevant raw data this year
    //console.log("monthEndInfo:==================>", monthEndInfo)
    //console.log("lastDayBalances:==================>", lastDayBalances)

    if (
      enableUpdate &&
      targetYear === new Date().getFullYear() &&
      monthEndInfo.isAlreadyEndOfMonth === true &&
      monthEndInfo.isSafeToUpdate === true &&
      Object.keys(lastDayBalances).length > 0
    ) {
      const relevantRawData = Object.values(lastDayBalances).map(
        (balance) => balance.rawData
      );

      console.log("relevantRawData before storing:==================>", relevantRawData);

      await storeLastDayBalances({
        originalId: accountId,
        balances: lastDayBalances,
        rawData: relevantRawData,
      });
    } else {
      console.log("Skipping storeLastDayBalances due to conditions:", {
        enableUpdate,
        targetYear,
        currentYear: new Date().getFullYear(),
        isAlreadyEndOfMonth: monthEndInfo.isAlreadyEndOfMonth,
        isSafeToUpdate: monthEndInfo.isSafeToUpdate,
        hasBalances: Object.keys(lastDayBalances).length > 0
      });
    }


    //console.log("Updated cells:", updatedCells);

    return {
      success: true,
      message: `CSV data processed and uploaded. Updated ${updatedCells.length} cells.`,
      spreadsheetUrl: `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit#gid=SHEET_TAB_ID_IF_KNOWN`,
      //updatedCells: updatedCells.map(c => [c.residentId, c.monthYear, c.amount]), // Separate field for updates
      depositsMaintenanceFee: [headers, ...rows],
      depositsUnmatched: globalThis.depositsUnmatched,
      depositsSmallAmount: globalThis.depositsSmallAmount,
      motorBikeParkingLot: globalThis.motorBikeParkingLot,
      withdrawals: withdrawalsData || [],
      withdrawalsUnmatched: globalThis.withdrawalsUnmatched,
    };
  } catch (error: any) {
    console.error("Error in uploadDepositToSheets:", error);
    return {
      success: false,
      message: `Upload error: ${error.message || "Unknown server error"}`,
    };
  }
}

// ---  將可歸類支出匯入 bankAccount.expense 資料表 ---
async function createExpenseFromWithdrawals(
  withdrawalsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error(
        "Virtual account IDs not configured in environment variables"
      );
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    // Process all withdrawals and prepare the data first
    const preparedData = await Promise.all(
      withdrawalsData
        .filter((withdrawal) => withdrawal.remark !== "−") // Filter out transactions with remark "−"
        .map(async (withdrawal) => {
          const amount =
            typeof withdrawal.amount === "string"
              ? parseFloat(withdrawal.amount.replace(/,/g, ""))
              : withdrawal.amount;

          if (isNaN(amount)) return null;

          // Get category suggestion
          const { categoryId: suggestedCategoryId } = await suggestCategory(
            withdrawal.remark,
            amount,
            "DEBIT"
          );

          // Parse and validate date
          const dateParts = withdrawal.transactionDate.split("/");
          if (dateParts.length !== 3) {
            throw new Error(
              `Invalid date format for monthYear: ${withdrawal.transactionDate}`
            );
          }

          const [year, month, day] = dateParts;
          const parsedYear = parseInt(year);
          const parsedMonth = parseInt(month) - 1;
          const parsedDay = parseInt(day);

          if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
            throw new Error(
              `Invalid date components for ${withdrawal.transactionDate}`
            );
          }

          // Parse time components (Taipei time UTC+8)
          let hours = 0,
            minutes = 0;
          if (withdrawal.transactionDate) {
            const [_, time] = withdrawal.transactionDate.split(" ");
            if (time) {
              const [h, m] = time.split(":");
              const parsedHours = parseInt(h);
              const parsedMinutes = parseInt(m);

              if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
                hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
                minutes = parsedMinutes;
              }
            }
          }

          const date = new Date(
            Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0)
          );

          return {
            amount,
            suggestedCategoryId,
            date,
            description: withdrawal.remark,
            transactionDate: withdrawal.transactionDate,
          };
        })
    );

    // Filter out any null values from invalid amounts
    const validData = preparedData.filter(
      (data): data is NonNullable<typeof data> => data !== null
    );

    // First ensure we have a default category
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: "OTHER",
          userId,
        },
      },
      update: {},
      create: {
        name: "OTHER",
        userId,
        icon: "HelpCircle",
        type: "DEBIT"
      },
    });

    // Execute all database operations in a single transaction
    //await prisma.$transaction(async (tx) => {
    // Process each withdrawal data
    for (const data of validData) {
      // Generate deduplication hash
      const importHash = calculateTransactionFullHash({
        date: data.transactionDate,
        type: "DEBIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.amount,
        description: data.description,
      });

      // Create expense record if it doesn't exist
      await prisma.expense.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Prisma.Decimal(data.amount),
          description: data.description,
          date: data.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "DEBIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }
    //});

    return { success: true };
  } catch (error) {
    console.error("Error creating expense records:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create expense records",
    };
  }
}

// --- 將管理費收入匯入 bankAccount.income 資料表 ---
async function createIncomeFromDeposits(
  depositsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error("Virtual account IDs not configured in environment variables");
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    // Process all deposits and prepare the data first
    const preparedData = await Promise.all(
      depositsData
        .filter(deposit => 
          !(deposit.remark === "−" && deposit.row.交易資訊 === "−") || // Keep if at least one field has info
          deposit.row.說明.includes("存款息") || // Keep deposit interest info
          deposit.row.交易資訊.includes("168888") || // Keep management committee account
          deposit.row.交易資訊.includes("僑＊福華社區管理委員會") // Keep management committee name
        )
        .map(async (deposit) => {
          const amount =
            typeof deposit.amount === "string"
              ? parseFloat(deposit.amount.replace(/,/g, ""))
              : deposit.amount;

          if (isNaN(amount)) return null;

          // Select the best description based on available information
          const description = deposit.remark !== "−" 
            ? deposit.remark 
            : deposit.row.交易資訊 !== "−"
              ? deposit.row.交易資訊.trim()
              : deposit.row.說明; // Fallback to 說明 if both are "-"

          // Get category suggestion
          const { categoryId: suggestedCategoryId } = await suggestCategory(
            description,
            amount,
            "CREDIT"
          );

          // Parse and validate date
          const dateParts = deposit.row.交易日期.split("/");
          if (dateParts.length !== 3) {
            throw new Error(`Invalid date format for monthYear: ${deposit.row.交易日期}`);
          }

          const [year, month, day] = dateParts;
          const parsedYear = parseInt(year);
          const parsedMonth = parseInt(month) - 1;
          const parsedDay = parseInt(day);

          if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
            throw new Error(`Invalid date components for ${deposit.transactionDate}`);
          }

          // Parse time components (Taipei time UTC+8)
          let hours = 0, minutes = 0;
          if (deposit.row.交易日期) {
            const [_, time] = deposit.row.交易日期.split(" ");
            if (time) {
              const [h, m] = time.split(":");
              const parsedHours = parseInt(h);
              const parsedMinutes = parseInt(m);

              if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
                hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
                minutes = parsedMinutes;
              }
            }
          }

          const date = new Date(
            Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0)
          );

          return {
            amount,
            suggestedCategoryId,
            date,
            description,
            transactionDate: deposit.transactionDate
          };
        })
    );

    // Filter out any null values from invalid amounts
    const validData = preparedData.filter((data): data is NonNullable<typeof data> => data !== null);

    // First ensure we have a default category for income
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: 'INCOME',
          userId,
        },
      },
      update: {},
      create: {
        name: 'INCOME',
        userId,
        icon: 'Briefcase',
        type: "CREDIT"
      },
    });

    // Process each deposit data
    console.log("----------------------------preparedData-----------------------------", preparedData)
    console.log("----------------------------validateData-----------------------------", validData)
    for (const data of validData) {
      // Generate deduplication hash
      const importHash = calculateTransactionFullHash({
        date: data.date,
        type: "CREDIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.amount,
        description: data.description,
      });

      // Create income record if it doesn't exist
      await prisma.income.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Prisma.Decimal(data.amount),
          description: data.description,
          date: data.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "CREDIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }

    return { success: true };
  } catch (error) {
    console.error("Error creating income records:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create income records",
    };
  }
}

// --- 將小額收入依金額歸類並匯入 bankAccount.income ---
async function createIncomeFromSmallDeposits(
  depositsData: Array<{
    transactionDate: string;
    type: "DEBIT" | "CREDIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }>,
  accountId: string
): Promise<{ success: boolean; error?: string; unmatched?: any[] }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("未經授權");

    // Account mapping based on pattern
    const managementAccountId = process.env.VIRTUAL_ACCOUNT_ID_MANAGEMENT;
    const parkingAccountId = process.env.VIRTUAL_ACCOUNT_ID_CAR_PARKING_SPACE;

    if (!managementAccountId || !parkingAccountId) {
      throw new Error(
        "Virtual account IDs not configured in environment variables"
      );
    }

    const targetAccountId = accountId.includes("************")
      ? managementAccountId
      : parkingAccountId;

    const unmatchedDeposits: any[] = [];
    
    // Create accumulators for each description type
    const accumulatedDeposits: {
      [key: string]: {
        totalAmount: number;
        date: Date;
        description: string;
        suggestedCategoryId?: string;
        transactions: Array<{
          date: Date;
          amount: number;
          transactionDate: string;
        }>;
      };
    } = {};

    // First pass: Prepare and validate all data
    for (const deposit of depositsData) {
      const amount = typeof deposit.amount === "string"
        ? parseFloat(deposit.amount.replace(/,/g, ""))
        : deposit.amount;

      if (isNaN(amount)) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid amount" });
        continue;
      }

      // Determine description based on amount and remark
      let description;
      if (amount < 500 || deposit.remark.includes("磁扣")) {
        description = "臨時停車費／磁扣";
      //} else if (amount % 500 === 0 && amount <= 2500 && amount !== 974 && amount !== 996) {
      } else if (amount % 500 === 0 && amount <= 2500) {
        description = "機車自行車清潔費";
        // Note: Motor bike parking data is now collected in processMotorBikeParkingLot function
      } else {
        unmatchedDeposits.push({
          ...deposit,
          reason: "Amount too large for small deposits"
        });
        continue;
      }

      // Parse date parts
      const dateParts = deposit.row.交易日期.split("/");
      if (dateParts.length !== 3) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid date format" });
        continue;
      }

      const [year, month, day] = dateParts;
      const parsedYear = parseInt(year);
      const parsedMonth = parseInt(month) - 1;
      const parsedDay = parseInt(day);

      if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
        unmatchedDeposits.push({ ...deposit, reason: "Invalid date components" });
        continue;
      }

      // Parse time components (Taipei time UTC+8)
      let hours = 0, minutes = 0;
      if (deposit.row.交易日期) {
        const [_, time] = deposit.row.交易日期.split(" ");
        if (time) {
          const [h, m] = time.split(":");
          const parsedHours = parseInt(h);
          const parsedMinutes = parseInt(m);

          if (!isNaN(parsedHours) && !isNaN(parsedMinutes)) {
            hours = parsedHours >= 8 ? parsedHours - 8 : parsedHours + 16;
            minutes = parsedMinutes;
          }
        }
      }

      const date = new Date(Date.UTC(parsedYear, parsedMonth, parsedDay, hours, minutes, 0));

      // Get category suggestion
      const { categoryId: suggestedCategoryId } = await suggestCategory(
        description,
        amount,
        "CREDIT"
      );

      // Accumulate by description
      if (!accumulatedDeposits[description]) {
        accumulatedDeposits[description] = {
          totalAmount: 0,
          date: date, // Use the first transaction's date
          description,
          suggestedCategoryId,
          transactions: []
        };
      }

      accumulatedDeposits[description].totalAmount += amount;
      accumulatedDeposits[description].transactions.push({
        date,
        amount,
        transactionDate: deposit.transactionDate
      });
    }

    // First ensure we have a default category for income
    const defaultCategory = await prisma.category.upsert({
      where: {
        name_userId: {
          name: 'INCOME',
          userId,
        },
      },
      update: {},
      create: {
        name: 'INCOME',
        userId,
        icon: 'Briefcase',
        type: "CREDIT"
      },
    });

    // Process each accumulated deposit
    for (const [description, data] of Object.entries(accumulatedDeposits)) {
      // Sort transactions by date and use the latest date
      const sortedTransactions = data.transactions.sort((a, b) => b.date.getTime() - a.date.getTime());
      const latestTransaction = sortedTransactions[0];

      // Generate deduplication hash using the accumulated data
      const importHash = calculateTransactionFullHash({
        date: latestTransaction.date,
        type: "CREDIT",
        categoryId: defaultCategory.id,
        accountId: targetAccountId,
        amount: data.totalAmount,
        description: description,
      });

      // Create or update the income record
      await prisma.income.upsert({
        where: { importHash },
        update: {}, // No update if exists
        create: {
          amount: new Prisma.Decimal(data.totalAmount),
          description: description,
          date: latestTransaction.date,
          categoryId: defaultCategory.id,
          userId,
          accountId: targetAccountId,
          type: "CREDIT",
          categoryValidated: true,
          suggestedCategoryId: data.suggestedCategoryId,
          importHash,
        },
      });
    }

    return {
      success: true,
      unmatched: unmatchedDeposits,
    };
  } catch (error) {
    console.error("Error creating income records:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create income records",
      unmatched: [],
    };
  }
}

// --- 抓取管理費收入年份 Sheets 資料表 ---
interface FetchSheetDataResult {
  success: boolean;
  message: string;
  data?: (string | number | null)[][]; // Array of arrays (rows and cells)
  headers?: (string | number | null)[];
  depositsUnmatched?: any[];
  depositsSmallAmount?: any[];
  motorBikeParkingLot?: any[];
  withdrawals?: any[];
  withdrawalsUnmatched?: any[];
}

export async function fetchManagementSheetData(targetYear: string): Promise<FetchSheetDataResult> {
  try {
    const { userId: clerkUserId } = await auth();
    const spreadsheetId = process.env.GOOGLE_MANAGEMENT_SHEETS_ID;
    if (!spreadsheetId) {
      console.error("Google Sheets Management Spreadsheet ID not configured.");
      return {
        success: false,
        message: "Server configuration error for display.",
      };
    }
    const googleAuth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY!?.replace(
          /\\n/g,
          "\n"
        ),
      },
      scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
    });
    if (!clerkUserId || !googleAuth) {
      return { success: false, message: "Unauthorized: Please sign in." };
    }
    const sheets = google.sheets({ version: "v4", auth: googleAuth });
    const sheetName = `管理費收入${targetYear}`; // Your specified sheet name
    const range = `${sheetName}!A:Z`; // Read all columns
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: spreadsheetId,
      range: range,
    });
    const values = response.data.values as
      | (string | number | null)[][]
      | undefined; // Type assertion
    // Return the globalThis data if available
    return {
      success: !!values && values.length > 0,
      message:
        values && values.length > 0
          ? "Data fetched successfully."
          : `No data found in sheet '${sheetName}'.`,
      headers: values ? values[0] : undefined,
      data: values ? values.slice(1) : undefined,
      depositsUnmatched: globalThis.depositsUnmatched || [],
      depositsSmallAmount: globalThis.depositsSmallAmount || [],
      motorBikeParkingLot: globalThis.motorBikeParkingLot || [],
      withdrawals: globalThis.withdrawals || [],
      withdrawalsUnmatched: globalThis.withdrawalsUnmatched || [],
    };
  } catch (error: any) {
    console.error("Error fetching management sheet data:", error);
    return {
      success: false,
      message: `Failed to fetch sheet data: ${error.message || "Unknown server error"}`,
    };
  }
}

// Helper to convert processingDate (e.g., "2025年05月16日") to "2025/05"
function formatMonthYear(date: string): string {
  const match = date.match(/(\d{4})年(\d{2})月/);
  if (!match) return "";

  const year = match[1]; // "2025"
  const month = match[2]; // "05"

  return `${year}/${month}`;
}

// Private helper for single fill 塡入住戶自動轉帳資料表
async function fillAutoPayAmountForResidentSingle({
  residentId,
  monthYear,
  amount,
}: {
  residentId: string;
  monthYear: string;
  amount: number;
}): Promise<{ success: boolean; message: string }> {
  console.log(
    "===================================residentId, monthYear, amount",
    {
      residentId,
      monthYear,
      amount,
    }
  );
  const spreadsheetId = process.env.GOOGLE_MANAGEMENT_SHEETS_ID;
  if (!spreadsheetId) {
    return { success: false, message: "Server configuration error." };
  }
  try {
    const googleAuth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL!,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY!?.replace(
          /\\n/g,
          "\n"
        ),
      },
      scopes: ["https://www.googleapis.com/auth/spreadsheets"],
    });
    const sheets = google.sheets({ version: "v4", auth: googleAuth });
    const targetYear = monthYear.split("/")[0];
    const sheetName = `管理費收入${targetYear}`;
    // 1. Fetch current data
    const getResp = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range: `${sheetName}!A:Z`,
    });
    const values = getResp.data.values as
      | (string | number | null)[][]
      | undefined;
    if (!values || values.length < 2) {
      return { success: false, message: "Sheet data not found or empty." };
    }
    const headers = values[0];
    const rows = values.slice(1);
    // 2. Find the column index for monthYear
    const colIdx = headers.findIndex((h) => h === monthYear);
    if (colIdx === -1) {
      return { success: false, message: `Column '${monthYear}' not found.` };
    }
    // 3. Find the row index for residentId (住戶編號 is column 1)
    const rowIdx = rows.findIndex((r) => r[1] === residentId);
    if (rowIdx === -1) {
      return {
        success: false,
        message: `Resident ID '${residentId}' not found.`,
      };
    }
    // 4. Update the value in the correct cell
    const sheetRowIdx = rowIdx + 2; // +2 because sheet is 1-based and first row is header
    const sheetColIdx = colIdx + 1; // 1-based
    const cellRange = `${sheetName}!${String.fromCharCode(64 + sheetColIdx)}${sheetRowIdx}`;
    await sheets.spreadsheets.values.update({
      spreadsheetId,
      range: cellRange,
      valueInputOption: "USER_ENTERED",
      requestBody: { values: [[amount]] },
    });
    return {
      success: true,
      message: `Filled amount for ${residentId} in ${monthYear}.`,
    };
  } catch (error: any) {
    return { success: false, message: error.message || "Unknown error" };
  }
}

// Unified 塡入住戶自動轉帳資料表: supports both single and batch input
export async function fillAutoPayAmountForResident(arg: any): Promise<any> {
  // Batch mode: AutoPayResidentData
  if (arg && Array.isArray(arg.records)) {
    const data = arg;
    const monthYear = data.processingDate ? formatMonthYear(data.processingDate) : "";
    const results: { success: boolean; message: string }[] = [];
    for (const record of data.records) {
      const result = await fillAutoPayAmountForResidentSingle({
        residentId: record.residentId,
        monthYear,
        amount: record.amount,
      });
      results.push(result);
    }
    return results;
  }
  // Single record mode (original logic)
  const { residentId, monthYear, amount } = arg;
  return fillAutoPayAmountForResidentSingle({ residentId, monthYear, amount });
}

function calculateMonthlyTotals(records: CsvRow[]): MonthlyTotals {
  const monthlyTotals: MonthlyTotals = {};

  for (const row of records) {
    // Skip rows without transaction date
    if (!row["交易日期"]) continue;

    // Extract year and month from transaction date (format: YYYY/MM/DD)
    const fullDateTimeStr = String(row["交易日期"]);
    const dateStr = fullDateTimeStr.split(/[\s\n]/)[0]; // Get date part only
    const dateParts = dateStr.split('/');
    
    if (dateParts.length < 3) continue; // Need at least year/month/day
    
    const year = parseInt(dateParts[0]);
    const month = parseInt(dateParts[1]);
    
    // Validate year and month
    if (isNaN(year) || isNaN(month) || year < 1900 || year > 2100 || month < 1 || month > 12) {
      console.warn(`Invalid date found: ${row["交易日期"]}, parsed as year: ${year}, month: ${month}`);
      continue;
    }
    
    const yearMonth = `${year}-${String(month).padStart(2, '0')}`;

    // Initialize month entry if it doesn't exist
    if (!monthlyTotals[yearMonth]) {
      monthlyTotals[yearMonth] = {
        deposits: 0,
        withdrawals: 0
      };
    }

    // Add deposit amount
    if (row["存入"] && row["存入"] !== "−") {
      const depositAmount = parseFloat(row["存入"].replace(/,/g, '')) || 0;
      monthlyTotals[yearMonth].deposits += depositAmount;
    }

    // Add withdrawal amount
    if (row["提出"] && row["提出"] !== "−") {
      const withdrawalAmount = parseFloat(row["提出"].replace(/,/g, '')) || 0;
      monthlyTotals[yearMonth].withdrawals += withdrawalAmount;
    }
  }

  return monthlyTotals;
}
// Helper function to find resident by pattern
function findResidentByPattern(transactionInfo: string, remark: string, amount: number): string | null {
  //const textToSearch = `${transactionInfo || ''} ${remark || ''}`;
  const textToSearch = `${transactionInfo || ''}`;

  for (const [residentId, patterns] of RESIDENT_PATTERNS) {
    // Check if any of the patterns match
    const hasMatch = patterns.some(pattern => pattern.test(textToSearch));

    if (hasMatch) {
      // Special cases for amount-dependent matching
      if (residentId === '69-11-1' && amount < 1288) continue;
      if (residentId === '69-11-2' && amount < 1396) continue;
      return residentId;
    }
  }
  return null;
}

// Separate processing functions
function processLastDayBalances(records: CsvRow[]): { [monthYear: string]: MonthBalance } {
  const balances: { [monthYear: string]: MonthBalance } = {};

  console.log("[DEBUG] processLastDayBalances called with", records.length, "records");

  for (const row of records) {
    const transactionDateStr = row["交易日期"];
    if (!transactionDateStr) {
      console.log("[DEBUG] Skipping row - no transaction date:", row);
      continue;
    }

    const fullDateTimeStr = String(transactionDateStr);
    const dateStr = fullDateTimeStr.split(/[\s\n]/)[0];
    const dateParts = dateStr.split("/");

    console.log("[DEBUG] Processing row:", {
      transactionDateStr,
      fullDateTimeStr,
      dateStr,
      dateParts,
      balance: row["餘額"]
    });

    if (dateParts.length !== 3) {
      console.log("[DEBUG] Skipping row - invalid date parts:", dateParts);
      continue;
    }

    const year = parseInt(dateParts[0]);
    const month = parseInt(dateParts[1]);
    const day = parseInt(dateParts[2]);

    if (isNaN(year) || isNaN(month) || isNaN(day)) {
      console.log("[DEBUG] Skipping row - invalid date numbers:", { year, month, day });
      continue;
    }
    
    const monthYearKey = `${year}/${String(month).padStart(2, "0")}`;
    const lastDay = new Date(year, month, 0).getDate();
    const balance = row["餘額"];
    const isLastDayOfMonth = day === lastDay;

    const currentDate = new Date(year, month - 1, day);
    const currentTime = fullDateTimeStr.split(/[\s\n]/)[1];
    console.log("balance", balance, "isLastDayOfMonth", isLastDayOfMonth);
    
    if (currentTime) {
      const [hours, minutes] = currentTime.split(":");
      currentDate.setHours(parseInt(hours), parseInt(minutes));
    }

    const existingBalance = balances[monthYearKey];
    const shouldUpdate = !existingBalance || 
      new Date(existingBalance.fullDateTime) <= currentDate;

    console.log("shouldUpdate===============================>", shouldUpdate);
    console.log("balances", balances);
    console.log("existingBalance", existingBalance);
    console.log("balances[monthYearKey]", balances[monthYearKey]);
    if (shouldUpdate) {
      const balanceNum = typeof balance === "string" 
        ? parseFloat(balance.replace(/,/g, "")) 
        : balance;
        
      if (!isNaN(balanceNum)) {
        balances[monthYearKey] = {
          date: dateStr,
          fullDateTime: fullDateTimeStr,
          balance: balanceNum,
          isLastDayOfMonth,
          rawData: row,
        };
      }
    }
  }

  console.log("[DEBUG] processLastDayBalances returning:", balances);
  console.log("[DEBUG] Number of balance entries found:", Object.keys(balances).length);

  return balances;
}

function processMotorBikeParkingLot(depositRecords: CsvRow[]): Array<{
  transactionDate: string;
  type: "CREDIT";
  amount: number;
  remark: string;
  row: any;
}> {
  const motorBikeParkingData: Array<{
    transactionDate: string;
    type: "CREDIT";
    amount: number;
    remark: string;
    row: any;
  }> = [];

  // Initialize global array if it doesn't exist
  if (!globalThis.motorBikeParkingLot) globalThis.motorBikeParkingLot = [];

  for (const row of depositRecords) {
    const depositAmountStr = row["存入"];
    const remark = row["備註"];
    const transactionDateStr = row["交易日期"];

    if (!depositAmountStr || depositAmountStr.trim() === "−" || !transactionDateStr) {
      continue;
    }

    try {
      const amount = parseFloat(depositAmountStr.replace(/,/g, ""));

      if (isNaN(amount)) continue;

      // Check if this is motor bike parking fee (機車自行車清潔費)
      if (amount % 500 === 0 && amount <= 2500) {
        const motorBikeEntry = {
          transactionDate: String(transactionDateStr).split(/[\s\n]/)[0],
          type: "CREDIT" as const,
          amount,
          remark: remark || "",
          row
        };

        motorBikeParkingData.push(motorBikeEntry);
        globalThis.motorBikeParkingLot.push(motorBikeEntry);
        console.log("Added motor bike parking entry:##################################", motorBikeEntry);
      }
    } catch (error) {
      console.error("Error processing motor bike parking deposit:", error);
    }
  }

  console.log("Processed motor bike parking data:", motorBikeParkingData.length, "entries");
  return motorBikeParkingData;
}

function processDeposits(
  depositRecords: CsvRow[],
  allCanonicalIdsSet: Set<string>,
  targetYear: number
): { [residentId: string]: { [monthYear: string]: number } } {
  const depositsMaintenanceFee: { [residentId: string]: { [monthYear: string]: number } } = {};
  
  // Initialize global arrays if they don't exist
  if (!globalThis.depositsSmallAmount) globalThis.depositsSmallAmount = [];
  if (!globalThis.depositsUnmatched) globalThis.depositsUnmatched = [];

  for (const row of depositRecords) {
    const depositAmountStr = row["存入"];
    const remark = row["備註"];
    const transactionInfo = row["交易資訊"];
    const transactionDateStr = row["交易日期"];

    if (!depositAmountStr || depositAmountStr.trim() === "−" || !transactionDateStr) {
      continue;
    }

    try {
      const amount = parseFloat(depositAmountStr.replace(/,/g, ""));

      // Debug logging for specific amount
      if (amount === 1472) {
        console.log("[processDeposits] Debug for amount 1472:", {
          amount,
          remark,
          transactionInfo,
          transactionDateStr,
          amountCheck: amount < 974 && amount % 500 !== 0
        });
      }

      if (isNaN(amount) || amount < 974 && amount % 500 !== 0) {
        globalThis.depositsSmallAmount.push({
          transactionDate: String(transactionDateStr).split(/[\s\n]/)[0],
          type: "CREDIT",
          amount,
          remark,
          row,
          reason: "amount < 974",
        });
        continue;
      }

      const dateStr = String(transactionDateStr).split(/[\s\n]/)[0];
      const dateParts = dateStr.split("/");
      
      if (dateParts.length !== 3) continue;

      const year = parseInt(dateParts[0]);
      const monthNum = parseInt(dateParts[1]);

      if (year === targetYear && monthNum >= 1 && monthNum <= 12) {
        const monthAbbr = MONTH_ABBREVIATIONS[monthNum - 1];
        const monthYearKey = `${year}/${monthAbbr}`;
        
        // Try to parse resident ID using the optimized function
        let parsedRid = parseIdFromRemark(remark, allCanonicalIdsSet, amount) ||
                       findResidentByPattern(transactionInfo || '', remark || '', amount);

        // Debug logging for specific amount
        if (amount === 1472) {
          console.log("[processDeposits] Parsing result for amount 1472:", {
            remark,
            transactionInfo,
            parsedRid,
            monthYearKey,
            year,
            targetYear,
            monthNum
          });
        }

        if (parsedRid) {
          if (!depositsMaintenanceFee[parsedRid]) {
            depositsMaintenanceFee[parsedRid] = {};
          }
          depositsMaintenanceFee[parsedRid][monthYearKey] =
            (depositsMaintenanceFee[parsedRid][monthYearKey] || 0) + amount;

          console.log("Added deposit from resident:", parsedRid, monthYearKey, amount);
        } else {
          // Debug logging for specific amount
          if (amount === 1472) {
            console.log("[processDeposits] Adding to depositsUnmatched - amount 1472:", {
              remark,
              transactionInfo,
              dateStr,
              reason: "No resident ID parsed"
            });
          }

          globalThis.depositsUnmatched.push({
            transactionDate: dateStr,
            type: "CREDIT",
            amount,
            remark,
            row,
          });
        }
      }
    } catch (error) {
      console.error("Error processing deposit row:", row, error);
    }
  }
  
  return depositsMaintenanceFee;
}

function processWithdrawals(withdrawalRecords: CsvRow[]): Array<{
  transactionDate: string;
  type: "DEBIT";
  remark: string;
  amount: string | number;
  row: CsvRow;
}> {
  const withdrawalsData: Array<{
    transactionDate: string;
    type: "DEBIT";
    remark: string;
    amount: string | number;
    row: CsvRow;
  }> = [];

  // Initialize global arrays if they don't exist
  if (!globalThis.withdrawals) globalThis.withdrawals = [];
  if (!globalThis.withdrawalsUnmatched) globalThis.withdrawalsUnmatched = [];

  // Map to accumulate special withdrawals by date and keyword
  const specialWithdrawalAccumulator = new Map<string, {
    transactionDate: string;
    type: "DEBIT";
    remark: string;
    amount: number;
    rows: CsvRow[];
    count: number;
  }>();

  for (const row of withdrawalRecords) {
    const withdrawAmountStr = row["提出"];
    const remark = row["備註"];
    const transactionInfo = row["交易資訊"];
    const transactionDateStr = row["交易日期"];

    if (!withdrawAmountStr || withdrawAmountStr.trim() === "−" || !transactionDateStr) {
      continue;
    }

    try {
      const dateStr = String(transactionDateStr).split(/[\s\n]/)[0];
      const dateParts = dateStr.split("/");
      
      if (dateParts.length !== 3) continue;
      
      const year = parseInt(dateParts[0]);
      const monthNum = parseInt(dateParts[1]);
      
      if (isNaN(year) || monthNum < 1 || monthNum > 12) continue;

      // Check if this is a special withdrawal
      const textToSearch = `${remark || ''} ${transactionInfo || ''}`;
      const matchedKeyword = SPECIAL_WITHDRAWAL_KEYWORDS.find(keyword =>
        textToSearch.includes(keyword)
      );

      const amount = parseFloat(withdrawAmountStr.replace(/,/g, ""));

      if (matchedKeyword) {
        // Special withdrawal - accumulate by date and keyword
        const accumulatorKey = `${dateStr}_${matchedKeyword}`;

        if (specialWithdrawalAccumulator.has(accumulatorKey)) {
          // Add to existing entry
          const existing = specialWithdrawalAccumulator.get(accumulatorKey)!;
          existing.amount += isNaN(amount) ? 0 : amount;
          existing.rows.push(row);
          existing.count += 1;

          console.log(`[DEBUG] Accumulating ${matchedKeyword} on ${dateStr}: ${existing.amount} (count: ${existing.count})`);
        } else {
          // Create new entry
          specialWithdrawalAccumulator.set(accumulatorKey, {
            transactionDate: dateStr,
            type: "DEBIT" as const,
            remark: matchedKeyword,
            amount: isNaN(amount) ? 0 : amount,
            rows: [row],
            count: 1
          });

          console.log(`[DEBUG] New ${matchedKeyword} entry on ${dateStr}: ${isNaN(amount) ? 0 : amount}`);
        }
      } else {
        // Unmatched withdrawal - add directly
        const withdrawalData = {
          transactionDate: dateStr,
          type: "DEBIT" as const,
          remark: remark || "",
          amount: isNaN(amount) ? withdrawAmountStr : amount,
          row,
        };
        globalThis.withdrawalsUnmatched.push(withdrawalData);
      }
    } catch (error) {
      console.error("Error processing withdrawal row:", row, error);
    }
  }

  // Process accumulated special withdrawals
  for (const [key, accumulated] of specialWithdrawalAccumulator.entries()) {
    const withdrawalData = {
      transactionDate: accumulated.transactionDate,
      type: "DEBIT" as const,
      remark: accumulated.count > 1
        ? `${accumulated.remark} (${accumulated.count} transactions)`
        : accumulated.remark,
      amount: accumulated.amount,
      row: accumulated.rows[0], // Use the first row as representative
    };

    withdrawalsData.push(withdrawalData);
    globalThis.withdrawals.push(withdrawalData);

    console.log(`[DEBUG] Final accumulated withdrawal: ${accumulated.remark} on ${accumulated.transactionDate} = ${accumulated.amount} (${accumulated.count} transactions)`);
  }

  return withdrawalsData;
}

"use client"

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTransition } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription,
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { joinWorkspace }  from "@/actions/workspaces/join-workspace";
import { toast } from "sonner";

interface JoinWorkspaceFormProps {
  initialValues: { id: string; name: string };
  inviteCode: string;
}

export const JoinWorkspaceForm = ({
  initialValues,
  inviteCode,
}: JoinWorkspaceFormProps) => {
  const router = useRouter()
  const [isPending, startTransition] = useTransition();

  const onSubmit = () => {
    startTransition(async () => {
      const response = await joinWorkspace(initialValues.id,inviteCode)
      if (response.success && response.data) {
        toast.success("You've successfully joined the workspace")
        router.push(`/workspaces/${response.data.id}`)
      } else {
        console.log("response", response)
        toast.error(response.error || "Something went wrong")
      }
    })
  }

  return (
    <Card className="w-full h-full border-none shadow-none">
      <CardHeader className="flex p-7">
        <CardTitle className="text-xl font-bold">
          Join workspace
        </CardTitle>
        <CardDescription>
          You&apos;ve been invited to join workspace: <strong>{initialValues?.name}</strong>
        </CardDescription>
      </CardHeader>
      <div className="px-7">
        <Separator />
      </div>
      <CardContent className="p-7">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-2">
          <Button variant="secondary" type="button" asChild className="w-full lg:-w-fit">
            <Link href="/dashboard">Cancel</Link>
          </Button>
          <Button disabled={isPending} onClick={onSubmit} variant="default" type="button" className="w-full lg:-w-fit">
            Join Workspace
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
  

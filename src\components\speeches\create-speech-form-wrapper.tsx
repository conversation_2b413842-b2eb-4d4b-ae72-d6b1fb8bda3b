"use client"

import { 
  Card, 
  CardContent, 
} from "@/components/ui/card";

import { Loader } from "lucide-react";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { useGetProjects } from "@/components/projects/use-get-projects";
import { CreateSpeechForm } from "./create-speech-form";


interface CreateSpeechFormWrapperProps {
  onCancel?: () => void;
}

export const CreateSpeechFormWrapper = ({ onCancel }: CreateSpeechFormWrapperProps) => {
  const workspaceId = useWorkspaceId();
  
  const { projects, isLoading: isLoadingProjects } = useGetProjects({
    workspaceId,
  });

  const projectOptions = projects?.map((project) => ({
    id: project.id,
    name: project.name,
    imageUrl: project.imageUrl
  }));


  const isLoading = isLoadingProjects || !workspaceId;

  if (isLoading) {
    <Card className="w-full h-[714px] border-none shadow-none">
      <CardContent className="flex items-center jistify-center h-full">
        <Loader className="size-5 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>;
  }

  return (
    <CreateSpeechForm 
      onCancel={onCancel}
      projectOption={projectOptions ?? []}
    />
  );
};
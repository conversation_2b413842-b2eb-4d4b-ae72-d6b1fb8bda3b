// src/components/statements/add-statement-from-row-dialog.tsx
"use client";

// Imports are the same as add-statements.tsx
import { BankAccount, Category, AccountType } from "@prisma/client";
import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray, SubmitHandler } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
// No DialogTrigger needed here
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { format, parse, isValid, startOfDay } from "date-fns";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Loader2, Plus } from "lucide-react";
import { CreateCategoryDialog } from "@/components/categories/create-categories";
import { createStatement } from "@/actions/statements/create-statement";

// Schema and types are the same
const statementSchema = z.object({
  type: z.enum(["CREDIT", "DEBIT"], { message: "請選擇有效的類型" }),
  amount: z.number().min(1, "金額為必填"),
  categoryId: z.string().min(3, "請選擇類別"),
  accountId: z.string().min(1, "請選擇帳戶"), // Keep accountId in schema
  description: z.string().optional(),
  transactionDate: z
    .union([
      z.date(),
      z.string().transform((val) => {
        if (val === '') return startOfDay(new Date());
        const parsedDate = parse(val, 'yyyy-MM-dd', new Date());
        return isValid(parsedDate) ? startOfDay(parsedDate) : startOfDay(new Date());
      })
    ])
    .optional()
    .default(startOfDay(new Date())),
});

type StatementFormValues = z.infer<typeof statementSchema>;
type AccountData = Pick<BankAccount, 'id' | 'name' | 'accountType'>;
type CategoryData = Pick<Category, 'id' | 'name'>;

// Props are similar to the previous controlled version
interface AddStatementFromRowDialogProps {
  categories: CategoryData[];
  accounts: AccountData[]; // Pass all relevant accounts initially
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultAccountId: string; // Make defaultAccountId required here
  onTransactionUpdate: () => Promise<void>;
}

export function AddStatementFromRowDialog({
  categories,
  accounts,
  open,
  onOpenChange,
  defaultAccountId,
  onTransactionUpdate,
}: AddStatementFromRowDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Initialize useForm with defaultValues including the first item
  const form = useForm<{ statements: StatementFormValues[] }>({
    resolver: zodResolver(z.object({
      statements: z.array(statementSchema).min(1) // Ensure at least one statement
    })),
    // Set the default structure with one item directly here
    defaultValues: {
      statements: [{
        type: "DEBIT",
        amount: 0,
        categoryId: "",
        accountId: defaultAccountId, // Use prop directly for initial value
        description: "",
        transactionDate: startOfDay(new Date()),
      }]
    }
  });

  // useEffect now focuses on resetting ONLY IF the defaultAccountId changes
  // while the dialog is already open, or to ensure correct ID when opening.
  useEffect(() => {
    if (open) {
       // Reset ensures the correct accountId is used if the dialog
       // was somehow kept mounted or if defaultAccountId changes.
       // We reset to a single item structure.
       form.reset({
         statements: [{
           type: "DEBIT",
           amount: 0,
           categoryId: "",
           accountId: defaultAccountId,
           description: "",
           transactionDate: startOfDay(new Date()),
         }],
       });
    }
    // Dependency array ensures reset happens when dialog opens OR accountId changes
  }, [open, defaultAccountId, form.reset]);


  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "statements",
  });

  const onSubmit: SubmitHandler<{ statements: StatementFormValues[] }> = async (formData) => {
    setIsLoading(true);
    try {
      const statementsToSend = formData.statements.map(statement => ({
          ...statement,
          accountId: defaultAccountId,
          description: statement?.description ?? "",
          date: statement.transactionDate || startOfDay(new Date()),
          currencyIso: "TWD",
      }));
      const result = await createStatement({ statements: statementsToSend });
      if (!result.success) {
        toast.error(result.error || "Failed to create statement(s)");
      } else {
        toast.success("記錄已成功創建！");
        onOpenChange(false);
        await onTransactionUpdate();
        form.reset();
      }
    } catch (error) {
      console.error("提交表單時發生錯誤:", error);
      toast.error("創建記錄失敗");
    } finally {
      setIsLoading(false);
    }
  };

  // Find the specific account object to display its name/type
  const targetAccount = accounts.find(acc => acc.id === defaultAccountId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle className="my-4">為 {targetAccount?.name || '帳戶'} 新增記錄</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="space-y-4 border-b pb-4 last:border-b-0">
                {/* --- Fields --- */}
                 <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {/* Amount field */}
                    <FormField
                      control={form.control}
                      name={`statements.${index}.amount`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>金額</FormLabel>
                          <FormControl>
                            <Input type="number" placeholder="輸入金額" {...field} onChange={(e) => field.onChange(Number(e.target.value) || 0)} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Transaction type dropdown */}
                    <FormField
                      control={form.control}
                      name={`statements.${index}.type`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>交易類型</FormLabel>
                          <FormControl>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <SelectTrigger><SelectValue placeholder="選擇類型" /></SelectTrigger>
                              <SelectContent>
                                <SelectItem value="CREDIT">收入</SelectItem>
                                <SelectItem value="DEBIT">支出</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Transaction date */}
                    <FormField
                      control={form.control}
                      name={`statements.${index}.transactionDate`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>交易日期</FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              value={ field.value instanceof Date ? format(field.value, 'yyyy-MM-dd') : format(startOfDay(new Date()), 'yyyy-MM-dd') }
                              onChange={(e) => {
                                const parsedDate = parse(e.target.value, 'yyyy-MM-dd', new Date());
                                field.onChange(isValid(parsedDate) ? startOfDay(parsedDate) : startOfDay(new Date()));
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {/* Category dropdown */}
                    <FormField
                      control={form.control}
                      name={`statements.${index}.categoryId`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel><div className="h-8 flex items-center">類別 <CreateCategoryDialog /></div></FormLabel>
                          <FormControl>
                             <Select onValueChange={field.onChange} value={field.value}>
                               <SelectTrigger><SelectValue placeholder="選擇類別" /></SelectTrigger>
                               <SelectContent>
                                 {categories.map((category) => (
                                   <SelectItem key={category.id} value={category.id}>
                                     {category.name}
                                   </SelectItem>
                                 ))}
                               </SelectContent>
                             </Select>
                           </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Account Field (Display Only) */}
                    <FormItem>
                       <FormLabel><div className="h-8 flex items-center">銀行帳戶</div></FormLabel>
                       <FormControl>
                         <Input value={targetAccount ? `${targetAccount.name} (${targetAccount.accountType})` : defaultAccountId} disabled className="bg-muted/50" />
                       </FormControl>
                        {/* Hidden field to ensure accountId is submitted if needed by schema validation, though we override on submit */}
                       <input type="hidden" {...form.register(`statements.${index}.accountId`)} />
                    </FormItem>
                  </div>

                  {/* Description field */}
                  <FormField
                    control={form.control}
                    name={`statements.${index}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>描述（選填）</FormLabel>
                        <FormControl>
                          <Input placeholder="新增交易詳情" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* --- End Fields --- */}

                 {/* Remove button */}
                 {fields.length > 1 && (
                    <Button type="button" variant="destructive" size="sm" onClick={() => remove(index)} className="mt-2">
                      移除記錄
                    </Button>
                  )}
              </div>
            ))}
             {/* Action buttons */}
            <div className="flex justify-between items-center pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => append({
                  type: "DEBIT",
                  amount: 0,
                  categoryId: "",
                  accountId: defaultAccountId, // Use required ID for new rows
                  description: "",
                  transactionDate: startOfDay(new Date())
                 })}
              >
                <Plus className="mr-2 h-4 w-4" /> 新增更多記錄
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (<Loader2 className="mr-2 h-4 w-4 animate-spin" />) : null}
                儲存記錄
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
"use server";

import { prisma } from "@/lib/db";
import { Prisma } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

export async function updateTimeDeposit({
  id,
  accountId,
  certificateNo,
  period,
  interestRate,
  description,
  amount,
  type,
  categoryId,
  currencyIso,
  date,
}: {
  id: string;
  accountId?: string;
  certificateNo?: string;
  period?: string;
  interestRate?: number;
  description?: string;
  amount?: number;
  type?: "AVAILABLE" | "WITHDRAWN";
  categoryId?: string;
  currencyIso?: string;
  date?: Date;
}) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const existingDeposit = await prisma.timeDeposit.findUnique({
      where: { id },
    });

    if (!existingDeposit) {
      throw new Error("Time deposit not found");
    }
    // Ensure `amount` and `accountId` are not null
    const prevAmount = existingDeposit.amount ?? 0;
    const depositAccountId = existingDeposit.accountId ?? accountId;

    if (!depositAccountId) {
      throw new Error("Account ID is missing");
    }

    // Calculate balance adjustments only if amount or type is changing
    let balanceAdjustment = 0;
    if (type !== existingDeposit.type || (amount !== undefined && amount !== prevAmount)) {
      // If type changes to WITHDRAWN, deduct the full amount
      if (type === "WITHDRAWN") {
        balanceAdjustment = -prevAmount;
      } 
      // If type changes from WITHDRAWN to AVAILABLE, add the amount back
      else if (existingDeposit.type === "WITHDRAWN") {
        balanceAdjustment = prevAmount;
      }
      // If amount changes and type is AVAILABLE, adjust accordingly
      else if (type === "AVAILABLE" && amount !== undefined) {
        balanceAdjustment = amount - prevAmount;
      }
    }

    // Update the time deposit
    const updatedDeposit = await prisma.timeDeposit.update({
      where: { id },
      data: {
        accountId,
        certificateNo,
        period,
        interestRate: new Prisma.Decimal(interestRate!),
        description,
        amount,
        type,
        categoryId,
        currencyIso,
        date,
      },
    });

    // Adjust account balance if amount or type changed
    if (balanceAdjustment !== 0) {
      const latestBalance = await prisma.balance.findFirst({
        where: { accountId: depositAccountId },
        orderBy: { date: "desc" },
      });

      const newBalanceAmount = (latestBalance?.amount ?? 0) + balanceAdjustment;
      const balanceCurrency = currencyIso ?? latestBalance?.currencyIso ?? "TWD";

      await prisma.balance.create({
        data: {
          amount: newBalanceAmount,
          date: new Date(),
          bankAccount: {
            connect: { id: depositAccountId },
          },
          currency: {
            connect: { iso: balanceCurrency },
          },
        },
      });
    }

    revalidatePath("/banking");
    return { 
      success: true, 
      timeDeposit: {
        ...updatedDeposit,
        interestRate: Number(updatedDeposit.interestRate)
      }
    };
  } catch (error) {
    console.error("Error updating time deposit:", error);
    return { success: false, error: "Failed to update time deposit" };
  }
}

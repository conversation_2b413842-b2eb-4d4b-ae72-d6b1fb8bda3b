import { format, setHours, setMinutes } from "date-fns";

export interface TimeComponents {
  hour: string;
  minute: string;
  ampm: string;
}

export const dateUtils = {
  // Convert local date to UTC for server
  toUTC: (date: Date | null | undefined): Date | null => {
    if (!date) return null;
    return new Date(date.getTime() + date.getTimezoneOffset() * 60000);
  },

  // Convert UTC date to local for display
  toLocal: (date: Date | null | undefined): Date | null => {
    if (!date) return null;
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000);
  },

  // Extract time components from a date
  getTimeComponents: (date: Date | null | undefined): TimeComponents => {
    if (!date) return { hour: "", minute: "", ampm: "AM" };
    
    return {
      hour: format(date, 'h'),
      minute: format(date, 'mm'),
      ampm: format(date, 'a')
    };
  },

  // Combine date and time components into a single date
  combineDateAndTime: (
    date: Date | undefined,
    { hour, minute, ampm }: TimeComponents
  ): Date | undefined => {
    if (!date || !hour || !minute) return undefined;

    let hours = parseInt(hour);
    if (ampm === "PM" && hours !== 12) {
      hours += 12;
    } else if (ampm === "AM" && hours === 12) {
      hours = 0;
    }

    let newDate = new Date(date);
    newDate = setHours(newDate, hours);
    newDate = setMinutes(newDate, parseInt(minute));

    return newDate;
  }
};
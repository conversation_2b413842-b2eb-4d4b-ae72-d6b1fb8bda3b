import { google } from "googleapis";
import { NextResponse } from "next/server";
import { authenticateGoogleOAuth2 } from "../authenticate";

const renameItemInDrive = async (fileId: string, newName: string) => {
  const auth = await authenticateGoogleOAuth2();
  if (!auth) {
    throw new Error('Authentication failed');
  }

  const drive = google.drive({ version: "v3", auth });

  try {
    const file = await drive.files.update({
      fileId: fileId,
      requestBody: {
        name: newName,
      },
      supportsAllDrives: true,
    });

    return { file: file.data };
  } catch (error) {
    console.error('Error renaming item:', error);
    throw error;
  }
};

export async function POST(req: Request) {
  try {
    const res = await req.json();
    const { fileId, newName } = res;

    if (!fileId || !newName) {
      return NextResponse.json(
        { error: 'File ID and new name are required' },
        { status: 400 }
      );
    }

    const result = await renameItemInDrive(fileId, newName);
    
    return NextResponse.json(
      { file: result.file },
      { status: 200 }
    );
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Failed to rename item' },
      { status: 500 }
    );
  }
}
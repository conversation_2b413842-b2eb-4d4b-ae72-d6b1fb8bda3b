import { WebhookEvent } from '@line/bot-sdk';
import { RichMenuType, createRichMenu, setUserRichMenu, uploadRichMenuImage } from './richMenu';
import { generateRichMenuImage } from './generate-richmenu-image';
import { sendMessage } from './send-message';
import { createTextMessage, createFlexMessage, createCarouselMessage } from './message-templates';

export const handleMenuChange = async (event: WebhookEvent) => {
  if (event.type !== 'postback' || !event.postback.data.startsWith('action=change_menu')) {
    return;
  }

  const params = new URLSearchParams(event.postback.data);
  const menuType = params.get('action_type') as RichMenuType;
  
  if (!menuType) {
    console.error('Invalid menu type');
    return;
  }

  try {
    // 創建新的 Rich Menu
    const richMenuId = await createRichMenu(menuType);
    console.log("==============richMenuId===================", richMenuId)
    
    // 生成並上傳圖片
    const imagePath = await generateRichMenuImage(menuType);
    await uploadRichMenuImage(richMenuId, imagePath);
    
    // 設定用戶的 Rich Menu
    await setUserRichMenu(event.source.userId!, richMenuId);
    
    console.log(`成功切換到 ${menuType} 選單`);
  } catch (error) {
    console.error('切換選單失敗:', error);
  }
};

export const handleMessageSend = async (event: WebhookEvent) => {
  if (event.type !== 'postback' || !event.postback.data.startsWith('action=send_message')) {
    return;
  }

  const params = new URLSearchParams(event.postback.data);
  const messageType = params.get('type') as RichMenuType;
  
  if (!messageType || !event.source.userId) {
    console.error('無效的訊息類型或用戶ID');
    return;
  }

  try {
    // 根據不同的訊息類型傳送對應的訊息
    switch (messageType) {
      case 'message':
        await sendMessage(event.source.userId, createTextMessage(event.source.userId));
        break;
      case 'flex':
        await sendMessage(event.source.userId, createFlexMessage());
        break;
      case 'carousel':
        await sendMessage(event.source.userId, createCarouselMessage());
        break;
      default:
        console.error('未支援的訊息類型:', messageType);
        return;
    }
    
    console.log(`成功傳送 ${messageType} 訊息給用戶 ${event.source.userId}`);
  } catch (error) {
    console.error('傳送訊息失敗:', error);
  }
};

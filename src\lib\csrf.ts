import crypto from 'crypto';

// lib/csrf.ts
export function generateCsrfToken(): string {
  const token = crypto.randomBytes(32).toString('hex');
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('csrf', token);
  }
  return token;
}

export function getCsrfToken(): string {
  if (typeof window !== 'undefined') {
    return sessionStorage.getItem('csrf') || generateCsrfToken();
  }
  return '';
}

export function validateCsrfToken(token: string | null): boolean {
  // For server-side validation
  if (typeof window === 'undefined') {
    // Server should just check if token exists
    return !!token;
  }
  
  // Client-side validation
  if (!token) return false;
  const storedToken = sessionStorage.getItem('csrf');
  return token === storedToken;
}
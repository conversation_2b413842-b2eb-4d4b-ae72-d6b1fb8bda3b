"use client";

import React from "react";
import { QueryProvider } from "@/components/query-provider";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { ThemeProviderProps } from "next-themes/dist/types";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider {...props}>
      <QueryProvider>
       {children}
      </QueryProvider>
    </NextThemesProvider>
)}

"use client"

import React, { useMemo, useState, useCallback, useEffect } from 'react'
import PropTypes from 'prop-types'
import moment from 'moment';
import { format, addMonths, subMonths } from "date-fns";
import { useGetGoogleCalendarEvents } from "./use-get-calendar-evnets";
import { useUpdateCalendarEvents } from './use-update-calendar-events';
import { useDeleteMeeting } from "@/components/meetings/use-delete-meeting";
import { useCreateMeetingModal } from "@/hooks/use-create-meeting-modal";
import { useUpdateMeetingModal } from "@/hooks/use-update-meeting-modal";
import { Calendar, Views, DateLocalizer, momentLocalizer } from 'react-big-calendar';
import withDragAndDrop from 'react-big-calendar/lib/addons/dragAndDrop'
import 'react-big-calendar/lib/css/react-big-calendar.css';
import 'react-big-calendar/lib/addons/dragAndDrop/styles.css';
import "./data-calendar.css";
import { 
  Card, 
  CardContent, 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  CalendarIcon, ChevronLeftIcon, 
  ChevronRightIcon, CalendarClockIcon, Loader, 
  CircleX
} from "lucide-react";
import { RiAddCircleFill } from "react-icons/ri";
import { AIChatButton } from "./aiChatButton"
import cuid from 'cuid';

type CalendarEvent = {
  id: string;
  title: string;
  status: string;
  description: string;
  start: Date;
  end: Date;
  allDay?: boolean;
};


const localizer = momentLocalizer(moment);

const DragAndDropCalendar = withDragAndDrop(Calendar)

export function MyCalendar() {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const [value, setValue] = useState(new Date());
  const { open: openCreateMeeting } = useCreateMeetingModal();
  const { open: openUpdateMeeting } = useUpdateMeetingModal();
  const { data, isLoading: isCalendarLoading } = useGetGoogleCalendarEvents();
  const { mutate, isPending: isUpdatingCalenderEvents } = useUpdateCalendarEvents()
  const { mutate: deleteMeetingMutate, isPending: isDeletingMeeting } = useDeleteMeeting();

  const events = data && data.map((event) => ({
    id: event.id,
    title: event.summary,
    status: event.status,
    description: event.description,
    start: new Date(event.start ?? ''),
    end: new Date(event.end ?? ''),
  })) as CalendarEvent[];
  
  const [myEvents, setMyEvents] = useState<CalendarEvent[]>(events ?? []);
  const [copyEvent, setCopyEvent] = useState<boolean>(false)

  interface PendingUpdate {
    id: string;
    type: 'move' | 'copy';
    event: CalendarEvent;
    originalEventId?: string;  // Track the original event ID for copy operations
    needsSync: boolean;        // Flag to track if this update needs to be synced
  }
  
  const [pendingUpdates, setPendingUpdates] = useState<PendingUpdate[]>([]);

  useEffect(() => {
    if (events) {
      setMyEvents(events);
      console.log("myEvents", myEvents)
    }
  }, [data]);
  
  useEffect(() => {
      console.log("pendingUpdates", pendingUpdates)
  }, [pendingUpdates]);

  const toggleCopyEvent = useCallback(() => setCopyEvent((val) => !val), [])

  const deleteEvent = useCallback(
    (event: any) => {
      const eventToDelete = event as CalendarEvent;
      if (window.confirm(`Are you sure you want to delete "${eventToDelete.title} ${format(eventToDelete.start.toLocaleString(), 'h:mm a')} - ${format(eventToDelete.end.toLocaleString(), 'h:mm a')}"?`)) {
        setMyEvents((prevEvents) => prevEvents.filter((ev) => ev.id !== eventToDelete.id));
        deleteMeetingMutate(eventToDelete.id);
      }
    },[]
  );
  const updateEvent = useCallback(
    (event: any) => {
      const eventToUpdate = event as CalendarEvent;
      //if (window.confirm(`Are you sure you want to update "${eventToUpdate.title} ${format(eventToUpdate.start.toLocaleString(), 'h:mm a')} - ${format(eventToUpdate.end.toLocaleString(), 'h:mm a')}"?`)) {
        openUpdateMeeting(eventToUpdate.id)
      //}
    },[]
  );

  const moveEvent = useCallback(
    ({ event, start, end }: any) => {
      setMyEvents((prev = []) => {
        if (copyEvent) {
          const newEvent = {
            ...event,
            id: cuid(),
            start,
            end,
          };
          
          setPendingUpdates([{
            id: newEvent.id,
            type: 'copy',
            event: newEvent,
            originalEventId: event.id,  // Store the original event ID
            needsSync: true             // Mark as needing sync
          }]);
          
          return [...prev, newEvent] as CalendarEvent[];
        } else {
          const existing = prev.find((ev) => ev.id === event.id) ?? event;
          const filtered = prev.filter((ev) => ev.id !== event.id);
          const updatedEvent = {
            ...existing,
            start,
            end,
          };
          
          setPendingUpdates((prev) => {
            const filtered = prev.filter(p => p.event.id !== event.id);
            return [...filtered, {
              id: event.id,
              type: 'move',
              event: updatedEvent,
              needsSync: true  // Mark as needing sync
            }];
          });
          
          return [...filtered, updatedEvent] as CalendarEvent[];
        }
      });
    },
    [setMyEvents, copyEvent]
  );
  /*const moveEvent = useCallback(
    ({ event, start, end }: any) => {
      setMyEvents((prev = []) => {
        if (copyEvent) {
          // When copying, create a new event with a unique ID
          const newEvent = {
            ...event,
            id: crypto.randomUUID(), // Generate new unique ID for the copy
            start,
            end,
          };
          return [...prev, newEvent] as CalendarEvent[];
        } else {
          // When moving, update the existing event
          const existing = prev.find((ev) => ev.id === event.id) ?? event;
          const filtered = prev.filter((ev) => ev.id !== event.id);
          return [
            ...filtered,
            {
              ...existing,
              start,
              end,
            },
          ] as CalendarEvent[];
        }
      });
    },
    [setMyEvents, copyEvent]
  );*/
  
  const resizeEvent = useCallback(
    ({ event, start, end }: any) => {
      setMyEvents((prev = []) => {
        const existing = prev.find((ev) => ev.id === event.id) ?? event;
        const filtered = prev.filter((ev) => ev.id !== event.id);

        const updatedEvent = {
          ...existing,
          start,
          end,
        };
        setPendingUpdates((prev) => {
          const filtered = prev.filter(p => p.event.id !== event.id);
          return [...filtered, {
            id: event.id,
            type: 'move',
            event: updatedEvent,
            needsSync: true  // Mark as needing sync
          }];
        });
        return [...filtered, { ...existing, start, end }] as CalendarEvent[];
      });
    },
    [setMyEvents]
  );

  //const defaultDate = useMemo(() => (new Date()), [])
  const { defaultDate, scrollToTime } = useMemo(
    () => ({
      defaultDate: new Date(),
      scrollToTime: new Date(1972, 0, 1, 8),
    }),
    []
  )

  const handleNavigate = (action: "PREV" | "NEXT" | "TODAY") => {
    switch (action) {
      case "PREV":
        setValue(subMonths(value, 1));
        break;
      case "NEXT":
        setValue(addMonths(value, 1));
        break;
      case "TODAY":
        setValue(new Date());
        break;
      default:
        break;
    }
  }

  if (!data) return null;
  
  const handleSaveUpdates = async () => {
    const updatesToProcess = pendingUpdates.filter(update => update.needsSync);
    
    for (const update of updatesToProcess) {
      try {
        const eventData = {
          summary: update.event.title,
          description: update.event.description,
          start: {
            dateTime: update.event.start.toISOString(),
            timeZone: timezone,
          },
          end: {
            dateTime: update.event.end.toISOString(),
            timeZone: timezone,
          },
        };
  
        if (update.type === 'copy') {
          // For copy operations, always create new event
          mutate({ values: eventData }, {
            onSuccess: ({ data }) => {
              console.log("Created new event:", data);
              // Remove this update from pending list
              setPendingUpdates(prev => prev.filter(p => p.id !== update.id));
            }
          });
        } else {
          // For move operations, update existing event
          mutate(
            { 
              eventId: update.originalEventId || update.event.id,
              values: eventData 
            }, 
            {
              onSuccess: ({ data }) => {
                console.log("Updated event:", data);
                setPendingUpdates(prev => prev.filter(p => p.id !== update.id));
              }
            }
          );
        }
      } catch (error) {
        console.error(`Failed to save update for event ${update.id}:`, error);
        // Mark this update as still needing sync
        setPendingUpdates(prev => 
          prev.map(p => p.id === update.id ? { ...p, needsSync: true } : p)
        );
      }
    }
  };
  
  const CopyEventToggle = () => (
    <div className='flex items-center ml-8'>
      <label className='text-sm'>
        <input
          type="checkbox"
          checked={copyEvent}
          onChange={toggleCopyEvent}
          className='size-3 rounded-2xl mx-1 hover:cursor-pointer'
        />
        Copy events when dragging
      </label>
    </div>
  );


  const isLoading = isCalendarLoading || isUpdatingCalenderEvents || isDeletingMeeting;

  if (isLoading) {
    <Card className="w-full h-[714px] border-none shadow-none">
      <CardContent className="flex items-center jistify-center h-full">
        <Loader className="size-5 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>;
  }

  return (
    <div className="flex flex-col items-start gap-x-2 ml-4">
      <div className="w-full flex flex-row items-center gap-2">
        <CalendarClockIcon size={"16"} className="mb-[0.1rem] rounded-md" />
        <p className="text-xs uppercase text-primary font-semibold">Meetings</p>
        <RiAddCircleFill
          className="size-5 text-neutral-500 cursor-pointer hover:text-neutral-400"
          onClick={openCreateMeeting}
        />
        <div className="w-full flex flex-items justify-between">
          <CopyEventToggle />
          <div className="flex flex-items">
            {pendingUpdates.length > 0 && (
              <Button
                size="sm"
                onClick={handleSaveUpdates}
                className="h-7 text-[13px]"
              >
                {isUpdatingCalenderEvents ? (
                  <div className='flex flex-row items-center gap-1'>
                    <Loader className="animate-spin" />
                    `Save Changes ({pendingUpdates.length})`
                  </div>
                ) : ( 
                  `Save Changes (${pendingUpdates.length})`
                )}
              </Button>
            )}
            {pendingUpdates.length > 0 && (
              <Button
                size="xs"
                variant="ghost"
                onClick={() => setPendingUpdates([])}
                className="relative top-[-0.8rem] text-red-600 p-0"
              >
                <CircleX />
              </Button>
            )}
          </div>
        </div>
        <AIChatButton />
      </div>
      <div className="w-full p-4">
        <DragAndDropCalendar
          defaultDate={defaultDate}
          defaultView={Views.MONTH}
          events={myEvents}
          localizer={localizer}
          onEventDrop={moveEvent}
          onEventResize={resizeEvent}
          resizable
          scrollToTime={scrollToTime}
          selectable
          showMultiDayTimes={true}
          step={15}
          toolbar
          onSelectEvent={updateEvent}
          onDoubleClickEvent={deleteEvent}
          style={{ fontSize: "12px" }}
        />
      </div>
    </div>
  );
}

type CustomToolbarProps = {
  date: Date;
  onNavigate: (action: "PREV" | "NEXT" | "TODAY") => void;
}
  
const CustomToolbar = ({
  date,
  onNavigate,
}: CustomToolbarProps) => {
  return (
    <div className="flex mb-4 items-center w-full lg:w-auto justify-center lg:justify-start">
      <Button
        onClick={() => onNavigate("PREV")}
        variant="ghost"
        size="icon"
        className="flex items-center"
      >
        <ChevronLeftIcon className="size-4 mr-2" />
      </Button>
      <div className="flex items-center border border-input rounded-md px-3 py-2 h-8 justify-center w-full lg:w-auto">
        <CalendarIcon className="size-4 mr-2" />
        <p className="text-sm">{format(date, "MMMM yyyy")}</p>
      </div>
      <Button
        onClick={() => onNavigate("NEXT")}
        variant="ghost"
        size="icon"
        className="flex items-center"
      >
        <ChevronRightIcon className="size-4 ml-2" />
      </Button>
    </div>    
  )
}

MyCalendar.propTypes = {
  localizer: PropTypes.instanceOf(DateLocalizer),
}

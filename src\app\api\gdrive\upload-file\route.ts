import { NextResponse } from "next/server";
import { currentUser } from "@clerk/nextjs/server";
import { google } from 'googleapis';
import { authenticateGoogleOAuth2 } from "../authenticate";
import mime from "mime";
import { Readable } from "stream";

// upload function
const uploadFileToDrive = async (
  folderId: string,
  file: File,
  driveId: string
) => {
  const auth = await authenticateGoogleOAuth2();
  if (!auth) {
    throw new Error('Authentication failed');
  }

  const drive = google.drive({ version: "v3", auth });

  const mimeType = mime.getType(file.name);

  const fileMetadata = {
    name: file.name,
    parents: [folderId],
    //driveId: driveId,
    mimeType: mimeType,
  };

  const fileBuffer = file.stream();

  const response = await drive.files.create({
    requestBody: fileMetadata,
    media: {
      mimeType: mimeType!,
      body: Readable.from(fileBuffer),
    },
    fields: "id", // only return the file ID, we don't need all the other information
    supportsAllDrives: true, // required to allow folders to be created in shared drives
  });

  // get file link
  const fileData = await drive.files.get({
    fileId: response.data.id!,
    fields: "id, name, mimeType, fileExtension, size, iconLink, owners, modifiedTime, shared, permissions, webViewLink",
    supportsAllDrives: true,
  });

  const fileInfo = fileData.data;

  const item = {
    id: fileInfo.id,
    name: fileInfo.name,
    type:
      fileInfo.mimeType === 'application/vnd.google-apps.folder'
        ? 'folder'
        : 'file',
    extension: fileInfo.fileExtension,
    size: fileInfo.size,
    icon: fileInfo.iconLink,
    owner: fileInfo.owners?.[0]?.displayName || 'Unknown',
    modifiedTime: fileInfo.modifiedTime,
    share: fileInfo.shared,
    permissions: fileInfo.permissions,
    webViewLink: fileInfo.webViewLink,
  };


  return {
    data: item
  };
};

// POST request handler
export async function POST(req: Request) {
  const user = await currentUser();
  if (!user)
    return NextResponse.json(
      { error: "Unauthorized", success: false },
      { status: 401 }
    );

  const res = await req.formData();

  const folderId = res.get("folderId") as string;
  const driveId = res.get("driveId") as string;
  const file = res.get("file") as File;

  if (!folderId || !driveId || !file) {
    return NextResponse.json(
      {
        error: "Missing folderId, driveId, or file",
      },
      {
        status: 400,
      }
    );
  }

  const uploadedFile = await uploadFileToDrive(folderId, file, driveId);

  return NextResponse.json(
    { file: uploadedFile },
    { status: 200 }
  );
}
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { MyCalendar } from "@/components/meetings/my-calendar"

interface ScheduleMeetingPageProps {
  params: Promise<{
    workspaceId: string;
  }>;
}

const ScheduleMeetingPage = async (props: ScheduleMeetingPageProps) => {
  const params = await props.params;
  const user = await getCurrentUser();
  if (!user) redirect("/");


  return (
    <div className="w-full mx-auto">
      <MyCalendar />
    </div>
  )
}

export default ScheduleMeetingPage
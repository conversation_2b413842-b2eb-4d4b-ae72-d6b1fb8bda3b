import { useState } from "react";
import {
  ChevronDown,
  MoreHorizontal,
} from "lucide-react";
import { CATEGORY_ICONS } from "@/lib/config/categories";


export function IconDropdown({
  selectedIcon,
  onIconSelect,
}: {
  selectedIcon: string;
  onIconSelect: (icon: string) => void;
}) {
  const [open, setOpen] = useState(false);

  const toggleDropdown = () => setOpen(!open);
  const closeDropdown = () => setOpen(false);

  return (
    <div className="relative">
      <button
        type="button"
        className="flex items-center justify-between w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none"
        onClick={toggleDropdown}
      >
        {/* Show selected icon */}
        <div className="flex items-center space-x-2">
          {CATEGORY_ICONS[selectedIcon] ? (
            (() => {
              const SelectedIcon = CATEGORY_ICONS[selectedIcon];
              return <SelectedIcon className="w-5 h-5 text-primary" />;
            })()
            ) : (
              <MoreHorizontal className="w-5 h-5 text-primary" />
            )}
          <span>{selectedIcon || "Select Icon"}</span>
        </div>
        <ChevronDown className="w-4 h-4" />
      </button>

      {/* Dropdown List */}
      {open && (
        <ul
          className="absolute z-10 mt-2 w-full max-h-64 overflow-y-auto bg-background border rounded-md shadow-md"
          onMouseLeave={closeDropdown}
        >
          {Object.entries(CATEGORY_ICONS).map(([iconName, Icon]) => (
            <li
              key={iconName}
              className="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-100/10"
              onClick={() => {
                onIconSelect(iconName);
                closeDropdown();
              }}
            >
              <Icon className="w-5 h-5 mr-2 text-primary" />
              <span>{iconName}</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

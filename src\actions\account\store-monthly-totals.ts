"use server";

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { Prisma } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { suggestCategory } from "@/lib/utils/transaction-categorization";

interface MonthlyTotals {
  [yearMonth: string]: {
    deposits: number;
    withdrawals: number;
  };
}

export async function storeMonthlyTotals({
  originalId,
  monthlyTotals,
}: {
  originalId: string;
  monthlyTotals: MonthlyTotals;
}) {
  try {
    // Validate input data
    if (!originalId) {
      throw new Error("originalId is required");
    }
    if (!monthlyTotals || Object.keys(monthlyTotals).length === 0) {
      throw new Error("monthlyTotals object is required and must not be empty");
    }

    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const bankAccount = await prisma.bankAccount.findFirst({
      where: {
        userId,
        originalId: originalId.replace(/\D/g, ''),
      },
    });

    console.log("bankAccount, monthlyTotals:", { bankAccount, monthlyTotals });
    
    if (!bankAccount) {
      throw new Error(`Bank account not found for originalId: ${originalId}`);
    }

    // Process monthly totals
    for (const [yearMonth, totals] of Object.entries(monthlyTotals)) {
      // Validate totals object structure
      if (typeof totals.deposits !== 'number' || typeof totals.withdrawals !== 'number') {
        console.warn(`Skipping invalid totals record for ${yearMonth}:`, totals);
        continue;
      }

      // Parse year-month format (e.g., "2024-12")
      const [yearStr, monthStr] = yearMonth.split('-');
      const year = parseInt(yearStr);
      const month = parseInt(monthStr);

      // Validate date components
      if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
        throw new Error(`Invalid year-month format: ${yearMonth}`);
      }

      // Create date for the last day of the month (UTC)
      //const lastDay = new Date(Date.UTC(year, month, 0)).getUTCDate();
      //const monthDate = new Date(Date.UTC(year, month - 1, lastDay, 15, 30, 0));
      const monthDate = new Date(Date.UTC(year, month, 0, 15, 30, 0));
      
      // Validate parsed date
      if (isNaN(monthDate.getTime())) {
        throw new Error(`Invalid date generated for ${yearMonth}`);
      }

      // Process deposits if amount > 0
      if (totals.deposits > 0) {
        await upsertMonthlyTransaction({
          bankAccount,
          date: monthDate,
          amount: totals.deposits,
          type: 'CREDIT',
          description: `${yearMonth} 月總存入`,
          yearMonth,
          totals,
        });
      }

      // Process withdrawals if amount > 0
      if (totals.withdrawals > 0) {
        await upsertMonthlyTransaction({
          bankAccount,
          date: monthDate,
          amount: totals.withdrawals,
          type: 'DEBIT',
          description: `${yearMonth} 月總提出`,
          yearMonth,
          totals,
        });
      }

      console.log(`Successfully processed monthly totals for ${yearMonth}: deposits ${totals.deposits}, withdrawals ${totals.withdrawals}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error storing monthly totals:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      details: error instanceof Error ? error.stack : undefined,
    };
  }
}

// Helper function to upsert monthly transaction records
async function upsertMonthlyTransaction({
  bankAccount,
  date,
  amount,
  type,
  description,
  yearMonth,
  totals,
}: {
  bankAccount: any;
  date: Date;
  amount: number;
  type: 'CREDIT' | 'DEBIT';
  description: string;
  yearMonth: string;
  totals: { deposits: number; withdrawals: number };
}) {
  // Check for existing transaction record for the same month and type
  const existingTransaction = await prisma.transaction.findFirst({
    where: {
      accountId: bankAccount.id,
      type,
      description,
      date: {
        gte: new Date(
          Date.UTC(date.getFullYear(), date.getMonth(), 1, 0, 0, 0)
        ),
        lt: new Date(
          Date.UTC(date.getFullYear(), date.getMonth() + 1, 1, 0, 0, 0)
        ),
      },
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  // Category logic
  const { categoryId: suggestedCategoryId, confidence } = await suggestCategory(
    description,
    amount,
    type
  );

  const originalPayload = {
    yearMonth,
    monthlyTotals: totals,
    generatedAt: new Date().toISOString(),
    type: "MONTHLY_SUMMARY",
  } as Prisma.InputJsonValue;

  if (existingTransaction) {
    // Update existing record
    await prisma.transaction.update({
      where: { id: existingTransaction.id },
      data: {
        amount: amount,
        description,
        categoryId: suggestedCategoryId ? suggestedCategoryId : undefined,
        originalPayload,
        updatedAt: new Date(),
      },
    });
    console.log(
      `Updated existing ${type} transaction for ${yearMonth} with amount ${amount}`
    );
  } else {
    // Create new record
    await prisma.transaction.create({
      data: {
        amount: amount,
        date,
        description,
        type,
        currencyIso: "TWD",
        originalPayload,
        accountId: bankAccount.id,
        userId: bankAccount.userId!,
        review: false,
        categoryId: suggestedCategoryId ? suggestedCategoryId : undefined,
        categoryValidated: false,
      },
    });
    console.log(
      `Created new ${type} transaction for ${yearMonth} with amount ${amount}`
    );
  }
}
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { useUpdateTaskModal } from "@/hooks/use-update-task-modal";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ExternalLinkIcon, PencilIcon, TrashIcon } from "lucide-react";
import { DeleteTaskButton } from "./delete-task-button";


interface TaskActionProps {
  id: string;
  projectId: string;
  children: React.ReactNode;
}

export function TaskAction({ id, projectId, children }: TaskActionProps) {
  const router = useRouter();
  const workspaceId = useWorkspaceId();
  const [isAlertOpen, setIsAlertOpen] = useState<boolean>(false);

  const {open} = useUpdateTaskModal()

  const onOpenTask = () => {
    router.push(`/workspaces/${workspaceId}/tasks/${id}`)
  }

  const onOpenProject = () => {
    router.push(`/workspaces/${workspaceId}/projects/${projectId}`)
  }

  return (
    <div className="flex justify-end">
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          {children}
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48" onInteractOutside={(e: any) => e.preventDefault()}>
          <DropdownMenuItem
            onClick={onOpenTask}
            className="font-medium p-[10px]"
          >
            <ExternalLinkIcon className="size-4 mr-2 stroke-2" />
            Task Details
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={onOpenProject}
            className="font-medium p-[10px]"
          >
            <ExternalLinkIcon className="size-4 mr-2 stroke-2" />
            Open Project
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => { open(id) }}
            className="font-medium p-[10px]"
          >
            <PencilIcon className="size-4 mr-2 stroke-2" />
            Edit Task
          </DropdownMenuItem>
          <DropdownMenuItem            
            disabled={false}
            className="text-amber-700 focus:text-amber-700font-medium p-[10px]"
          >
            <TrashIcon className="size-4 mr-2 stroke-2" />
            <DeleteTaskButton
              taskId={id}
              isAlertOpen={isAlertOpen}
              setIsAlertOpen={setIsAlertOpen}
            />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu> 
    </div>
  )
}


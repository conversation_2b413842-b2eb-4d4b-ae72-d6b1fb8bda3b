'use server';

import { auth } from "@clerk/nextjs/server";
import { SchematicClient } from "@schematichq/schematic-typescript-node";

export async function getTemporaryAccessToken() {
  const apiKey = process.env.SCHEMATIC_API_KEY
  const client = new SchematicClient({ apiKey });
  const { userId } = await auth();
  if (!userId) {
    console.error("User not found");
    return null;
  }

  const resp = await client.accesstokens.issueTemporaryAccessToken({
    resourceType: "company",
    lookup: { id: userId },
  });

  console.log(
    "Token response received: ",
    resp.data?.token ? "Token received" : "No token received"
  )
  return resp.data?.token;
}

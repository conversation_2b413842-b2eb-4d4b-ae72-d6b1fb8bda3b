"use client"

import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Doc } from '@/convex/_generated/dataModel';
import { ChevronRight, FileText } from 'lucide-react';


function ReceiptList() {
  const { user } = useUser()
  const router = useRouter()
  const receipts = useQuery(api.receipts.getReceipts, {
    userId: user?.id  || "",
  })

  if (!user) {
    return (
      <div className='w-full p-8 text-center'>
        <p className=' text-gray-600'>Please sign in to view your receipts</p>
      </div>
    )
  }

  if (!receipts) {
    return (
      <div className='w-full p-8 text-center'>
        <div className='animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500 mx-auto'>
        </div>  
        <p className='mt-2 text-gray-600'>Loading receipts...</p>
      </div>
    )
  }

  if (receipts.length === 0) {
    return (
      <div className='w-full p-8 text-center border border-gray-200 rounded-lg bg-gray-50'>
        <p className=' text-gray-600'>No receipts have been uploaded yet.</p>
      </div>
    )
  }

  return (
    <div className="w-full">
      <h2 className="text-2xl font-bold mb-4">Your Receipts</h2>
      <div className="bg-white border border-gray-200 rounded-lg overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]"></TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Uploaded</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[40px]"></TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {receipts.map((receipt: Doc<"receipts">) => (
              <TableRow 
                key={receipt._id}
                className="hover:bg-gray-50 cursor-pointer"
                onClick={() => (router.push(`/receipts/${receipt._id}`))}
              >
                <TableCell className="py-2">
                  <FileText className="size-6 text-red-500" />
                </TableCell>
                <TableCell className="font-medium">
                  {receipt.fileDisplayName || receipt.fileName}
                </TableCell>
                <TableCell className="py-2">
                  {new Date(receipt.uploadedAt).toLocaleString()}
                </TableCell>
                <TableCell className="py-2">
                  {formatFileSize(receipt.size)}
                </TableCell>
                <TableCell className="py-2">
                  {receipt.transactionAmount
                    ? `${receipt.transactionAmount} ${receipt.currency}`
                    : "_"}
                </TableCell>
                <TableCell className="py-2">
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    receipt.status === "pending" 
                      ? "bg-yellow-100 text-yellow-800" 
                      : receipt.status === "processed" 
                        ? "bg-green-100 text-green-800" 
                        : "bg-gray-100 text-gray-800"
                      }`}
                  >
                    {receipt.status.charAt(0).toUpperCase() + receipt.status.slice(1)}
                  </span>
                </TableCell>
                <TableCell>
                  <ChevronRight className="size-5 text-gray-400 ml-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

export default ReceiptList

function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}


import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { cookies } from 'next/headers';

export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ isAuthenticated: false });
    }

    const cookieStore = await cookies();
    const accessToken = cookieStore.get('access_token');
    const refreshToken = cookieStore.get('refresh_token');

    return NextResponse.json({
      isAuthenticated: !!(accessToken && refreshToken)
    });
  } catch (error) {
    console.error('Error checking auth status:', error);
    return NextResponse.json({ isAuthenticated: false });
  }
}
"use client";

import {
  useState,
  ChangeEvent,
  FormEvent,
  ReactNode,
  useEffect,
  useMemo,
} from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  uploadDepositToSheets,
  fetchManagementSheetData,
} from "@/lib/google/upload-deposit-to-sheets";
import {
  Loader2,
  FileText,
  CheckCircle,
  XCircle,
  ExternalLink,
  RefreshCw,
  TableIcon,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";

// Properly initialize pdfMake with fonts
pdfMake.vfs = (pdfFonts as any).pdfMake?.vfs || pdfFonts;

type StatusType = "idle" | "loading" | "success" | "error";

interface ProcessStatus {
  type: StatusType;
  message: string | ReactNode;
  spreadsheetUrl?: string;
  googleDriveLink?: string;
}

interface SheetDisplayData {
  headers: (string | number | null)[]; // Allow headers to be string, number, or null
  rows: (string | number | null)[][];
}

export default function UploadPage() {
  const [file, setFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<ProcessStatus>({
    type: "idle",
    message: "",
  });
  const [enableUpdate, setEnableUpdate] = useState({
    value: false,
    onChange: (v: boolean) => setEnableUpdate({ value: v, onChange: enableUpdate.onChange }),
  });
  const [targetYear, setTargetYear] = useState<string>(new Date().getFullYear().toString());
  const [sheetData, setSheetData] = useState<SheetDisplayData | null>(null);
  const [isFetchingSheet, setIsFetchingSheet] = useState<boolean>(false);
  const [fetchSheetError, setFetchSheetError] = useState<string | null>(null);
  const [isUploadOpen, setIsUploadOpen] = useState<boolean>(false);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [imageUploadStatus, setImageUploadStatus] = useState<ProcessStatus>({
    type: "idle",
    message: "",
  });
  const [unpaidDialogOpen, setUnpaidDialogOpen] = useState(false);
  const [unpaidList, setUnpaidList] = useState<string[]>([]);
  const [unpaidMonth, setUnpaidMonth] = useState<string>("");

  const currentYear = new Date().getFullYear()
  const yearOptions = Array.from({ length: 50 }, (_, i) => currentYear - i)

  // Add state for dialog and data
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<
    | "depositsUnmatched"
    | "depositsSmallAmount"
    | "withdrawals"
    | "withdrawalsUnmatched"
    | "motorBikeParkingLot"
    | null
  >(null);
  const [dialogData, setDialogData] = useState<any[]>([]);
  const [overdueDialogOpen, setOverdueDialogOpen] = useState(false);
  const [overdueList, setOverdueList] = useState<
    Array<{
      id: string;
      amount: string;
      months: string;
    }>
  >([]);

  const convertToCSV = (headers: (string | number | null)[], rows: (string | number | null)[][]): string => {
    const escapeCSV = (field: any): string => {
      if (field === null || typeof field === 'undefined') {
        return '';
      }
      const stringField = String(field);
      // Check if field contains comma, newline, or double quote
      if (stringField.includes(',') || stringField.includes('\n') || stringField.includes('"')) {
        // Escape double quotes by replacing them with two double quotes, then wrap in double quotes
        return `"${stringField.replace(/"/g, '""')}"`;
      }
      return stringField;
    };

    const headerRow = headers.map(escapeCSV).join(',');
    const dataRows = rows.map(row => row.map(escapeCSV).join(','));
    
    return [headerRow, ...dataRows].join('\n');
  };

  const handleDownloadCSV = () => {
    if (!sheetData || !sheetData.headers || !sheetData.rows || sheetData.rows.length === 0) {
      toast.error("沒有可供下載的資料。");
      return;
    }

    try {
      const csvString = convertToCSV(sheetData.headers, sheetData.rows);
      const blob = new Blob(['\uFEFF' + csvString], { type: 'text/csv;charset=utf-8;' }); // Prepend BOM for Excel
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", "社區管理費繳費統計表資料表.csv");
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success("CSV下載成功！");
    } catch (error) {
      console.error("Error generating CSV: ", error);
      toast.error("CSV下載失敗。");
    }
  };

  const handleDownloadPDF = async () => {
    if (!sheetData || !sheetData.headers || !sheetData.rows || sheetData.rows.length === 0) {
      toast.error("沒有可供下載的資料製作PDF。");
      return;
    }

    try {
      // Use default pdfMake fonts and let the system handle Chinese characters
      // This avoids font loading issues while still allowing Chinese text to render
      // The PDF will use the system's default font for Chinese characters
      // VFS is now properly initialized at the top of the file


      // Debug: Log the data structure
      console.log('Headers:', sheetData.headers);
      console.log('First few rows:', sheetData.rows.slice(0, 3));

      const tableHeaders = sheetData.headers.map((header, index) => {
        const headerText = header === null || header === undefined ? `Col${index + 1}` : String(header);
        return {
          text: headerText,
          style: 'tableHeader'
        };
      });

      const tableRows = sheetData.rows.map((row, rowIndex) => {
        if (!Array.isArray(row)) {
          console.warn(`Row ${rowIndex} is not an array:`, row);
          return [];
        }

        // Ensure row has same length as headers
        const normalizedRow = [];
        for (let i = 0; i < sheetData.headers.length; i++) {
          const cell = row[i];
          const cellText = cell === null || cell === undefined ? '' : String(cell);
          normalizedRow.push({
            text: cellText
          });
        }
        return normalizedRow;
      }).filter(row => row.length > 0); // Remove empty rows

      // Validate table data before creating PDF
      console.log('Table headers length:', tableHeaders.length);
      console.log('Table rows count:', tableRows.length);
      console.log('First table row length:', tableRows[0]?.length);

      // Ensure all rows have the same length as headers
      const validatedRows = tableRows.map((row, index) => {
        if (row.length !== tableHeaders.length) {
          console.warn(`Row ${index} length (${row.length}) doesn't match headers length (${tableHeaders.length})`);
          // Pad or trim row to match headers length
          const paddedRow = [...row];
          while (paddedRow.length < tableHeaders.length) {
            paddedRow.push({ text: '' });
          }
          return paddedRow.slice(0, tableHeaders.length);
        }
        return row;
      });

      const docDefinition: any = {
        pageOrientation: 'landscape',
        content: [
          { text: 'Management Fee Payment Report', style: 'header' },
          {
            table: {
              headerRows: 1,
              body: [tableHeaders, ...validatedRows],
            },
            layout: 'lightHorizontalLines'
          }
        ],
        styles: {
          header: {
            fontSize: 18,
            bold: true,
            margin: [0, 0, 0, 20],
            // Using default font - system will handle Chinese characters
          },
          tableHeader: {
            bold: true,
            fontSize: 10,
            // fillColor: '#eeeeee' // Optional: background color for header
          }
        },
        defaultStyle: {
          // Using default font - system will handle Chinese characters
        }
      };

      pdfMake.createPdf(docDefinition).download('社區管理費繳費統計表資料表.pdf');
      toast.success("PDF產生成功並開始下載。");

    } catch (error: any) {
      console.error("Error generating PDF: ", error);
      toast.error(`PDF產生失敗: ${error.message || "Unknown error"}`);
    }
  };

  // Store all returned items
  const [depositsUnmatched, setDepositsUnmatched] = useState<any[]>([]);
  const [depositsSmallAmount, setDepositsSmallAmount] = useState<any[]>([]);
  const [withdrawals, setWithdrawals] = useState<any[]>([]);
  const [withdrawalsUnmatched, setWithdrawalsUnmatched] = useState<any[]>([]);
  const [motorBikeParkingLot, setMotorBikeParkingLot] = useState<any[]>([]);

  useEffect(() => {
    console.log("dialogData: ", dialogData);
  }, [dialogData]);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    setUploadStatus({ type: "idle", message: "" });
    if (event.target.files && event.target.files[0]) {
      if (
        event.target.files[0].type === "text/csv" ||
        event.target.files[0].name.endsWith(".csv")
      ) {
        setFile(event.target.files[0]);
      } else {
        toast.error("Invalid file type. Please upload a CSV file.");
        setFile(null);
        event.target.value = "";
      }
    } else {
      setFile(null);
    }
  };

  // Helper to save to localStorage (1 year)
  const saveToLocalStorage = (key: string, value: any) => {
    const data = { value, expires: Date.now() + 365 * 24 * 60 * 60 * 1000 };
    localStorage.setItem(key, JSON.stringify(data));
  };
  // Helper to load from localStorage
  const loadFromLocalStorage = (key: string) => {
    const raw = localStorage.getItem(key);
    if (!raw) return null;
    try {
      const data = JSON.parse(raw);
      if (data.expires && data.expires > Date.now()) return data.value;
      localStorage.removeItem(key);
      return null;
    } catch {
      return null;
    }
  };

  const getOverdueAccounts = () => {
    if (!sheetData) return [];

    const monthsColIdx = sheetData.headers.findIndex(
      (h) => String(h) === "(欠)預繳月數"
    );
    const amountColIdx = sheetData.headers.findIndex(
      (h) => String(h) === "(欠)預繳金額"
    );
    const residentIdColIdx = 1; // 住戶ID欄位

    if (monthsColIdx === -1 || amountColIdx === -1) return [];

    return sheetData.rows
      .filter((row) => {
        const months = parseFloat(String(row[monthsColIdx]));
        return !isNaN(months) && months <= -2;
      })
      .map((row) => ({
        id: String(row[residentIdColIdx]),
        amount: String(row[amountColIdx]),
        months: String(row[monthsColIdx]),
      }));
  };

  // Add button click handler
  const handleShowOverdue = () => {
    setOverdueList(getOverdueAccounts());
    setOverdueDialogOpen(true);
  };

  // Reusable dialog component
  function DataDialog({
    open,
    onOpenChange,
    title,
    items,
    itemLabel,
  }: {
    open: boolean;
    onOpenChange: (v: boolean) => void;
    title: string;
    items: any[];
    itemLabel?: (item: any) => ReactNode;
  }) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <DialogDescription>
            {items && items.length > 0 ? (
              <ul className="max-h-96 overflow-y-auto list-disc pl-5">
                {items.map((item, idx) => (
                  <li key={idx} className="text-sm break-all">
                    {itemLabel ? itemLabel(item) : JSON.stringify(item)}
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-muted-foreground text-center py-4">
                無資料
              </div>
            )}
          </DialogDescription>
        </DialogContent>
      </Dialog>
    );
  }

  const loadSheetData = async () => {
    setIsFetchingSheet(true);
    setFetchSheetError(null);
    try {
      const result = await fetchManagementSheetData(targetYear);
      if (result.success && result.data && result.headers) {
        setSheetData({ headers: result.headers, rows: result.data });
        toast.success("Sheet data loaded successfully!");
        // Assume result.depositsUnmatched, result.depositsSmallAmount, result.withdrawals, result.withdrawalsUnmatched are returned
        if (result.depositsUnmatched) {
          setDepositsUnmatched(result.depositsUnmatched);
          saveToLocalStorage("depositsUnmatched", result.depositsUnmatched);
        }
        if (result.depositsSmallAmount) {
          setDepositsSmallAmount(result.depositsSmallAmount);
          saveToLocalStorage("depositsSmallAmount", result.depositsSmallAmount);
        }
        if (result.withdrawals) {
          setWithdrawals(result.withdrawals);
          saveToLocalStorage("withdrawals", result.withdrawals);
        }
        if (result.withdrawalsUnmatched) {
          setWithdrawalsUnmatched(result.withdrawalsUnmatched);
          saveToLocalStorage(
            "withdrawalsUnmatched",
            result.withdrawalsUnmatched
          );
        }
        if (result.motorBikeParkingLot) {
          setMotorBikeParkingLot(result.motorBikeParkingLot);
          saveToLocalStorage("motorBikeParkingLot", result.motorBikeParkingLot);
        }
      } else {
        setFetchSheetError(result.message);
        toast.error(`Failed to load sheet data: ${result.message}`);
        setSheetData(null);
      }
    } catch (error: any) {
      const msg = `Error fetching sheet: ${error.message || "Unknown error"}`;
      setFetchSheetError(msg);
      toast.error(msg);
      setSheetData(null);
    } finally {
      setIsFetchingSheet(false);
    }
  };

  useEffect(() => {
    loadSheetData();
  }, [targetYear]);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!file) {
      toast.error("Please select a CSV file to upload.");
      return;
    }

    setUploadStatus({
      type: "loading",
      message: "Processing CSV file. This may take a moment...",
    });
    toast.info("Processing CSV file. This may take a moment...");

    const reader = new FileReader();
    reader.onload = async (e) => {
      const csvContent = e.target?.result as string;
      if (csvContent) {
        try {
          // Call your server action
          const result = await uploadDepositToSheets(csvContent, enableUpdate.value);
          if (result.success) {
            setUploadStatus({
              type: "success",
              message: result.message,
              spreadsheetUrl: result.spreadsheetUrl,
            });
            toast.success(result.message);
            setFile(null);
            const fileInput = document.getElementById(
              "csv-file"
            ) as HTMLInputElement;
            if (fileInput) fileInput.value = "";

            // Update displayed sheet data immediately if `uploadedData` is returned
            if (result.depositsMaintenanceFee && result.depositsMaintenanceFee.length > 0 && enableUpdate.value === false) {
              //console.log("result.depositsMaintenanceFee: ", result.depositsMaintenanceFee)
              setSheetData({
                headers: result.depositsMaintenanceFee[0], // First row is headers
                rows: result.depositsMaintenanceFee.slice(1), // Rest are data rows
              });
            } else {
              // Fallback to refetching if uploadedData isn't provided or is empty
              await loadSheetData();
            }
          } else {
            setUploadStatus({ type: "error", message: result.message });
            toast.error(result.message);
          }
        } catch (error: any) {
          const errorMessage = `An unexpected error occurred: ${error.message || "Unknown client error"}`;
          setUploadStatus({ type: "error", message: errorMessage });
          toast.error(errorMessage);
        }
      } else {
        const noContentMsg = "Could not read file content.";
        setUploadStatus({ type: "error", message: noContentMsg });
        toast.error(noContentMsg);
      }
    };
    reader.onerror = () => {
      const readErrorMsg = "Failed to read the file.";
      setUploadStatus({ type: "error", message: readErrorMsg });
      toast.error(readErrorMsg);
    };
    reader.readAsText(file);
  };

  const handleImageChange = (event: ChangeEvent<HTMLInputElement>) => {
    setImageUploadStatus({ type: "idle", message: "" });
    if (event.target.files && event.target.files.length > 0) {
      setImageFiles(Array.from(event.target.files));
    } else {
      setImageFiles([]);
    }
  };

  const handleImageUpload = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!imageFiles.length) {
      toast.error("請選擇至少一個圖片檔案。");
      return;
    }
    setImageUploadStatus({ type: "loading", message: "Processing images..." });
    toast.info("Processing images. This may take a moment...");
    try {
      const results: any[] = [];
      let spreadsheetUrl = "";
      let googleDriveLink = "";
      for (const file of imageFiles) {
        const formData = new FormData();
        formData.append("file", file);
        const response = await fetch("/api/autopay-image-upload", {
          method: "POST",
          body: formData,
        });
        if (!response.ok) {
          results.push({
            success: false,
            message: `Server error: ${response.status}`,
          });
          continue;
        }
        const result = await response.json();
        if (result.success !== false && result.appendResult) {
          results.push(
            ...(Array.isArray(result.fillResultsArr)
              ? result.fillResultsArr
              : [result.fillResultsArr])
          );
          spreadsheetUrl = result.appendResult.spreadsheetUrl || spreadsheetUrl;
          googleDriveLink =
            result.appendResult.googleDriveLink || googleDriveLink;
        } else {
          results.push({
            success: false,
            message: result.message || "Image upload failed",
          });
        }
      }
      const successful = results.filter((r: any) => r.success).length;
      const unsuccessful = results.filter((r: any) => !r.success);
      const unsuccessfulCount = unsuccessful.length;
      setImageUploadStatus({
        type: "success",
        message: `資料處理完成！成功填入：${successful} 筆，失敗：${unsuccessfulCount} 筆。`,
        spreadsheetUrl,
        googleDriveLink,
      });
      toast.success("Images processed and uploaded successfully!");
      setImageFiles([]);
      const fileInput = document.getElementById(
        "image-file"
      ) as HTMLInputElement;
      if (fileInput) fileInput.value = "";
      await loadSheetData();
    } catch (error: any) {
      const errorMessage = `Image upload failed: ${error.message || "Unknown error"}`;
      setImageUploadStatus({ type: "error", message: errorMessage });
      toast.error(errorMessage);
    }
  };

  const renderStatusAlert = () => {
    if (
      uploadStatus.type === "idle" ||
      (uploadStatus.type === "loading" && !uploadStatus.message)
    )
      return null;

    let icon = <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
    let title = "Processing";
    let variant: "default" | "destructive" = "default";

    if (uploadStatus.type === "success") {
      icon = <CheckCircle className="h-5 w-5 text-green-500" />;
      title = "Success!";
    } else if (uploadStatus.type === "error") {
      icon = <XCircle className="h-5 w-5 text-red-500" />;
      title = "Error";
      variant = "destructive";
    } else if (uploadStatus.type === "loading") {
      title = "In Progress";
    }

    return (
      <Alert variant={variant} className="mt-6">
        <div className="flex items-center">
          {icon}
          <AlertTitle className="ml-2 font-semibold">{title}</AlertTitle>
        </div>
        <AlertDescription className="mt-2 ml-7">
          {uploadStatus.message}
          {uploadStatus.type === "success" && uploadStatus.spreadsheetUrl && (
            <a
              href={uploadStatus.spreadsheetUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="ml-2 inline-flex items-center text-sm font-medium text-blue-600 hover:underline"
            >
              View Uploaded Spreadsheet{" "}
              <ExternalLink className="ml-1 h-4 w-4" />
            </a>
          )}
        </AlertDescription>
      </Alert>
    );
  };

  const renderImageStatusAlert = () => {
    if (imageUploadStatus.type === "idle") return null;

    let icon = <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
    let title = "Processing Image";
    let variant: "default" | "destructive" = "default";

    if (imageUploadStatus.type === "success") {
      icon = <CheckCircle className="h-5 w-5 text-green-500" />;
      title = "Image Processed!";
    } else if (imageUploadStatus.type === "error") {
      icon = <XCircle className="h-5 w-5 text-red-500" />;
      title = "Image Processing Error";
      variant = "destructive";
    }

    return (
      <Alert variant={variant} className="mt-6">
        <div className="flex items-center">
          {icon}
          <AlertTitle className="ml-2 font-semibold">{title}</AlertTitle>
        </div>
        <AlertDescription className="mt-2 ml-7">
          {imageUploadStatus.message}
          {imageUploadStatus.type === "success" &&
            imageUploadStatus.spreadsheetUrl && (
              <a
                href={imageUploadStatus.spreadsheetUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 inline-flex items-center text-sm font-medium text-blue-600 hover:underline"
              >
                View Processed Data Spreadsheet{" "}
                <ExternalLink className="ml-1 h-4 w-4" />
              </a>
            )}
        </AlertDescription>
      </Alert>
    );
  };

  // 取得所有月份（header 格式為 MONTH_YEAR）
  // 取得所有月份（header 格式為 XXX_YYYY，例如 JAN_2025）
  const monthHeaders = useMemo(() => {
    if (!sheetData || !sheetData.headers) return [];
    // 篩選出格式為 "2025/01" 這類 header
    console.log("sheetData.headers: ", sheetData.headers);
    const monthHeaderRegex = /^\d{4}\/\d{1,2}$/; // Allows 1-12 or 01-12
    return sheetData.headers
      .filter((h) => typeof h === "string" && monthHeaderRegex.test(h))
      .map((h) => String(h));
  }, [sheetData]);

  // 取得指定月份未繳名單
  const getUnpaidList = (month: string) => {
    if (!sheetData) return [];
    const monthColIdx = sheetData.headers.findIndex((h) => String(h) === month);
    const residentIdColIdx = 1; // 住戶ID欄位
    if (monthColIdx === -1) return [];
    return sheetData.rows
      .filter(
        (row) => !row[monthColIdx] || String(row[monthColIdx]).trim() === ""
      )
      .map((row) => String(row[residentIdColIdx]));
  };

  const handleShowUnpaid = (month: string) => {
    setUnpaidList(getUnpaidList(month));
    setUnpaidMonth(month);
    setUnpaidDialogOpen(true);
  };

  // Dialog: 計算過濾後的未繳名單數量
  const filteredUnpaidList = useMemo(() => {
    return unpaidList.filter((id, _, arr) => {
      if (id === "08-01-1" && !arr.includes("06-01-1")) return false;
      return true;
    });
  }, [unpaidList]);

  // On mount, try to load from localStorage
  useEffect(() => {
    setDepositsUnmatched(loadFromLocalStorage("depositsUnmatched") || []);
    setDepositsSmallAmount(loadFromLocalStorage("depositsSmallAmount") || []);
    setWithdrawals(loadFromLocalStorage("withdrawals") || []);
    setWithdrawalsUnmatched(loadFromLocalStorage("withdrawalsUnmatched") || []);
    setMotorBikeParkingLot(loadFromLocalStorage("motorBikeParkingLot") || []);
  }, []);

  // Handler to open dialog
  const openDialog = (type: typeof dialogType) => {
    setDialogType(type);
    switch (type) {
      case "depositsUnmatched":
        setDialogData(depositsUnmatched);
        break;
      case "depositsSmallAmount":
        setDialogData(depositsSmallAmount);
        break;
      case "withdrawals":
        setDialogData(withdrawals);
        break;
      case "withdrawalsUnmatched":
        setDialogData(withdrawalsUnmatched);
        break;
      case "motorBikeParkingLot":
        setDialogData(motorBikeParkingLot);
        break;
      default:
        setDialogData([]);
    }
    setDialogOpen(true);
  };

  return (
    <div className="container mx-auto p-2 space-y-4">
      {/* Collapsible Upload Area */}
      <div className="w-full max-w-4xl mx-auto flex flex-col md:flex-row gap-8">
        {/* Deposit CSV Upload (left) */}
        <div className="w-full md:w-1/2">
          <div className="w-full max-w-lg mx-auto">
            <button
              type="button"
              className="flex items-center w-full px-4 py-2 mb-2 bg-muted rounded hover:bg-muted/80 transition-colors border border-border"
              onClick={() => setIsUploadOpen((open) => !open)}
            >
              {isUploadOpen ? (
                <ChevronDown className="mr-2 h-5 w-5" />
              ) : (
                <ChevronRight className="mr-2 h-5 w-5" />
              )}
              <span className="font-semibold">上傳銀行匯出CSV檔</span>
            </button>
            {isUploadOpen && (
              <Card className="w-full shadow-lg">
                <CardHeader className="text-center">
                  <div className="flex flex-row justify-center items-center space-x-4">
                    <CardTitle className="text-2xl">
                      上傳銀行匯出CSV檔
                    </CardTitle>
                    <Switch
                      checked={enableUpdate.value}
                      onCheckedChange={enableUpdate.onChange}
                    />
                  </div>
                  <CardDescription>
                    Select your CSV file to process and upload it to Google
                    Sheets.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <Label
                        htmlFor="csv-file"
                        className="text-sm font-medium sr-only"
                      >
                        Select CSV File
                      </Label>
                      <div
                        className={`flex items-center justify-center w-full p-2 border-2 border-dashed rounded-md cursor-pointer 
                        ${uploadStatus.type === "loading" ? "border-gray-300 bg-gray-50" : "border-gray-300 hover:border-primary"}`}
                        onClick={() =>
                          document.getElementById("csv-file")?.click()
                        }
                      >
                        <Input
                          id="csv-file"
                          type="file"
                          accept=".csv"
                          onChange={handleFileChange}
                          className="hidden"
                          disabled={uploadStatus.type === "loading"}
                        />
                        {!file && (
                          <div className="text-center py-4">
                            <FileText className="mx-auto h-10 w-10 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-600">
                              <span className="font-semibold text-primary">
                                Click to upload
                              </span>{" "}
                              or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">
                              CSV files only
                            </p>
                          </div>
                        )}
                        {file && (
                          <div className="text-center py-4">
                            <CheckCircle className="mx-auto h-10 w-10 text-green-500 mb-2" />
                            <p className="text-sm font-medium text-gray-700">
                              {file.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024).toFixed(2)} KB - Click to
                              change
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    {renderStatusAlert()}
                    <Button
                      type="submit"
                      className="w-full mt-6"
                      disabled={uploadStatus.type === "loading" || !file}
                    >
                      {uploadStatus.type === "loading" ? (
                        <>
                          {" "}
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />{" "}
                          Processing...
                        </>
                      ) : (
                        "Upload and Process"
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
        {/* Image Upload (right) */}
        <div className="w-full md:w-1/2">
          <div className="w-full max-w-lg mx-auto">
            <button
              type="button"
              className="flex items-center w-full px-4 py-2 mb-2 bg-muted rounded hover:bg-muted/80 transition-colors border border-border"
              onClick={() => setIsUploadOpen((open) => !open)}
            >
              {isUploadOpen ? (
                <ChevronDown className="mr-2 h-5 w-5" />
              ) : (
                <ChevronRight className="mr-2 h-5 w-5" />
              )}
              <span className="font-semibold">上傳管理費自動轉帳圖檔</span>
            </button>
            {isUploadOpen && (
              <Card className="w-full shadow-lg">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">
                    上傳管理費自動轉帳圖檔
                  </CardTitle>
                  <CardDescription>
                    上傳管理費自動轉帳對帳單圖片，AI 辨識後自動填入 Google
                    Sheets。
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleImageUpload} className="space-y-6">
                    <div>
                      <Label
                        htmlFor="image-file"
                        className="text-sm font-medium sr-only"
                      >
                        Select Image File
                      </Label>
                      <div
                        className={`flex items-center justify-center w-full p-2 border-2 border-dashed rounded-md cursor-pointer ${
                          imageUploadStatus.type === "loading"
                            ? "border-gray-300 bg-gray-50"
                            : "border-gray-300 hover:border-primary"
                        }`}
                        onClick={() =>
                          document.getElementById("image-file")?.click()
                        }
                      >
                        <Input
                          id="image-file"
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={handleImageChange}
                          className="hidden"
                          disabled={imageUploadStatus.type === "loading"}
                        />
                        {!imageFiles.length && (
                          <div className="text-center py-4">
                            <FileText className="mx-auto h-10 w-10 text-gray-400 mb-2" />
                            <p className="text-sm text-gray-600">
                              <span className="font-semibold text-primary">
                                Click to upload
                              </span>{" "}
                              or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">
                              Image files only (可多選)
                            </p>
                          </div>
                        )}
                        {!!imageFiles.length && (
                          <div className="text-center py-4">
                            <CheckCircle className="mx-auto h-10 w-10 text-green-500 mb-2" />
                            <ul className="text-sm font-medium text-gray-700">
                              {imageFiles.map((file, idx) => (
                                <li key={idx}>
                                  {file.name}{" "}
                                  <span className="text-xs text-gray-500">
                                    ({(file.size / 1024).toFixed(2)} KB)
                                  </span>
                                </li>
                              ))}
                            </ul>
                            <p className="text-xs text-gray-500">
                              點擊可重新選擇
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    {/* Status Alert for Image Upload */}
                    {renderImageStatusAlert()}
                    <Button
                      type="submit"
                      className="w-full mt-6"
                      disabled={
                        imageUploadStatus.type === "loading" ||
                        !imageFiles.length
                      }
                    >
                      {imageUploadStatus.type === "loading" ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />{" "}
                          Processing...
                        </>
                      ) : (
                        "Upload and Recognize"
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
      <Card className="w-full shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex flex-col justify-between items-start">
            <div className="flex items-center space-x-2">
              <TableIcon className="h-6 w-6 text-primary" />
              <CardTitle>社區管理費繳費統計表</CardTitle>
              {/* Year Picker  */}
              <div className="flex items-center space-x-2 pl-8">
                <Label htmlFor="target-year" className="text-sm font-medium">
                  選擇年份
                </Label>
                <Select value={targetYear} onValueChange={setTargetYear}>
                  <SelectTrigger className="w-24 h-8 text-sm">
                    <SelectValue placeholder="年份" />
                  </SelectTrigger>
                  <SelectContent>
                    {yearOptions.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSheetData}
                disabled={isFetchingSheet}
              >
                {isFetchingSheet ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="mr-2 h-4 w-4" />
                )}
                更新資料
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={!sheetData || sheetData.rows.length === 0}
                    className="ml-2"
                  >
                    下載報表 <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={handleDownloadCSV}>
                    <FileText className="mr-2 h-4 w-4" />
                    下載CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleDownloadPDF}>
                    <FileText className="mr-2 h-4 w-4" /> {/* Consider a PDF specific icon if available */}
                    下載PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {/* New: Buttons for each data type */}
              <div className="flex space-x-1 pl-8">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("depositsUnmatched")}
                >
                  未歸類存入
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("depositsSmallAmount")}
                >
                  小額存入
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("withdrawals")}
                >
                  已歸類提領
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("withdrawalsUnmatched")}
                >
                  未歸類提領
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => openDialog("motorBikeParkingLot")}
                >
                  機踏車停車費
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleShowOverdue}
                >
                  欠費＞2個月
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="overflow-auto p-0">
          {isFetchingSheet && !sheetData && (
            <div className="flex justify-center items-center py-4">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="ml-3 text-muted-foreground">
                Loading sheet data...
              </p>
            </div>
          )}
          {fetchSheetError && !isFetchingSheet && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertTitle>Error Loading Data</AlertTitle>
              <AlertDescription>{fetchSheetError}</AlertDescription>
            </Alert>
          )}
          {!isFetchingSheet && sheetData && sheetData.rows.length > 0 && (
            <>
              <div className="max-w-[92vw] flex items-center justify-start gap-x-2 mb-2">
                <div className="flex items-center font-bold ml-[1.3rem] p-2 whitespace-nowrap">
                  ＜ 未繳費名單 ＞
                </div>
                {/* 動態產生每月未繳名單按鈕（不顯示住戶ID欄） */}
                {monthHeaders.map((month) => (
                  <Button
                    key={month}
                    className="h-8 text-[14px] px-1.5 font-sans"
                    variant="secondary"
                    onClick={() => handleShowUnpaid(month)}
                  >
                    {month}
                  </Button>
                ))}
              </div>
              <div className="overflow-x-auto max-h-[500px] xl:max-h-[720px]">
                <Table>
                  <TableHeader className="sticky top-0 bg-background z-10">
                    <TableRow>
                      {sheetData.headers.map((header, index) => (
                        // 不顯示 住戶編號 欄的尚未繳費名單按鈕
                        <TableHead
                          key={index}
                          className="px-[0.4rem] font-bold text-right whitespace-nowrap"
                        >
                          {String(header)}{" "}
                          {/* Ensure header is a string for rendering */}
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sheetData.rows.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {row.map((cell, cellIndex) => (
                          <TableCell
                            key={cellIndex}
                            className="py-1 font-semibold text-right whitespace-nowrap"
                          >
                            {String(cell)}{" "}
                            {/* Ensure cell is a string for rendering */}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </>
          )}
          {!isFetchingSheet && sheetData && sheetData.rows.length === 0 && (
            <p className="text-center text-muted-foreground py-10">
              No data found in the sheet.
            </p>
          )}
        </CardContent>
      </Card>

      {/* 尚未繳費名單 Dialog */}
      {unpaidDialogOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-green-200 dark:bg-green-700 bg-opacity-90 rounded-lg shadow-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold">
                {unpaidMonth} 尚未繳費名單 （共 {filteredUnpaidList.length} 筆）
              </h2>
              <button
                onClick={() => setUnpaidDialogOpen(false)}
                className="text-gray-500 dark:text-gray-200 hover:text-gray-700 text-xl"
              >
                ×
              </button>
            </div>
            <div className="flex items-center justify-between mb-2 gap-x-2">
              <p className="w-60 text-sm text-gray-500 dark:text-gray-300">
                註：7-ELEVEN包含「06-01-1」、「08-01-1」，繳費金額歸戶於「06-01-1」。
              </p>
              <Button
                size="sm"
                variant="outline"
                onClick={async () => {
                  if (filteredUnpaidList.length === 0) return;
                  try {
                    await navigator.clipboard.writeText(
                      filteredUnpaidList.join("\n")
                    );
                    toast.success("已複製未繳住戶ID清單！");
                  } catch {
                    toast.error("複製失敗，請手動選取複製。");
                  }
                }}
              >
                複製清單
              </Button>
            </div>
            {filteredUnpaidList.length === 0 ? (
              <p className="text-center text-muted-foreground">
                全部住戶皆已繳費！
              </p>
            ) : (
              <ul className="max-h-80 overflow-y-auto list-disc pl-5">
                {filteredUnpaidList.map((id, idx) => (
                  <li
                    key={id + idx}
                    className="lining-nums font-mono font-stretch-ultra-expanded text-sm w-40 inline-block text-left"
                  >
                    {id}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}
      {/* Data Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {dialogType === "depositsUnmatched"
                ? "未歸類存入"
                : dialogType === "depositsSmallAmount"
                  ? "小額存入"
                  : dialogType === "withdrawals"
                    ? "已歸類提領"
                    : dialogType === "withdrawalsUnmatched"
                      ? "未歸類提領"
                      : dialogType === "motorBikeParkingLot"
                        ? "機車停車費"
                        : ""}
            </DialogTitle>
          </DialogHeader>
          <div className="flex justify-between items-center">
            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                // Prepare tab-separated or comma-separated values for copy
                const header = [
                  "交易日期",
                  "類型",
                  "金額",
                  "說明",
                  "交易資訊",
                  "備註",
                ];
                const rows = dialogData.map((item) => {
                  const row = item?.row || item || {};
                  return [
                    row["交易日期"] || item.transactionDate || "",
                    row["說明"] || item.description || row["說明"] || "",
                    row["類型"] || item.type || "",
                    row["金額"] || item.amount || "",
                    row["交易資訊"] || item.info || row["交易資訊"] || "",
                    row["備註"] || item.remark || row["備註"] || "",
                  ].map((v) => (typeof v === "undefined" ? "" : String(v)));
                });
                const text = [header, ...rows]
                  .map((r) => r.join("\t"))
                  .join("\n");
                try {
                  await navigator.clipboard.writeText(text);
                  toast.success("已複製資料！");
                } catch {
                  toast.error("複製失敗，請手動選取複製。");
                }
              }}
            >
              複製資料
            </Button>
          </div>
          <div className="overflow-x-auto max-h-[60vh]">
            <table className="min-w-full border text-xs rounded-lg shadow-lg">
              <thead>
                <tr className="bg-muted">
                  <th className="px-2 py-1 border whitespace-nowrap">
                    交易日期
                  </th>
                  <th className="px-2 py-1 border whitespace-nowrap">說明</th>
                  <th className="px-2 py-1 border whitespace-nowrap">類型</th>
                  <th className="px-2 py-1 border text-right whitespace-nowrap">
                    金額
                  </th>
                  <th className="px-2 py-1 border whitespace-nowrap">
                    交易資訊
                  </th>
                  <th className="px-2 py-1 border whitespace-nowrap">備註</th>
                </tr>
              </thead>
              <tbody>
                {dialogData && dialogData.length > 0 ? (
                  dialogData.map((item, idx) => {
                    const row = item?.row || item || {};
                    return (
                      <tr
                        key={idx}
                        className="even:bg-gray-50 dark:even:bg-gray-800"
                      >
                        <td className="px-2 py-1 border whitespace-nowrap">
                          {row["交易日期"] || item.transactionDate || ""}
                        </td>
                        <td className="px-2 py-1 border whitespace-nowrap">
                          {row["說明"] || item.description || row["說明"] || ""}
                        </td>
                        <td className="px-2 py-1 border">
                          {row["類型"] || item.type || ""}
                        </td>
                        <td className="px-2 py-1 border text-right whitespace-nowrap">
                          {row["金額"] || item.amount || ""}
                        </td>
                        <td className="px-2 py-1 border whitespace-nowrap">
                          {row["交易資訊"] ||
                            item.info ||
                            row["交易資訊"] ||
                            ""}
                        </td>
                        <td className="px-2 py-1 border whitespace-nowrap">
                          {row["備註"] || item.remark || row["備註"] || ""}
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td
                      colSpan={6}
                      className="text-center py-4 text-muted-foreground"
                    >
                      無資料
                    </td>
                  </tr>
                )}
              </tbody>
              {/* Totals row */}
              {dialogData && dialogData.length > 0 && (
                <tfoot>
                  <tr className="bg-muted font-bold">
                    <td className="px-2 py-1 border text-right" colSpan={3}>
                      合計
                    </td>
                    <td className="px-2 py-1 border text-right whitespace-nowrap">
                      {dialogData
                        .reduce((sum, item) => {
                          const row = item?.row || item || {};
                          // Try both "金額" and amount, and parse as float
                          const val = row["金額"] ?? item.amount ?? "";
                          const num =
                            typeof val === "string"
                              ? parseFloat(val.replace(/,/g, ""))
                              : typeof val === "number"
                                ? val
                                : 0;
                          return sum + (isNaN(num) ? 0 : num);
                        }, 0)
                        .toLocaleString("en-US", {
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 2,
                        })}
                    </td>
                    <td className="px-2 py-1 border" colSpan={2}></td>
                  </tr>
                </tfoot>
              )}
            </table>
          </div>
        </DialogContent>
      </Dialog>
      {overdueDialogOpen && (
        <Dialog open={overdueDialogOpen} onOpenChange={setOverdueDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>欠費超過2個月住戶</DialogTitle>
            </DialogHeader>
            <div className="flex justify-between items-center mb-4">
              <div className="text-sm text-muted-foreground">
                共 {overdueList.length} 筆
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={async () => {
                  const header = ["住戶編號", "(欠)預繳金額", "(欠)預繳月數"];
                  const rows = overdueList.map((item) => [
                    item.id,
                    item.amount,
                    item.months,
                  ]);
                  const text = [header, ...rows]
                    .map((r) => r.join("\t"))
                    .join("\n");

                  try {
                    await navigator.clipboard.writeText(text);
                    toast.success("已複製資料！");
                  } catch {
                    toast.error("複製失敗，請手動選取複製。");
                  }
                }}
              >
                複製資料
              </Button>
            </div>
            <div className="overflow-x-auto max-h-[60vh]">
              <table className="min-w-full border rounded-lg shadow-lg">
                <thead className="text-sm">
                  <tr className="bg-muted">
                    <th className="px-4 py-2 border">住戶編號</th>
                    <th className="px-4 py-2 border">(欠)預繳金額</th>
                    <th className="px-4 py-2 border">(欠)預繳月數</th>
                  </tr>
                </thead>
                <tbody className="text-sm">
                  {overdueList.map((item, idx) => (
                    <tr
                      key={idx}
                      className="even:bg-gray-50 dark:even:bg-gray-800"
                    >
                      <td className="px-4 py-2 border text-center">
                        {item.id}
                      </td>
                      <td className="px-4 py-2 border text-right">
                        {item.amount}
                      </td>
                      <td className="px-4 py-2 border text-right">
                        {item.months}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

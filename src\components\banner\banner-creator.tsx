import React, { useState, useEffect } from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Plus, Trash } from "lucide-react";

export type Banner = {
  id: string;
  title: string;
  size?: string;
  color?: string;
  fontWeight?: string;
  type: "title" | "assignee" | "custom";
  shared?: boolean;
  description?: string;
  userId?: string;
};

interface BannerCreatorProps {
  onBannerCreate: (banner: Banner) => void;
  editBanner?: Banner; // Add this prop for editing
  onEditComplete?: () => void; // Optional callback when edit is complete
}

export const BannerCreator: React.FC<BannerCreatorProps> = ({
  onBannerCreate,
  editBanner,
  onEditComplete,
}) => {
  const [titles, setTitles] = useState<string[]>(() => {
    if (editBanner?.title) {
      // Split the combined title back into individual titles
      return editBanner.title.split(
        "\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0"
      );
    }
    return [""];
  });
  const [size, setSize] = useState(editBanner?.size || "text-base");
  const [type, setType] = useState<"title" | "assignee" | "custom">(
    editBanner?.type || "custom"
  );
  const [color, setColor] = useState(editBanner?.color || "#000000");
  const [fontWeight, setFontWeight] = useState(
    editBanner?.fontWeight || "font-normal"
  );

  // Update form state when editBanner changes
  useEffect(() => {
    if (editBanner) {
      const splitTitles = editBanner.title.split(
        "\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0"
      );
      setTitles(splitTitles);
      setSize(editBanner.size || "text-base");
      setType(editBanner.type);
      setColor(editBanner.color || "#000000");
      setFontWeight(editBanner.fontWeight || "font-normal");
    }
  }, [editBanner]);

  const addTitleInput = () => {
    setTitles([...titles, ""]);
  };

  const updateTitle = (index: number, value: string) => {
    const newTitles = [...titles];
    newTitles[index] = value;
    setTitles(newTitles);
  };

  const removeTitleInput = (index: number) => {
    setTitles(titles.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const combinedTitle = titles
      .filter((title) => title.trim() !== "")
      .join("\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0");
    try {
      const method = editBanner ? "PUT" : "POST";
      const url = editBanner
        ? `/api/banners?id=${editBanner.id}`
        : "/api/banners";

      const res = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: combinedTitle,
          size,
          color,
          fontWeight,
          type,
          description: "", // Optionally add description input if needed
          shared: false, // Optionally add shared toggle if needed
        }),
      });

      if (!res.ok)
        throw new Error(
          editBanner ? "Failed to update banner" : "Failed to create banner"
        );
      const data = await res.json();

      if (data.banner) {
        const bannerData = {
          id: data.banner.id,
          title: data.banner.title,
          size: data.banner.size,
          color: data.banner.color,
          fontWeight: data.banner.fontWeight,
          type,
        };
        onBannerCreate(bannerData);
        if (editBanner) {
          onEditComplete?.();
        }
      }
    } catch (err) {
      console.error(err);
    }

    if (!editBanner) {
      // Only reset if we're not in edit mode
      setTitles([""]);
      setType("custom");
      setSize("text-base");
      setColor("#000000");
      setFontWeight("font-normal");
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          {titles.map((title, index) => (
            <div key={index} className="flex items-end space-x-2">
              <Input
                id={`title-${index}`}
                value={title}
                onChange={(e) => updateTitle(index, e.target.value)}
                placeholder={`Enter banner title ${index + 1}`}
                className="flex-1"
              />
              {titles.length > 1 && (
                <Button
                  type="button"
                  size="sm"
                  onClick={() => removeTitleInput(index)}
                  className="flex-shrink-0"
                >
                  <Trash className="h-4 w-4" />
                  <span className="sr-only">Remove this title</span>
                </Button>
              )}
            </div>
          ))}
        </div>

        <div className="flex flex-wrap gap-4">
          <div>
            {" "}
            <Label htmlFor="banner-type">Type</Label>
            <Select
              value={type}
              onValueChange={(value: "title" | "assignee" | "custom") =>
                setType(value)
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="title">標題</SelectItem>
                <SelectItem value="assignee">簽章</SelectItem>
                <SelectItem value="custom">其他</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="banner-size">Font Size</Label>
            <Select value={size} onValueChange={setSize}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select Size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text-xs">12px (Extra Small)</SelectItem>
                <SelectItem value="text-sm">14px (Small)</SelectItem>
                <SelectItem value="text-base">16px (Base)</SelectItem>
                <SelectItem value="text-lg">18px (Large)</SelectItem>
                <SelectItem value="text-xl">20px (Extra Large)</SelectItem>
                <SelectItem value="text-2xl">24px (2XL)</SelectItem>
                <SelectItem value="text-3xl">30px (3XL)</SelectItem>
                <SelectItem value="text-4xl">36px (4XL)</SelectItem>
                <SelectItem value="text-5xl">48px (5XL)</SelectItem>
                <SelectItem value="text-6xl">60px (6XL)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="banner-font-weight">Font Weight</Label>
            <Select value={fontWeight} onValueChange={setFontWeight}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select Weight" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="font-thin">Thin</SelectItem>
                <SelectItem value="font-light">Light</SelectItem>
                <SelectItem value="font-normal">Normal</SelectItem>
                <SelectItem value="font-medium">Medium</SelectItem>
                <SelectItem value="font-semibold">Semibold</SelectItem>
                <SelectItem value="font-bold">Bold</SelectItem>
                <SelectItem value="font-extrabold">Extra Bold</SelectItem>
                <SelectItem value="font-black">Black</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="banner-color">Text Color</Label>
            <Input
              id="banner-color"
              type="color"
              value={color}
              onChange={(e) => setColor(e.target.value)}
              className="w-20 h-10"
            />
          </div>

          <Button
            type="button"
            onClick={addTitleInput}
            size="sm"
            className="self-end"
          >
            <Plus className="h-4 w-4" />
            <span className="sr-only">Add more titles</span>
          </Button>

          <Button type="submit" className="self-end">
            {editBanner ? "Update Banner" : "Create Banner"}
          </Button>
        </div>
      </form>
    </div>
  );
};

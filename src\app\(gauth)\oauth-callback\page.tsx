'use client';
import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function GoogleOAuthCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleRedirect = async () => {
      // Log 1: Initial parameters
      const code = searchParams.get("code");
      const state = searchParams.get("state");
      console.log('Initial parameters:', { code: code?.substring(0, 10) + '...', state });

      if (!code || !state) {
        console.error("Missing code or state parameters");
        router.push("/glogin");
        return;
      }

      // Log 2: State verification
      const storedState = sessionStorage.getItem("oauth_state");
      console.log('State comparison:', { 
        receivedState: state,
        storedState: storedState
      });

      if (state !== storedState) {
        console.error("State parameter mismatch");
        router.push("/glogin");
        return;
      }

      try {
        // Log 3: Before API call
        console.log('Attempting API call with code and state');

        const response = await fetch('/api/auth/google-callback', {
          method: 'POST',
          body: JSON.stringify({ code, state }),
          headers: {
            'Content-Type': 'application/json',
          },
        });

        // Log 4: API Response details
        console.log('API Response status:', response.status);
        const data = await response.json();
        console.log('API Response data:', data);

        if (data.success) {
          console.log('Authentication successful, redirecting to gdrive');
          router.push('/gdrive');
        } else {
          console.error("OAuth Error:", data.error);
          router.push("/glogin?error=auth_failed");
        }
      } catch (err: any) {
        // Log 5: Detailed error information
        console.error("OAuth Error Details:", {
          message: err.message,
          stack: err.stack,
          type: err.constructor.name
        });
        router.push("/glogin?error=auth_failed");
      }
    };

    handleRedirect();
  }, [searchParams, router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p>Completing sign-in...</p>
      </div>
    </div>
  );
}
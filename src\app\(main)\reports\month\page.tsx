"use client";

import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { getAccountIds } from "@/actions/account/get-accountIds"
import { AccountReports } from "@/components/reports/account-reports"
import { EmptyPlaceholder } from "@/components/empty-placeholder";
import { Wallet } from "lucide-react";
import { useEffect, useState } from "react";
import { Banner } from "@/components/banner/banner-creator";

export default function BankingPage() {
  const [user, setUser] = useState<any>(null);
  const [accounts, setAccounts] = useState<any[]>([]);
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      // Get current user
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        redirect("/");
      }
      setUser(currentUser);

      // Get accounts
      const { success, accounts: accountsData } = await getAccountIds();
      if (success && accountsData) {
        setAccounts(accountsData);
      }

      // Get banners
      const bannersRes = await fetch("/api/banners");
      if (bannersRes.ok) {
        const data = await bannersRes.json();
        setBanners(data.banners || []);
      }

      setLoading(false);
    }

    fetchData();
  }, []);

  if (loading) {
    return <div className="flex justify-center items-center">Loading...</div>;
  }

  if (!accounts || accounts.length === 0) {
    return (
      <div className="flex flex-1 flex-col gap-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Accounts</h2>
        </div>
        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon icon={Wallet} />
          <EmptyPlaceholder.Title>No accounts yet</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            Connect your accounts to start tracking your finances.
          </EmptyPlaceholder.Description>
        </EmptyPlaceholder>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 pt-0 mx-auto">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <AccountReports
            reportType="MONTHLY"
            bankAccounts={accounts} 
            initialBanners={banners}
          />
        </div>
      </div>
    </div>
  );
}

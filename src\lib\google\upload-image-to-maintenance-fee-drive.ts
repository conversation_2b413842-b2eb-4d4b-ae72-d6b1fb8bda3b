import { google } from 'googleapis';
import { Readable } from 'stream';

export const uploadImageToMaintenanceFeeDrive = async (buffer: Buffer, filename: string) => {
  const auth = new google.auth.GoogleAuth({
    credentials: {
      client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    },
    scopes: ['https://www.googleapis.com/auth/drive.file'],
  });
  // Test auth before making the API call
  try {
    await auth.getAccessToken();
    console.log('Authentication successful');
  } catch (authError) {
    console.error('Authentication failed:', authError);
    throw authError;
  }  
  try {
    const drive = google.drive({ version: "v3", auth });
    
    const fileMetadata: any = {
      name: filename,
      mimeType: 'image/jpeg',
    };
    if (process.env.GOOGLE_DRIVE_AUTOPAY_RECEIPTS_FOLDER_ID) {
      fileMetadata.parents = [process.env.GOOGLE_DRIVE_AUTOPAY_RECEIPTS_FOLDER_ID];
    }

    const media = {
      mimeType: 'image/jpeg',
      body: Readable.from(buffer),
    };

    const response = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id, webViewLink',
    });

    return {
      fileId: response.data.id,
      webViewLink: response.data.webViewLink
    };
  } catch (error) {
    console.error('Error uploading to Google Drive:', error);
    throw error;
  }
};

import { SpeechData } from "./types";
import { Pencil1Icon, PlayIcon } from "@radix-ui/react-icons";
import { useUpdateSpeechModal } from "@/hooks/use-update-speech-modal";
import { OverviewProperty } from "@/components/tasks/overview-property";
import { SpeechDate } from "./speech-date";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { ProjectAvatar } from "@/components/projects/project-avatar";

interface SpeechOverviewProps {
  data: SpeechData;
}

export const SpeechOverview = ({ data }: SpeechOverviewProps) => {
  const { open } = useUpdateSpeechModal();
  
  return (
    <div className="flex flex-col gap-y-4 col-span-1">
      {/* Basic overview section */}
      <div className="bg-muted/50 rounded-lg p-4">
        <div className="flex flex-start items-center justify-between">
          <p className="text-lg font-semibold">Overview</p>
          <Button
            onClick={() => open(data.speech.id)}
            className="h-8 ml-auto"
            variant="datePicker"
            size="sm"
          >
            <Pencil1Icon className="size-4 mr-2" />
            Edit
          </Button>
        </div>
        <Separator className="my-2" />
        <div className="flex flex-col gap-y-4">
          <OverviewProperty label="Title">
            <span className="text-sm font-medium">{data.speech.title}</span>
          </OverviewProperty>
          
          {data.speech.description && (
            <OverviewProperty label="Description">
              <span className="text-sm font-medium">{data.speech.description}</span>
            </OverviewProperty>
          )}
          
          <OverviewProperty label="Project">
            <div className="flex items-center gap-x-2">
              <ProjectAvatar 
                className="size-4" 
                name={data.project?.name || ""} 
                image={data.project?.imageUrl || ""} 
              />
              <span className="text-sm font-medium">{data.project?.name}</span>
            </div>
          </OverviewProperty>
          
          <div className="flex flex-row gap-x-6 2xl:gap-x-12">
            <div className="flex flex-col gap-y-2">
              <OverviewProperty label="Created Date">
                <SpeechDate value={data.speech.createdAt} className="text-sm font-medium" />
              </OverviewProperty>
              <OverviewProperty label="Updated Date">
                <SpeechDate value={data.speech.updatedAt} className="text-sm font-medium" />
              </OverviewProperty>
            </div>
          </div>
        </div>
      </div>

      {/* Recordings section */}
      <div className="bg-muted/50 rounded-lg p-4">
        <div className="flex flex-start items-center justify-between">
          <p className="text-lg font-semibold">Recordings</p>
          <Badge variant="outline" className="ml-2">
            {data.speech.recordings.length}
          </Badge>
        </div>
        <Separator className="my-2" />
        {data.speech.recordings.length === 0 ? (
          <p className="text-sm text-muted-foreground py-2">No recordings available</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
            {data.speech.recordings.map((recording) => (
              <Card key={recording.id} className="overflow-hidden">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <PlayIcon className="size-4 mr-2" />
                      <span className="text-sm font-medium">
                        {formatDuration(recording.duration)}
                      </span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      <SpeechDate value={recording.createdAt} />
                    </div>
                  </div>
                  <audio 
                    controls 
                    src={recording.url} 
                    className="w-full mt-2 h-8"
                  />
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Transcriptions section */}
      <div className="bg-muted/50 rounded-lg p-4">
        <div className="flex flex-start items-center justify-between">
          <p className="text-lg font-semibold">Transcriptions</p>
          <Badge variant="outline" className="ml-2">
            {data.speech.transcriptions.length}
          </Badge>
        </div>
        <Separator className="my-2" />
        {data.speech.transcriptions.length === 0 ? (
          <p className="text-sm text-muted-foreground py-2">No transcriptions available</p>
        ) : (
          <Accordion type="single" collapsible className="w-full">
            {data.speech.transcriptions.map((transcription, index) => (
              <AccordionItem key={transcription.id} value={transcription.id}>
                <AccordionTrigger className="py-2">
                  <div className="flex items-center gap-x-2">
                    <Badge variant="outline">{transcription.language}</Badge>
                    <span className="text-sm font-medium">Transcription {index + 1}</span>
                    <span className="text-xs text-muted-foreground ml-2">
                      <SpeechDate value={transcription.createdAt} />
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="bg-background p-3 rounded-md text-sm whitespace-pre-wrap">
                    {transcription.text}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>

      {/* Analyses section */}
      <div className="bg-muted/50 rounded-lg p-4">
        <div className="flex flex-start items-center justify-between">
          <p className="text-lg font-semibold">Analyses</p>
          <Badge variant="outline" className="ml-2">
            {data.speech.analyses.length}
          </Badge>
        </div>
        <Separator className="my-2" />
        {data.speech.analyses.length === 0 ? (
          <p className="text-sm text-muted-foreground py-2">No analyses available</p>
        ) : (
          <Accordion type="single" collapsible className="w-full">
            {data.speech.analyses.map((analysis, index) => (
              <AccordionItem key={analysis.id} value={analysis.id}>
                <AccordionTrigger className="py-2">
                  <div className="flex items-center gap-x-2">
                    <Badge 
                      variant={getSentimentVariant(analysis?.sentiment!)}
                    >
                      {analysis.sentiment}
                    </Badge>
                    <span className="text-sm font-medium">Analysis {index + 1}</span>
                    <span className="text-xs text-muted-foreground ml-2">
                      <SpeechDate value={analysis.createdAt} />
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Summary</h4>
                      <div className="bg-background p-3 rounded-md text-sm">
                        {analysis.summary}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium mb-1">Key Points</h4>
                      <div className="bg-background p-3 rounded-md text-sm">
                        <ul className="list-disc pl-5 space-y-1">
                          {renderKeyPoints(analysis.keyPoints)}
                        </ul>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>
    </div>
  );
};

// Helper functions
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function getSentimentVariant(sentiment: string) {
  switch (sentiment.toLowerCase()) {
    case 'positive':
      return 'success';
    case 'negative':
      return 'destructive';
    case 'neutral':
      return 'secondary';
    default:
      return 'outline';
  }
}

function renderKeyPoints(keyPoints: any) {
  if (!keyPoints) return <li>No key points available</li>;
  
  if (Array.isArray(keyPoints)) {
    return keyPoints.map((point, index) => (
      <li key={index}>{typeof point === 'string' ? point : JSON.stringify(point)}</li>
    ));
  }
  
  if (typeof keyPoints === 'object') {
    return Object.entries(keyPoints).map(([key, value], index) => (
      <li key={index}>{key}: {typeof value === 'string' ? value : JSON.stringify(value)}</li>
    ));
  }
  
  return <li>{String(keyPoints)}</li>;
}
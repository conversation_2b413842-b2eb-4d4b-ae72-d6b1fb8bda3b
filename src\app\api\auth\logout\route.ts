import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { serialize } from "cookie";

export async function POST() {
  const { userId } = await auth();
  if (!userId) return NextResponse.json({ error: "Unauthorized", success: false }, { status: 401 });
  
  // Clear all auth cookies
  const clearCookies = [
    serialize("access_token", "", { maxAge: -1, path: "/" }),
    serialize("refresh_token", "", { maxAge: -1, path: "/" }),
  ];

  const headers = new Headers();
  clearCookies.forEach(cookie => {
    headers.append("Set-Cookie", cookie);
  });

  return NextResponse.json(
    { success: true },
    { headers }
  );
}
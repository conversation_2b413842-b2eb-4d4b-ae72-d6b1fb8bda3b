{
  "theme": "Default",
  "selectAuthType": "oauth-personal",
  "preferedEditor": "vscode",
  "mcpServers": {
    "Bright Data": {
      "command": "npx",
      "args": ["@brightdata/mcp"],
      "env": {
        "API_TOKEN": "8fec0d7e87406df97acd1e51864c9d4760f67b5e97ceabb3c55a62292d014963",
        "WEB_UNLOCKER_ZONE": "web_unlocker1",
        "BROWSER_ZONE": "<optional browser zone name, defaults to mcp_browser>",
        "RATE_LIMIT": "10/5s" // <optional rate limit format: limit/time+unit, e.g., 100/1h, 50/30m, 10/5s>
      }
    }
  }
}
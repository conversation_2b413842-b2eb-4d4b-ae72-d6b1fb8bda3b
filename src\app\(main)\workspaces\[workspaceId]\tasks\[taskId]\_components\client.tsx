"use client"

import { TaskData } from "@/actions/tasks/get-task";
import { TaskBreadcrumbs } from "@/components/tasks/task-breadcrumbs";
import { TaskOverview } from "@/components/tasks/task-overview";
import { TaskDescription } from "@/components/tasks/task-description";
import { Separator } from "@/components/ui/separator";

interface TaskIdClientProps {
  initialValues: TaskData;
}

export const TaskIdClient = ({
    initialValues,
}: TaskIdClientProps) => {
  return (
    <div className="flex flex-col">
      <TaskBreadcrumbs project={initialValues.project} task={initialValues.task} />
      <Separator className="my-4" />
      <div className="grid grid-col-1 lg:grid-cols-2 gap-4">
        <TaskOverview data={initialValues} />
        <TaskDescription data={initialValues} />
      </div>
    </div>
  )
}
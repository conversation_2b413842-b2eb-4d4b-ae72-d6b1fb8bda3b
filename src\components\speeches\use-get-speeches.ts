import { useQuery } from "@tanstack/react-query";
import { getSpeeches } from "@/actions/speeches/get-speeches";
import { SpeechWithRelations } from "./types";

interface UseSpeechesProps {
  workspaceId: string;
  projectId?: string | null;
  search?: string | null;
  startDate?: string | null;
  endDate?: string | null;
}

export const useGetSpeeches = ({
  workspaceId,
  projectId,
  search,
  startDate,
  endDate,
}: UseSpeechesProps) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["speeches", workspaceId, projectId, search, startDate, endDate],
    queryFn: async () => {
      const response = await getSpeeches({
        workspaceId,
        projectId: projectId || undefined,
        search: search || undefined,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
      });

      console.log("Raw response:", response);

      if (!response.success) {
        throw new Error(response.error || "Failed to fetch speeches");
      }

      return response.data;
    },
    enabled: !!workspaceId,
  });

  return {
    speeches: data as SpeechWithRelations[] || [],
    isLoading,
    error,
  };
};

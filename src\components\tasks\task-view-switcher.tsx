"use client"

import { debounce } from "lodash";
import { useCallback } from "react";
import { TaskStatus } from "@prisma/client";
import { useQueryState } from "nuqs";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Loader, PlusIcon } from "lucide-react";
import { Separator } from "@radix-ui/react-dropdown-menu";
import { useWorkspaceId } from "../workspaces/use-workspace-id";
import { useCreateTaskModal } from "@/hooks/use-create-task-modal";
import { useGetTasks } from "./use-get-tasks";
import { usebulkUpdateTasks } from "./use-bulk-update-tasks";
import { DataFilters } from "@/components/tasks/data-filters";
import { useTaskFilters } from "@/components/tasks/use-task-filters";
import { DataTable } from "./data-table";
import { columns } from "./columns";
import { DataKanban } from "./data-kanban";
import { DataCalendar} from "./data-calendar";

interface TaskViewSwitcherProps {
  initialProjectId?: string;
  hideProjectFilter?: boolean;
}

export const TaskViewSwitcher = ({
  initialProjectId,
  hideProjectFilter,
}: TaskViewSwitcherProps) => {
  const workspaceId = useWorkspaceId();
  const [view, setView] = useQueryState("task-view", {defaultValue: "table"});
  const [{
    status,
    assigneeId,
    projectId,
    startDate,
    dueDate,
  }] = useTaskFilters();

  const { populatedTasks, isLoading: isLoadingTasks } = useGetTasks({
    workspaceId,
    projectId: initialProjectId || projectId,
    status,
    assigneeId,
    startDate,
    dueDate,
  });
  
  const { open } = useCreateTaskModal();

  const { mutate: bulkUpdateTasks, isPending} = usebulkUpdateTasks();

  const debouncedOnKanbanChange = useCallback(
    debounce((tasks) => {
      console.log("Debounced tasks", tasks);
      bulkUpdateTasks({ tasks });
    }, 100), // Adjust debounce delay as needed
    []
  );
  const onKanbanChange = useCallback(
    (tasks: { id: string; status: TaskStatus; position: number }[]) => {
      //console.log("tasks", tasks);
      debouncedOnKanbanChange(tasks);
    },
    [debouncedOnKanbanChange]
  );

  return (
    <Tabs
      defaultValue={view}
      onValueChange={setView}
      className="flex-1 w-full border rounded-lg"
    >
      <div className="h-full flex flex-col overflow-auto p-4">
        <div className="flex flex-col gap-y-2 lg:flex-row justify-between items-center">
          <TabsList className="w-full lg:w-auto">
            <TabsTrigger
              className="h-8 w-full lg:w-auto"
              value="table"
            >
              Table
            </TabsTrigger>
            <TabsTrigger
              className="h-8 w-full lg:w-auto"
              value="kanban"
            >
              Kanban
            </TabsTrigger>
            <TabsTrigger
              className="h-8 w-full lg:w-auto"
              value="calendar"
            >
              Calendar
            </TabsTrigger>
          </TabsList>
            <Button onClick={open} size="sm" variant="outline" className="w-full lg:w-auto">
              <PlusIcon className="size-4" />
              New
            </Button>
        </div>
        <Separator className="my-4"/>
          <DataFilters hideProjectFilter={hideProjectFilter} />
        <Separator className="my-4"/>
          {isLoadingTasks ? (
            <div className="w-full border rounded-lg h-[200px] flex flex-col items-center justify-center">
              <Loader className="size-5 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <>
              <TabsContent value="table" className="mt-0">
                <DataTable 
                  columns={columns}
                  data={populatedTasks}
                />
              </TabsContent>
              <TabsContent value="kanban" className="mt-0">
                <DataKanban data={populatedTasks} onChange={onKanbanChange}/>
              </TabsContent>
              <TabsContent value="calendar" className="mt-0 h-full pb-4">
                <DataCalendar data={populatedTasks} />
              </TabsContent>
            </>            
          )}
      </div>
    </Tabs>
  ) 
}
"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { GoogleDriveAuthStatus } from "./google-drive-auth-status";
import { useConnectGdriveModal } from "@/hooks/use-connect-gdrive-modal";

export const ConnectGdriveModal = () => {
  const { isOpen, setIsOpen, close } = useConnectGdriveModal()
  return (
    <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
      <GoogleDriveAuthStatus onCancel={close} />
    </ResponsiveModal>
  )
}
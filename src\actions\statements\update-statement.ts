"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { calculateTransactionHash } from "@/lib/utils/hash-calculation";

const updateStatementSchema = z.object({
  id: z.string(),
  description: z.string().optional(),
  amount: z.number().min(0, "金額必須大於 0"),
  date: z.date(),
  type: z.enum(["CREDIT", "DEBIT"]),
  categoryId: z.string(),
});

export async function updateStatement(
  input: z.infer<typeof updateStatementSchema>
): Promise<{ success: boolean; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) throw new Error("Unauthorized");

    const validatedFields = updateStatementSchema.parse(input);
    console.log("validatedFields", validatedFields);


    // 驗證並獲取正確的類別 ID
    //const categoryName = validatedFields.categoryId.charAt(0).toUpperCase() + validatedFields.categoryId.slice(1).toLowerCase();
    const category = await prisma.category.findFirst({
      where: {
        id: validatedFields.categoryId,
        userId: userId,
      },
    });

    if (!category) {
      return { success: false, error: "Invalid category selected" };
    }

    // 首先嘗試在 income 表中查找
    const income = await prisma.income.findUnique({
      where: { id: validatedFields.id },
    });

    // 如果在 income 表中找不到，則嘗試在 expense 表中查找
    if (!income) {
      const expense = await prisma.expense.findUnique({
        where: { id: validatedFields.id },
      });

      if (!expense) {
        throw new Error("Statement not found");
      }

      if (!expense.accountId) {
        throw new Error("Invalid account ID");
      }

      // 計算新的 hash 值 - Expense
      const importHash = calculateTransactionHash({
        date: validatedFields.date,
        type: validatedFields.type,
        categoryId: category.id,
        accountId: expense.accountId,
        amount: validatedFields.amount,
        description: validatedFields.description || ''
      });

      // 更新 expense 記錄
      await prisma.expense.update({
        where: { id: validatedFields.id },
        data: {
          description: validatedFields.description,
          amount: validatedFields.amount,
          date: validatedFields.date,
          type: validatedFields.type,
          categoryId: category.id,
          importHash,
        },
      });
    } else {
      if (!income.accountId) {
        throw new Error("Invalid account ID");
      }

      // 計算新的 hash 值 - Income
      const importHash = calculateTransactionHash({
        date: validatedFields.date,
        type: validatedFields.type,
        categoryId: category.id,
        accountId: income.accountId,
        amount: validatedFields.amount,
        description: validatedFields.description || ''
      });

      // 更新 income 記錄
      await prisma.income.update({
        where: { id: validatedFields.id },
        data: {
          description: validatedFields.description,
          amount: validatedFields.amount,
          date: validatedFields.date,
          type: validatedFields.type,
          categoryId: category.id,
          importHash,
        },
      });
    }

    revalidatePath("/statements");
    return { success: true };
  } catch (error) {
    console.error("Error updating statement:", error);
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0].message };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : "更新交易失敗",
    };
  }
}
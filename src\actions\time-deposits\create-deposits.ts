"use server";

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { Prisma } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { suggestCategory } from "@/lib/utils/transaction-categorization";

export async function createTimeDeposits({
  timeDeposits,
}: {
  timeDeposits: {
    accountId: string;
    certificateNo: string;
    period: string;
    interestRate: number;
    description: string;
    amount: number;
    type: "AVAILABLE" | "WITHDRAWN";
    categoryId?: string;
    currencyIso?: string;
    date?: Date;
  }[];
}) {
  try {
    console.log("Received time deposits:", JSON.stringify(timeDeposits, null, 2));

    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    // Validate input
    if (!timeDeposits || timeDeposits.length === 0) {
      throw new Error("No time deposits provided");
    }

    const createdTimeDeposits = [];
    const accountBalanceChanges: Record<string, number> = {};
    
    for (const timeDepositData of timeDeposits) {
      // Validate required fields
      if (!timeDepositData.accountId) {
        throw new Error("Account ID is required for each time deposit");
      }
      if (!timeDepositData.amount) {
        throw new Error("Amount is required for each time deposit");
      }

      const {
        accountId,
        certificateNo,
        period,
        interestRate,
        description,
        amount,
        type,
        categoryId,
        currencyIso = "TWD",
        date = new Date(),
      } = timeDepositData;

      // Verify account belongs to user
      const account = await prisma.bankAccount.findFirst({
        where: { 
          id: accountId, 
          userId 
        }
      });

      if (!account) {
        throw new Error(`Account ${accountId} not found or unauthorized`);
      }

      // Determine the suggested category
      const { categoryId: suggestedCategoryId, confidence } = suggestCategory(
        description,
        amount,
        "CREDIT"
      );

      // Use the user-provided categoryId, if available
      const category = categoryId
        ? await prisma.category.findUnique({
            where: { id: categoryId, userId },
          })
        : null;

      // Check if the category exists or is valid
      if (categoryId && !category) {
        throw new Error("Category not found or unauthorized");
      }

      // Create the new time deposit
      const timeDeposit = await prisma.timeDeposit.create({
        data: {
          accountId,
          certificateNo,
          period,
          interestRate: new Prisma.Decimal(interestRate),
          description,
          amount,
          type,
          currencyIso,
          date,
          categoryId: category ? category.id : undefined,
          suggestedCategoryId,
          categoryValidated: !!categoryId,
        },
      });

      createdTimeDeposits.push(timeDeposit);
      
      // Track balance changes by account
      const balanceChange = type === "AVAILABLE" ? amount : -amount;
      accountBalanceChanges[accountId] = (accountBalanceChanges[accountId] || 0) + balanceChange;
    }
    
    // Update balances for affected accounts
    for (const accountId in accountBalanceChanges) {
      const latestBalance = await prisma.balance.findFirst({
        where: { accountId },
        orderBy: { date: 'desc' },
      });
      
      const account = await prisma.bankAccount.findUnique({
        where: { id: accountId },
      });
      
      if (!account) continue;
      
      const currencyIso = createdTimeDeposits.find(td => td.accountId === accountId)?.currencyIso || 
                          (latestBalance?.currencyIso) || 
                          "TWD";
      
      const currentAmount = latestBalance?.amount ?? 0;
      const newAmount = currentAmount + accountBalanceChanges[accountId];
      
      await prisma.balance.create({
        data: {
          amount: newAmount,
          date: new Date(),
          bankAccount: {
            connect: {
              id: accountId,
            },
          },
          currency: {
            connect: {
              iso: currencyIso,
            },
          },
        },
      });
    }

    console.log("Created time deposits:", JSON.stringify(createdTimeDeposits, null, 2));

    revalidatePath("/banking")
    return { 
      success: true, 
      timeDeposits: createdTimeDeposits.map(td => ({
        ...td,
        interestRate: Number(td.interestRate)
      }))
    };
  } catch (error) {
    console.error("Error creating time deposits:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create time deposits" 
    };
  }
}
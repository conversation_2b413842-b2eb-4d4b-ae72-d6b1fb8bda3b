"use client"

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateSpeech } from '@/actions/speeches/update-speech';
import { z } from 'zod';
import { updateSpeechSchema } from '@/components/speeches/schemas';
import { toast } from 'sonner';

export function useUpdateSpeech() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof updateSpeechSchema>) => {
      console.log("values", values);
      const response = await updateSpeech(values);
      if (!response.success) {
        throw new Error(response.error || 'Failed to update speech.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Speech updated!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['speeches'] });
      queryClient.invalidateQueries({ queryKey: ['speech', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update speech.');
    },
  });

  return mutation;
}
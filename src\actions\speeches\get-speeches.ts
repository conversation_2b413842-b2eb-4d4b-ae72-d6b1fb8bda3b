"use server"

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";

interface GetSpeechesProps {
  workspaceId: string;
  projectId?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
}

export async function getSpeeches({
  workspaceId,
  projectId,
  search,
  startDate,
  endDate,
}: GetSpeechesProps) {
  const { userId } = await auth();
  if (!userId) {
    return { error: "Unauthorized", success: false };
  }

  try {
    const speeches = await prisma.speech.findMany({
      where: {
        workspaceId,
        projectId: projectId || undefined,
        title: search ? { contains: search, mode: "insensitive" } : undefined,
        createdAt: {
          gte: startDate ? new Date(startDate) : undefined,
          lte: endDate ? new Date(endDate) : undefined,
        },
      },
      include: {
        project: true,
        recordings: true,
        transcriptions: true,
        analyses: true,
      },
      orderBy: { createdAt: "desc" },
    });

    return { success: true, data: speeches };
  } catch (error) {
    console.error("Error fetching speeches:", error);
    return { error: "Failed to fetch speeches", success: false };
  }
}

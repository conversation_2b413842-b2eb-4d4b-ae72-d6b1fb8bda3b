import { getTemporaryAccessToken } from "@/actions/receipts/getTemporaryAccessToken";
import SchematicEmbed from "./SchematicEmbed";
async function SchematicComponent({ componentId }: { componentId: string }) {
  if (!componentId) {
    return null;
  }

  const accessToken = await getTemporaryAccessToken();
  if (!accessToken) {
    throw new Error("No access token found");
  }

  return <SchematicEmbed accessToken={accessToken} componentId={componentId} />;
};




export default SchematicComponent;
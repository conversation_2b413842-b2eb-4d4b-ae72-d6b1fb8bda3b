"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { Project, Speech } from "@prisma/client";
import { useWorkspaceId } from "../workspaces/use-workspace-id";
import { useDeleteSpeech } from "./use-delete-speech";
import { ProjectAvatar } from "../projects/project-avatar";
import { ChevronRightIcon, TrashIcon, Loader } from "lucide-react";
import { Button } from "@/components/ui/button";


interface SpeechBreadcrumbsProps {
  project: Project;
  speech: Speech;
}

export const SpeechBreadcrumbs = ({ 
  project, 
  speech 
}: SpeechBreadcrumbsProps) => {
  const router = useRouter();
  const workspaceId = useWorkspaceId();
  
  const { mutate, isPending } = useDeleteSpeech()

  const handleDeleteSpeech = async () => {
    mutate(speech.id, {
      onSuccess: () => {
        router.push(`/workspaces/${workspaceId}/projects/${project.id}`)
      },        
    })
  }

  return (
    <div className="flex items-center gap-x-2">
      <ProjectAvatar 
        name={project.name} 
        image={project?.imageUrl!} 
        className="size-6 lg:size-5"
      />
      <Link href={`/workspaces/${workspaceId}/projects/${project.id}`}>
        <p className="text-sm lg:text-md font-semibold text-foreground/60 hover:opacity-75 transition">
           {project.name}
        </p>
      </Link>
      <ChevronRightIcon className="size-3 lg:size-5 text-muted-foreground" />
      <p className="text-sm lg:text-md font-semibold text-foreground/60 hover:opacity-75 transition">
        {speech.title}
      </p>
      <Button
        onClick={handleDeleteSpeech}
        disabled={isPending}
        className="ml-auto"
        variant="destructive"
        size="sm"
      >
        {isPending ? (
          <Loader className="h-4 w-4 animate-spin" /> 
        ) : (
          <TrashIcon className="size-4" /> )}
        <span className="hidden lg:block">Delete Speech</span>
      </Button>
    </div>
  )
}

"use client"

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateTimeDeposit } from "@/actions/time-deposits/update-deposit";
import { z } from 'zod';
import { updateDepositSchema } from './schemas';
import { toast } from 'sonner';

export function useUpdateDeposit(bankAccountId: string) {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof updateDepositSchema>) => {
      console.log("values", values);
      const response = await updateTimeDeposit(values);
      if (!response.success) {
        throw new Error(response.error || 'Failed to update deposit.');
      }
      return { data: response.timeDeposit };
    },
    onSuccess: ({ data }) => {
      toast.success('Deposit updated!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ["banking", bankAccountId] });
      queryClient.invalidateQueries({ queryKey: ["deposites"] });
      queryClient.invalidateQueries({ queryKey: ['deposit', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update deposit.');
    },
  });

  return mutation;
}
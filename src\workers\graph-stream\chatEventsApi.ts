"use server"
import { createStreamableValue } from 'ai/rsc';
import { type AIMessageChunk } from "@langchain/core/messages";
import { type ToolCallChunk } from "@langchain/core/messages/tool";
import { graph } from "@/ai/research/open-canvas/index"
import { CustomModelConfig } from '@badget/shared/types';
import { createAI } from 'ai/rsc';

export type ModelConfigMap = {
  [modelName: string]: CustomModelConfig;
};

export const sendMessage = async (params: {
  threadId: string;    
  assistantId: string;
  input: Record<string, unknown>;  
  modelName: string;
  modelConfigs: ModelConfigMap;

}) => {
  
  let input: Record<string, any> | null = null;
  if (params.input !== null) {
    input = params.input
  }

  // Convert ModelConfig to CustomModelConfig
  const config = {
    configurable: {
      assistant_id: params?.assistantId!,
      thread_id: params?.threadId!,
      customModelName: params.modelName,
      modelConfig: params.modelConfigs[params.modelName as keyof typeof params.modelConfigs],
      system_prompt: "",
    },
    version: "v2" as "v2",
    encoding: undefined,
  }

  const stream = createStreamableValue();
  const eventStream = graph.streamEvents(input, config);

  (async () => {
    try {
      //console.log('Starting stream processing');
      
      for await (const chunk of eventStream) {
        //console.log('Raw chunk received:', chunk);
        
        try {
          // Log before serialization
          /*console.log('Attempting to serialize chunk:', {
            event: chunk.event,
            data: chunk.data,
            metadata: chunk.metadata
          });*/
          
          const serializedChunk = serializeEventChunk(chunk);
          //console.log('Successfully serialized chunk:', serializedChunk);
          
          stream.update(serializedChunk);
        } catch (serializationError) {
          console.error('Error during chunk serialization:', serializationError);
          throw serializationError;
        }
      }
    } catch (error: any) {
      console.error('Stream processing error:', {
        error,
        stack: error?.stack,
        fullError: JSON.stringify(error, null, 2)
      });
      
      stream.update({ 
        event: "error", 
        error: typeof error === 'object' ? 
          { 
            message: error?.message, 
            name: error?.name,
            stack: error?.stack // Include stack trace in development
          } : 
          { message: String(error) }
      });
    } finally {
      //console.log('Stream processing completed');
      stream.done();
    }
  })();

  return { output: stream.value };
}

export const CanvasAI = createAI({
  actions: {
    sendMessage
  },
  initialUIState: [],
  initialAIState: { messages: [] },
});

// Helper to serialize chunks to plain objects
function serializeEventChunk(chunk: any) {
  const { event, tags, data, run_id, metadata, name } = chunk;
  //console.log("================================================================= event: =================================================================", event)
  //console.log("================================================================= data: =================================================================", data)
  // Serialize message content based on event type
  if (event === "on_chat_model_stream") {
    const message = data?.chunk;
    if (!message) return { event, run_id, metadata };
    
    // Convert AIMessageChunk to plain object
    return {
      event,
      run_id,
      metadata: {
        langgraph_node: metadata?.langgraph_node || ""
      },
      name,
      data: {
        chunk: {
          id: message.id,
          content: typeof message.content === "string" ? message.content.trim() : "" !== "",
          type: message.getType ? message.getType() : "ai",
          tool_call_chunks: serializeToolCalls(message.tool_call_chunks)            
        }
      }
    };
  }
  
  if (event === "on_chat_model_end") {
    return {
      event,
      run_id,
      metadata: {
        langgraph_node: metadata?.langgraph_node || ""
      },
      name,
      data: {
        output: data?.output ? {
          id: data.output.id || crypto.randomUUID(),
          type: data.output.getType ? data.output.getType() : "ai",
          content: data.output.content || "",
          tool_call_chunks: serializeToolCalls(data.output.tool_call_chunks)
        } : null
      }
    };
  }
  
  if (event === "on_tool_end") {
    return {
      event,
      run_id,
      metadata: {
        langgraph_node: metadata?.langgraph_node || ""
      },
      name,
      data: {
        tool: data?.output ? {
          id: data.output.id || crypto.randomUUID(),
          type: data.output.getType ? data.output.getType() : "tool",
          toolCallId: data.output.tool_call_id,
          name: data.output.name,
          content: data.output.content || ""
        } : null
      }
    };
  }
  
  // Default serialization for other events
  return {
    event,
    run_id,
    metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : {},
    name,
    data: data ? JSON.parse(JSON.stringify(data)) : null
  };
}

// Helper to serialize tool calls
function serializeToolCalls(toolCalls: any[] = []) {
  if (!toolCalls || !Array.isArray(toolCalls)) return [];
  
  return toolCalls.map(call => ({
    id: call.id || crypto.randomUUID(),
    name: call.name,
    args: call.args,
  }));
}
'use client'

import Link from 'next/link'
import { api } from '@/convex/_generated/api'
import { Id } from '@/convex/_generated/dataModel'
import { useSchematicFlag } from '@schematichq/schematic-react'
import { useQuery } from 'convex/react'
import { ChevronLeft, FileText, Sparkles, Lightbulb, Lock } from 'lucide-react'

import { useParams, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { getFileDownloadUrl } from '@/actions/receipts/getFileDownloadUrl'
import { deleteReceipt } from '@/actions/receipts/deleteReceipt'

function Receipt() {
  const params = useParams<{ id: string }>()
  const router = useRouter()
  const [receiptId, setReceiptId] = useState<Id<"receipts"> | null>(null)
  const isSummaryEnabled = useSchematicFlag("summary")
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoadingDownload, setIsLoadingDownload] = useState(false)
  console.log("isSummaryEnabled", isSummaryEnabled)

  // Fetch receipt details
  const receipt = useQuery(api.receipts.getReceiptById, 
    receiptId ? { id: receiptId } : "skip",
  )

  // Fetch file download URL
  const fileId = receipt?.fileId
  const downloadUrl = useQuery(
    api.receipts.getReceiptDownloadUrl, 
    fileId ? { fileId } : "skip",
  )

  const handleDownload = async () => {
    if (!receipt || !receipt.fileId) return
    try {
      setIsLoadingDownload(true)
      const result = await getFileDownloadUrl(receipt.fileId);
      console.log("result", result)
      if (!result?.success) {
        throw new Error("Failed to download file")
      }

      // Create a temporary link and trigger download
      const link = document.createElement('a')
      if (result.downloadUrl) {
        link.href = result.downloadUrl
        link.download = `${receipt.fileDisplayName || receipt.fileName}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        throw new Error("Failed to download file")
      }      
    } catch (error) {
      console.error(error)
      alert("Failed to download file, Please try again.")
    } finally {
      setIsLoadingDownload(false)
    }
  }

  const handleDeleteReceipt = async () => {
    if (!receiptId) return
    if (window.confirm("Are you sure you want to delete this receipt? This action cannot be undone.")) {
      try {
        setIsDeleting(true)
        const result = await deleteReceipt(receiptId)
        if (result.success) {
          router.push("/receipts")
        } else {
          throw new Error("Failed to delete receipt")
        }
      } catch (error) {
        console.error(error)
        alert("Failed to delete receipt, Please try again.")
      } finally {
        setIsDeleting(false)
      }
    }
  }
  
  useEffect(() => {
    try {
      const id = params.id as Id<"receipts">
      setReceiptId(id)
    } catch (error) {
      console.error(error)
      router.push(`/`)
    }
  }, [params.id, router])

  if (receipt === undefined) {
    return (
      <div className="container py-10 mx-auto">
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 dark:border-white"></div>
          <h1 className="text-2xl font-bold mt-4">Loading...</h1>
        </div>
      </div>
    )
  }

  if (receipt === null) {
    return (
      <div className="container px-4 py-10 mx-auto">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">Receipt not found</h1>
          <p className="mb-6">Please check the receipt ID and try again.</p>
          <Link href="/" className="px-6 py-2 text-white bg-blue-500 hover:bg-blue-600 rounded-lg">Return Home</Link>
        </div>
      </div>
    )
  }

  // Format upload date
  const uploadDate = new Date(receipt.uploadedAt).toLocaleDateString('zh-TW')

  // Check if receipt has extracted data
  const hasExtractedData = !!(
    receipt.merchantName ||
    receipt.merchantAddress ||
    receipt.transactionDate ||
    receipt.transactionAmount
  )

  

  return (
    <div className="container px-4 py-10 mx-auto">
      <div className="max-w-4xl mx-auto">
        <nav className="mb-6">
          <Link href="/receipts" className="text-blue-500 hover:unerline flex items-center">
            <ChevronLeft className="w-6 h-6 mr-1" />
            Back to Receipts
          </Link>
        </nav>
  
        <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold text-gray-900 truncate">
                {receipt.fileDisplayName || receipt.fileName}
              </h1>
              <div className="flex items-center">
                {receipt.status === "pending" ? (
                  <div className="mr-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-800"></div>
                  </div>
                ) : null}
                <span 
                  className={`px-3 py-1 rounded-full text-sm ${
                    receipt.status === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : receipt.status === "processed"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {receipt.status.charAt(0).toUpperCase() + 
                  receipt.status.slice(1)}
                </span>
              </div>              
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Information */}
              <div className="space-y-4">
                <div className="">
                  <h3 className="text-sm font-medium text-gray-500">
                    File Information
                  </h3>
                  <div className="mt-2 bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Uploaded</p>
                        <p className="font-medium">{uploadDate}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Size</p>
                        <p className="font-medium">
                          {formatFileSize(receipt.size)}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-500">Type</p>
                        <p className="font-medium">{receipt.mimeType}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">ID</p>
                        <p className="font-medium truncate" title={receipt._id}>
                          {receipt._id.slice(0, 10)}...
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Download */}
              <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <FileText className="w-16 h-16 text-blue-500 mx-auto mb-4" />
                  <p className="text-sm text-gray-500">
                    PDF preview
                  </p>
                  {downloadUrl && (
                    <a 
                      href={downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-4 px-4 py-2 text-sm text-white bg-blue-500 hover:bg-blue-600 inline-block rounded"
                    >
                      View PDF
                    </a>
                  )}
                </div>
              </div>
            </div>

            {/* Extracted Data */}
            {hasExtractedData && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Receipt Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Merchant Details */}
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-700 mb-3">
                      Merchant Information
                    </h4>
                    <div className="space-y-2">
                      {receipt.merchantName && (
                        <div>
                          <p className="text-sm text-gray-500">Name</p>
                          <p className="font-medium">{receipt.merchantName}</p>
                        </div>
                      )}
                      {receipt.merchantAddress && (
                        <div>
                          <p className="text-sm text-gray-500">Address</p>
                          <p className="font-medium">{receipt.merchantAddress}</p>
                        </div>
                      )}
                      {receipt.merchantContact && (
                        <div>
                          <p className="text-sm text-gray-500">Contact</p>
                          <p className="font-medium">{receipt.merchantContact}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Transaction Details */ }
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-700 mb-3">
                      Transaction Details
                    </h4>
                    <div className="space-y-2">
                      {receipt.transactionDate && (
                        <div>
                          <p className="text-sm text-gray-500">Date</p>
                          <p className="font-medium">{receipt.transactionDate}</p>
                        </div>
                      )}
                      {receipt.transactionAmount && (
                        <div>
                          <p className="text-sm text-gray-500">Amount</p>
                          <p className="font-medium">{receipt.transactionAmount} {receipt.currency || ""}</p>
                        </div>
                      )}                        
                    </div>
                  </div>
                </div>
                {/* Receipt Summary */}

                <div className="space-y-2">
                  {receipt.receiptSummary && (
                    <>
                      {isSummaryEnabled ? (
                        <div className="mt-6 bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-100 shadow-sm">
                          <div className="flex items-center mb-4">
                            <h4 className="font-semibold text-blue-700">AI Summary</h4>
                            <div className="flex ml-2">
                              <Sparkles className="w-3.5 h-3.5 text-yellow-500" />
                              <Sparkles className="w-3 h-3 text-yellow-r00 -ml-1" />
                            </div>
                          </div>
                          <div className="bg-white bg-opacity-60 p-4 rounded-lg border border-blue-100">
                            <p className="text-sm whitespace-pre-line leading-relaxed text-gray-700">
                              {receipt.receiptSummary}
                            </p>                                
                          </div>
                          <div className="mt-3 text-xs text-blue-600 italic flex items-center">
                            <Lightbulb className="w-3.5 h-3.5 mr-1" />
                            <span>
                              AI-generated summary based on receipt data
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div className="mt-6 bg-gray-100 p-6 rounded-lg border border-gray-200 shadow-sm">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center">
                              <h4 className="font-semibold text-gray-400">
                                AI Summary
                              </h4>
                              <div className="flex ml-2">
                                <Sparkles className="w-3.5 h-3.5 text-yellow-500" />
                                <Sparkles className="w-3 h-3 text-yellow-r00 -ml-1" />
                              </div>
                            </div>
                            <Lock className="w-4 h-4 text-gray-500" />
                          </div>
                          <div className="bg-white bg-opacity-50 p-4 rounded-lg border border-gray-200 flex items-center justify-center">
                            <Link
                              href="/manage-plan"
                              className="text-center py-4"
                            >
                              <Lock className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                              <p className="text-sm text-gray-500">
                                AI summary is a PRO level feature
                              </p>
                              <button className="mt-2 px-4 py-1.5 bg-blue-500 text-white rounded hover:bg-blue-600 hover:cursor-pointer inline-block">
                                Upgrade to Unlock
                              </button>
                            </Link>
                          </div>
                          <div className="mt-3 text-xs text-gray-400 italic flex items-center">
                            <Lightbulb className="w-3 h-3 mr-1" />
                            <span>
                              Get AI-powered insights from your receipts
                            </span>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>

              </div>
            )}


            {/* Items Table */}
            {receipt.items && receipt.items.length > 0 && (
              <div className="mt-6">
                <h4 className="font-medium text-gray-700 mb-3">
                  Items ({receipt.items.length})
                </h4>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="font-medium">Item</TableHead>
                        <TableHead className="text-right font-medium">Quantity</TableHead>
                        <TableHead className="text-right font-medium">Unit Price</TableHead>
                        <TableHead className="text-right font-medium">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {receipt.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{item.name}</TableCell>
                          <TableCell className="text-right">{item.quantity}</TableCell>
                          <TableCell className="text-right">{formatCurrency(item.unitPrice, receipt.currency)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(item.totalPrice, receipt.currency)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                    <TableFooter>
                      <TableRow>
                        <TableCell colSpan={3} className="text-right">
                          Total
                        </TableCell>
                        <TableCell className="font-medium text-right">
                          {formatCurrency(receipt.items.reduce((acc, item) => acc + item.totalPrice, 0), receipt.currency)}
                        </TableCell>
                      </TableRow>
                    </TableFooter>
                  </Table>
                </div>
              </div>
            )}

            {/* Action Section */}
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-500 mb-4">
                Actions
              </h3>
              <div className="flex flex-wrap gap-3">
                <button
                  className={`px-4 py-2 bg-white border border-gray-300 rounded text-sm  hover:cursor-pointer
                    text-gray-700 ${
                    isLoadingDownload
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={handleDownload}
                  disabled={isLoadingDownload || !fileId}
                >
                  {isLoadingDownload ? "Downloading..." : "Download PDF"}
                </button>
                <button
                  className={`px-4 py-2 rounded text-sm hover:cursor-pointer ${
                    isDeleting
                      ? "bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed"
                      : "bg-red-50 border border-red-200 text-red-600 hover:bg-red-100"
                  }`}
                  onClick={handleDeleteReceipt}
                  disabled={isDeleting}
                >
                  {isDeleting ? "Deleting..." : "Delete Receipt"}
                </button>
              </div>
            </div>
      
          </div>
        </div>
      </div>
    </div>
  );
}

export default Receipt

function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

function formatCurrency(amount: number, currency: string = ""): string {
  return `${amount.toFixed(2)} ${currency ? `${currency} ` : ""}`
}


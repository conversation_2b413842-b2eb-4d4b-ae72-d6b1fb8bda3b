import { UpdateWorkspaceModal } from "@/components/workspaces/update-workspace-modal";
import { UpdateProjectModal } from "@/components/projects/update-project-modal";
import { UpdateTaskModal } from "@/components/tasks/update-task-modal";
import { UpdateSpeechModal } from "@/components/speeches/update-speech-modal";

interface WorkspaceLayoutProps {
  children?: React.ReactNode;
  params: Promise<{
    workspaceId: string;
  }>;
}

export default async function WorkspaceLayout(props: WorkspaceLayoutProps) {
  const params = await props.params;

  return (
    <div>
      {/* Render the modal */}
      <UpdateWorkspaceModal />
      <UpdateProjectModal />
      <UpdateTaskModal />
      <UpdateSpeechModal />
      {props.children}
    </div>
  );
}

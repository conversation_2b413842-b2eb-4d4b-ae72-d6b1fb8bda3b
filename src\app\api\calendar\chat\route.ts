import { auth } from '@clerk/nextjs/server';
import { groq } from '@ai-sdk/groq';
import { openai } from '@ai-sdk/openai';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { experimental_createMCPClient as createMCPClient, smoothStream, streamText, createDataStreamResponse } from 'ai';
import {
  Experimental_StdioMCPTransport as StdioClientTransport
} from 'ai/mcp-stdio';
import { generateText } from "ai";

export const maxDuration = 120;

const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
});

export async function POST(req: Request) {
  const { messages, contractId } = await req.json();
  const { userId } = await auth();
  if (!userId) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const prompt = messages
  .filter((message: { role: string }) => message.role === 'user')
  .pop()
  const userPrompt: string | undefined = prompt?.content

  console.log("chat messages", JSON.stringify(messages.slice(-4), null, 2))
  
  let mcpClient;
  try {
    mcpClient = await createMCPClient({
      name: 'zapier',
      transport: {
        type: 'sse',
        url: 'https://mcp.zapier.com/api/mcp/s/NDAyODViYjItZmY1ZC00NDk3LWJkMjgtZGJmZDYwNTU3YjgyOjA0OTVjZTU4LTRiNTEtNDNjYy1iYjgzLTI2M2RkYjhiOWNmMQ==/sse',
      },
    });

    const tools = await mcpClient.tools();
    const { text } = await generateText({
      model: google('gemini-2.5-flash-preview-04-17'), //openai('gpt-4o-mini'),
      tools,
      maxSteps: 10,
      messages: messages.slice(-1)
    });

    return createDataStreamResponse({
      execute: async dataStream => {
        const result = streamText({
          model: groq('llama-3.3-70b-versatile'), //google("gemini-2.5-pro-exp-03-25"), //
          experimental_transform: smoothStream(),
          messages: [
            {
              role: 'user', 
              content: `Make a list of followings: ` + text,
            }
          ],
  
        })

        result.mergeIntoDataStream(dataStream, {
          experimental_sendFinish: false, // omit the finish event
        });
      },
      onError: error => {
        // Error messages are masked by default for security reasons.
        // If you want to expose the error message to the client, you can do so here:
        return error instanceof Error ? error.message : String(error);
      },
    });

  } catch (error) {
    console.error(error);
  } finally {
    await mcpClient?.close();
  }
}
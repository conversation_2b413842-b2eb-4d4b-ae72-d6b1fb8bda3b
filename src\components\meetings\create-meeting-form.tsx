"use client"

import { useState, useEffect } from "react"
import { getAvailableSlots } from "@/actions/tasks/schedule-meeting"
import { useGetAvailableSlots } from "./use-get-available-slots"
import { useCreateMeeting } from "./use-create-meeting"
import { format } from "date-fns"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, X, Clock, Users, Calendar as CalendarIcon } from 'lucide-react'
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface CreateMeetingFormProps {
  onCancel?: () => void;
}

export const CreateMeetingForm = ({ onCancel }: CreateMeetingFormProps) => {
  const [formState, setFormState] = useState({ message: "" })
  const [selected, setSelectedDate] = useState<Date>()
  const [slots, setAvailableSlots] = useState<string[]>()
  const [timetableError, setTimetableError] = useState<string>("")
  const [isTimeTableLoading, setIsTimeTableLoading] = useState(false)
  const [showMessage, setShowMessage] = useState(false)
  const [attendees, setAttendees] = useState<string[]>([])
  const [newAttendee, setNewAttendee] = useState("")
  const [duration, setDuration] = useState(20) // Duration in minutes
  const [minGapBetweenMeetings, setMinGapBetweenMeetings] = useState(5) // Default 5 minutes gap
  const [workingHours, setWorkingHours] = useState({
    start: 9,
    end: 17,
    slotDuration: 20,
    minGapBetweenMeetings: 5,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  })

  useEffect(() => {
    setWorkingHours(prevState => ({
      ...prevState,
      slotDuration: duration,
      minGapBetweenMeetings: minGapBetweenMeetings,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }))
  }, [duration, minGapBetweenMeetings])
 
  const { availableSlots, isLoading: isGetAvailableSlotsLoading } = useGetAvailableSlots(workingHours);

  useEffect(() => {
    if (availableSlots) {
      setAvailableSlots(availableSlots);
    }
  }, [availableSlots]);

  const { mutate, isPending: isCreateMeetingPending } = useCreateMeeting();

  const handleAddAttendee = () => {
    if (newAttendee && /\S+@\S+\.\S+/.test(newAttendee)) {
      setAttendees([...attendees, newAttendee])
      setNewAttendee("")
    }
  }

  const handleRemoveAttendee = (email: string) => {
    setAttendees(attendees.filter(a => a !== email))
  }

  const handleDayPickerSelect = async (date: Date | undefined) => {
    setTimetableError("")
    setShowMessage(false)
    if (!date) {
      setSelectedDate(undefined)
      setAvailableSlots([])
      return
    }
    
    // Create a new date object for today, with time set to 00:00:00
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // weekends (Sunday and Saturday, Past date)
    if (date.getDay() === 0 || date.getDay() === 6 || date < today) {
      setSelectedDate(undefined)
      setAvailableSlots([])
      return
    }

    setSelectedDate(date)
    setIsTimeTableLoading(true)
    
    try {
      const availableSlots = await getAvailableSlots(date, workingHours)
      
      setAvailableSlots(availableSlots)
    } catch (error) {
      console.error('Error fetching slots:', error)
      setTimetableError("Failed to fetch available slots. Please try again.")
    } finally {
      setIsTimeTableLoading(false)
    }
  }

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const form = event.currentTarget;
    const formData = new FormData(form);
    
    if (!formData.get("timetable") || !selected) {
      setTimetableError("Please select a date and time slot");
      return;
    }

    // Add attendees to formData
    attendees.forEach((attendee, index) => {
      formData.append(`attendees[${index}]`, attendee);
    });
    formData.append('duration', duration.toString());

    // Add workingHours to formData
    Object.entries(workingHours).forEach(([key, value]) => {
      formData.append(`workingHours[${key}]`, value.toString());
    });

    try {
      formData.set('selectedCalendarDate', selected.toLocaleDateString());
      mutate(formData, {
        onSuccess: ({ data: meeting }) => {
          setFormState({ message: meeting?.message ??  "Meeting scheduled!"});
          setShowMessage(true);
          setTimetableError("");
          setSelectedDate(undefined);
          setAvailableSlots([]);
          setAttendees([]);
          form.reset();
          if (meeting.data) {
            onCancel?.()            
          }
        }
      });

    } catch (error) {
      console.error('Form submission error:', error);
      setTimetableError("Failed to create meeting. Please try again.");
    }
  };

  const resetForm = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault()
    const form = event.currentTarget.form
    if (form) {
      form.reset()
      setSelectedDate(undefined)
    }
  }

  const isLoading = isCreateMeetingPending || isGetAvailableSlotsLoading

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl flex items-center gap-2">
          <CalendarIcon className="h-6 w-6" />
          Schedule a Meeting
        </CardTitle>
        <CardDescription>
          Select a date, time, and add attendees to schedule your meeting
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-8">
          {showMessage && formState.message && (
            <Alert variant="default">
              <AlertDescription>{formState.message}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left Column - Calendar and Time Selection */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                {selected ? format(selected, 'MMMM d, yyyy') : "Select a Date"}
              </h3>
              {/* Calendar */}
              <div className="w-fit bg-muted rounded-lg">
                <Calendar
                  mode="single"
                  selected={selected}
                  onSelect={handleDayPickerSelect}
                  className="rounded-md border"
                />
              </div>

              <div className="space-y-4">   
                {/* Duration selector */}
                <div className="space-y-2">
                  <Label>Duration</Label>
                  <Select
                    value={duration.toString()}
                    onValueChange={(value) => setDuration(parseInt(value))}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Select project" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={'15'}>
                        15 minutes
                      </SelectItem>
                      <SelectItem value={'20'}>
                        20 minutes
                      </SelectItem>
                      <SelectItem value={'30'}>
                        30 minutes
                      </SelectItem>
                      <SelectItem value={'45'}>
                        45 minutes
                      </SelectItem>
                      <SelectItem value={'60'}>
                        1 hour
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Minimum gap between meetings */}
                <div className="space-y-2">
                  <Label>Mini Gap Between Meetings</Label>
                  <Select
                    value={minGapBetweenMeetings.toString()}
                    onValueChange={(value) => setMinGapBetweenMeetings(parseInt(value, 10))}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue placeholder="Select minimum gap" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">No gap</SelectItem>
                      <SelectItem value="5">5 minutes</SelectItem>
                      <SelectItem value="10">10 minutes</SelectItem>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Right Column - Meeting Details */}
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Attendees
                </h3>
                
                <div className="flex gap-2">
                  <Input
                    type="email"
                    value={newAttendee}
                    onChange={(e) => setNewAttendee(e.target.value)}
                    placeholder="Add attendee email"
                    className="h-8 flex-1"
                  />
                  <Button 
                    type="button"
                    size={newAttendee ? "sm" : "xs"}
                    onClick={handleAddAttendee}
                    variant="secondary"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {attendees.map((email) => (
                    <Badge 
                      key={email} 
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {email}
                      <button
                        onClick={() => handleRemoveAttendee(email)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="space-y-1">
                  <Label htmlFor="title">Meeting Title</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="Enter meeting title"
                    required
                  />
                </div>

                <div className="space-y-1">
                  <Label htmlFor="message">Agenda</Label>
                  <Textarea
                    id="message"
                    name="message"
                    placeholder="Describe the meeting agenda..."
                    className="h-32"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {isTimeTableLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="time-slots-grid">
              {slots && slots.length > 0 ? (
                <RadioGroup name="timetable" className="grid grid-cols-5 gap-1">
                  {slots.map((slot) => (
                    <div key={slot}>
                      <RadioGroupItem
                        value={slot}
                        id={slot}
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor={slot}
                        className="h-8 flex items-center justify-center rounded-md border-2 border-muted text-sm bg-popover p-2 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                      >
                        {slot}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <p className="text-muted-foreground">No Available Slots</p>
                </div>
              )}
            </div>
          )}
          {timetableError && (
            <Alert variant="destructive">
              <AlertDescription>{timetableError}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={resetForm}>
              Reset
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Scheduling...
                </>
              ) : (
                'Schedule Meeting'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}


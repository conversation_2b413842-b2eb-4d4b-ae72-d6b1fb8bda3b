'use server'

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { TaskStatus } from "@prisma/client";
import { endOfMonth, startOfMonth, subMonths } from 'date-fns';

export async function getProjectAnalytics(projectId: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }
    
    // Get project from a projectId
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    })

    if (!project) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }
    
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId: project?.workspaceId },
    });
  
    if (!member) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    const now = new Date();
    const thisMonthStart = startOfMonth(now);
    const thisMonthEnd = endOfMonth(now);
    const lastMonthStart = startOfMonth(subMonths(now, 1));
    const lastMonthEnd = endOfMonth(subMonths(now, 1));

    const thisMonthTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        createdAt: {
          gte: thisMonthStart.toISOString(),
          lte: thisMonthEnd.toISOString(),
        },
      },
    });

    const lastMonthTasks = await prisma.task.findMany({
      where: {
       projectId: project.id,
         createdAt: {
          gte: lastMonthStart.toISOString(),
          lte: lastMonthEnd.toISOString(),
        },
      },
    });
    console.log("thisMonthTasks", thisMonthTasks, "lastMonthTasks", lastMonthTasks)

    const taskCount = thisMonthTasks.length;
    const taskDifference = taskCount - lastMonthTasks.length;

    const thisMonthAssignedTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        assigneeId: member.id,
        createdAt: {
          gte: thisMonthStart.toISOString(),
          lte: thisMonthEnd.toISOString(),
        },
      },
    }); 

    const lastMonthAssignedTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        assigneeId: member.id,
        createdAt: {
          gte: lastMonthStart.toISOString(),
          lte: lastMonthEnd.toISOString(),
        },
      },
    });

    const assignedTaskCount = thisMonthAssignedTasks.length;
    const assignedTaskDifference = assignedTaskCount - lastMonthAssignedTasks.length;
    

    const thisMonthIncompleteTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        status: {
          not: TaskStatus.DONE
        },
        createdAt: {
          gte: thisMonthStart.toISOString(),
          lte: thisMonthEnd.toISOString(),
        },
      },
    }); 

    const lastMonthIncompleteTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        status: {
          not: TaskStatus.DONE
        },
        createdAt: {
          gte: lastMonthStart.toISOString(),
          lte: lastMonthEnd.toISOString(),
        },
      },
    });

    const incompleteTaskCount = thisMonthIncompleteTasks.length;
    const incompleteTaskDifference = incompleteTaskCount - lastMonthIncompleteTasks.length;
    
    const thisMonthCompletedTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        status: {
          equals: TaskStatus.DONE
        },
        createdAt: {
          gte: thisMonthStart.toISOString(),
          lte: thisMonthEnd.toISOString(),
        },
      },
    }); 

    const lastMonthCompletedTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        status: {
            equals: TaskStatus.DONE
        },
        createdAt: {
          gte: lastMonthStart.toISOString(),
          lte: lastMonthEnd.toISOString(),
        },
      },
    });

    const completedTaskCount = thisMonthCompletedTasks.length;
    const completedTaskDifference = completedTaskCount - lastMonthCompletedTasks.length;
    
    
    const thisMonthOverdueTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        status: {
          not: TaskStatus.DONE
        },
        dueDate: {
          lt: now.toISOString(),
        },
        createdAt: {
          gte: thisMonthStart.toISOString(),
          lte: thisMonthEnd.toISOString(),
        },
      },
    }); 

    const lastMonthOverdueTasks = await prisma.task.findMany({
      where: {
        projectId: project.id,
        status: {
          not: TaskStatus.DONE
        },
        dueDate: {
          lt: now.toISOString(),
        },
        createdAt: {
          gte: thisMonthStart.toISOString(),
          lte: thisMonthEnd.toISOString(),
        },
      },
    });

    const overdueTaskCount = thisMonthOverdueTasks.length;
    const overdueTaskDifference = overdueTaskCount - lastMonthOverdueTasks.length;
        

    return {
      data: { 
        taskCount,
        taskDifference,
        assignedTaskCount,
        assignedTaskDifference,
        incompleteTaskCount,
        incompleteTaskDifference,
        completedTaskCount,
        completedTaskDifference,
        overdueTaskCount,
        overdueTaskDifference,
      },
      success: true
    }
    
  } catch (error) {
    console.error('Error getting project analytics', error)
    return {
      error: "Error getting project analytics.",
      success: false
    }
  }
}
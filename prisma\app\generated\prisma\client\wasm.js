
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/wasm-engine-edge.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  plan: 'plan',
  credits: 'credits',
  image: 'image',
  language: 'language',
  timezone: 'timezone',
  onboardingEmailSent: 'onboardingEmailSent',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  stripeCustomerId: 'stripeCustomerId',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripePriceId: 'stripePriceId',
  stripeCurrentPeriodEnd: 'stripeCurrentPeriodEnd'
};

exports.Prisma.WorkspaceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  orgnr: 'orgnr',
  address: 'address',
  postalCode: 'postalCode',
  city: 'city',
  inviteCode: 'inviteCode',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MemberScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  bankAccountId: 'bankAccountId',
  workspaceId: 'workspaceId',
  role: 'role'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  workspaceId: 'workspaceId',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  workspaceId: 'workspaceId',
  projectId: 'projectId',
  assigneeId: 'assigneeId',
  description: 'description',
  startDate: 'startDate',
  dueDate: 'dueDate',
  status: 'status',
  position: 'position',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReminderScalarFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  enabled: 'enabled',
  basis: 'basis',
  daysBefore: 'daysBefore',
  customDate: 'customDate',
  workflowRunId: 'workflowRunId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkflowRunScalarFieldEnum = {
  id: 'id',
  reminderId: 'reminderId',
  workflowRunId: 'workflowRunId',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BankAccountScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  resourceId: 'resourceId',
  originalId: 'originalId',
  orgId: 'orgId',
  userId: 'userId',
  name: 'name',
  originalPayload: 'originalPayload',
  initialAmount: 'initialAmount',
  accountType: 'accountType'
};

exports.Prisma.BalanceScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accountId: 'accountId',
  assetId: 'assetId',
  currencyIso: 'currencyIso',
  amount: 'amount',
  date: 'date',
  description: 'description',
  type: 'type',
  originalPayload: 'originalPayload'
};

exports.Prisma.TimeDepositScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accountId: 'accountId',
  assetId: 'assetId',
  categoryId: 'categoryId',
  currencyIso: 'currencyIso',
  amount: 'amount',
  date: 'date',
  certificateNo: 'certificateNo',
  period: 'period',
  interestRate: 'interestRate',
  description: 'description',
  type: 'type',
  originalPayload: 'originalPayload',
  categoryValidated: 'categoryValidated',
  suggestedCategoryId: 'suggestedCategoryId'
};

exports.Prisma.CurrencyScalarFieldEnum = {
  iso: 'iso',
  symbol: 'symbol',
  name: 'name',
  numericCode: 'numericCode'
};

exports.Prisma.AssetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  originalPayload: 'originalPayload',
  type: 'type',
  value: 'value',
  purchaseDate: 'purchaseDate',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.AssetValuationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  value: 'value',
  date: 'date',
  assetId: 'assetId'
};

exports.Prisma.AssetTransactionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  type: 'type',
  amount: 'amount',
  date: 'date',
  description: 'description',
  assetId: 'assetId'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  accountId: 'accountId',
  assetId: 'assetId',
  userId: 'userId',
  currencyIso: 'currencyIso',
  categoryId: 'categoryId',
  amount: 'amount',
  date: 'date',
  description: 'description',
  originalPayload: 'originalPayload',
  review: 'review',
  type: 'type',
  categoryValidated: 'categoryValidated',
  suggestedCategoryId: 'suggestedCategoryId'
};

exports.Prisma.IncomeScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  description: 'description',
  date: 'date',
  categoryId: 'categoryId',
  userId: 'userId',
  accountId: 'accountId',
  type: 'type',
  categoryValidated: 'categoryValidated',
  suggestedCategoryId: 'suggestedCategoryId',
  importHash: 'importHash'
};

exports.Prisma.ExpenseScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  description: 'description',
  date: 'date',
  categoryId: 'categoryId',
  userId: 'userId',
  accountId: 'accountId',
  type: 'type',
  categoryValidated: 'categoryValidated',
  suggestedCategoryId: 'suggestedCategoryId',
  importHash: 'importHash'
};

exports.Prisma.SpreadsheetImportScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  importedAt: 'importedAt',
  fileName: 'fileName',
  sheetName: 'sheetName',
  rowCount: 'rowCount',
  type: 'type',
  importHash: 'importHash'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  icon: 'icon',
  color: 'color',
  keywords: 'keywords',
  type: 'type',
  userId: 'userId',
  description: 'description'
};

exports.Prisma.BudgetPreferencesScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  autoCreateNextBudget: 'autoCreateNextBudget',
  autoApplyRollovers: 'autoApplyRollovers',
  rolloverThreshold: 'rolloverThreshold'
};

exports.Prisma.BudgetScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  startDate: 'startDate',
  endDate: 'endDate',
  amount: 'amount',
  userId: 'userId',
  accountId: 'accountId'
};

exports.Prisma.CategoryBudgetScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  budgetId: 'budgetId',
  categoryId: 'categoryId'
};

exports.Prisma.BudgetRolloverScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  categoryId: 'categoryId',
  previousBudgetId: 'previousBudgetId',
  nextBudgetId: 'nextBudgetId',
  amount: 'amount',
  createdAt: 'createdAt',
  periodStart: 'periodStart',
  periodEnd: 'periodEnd',
  rolloverPercentage: 'rolloverPercentage',
  accountId: 'accountId'
};

exports.Prisma.ConnectorConfigScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  orgId: 'orgId',
  secret: 'secret',
  env: 'env',
  connectorId: 'connectorId'
};

exports.Prisma.ConnectorScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  logoUrl: 'logoUrl',
  status: 'status',
  type: 'type'
};

exports.Prisma.IntegrationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  logoUrl: 'logoUrl',
  connectorProviderId: 'connectorProviderId',
  connectorId: 'connectorId'
};

exports.Prisma.ResourceScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  integrationId: 'integrationId',
  originalId: 'originalId',
  userId: 'userId'
};

exports.Prisma.StatsScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  totalUsers: 'totalUsers',
  activeUsers: 'activeUsers',
  totalAccounts: 'totalAccounts',
  totalTransactions: 'totalTransactions',
  totalAssets: 'totalAssets',
  totalLiabilities: 'totalLiabilities',
  totalInvestments: 'totalInvestments',
  avgAccountsPerUser: 'avgAccountsPerUser',
  avgTransactionsPerUser: 'avgTransactionsPerUser',
  dailyActiveUsers: 'dailyActiveUsers',
  weeklyActiveUsers: 'weeklyActiveUsers',
  monthlyActiveUsers: 'monthlyActiveUsers',
  operatingSystem: 'operatingSystem',
  browser: 'browser',
  country: 'country',
  lastUpdated: 'lastUpdated'
};

exports.Prisma.StatsHistoryScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  totalUsers: 'totalUsers',
  activeUsers: 'activeUsers',
  totalAccounts: 'totalAccounts',
  totalTransactions: 'totalTransactions',
  totalAssets: 'totalAssets',
  totalLiabilities: 'totalLiabilities',
  totalInvestments: 'totalInvestments',
  dailyActiveUsers: 'dailyActiveUsers',
  weeklyActiveUsers: 'weeklyActiveUsers',
  monthlyActiveUsers: 'monthlyActiveUsers',
  snapshot: 'snapshot'
};

exports.Prisma.InvestmentScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  type: 'type',
  amount: 'amount',
  shares: 'shares',
  purchasePrice: 'purchasePrice',
  currentPrice: 'currentPrice',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.InvestmentValuationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  value: 'value',
  date: 'date',
  investmentId: 'investmentId'
};

exports.Prisma.InvestmentTransactionScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  type: 'type',
  amount: 'amount',
  shares: 'shares',
  price: 'price',
  date: 'date',
  description: 'description',
  investmentId: 'investmentId'
};

exports.Prisma.LiabilityScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  type: 'type',
  amount: 'amount',
  interestRate: 'interestRate',
  monthlyPayment: 'monthlyPayment',
  startDate: 'startDate',
  endDate: 'endDate',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.LiabilityPaymentScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  amount: 'amount',
  date: 'date',
  type: 'type',
  description: 'description',
  liabilityId: 'liabilityId'
};

exports.Prisma.SavingsGoalScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  target: 'target',
  current: 'current',
  deadline: 'deadline',
  description: 'description',
  isDefault: 'isDefault',
  type: 'type',
  priority: 'priority',
  userId: 'userId'
};

exports.Prisma.SavingsProgressScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  amount: 'amount',
  date: 'date',
  goalId: 'goalId'
};

exports.Prisma.ContractAnalysisScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  contractText: 'contractText',
  summary: 'summary',
  recommendations: 'recommendations',
  keyClauses: 'keyClauses',
  legalCompliance: 'legalCompliance',
  negotiationPoints: 'negotiationPoints',
  contractDuration: 'contractDuration',
  terminationConditions: 'terminationConditions',
  overallScore: 'overallScore',
  performanceMetrics: 'performanceMetrics',
  intellectualPropertyClauses: 'intellectualPropertyClauses',
  createdAt: 'createdAt',
  version: 'version',
  userFeedback: 'userFeedback',
  customFields: 'customFields',
  expirationDate: 'expirationDate',
  language: 'language',
  filePath: 'filePath',
  contractType: 'contractType',
  financialTerms: 'financialTerms',
  specificClauses: 'specificClauses'
};

exports.Prisma.RiskScalarFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  risk: 'risk',
  explanation: 'explanation',
  severity: 'severity'
};

exports.Prisma.OpportunityScalarFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  opportunity: 'opportunity',
  explanation: 'explanation',
  impact: 'impact'
};

exports.Prisma.CompensationStructureScalarFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  baseSalary: 'baseSalary',
  bonuses: 'bonuses',
  equity: 'equity',
  otherBenefits: 'otherBenefits'
};

exports.Prisma.SpeechScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  workspaceId: 'workspaceId',
  projectId: 'projectId',
  title: 'title',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RecordingScalarFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  url: 'url',
  duration: 'duration',
  createdAt: 'createdAt'
};

exports.Prisma.TranscriptionScalarFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  text: 'text',
  language: 'language',
  createdAt: 'createdAt'
};

exports.Prisma.AnalysisScalarFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  summary: 'summary',
  keyPoints: 'keyPoints',
  sentiment: 'sentiment',
  createdAt: 'createdAt'
};

exports.Prisma.VectorStoreScalarFieldEnum = {
  id: 'id',
  documentId: 'documentId',
  documentType: 'documentType',
  content: 'content',
  metadata: 'metadata',
  filePath: 'filePath',
  createdAt: 'createdAt'
};

exports.Prisma.AssistantScalarFieldEnum = {
  assistant_id: 'assistant_id',
  graph_id: 'graph_id',
  config: 'config',
  created_at: 'created_at',
  updated_at: 'updated_at',
  metadata: 'metadata',
  version: 'version',
  name: 'name',
  description: 'description'
};

exports.Prisma.ThreadScalarFieldEnum = {
  thread_id: 'thread_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  metadata: 'metadata',
  status: 'status',
  values: 'values'
};

exports.Prisma.RunScalarFieldEnum = {
  run_id: 'run_id',
  thread_id: 'thread_id',
  assistant_id: 'assistant_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  status: 'status',
  metadata: 'metadata',
  multitask_strategy: 'multitask_strategy'
};

exports.Prisma.CheckpointScalarFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  checkpoint_id: 'checkpoint_id',
  parent_checkpoint_id: 'parent_checkpoint_id',
  type: 'type',
  checkpoint: 'checkpoint',
  metadata: 'metadata'
};

exports.Prisma.CheckpointBlobsScalarFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  channel: 'channel',
  version: 'version',
  type: 'type',
  blob: 'blob'
};

exports.Prisma.CheckpointWritesScalarFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  checkpoint_id: 'checkpoint_id',
  task_id: 'task_id',
  idx: 'idx',
  channel: 'channel',
  type: 'type',
  blob: 'blob'
};

exports.Prisma.CheckpointMigrationsScalarFieldEnum = {
  v: 'v'
};

exports.Prisma.ChatScalarFieldEnum = {
  id: 'id',
  assistantId: 'assistantId',
  contractId: 'contractId',
  createdAt: 'createdAt',
  title: 'title',
  userId: 'userId',
  visibility: 'visibility'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  chatId: 'chatId',
  role: 'role',
  content: 'content',
  createdAt: 'createdAt'
};

exports.Prisma.MemoryStoreScalarFieldEnum = {
  id: 'id',
  namespace: 'namespace',
  key: 'key',
  value: 'value',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.BannerScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  type: 'type',
  size: 'size',
  fontWeight: 'fontWeight',
  color: 'color',
  description: 'description',
  shared: 'shared',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  plan: 'plan',
  image: 'image',
  language: 'language',
  timezone: 'timezone',
  stripeCustomerId: 'stripeCustomerId',
  stripeSubscriptionId: 'stripeSubscriptionId',
  stripePriceId: 'stripePriceId'
};

exports.Prisma.WorkspaceOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  postalCode: 'postalCode',
  city: 'city',
  inviteCode: 'inviteCode'
};

exports.Prisma.MemberOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  bankAccountId: 'bankAccountId',
  workspaceId: 'workspaceId'
};

exports.Prisma.ProjectOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  workspaceId: 'workspaceId',
  imageUrl: 'imageUrl'
};

exports.Prisma.TaskOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  workspaceId: 'workspaceId',
  projectId: 'projectId',
  assigneeId: 'assigneeId',
  description: 'description'
};

exports.Prisma.ReminderOrderByRelevanceFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  workflowRunId: 'workflowRunId'
};

exports.Prisma.WorkflowRunOrderByRelevanceFieldEnum = {
  id: 'id',
  reminderId: 'reminderId',
  workflowRunId: 'workflowRunId',
  status: 'status'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.BankAccountOrderByRelevanceFieldEnum = {
  id: 'id',
  resourceId: 'resourceId',
  originalId: 'originalId',
  orgId: 'orgId',
  userId: 'userId',
  name: 'name'
};

exports.Prisma.BalanceOrderByRelevanceFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  assetId: 'assetId',
  currencyIso: 'currencyIso',
  description: 'description'
};

exports.Prisma.TimeDepositOrderByRelevanceFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  assetId: 'assetId',
  categoryId: 'categoryId',
  currencyIso: 'currencyIso',
  certificateNo: 'certificateNo',
  period: 'period',
  description: 'description',
  suggestedCategoryId: 'suggestedCategoryId'
};

exports.Prisma.CurrencyOrderByRelevanceFieldEnum = {
  iso: 'iso',
  symbol: 'symbol',
  name: 'name'
};

exports.Prisma.AssetOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.AssetValuationOrderByRelevanceFieldEnum = {
  id: 'id',
  assetId: 'assetId'
};

exports.Prisma.AssetTransactionOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  assetId: 'assetId'
};

exports.Prisma.TransactionOrderByRelevanceFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  assetId: 'assetId',
  userId: 'userId',
  currencyIso: 'currencyIso',
  categoryId: 'categoryId',
  description: 'description',
  type: 'type',
  suggestedCategoryId: 'suggestedCategoryId'
};

exports.Prisma.IncomeOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  categoryId: 'categoryId',
  userId: 'userId',
  accountId: 'accountId',
  type: 'type',
  suggestedCategoryId: 'suggestedCategoryId',
  importHash: 'importHash'
};

exports.Prisma.ExpenseOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  categoryId: 'categoryId',
  userId: 'userId',
  accountId: 'accountId',
  type: 'type',
  suggestedCategoryId: 'suggestedCategoryId',
  importHash: 'importHash'
};

exports.Prisma.SpreadsheetImportOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  fileName: 'fileName',
  sheetName: 'sheetName',
  type: 'type',
  importHash: 'importHash'
};

exports.Prisma.CategoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  icon: 'icon',
  color: 'color',
  keywords: 'keywords',
  userId: 'userId',
  description: 'description'
};

exports.Prisma.BudgetPreferencesOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.BudgetOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  userId: 'userId',
  accountId: 'accountId'
};

exports.Prisma.CategoryBudgetOrderByRelevanceFieldEnum = {
  id: 'id',
  budgetId: 'budgetId',
  categoryId: 'categoryId'
};

exports.Prisma.BudgetRolloverOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  categoryId: 'categoryId',
  previousBudgetId: 'previousBudgetId',
  nextBudgetId: 'nextBudgetId',
  accountId: 'accountId'
};

exports.Prisma.ConnectorConfigOrderByRelevanceFieldEnum = {
  id: 'id',
  orgId: 'orgId',
  connectorId: 'connectorId'
};

exports.Prisma.ConnectorOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  logoUrl: 'logoUrl'
};

exports.Prisma.IntegrationOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  logoUrl: 'logoUrl',
  connectorProviderId: 'connectorProviderId',
  connectorId: 'connectorId'
};

exports.Prisma.ResourceOrderByRelevanceFieldEnum = {
  id: 'id',
  integrationId: 'integrationId',
  originalId: 'originalId',
  userId: 'userId'
};

exports.Prisma.StatsOrderByRelevanceFieldEnum = {
  id: 'id'
};

exports.Prisma.StatsHistoryOrderByRelevanceFieldEnum = {
  id: 'id'
};

exports.Prisma.InvestmentOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.InvestmentValuationOrderByRelevanceFieldEnum = {
  id: 'id',
  investmentId: 'investmentId'
};

exports.Prisma.InvestmentTransactionOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  investmentId: 'investmentId'
};

exports.Prisma.LiabilityOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.LiabilityPaymentOrderByRelevanceFieldEnum = {
  id: 'id',
  description: 'description',
  liabilityId: 'liabilityId'
};

exports.Prisma.SavingsGoalOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  userId: 'userId'
};

exports.Prisma.SavingsProgressOrderByRelevanceFieldEnum = {
  id: 'id',
  goalId: 'goalId'
};

exports.Prisma.ContractAnalysisOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  contractText: 'contractText',
  summary: 'summary',
  recommendations: 'recommendations',
  keyClauses: 'keyClauses',
  legalCompliance: 'legalCompliance',
  negotiationPoints: 'negotiationPoints',
  contractDuration: 'contractDuration',
  terminationConditions: 'terminationConditions',
  performanceMetrics: 'performanceMetrics',
  language: 'language',
  filePath: 'filePath',
  contractType: 'contractType',
  specificClauses: 'specificClauses'
};

exports.Prisma.RiskOrderByRelevanceFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  risk: 'risk',
  explanation: 'explanation',
  severity: 'severity'
};

exports.Prisma.OpportunityOrderByRelevanceFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  opportunity: 'opportunity',
  explanation: 'explanation',
  impact: 'impact'
};

exports.Prisma.CompensationStructureOrderByRelevanceFieldEnum = {
  id: 'id',
  contractId: 'contractId',
  baseSalary: 'baseSalary',
  bonuses: 'bonuses',
  equity: 'equity',
  otherBenefits: 'otherBenefits'
};

exports.Prisma.SpeechOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  workspaceId: 'workspaceId',
  projectId: 'projectId',
  title: 'title',
  description: 'description'
};

exports.Prisma.RecordingOrderByRelevanceFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  url: 'url'
};

exports.Prisma.TranscriptionOrderByRelevanceFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  text: 'text',
  language: 'language'
};

exports.Prisma.AnalysisOrderByRelevanceFieldEnum = {
  id: 'id',
  speechId: 'speechId',
  summary: 'summary',
  sentiment: 'sentiment'
};

exports.Prisma.VectorStoreOrderByRelevanceFieldEnum = {
  id: 'id',
  documentId: 'documentId',
  documentType: 'documentType',
  content: 'content',
  filePath: 'filePath'
};

exports.Prisma.AssistantOrderByRelevanceFieldEnum = {
  assistant_id: 'assistant_id',
  graph_id: 'graph_id',
  name: 'name',
  description: 'description'
};

exports.Prisma.ThreadOrderByRelevanceFieldEnum = {
  thread_id: 'thread_id'
};

exports.Prisma.RunOrderByRelevanceFieldEnum = {
  run_id: 'run_id',
  thread_id: 'thread_id',
  assistant_id: 'assistant_id'
};

exports.Prisma.CheckpointOrderByRelevanceFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  checkpoint_id: 'checkpoint_id',
  parent_checkpoint_id: 'parent_checkpoint_id',
  type: 'type'
};

exports.Prisma.CheckpointBlobsOrderByRelevanceFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  channel: 'channel',
  version: 'version',
  type: 'type'
};

exports.Prisma.CheckpointWritesOrderByRelevanceFieldEnum = {
  thread_id: 'thread_id',
  checkpoint_ns: 'checkpoint_ns',
  checkpoint_id: 'checkpoint_id',
  task_id: 'task_id',
  channel: 'channel',
  type: 'type'
};

exports.Prisma.ChatOrderByRelevanceFieldEnum = {
  id: 'id',
  assistantId: 'assistantId',
  contractId: 'contractId',
  title: 'title',
  userId: 'userId'
};

exports.Prisma.MessageOrderByRelevanceFieldEnum = {
  id: 'id',
  chatId: 'chatId',
  role: 'role'
};

exports.Prisma.MemoryStoreOrderByRelevanceFieldEnum = {
  id: 'id',
  namespace: 'namespace',
  key: 'key'
};

exports.Prisma.BannerOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  type: 'type',
  size: 'size',
  fontWeight: 'fontWeight',
  color: 'color',
  description: 'description'
};
exports.ReminderBasis = exports.$Enums.ReminderBasis = {
  START_DATE: 'START_DATE',
  DUE_DATE: 'DUE_DATE'
};

exports.CategoryType = exports.$Enums.CategoryType = {
  CREDIT: 'CREDIT',
  DEBIT: 'DEBIT'
};

exports.Role = exports.$Enums.Role = {
  ADMIN: 'ADMIN',
  MEMBER: 'MEMBER'
};

exports.AccountType = exports.$Enums.AccountType = {
  BANK: 'BANK',
  SAVINGS: 'SAVINGS',
  INVESTMENT: 'INVESTMENT',
  CRYPTO: 'CRYPTO',
  VIRTUAL: 'VIRTUAL'
};

exports.BalanceType = exports.$Enums.BalanceType = {
  AVAILABLE: 'AVAILABLE',
  BOOKED: 'BOOKED',
  EXPECTED: 'EXPECTED'
};

exports.TimeDepositType = exports.$Enums.TimeDepositType = {
  AVAILABLE: 'AVAILABLE',
  WITHDRAWN: 'WITHDRAWN'
};

exports.ConnectorEnv = exports.$Enums.ConnectorEnv = {
  DEVELOPMENT: 'DEVELOPMENT',
  SANDBOX: 'SANDBOX',
  PRODUCTION: 'PRODUCTION'
};

exports.ConnectorStatus = exports.$Enums.ConnectorStatus = {
  ACTIVE: 'ACTIVE',
  BETA: 'BETA',
  DEV: 'DEV',
  INACTIVE: 'INACTIVE'
};

exports.ConnectorType = exports.$Enums.ConnectorType = {
  DIRECT: 'DIRECT',
  AGGREGATED: 'AGGREGATED'
};

exports.AssetType = exports.$Enums.AssetType = {
  REAL_ESTATE: 'REAL_ESTATE',
  VEHICLE: 'VEHICLE',
  PRECIOUS_METALS: 'PRECIOUS_METALS',
  OTHER: 'OTHER'
};

exports.TransactionType = exports.$Enums.TransactionType = {
  PURCHASE: 'PURCHASE',
  SALE: 'SALE',
  MAINTENANCE: 'MAINTENANCE',
  IMPROVEMENT: 'IMPROVEMENT',
  DEPRECIATION: 'DEPRECIATION',
  APPRECIATION: 'APPRECIATION'
};

exports.InvestmentType = exports.$Enums.InvestmentType = {
  STOCKS: 'STOCKS',
  CRYPTO: 'CRYPTO',
  ETF: 'ETF',
  OTHER: 'OTHER'
};

exports.InvestmentTransactionType = exports.$Enums.InvestmentTransactionType = {
  BUY: 'BUY',
  SELL: 'SELL',
  DIVIDEND: 'DIVIDEND',
  SPLIT: 'SPLIT',
  MERGE: 'MERGE'
};

exports.LiabilityType = exports.$Enums.LiabilityType = {
  MORTGAGE: 'MORTGAGE',
  CREDIT_CARD: 'CREDIT_CARD',
  CAR_LOAN: 'CAR_LOAN',
  STUDENT_LOAN: 'STUDENT_LOAN'
};

exports.PaymentType = exports.$Enums.PaymentType = {
  REGULAR: 'REGULAR',
  EXTRA: 'EXTRA',
  INTEREST: 'INTEREST',
  PRINCIPAL: 'PRINCIPAL'
};

exports.GoalType = exports.$Enums.GoalType = {
  EMERGENCY_FUND: 'EMERGENCY_FUND',
  RETIREMENT: 'RETIREMENT',
  DOWN_PAYMENT: 'DOWN_PAYMENT',
  CUSTOM: 'CUSTOM'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  BACKLOG: 'BACKLOG',
  TODO: 'TODO',
  IN_PROGRESS: 'IN_PROGRESS',
  IN_REVIEW: 'IN_REVIEW',
  DONE: 'DONE'
};

exports.RunStatus = exports.$Enums.RunStatus = {
  pending: 'pending',
  running: 'running',
  error: 'error',
  success: 'success',
  timeout: 'timeout',
  interrupted: 'interrupted'
};

exports.ThreadStatus = exports.$Enums.ThreadStatus = {
  idle: 'idle',
  busy: 'busy',
  interrupted: 'interrupted'
};

exports.MultitaskStrategy = exports.$Enums.MultitaskStrategy = {
  reject: 'reject',
  interrupt: 'interrupt',
  rollback: 'rollback',
  enqueue: 'enqueue'
};

exports.Visibility = exports.$Enums.Visibility = {
  PUBLIC: 'PUBLIC',
  PRIVATE: 'PRIVATE'
};

exports.Prisma.ModelName = {
  User: 'User',
  Workspace: 'Workspace',
  Member: 'Member',
  Project: 'Project',
  Task: 'Task',
  Reminder: 'Reminder',
  WorkflowRun: 'WorkflowRun',
  BankAccount: 'BankAccount',
  Balance: 'Balance',
  TimeDeposit: 'TimeDeposit',
  Currency: 'Currency',
  Asset: 'Asset',
  AssetValuation: 'AssetValuation',
  AssetTransaction: 'AssetTransaction',
  Transaction: 'Transaction',
  Income: 'Income',
  Expense: 'Expense',
  SpreadsheetImport: 'SpreadsheetImport',
  Category: 'Category',
  BudgetPreferences: 'BudgetPreferences',
  Budget: 'Budget',
  CategoryBudget: 'CategoryBudget',
  BudgetRollover: 'BudgetRollover',
  ConnectorConfig: 'ConnectorConfig',
  Connector: 'Connector',
  Integration: 'Integration',
  Resource: 'Resource',
  Stats: 'Stats',
  StatsHistory: 'StatsHistory',
  Investment: 'Investment',
  InvestmentValuation: 'InvestmentValuation',
  InvestmentTransaction: 'InvestmentTransaction',
  Liability: 'Liability',
  LiabilityPayment: 'LiabilityPayment',
  SavingsGoal: 'SavingsGoal',
  SavingsProgress: 'SavingsProgress',
  ContractAnalysis: 'ContractAnalysis',
  Risk: 'Risk',
  Opportunity: 'Opportunity',
  CompensationStructure: 'CompensationStructure',
  Speech: 'Speech',
  Recording: 'Recording',
  Transcription: 'Transcription',
  Analysis: 'Analysis',
  VectorStore: 'VectorStore',
  Assistant: 'Assistant',
  Thread: 'Thread',
  Run: 'Run',
  Checkpoint: 'Checkpoint',
  CheckpointBlobs: 'CheckpointBlobs',
  CheckpointWrites: 'CheckpointWrites',
  CheckpointMigrations: 'CheckpointMigrations',
  Chat: 'Chat',
  Message: 'Message',
  MemoryStore: 'MemoryStore',
  Banner: 'Banner'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "E:\\Source\\repos\\younkertw\\home\\prisma\\app\\generated\\prisma\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [
      "driverAdapters",
      "fullTextSearchPostgres",
      "postgresqlExtensions"
    ],
    "sourceFilePath": "E:\\Source\\repos\\younkertw\\home\\prisma\\schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../../../.env"
  },
  "relativePath": "../../../..",
  "clientVersion": "6.9.0",
  "engineVersion": "81e4af48011447c3cc503a190e86995b66d2a28e",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "postinstall": false,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "generator client {\n  provider        = \"prisma-client-js\"\n  output          = \"app/generated/prisma/client\"\n  previewFeatures = [\"driverAdapters\", \"fullTextSearchPostgres\", \"postgresqlExtensions\"]\n}\n\ndatasource db {\n  provider   = \"postgresql\"\n  url        = env(\"DATABASE_URL\")\n  directUrl  = env(\"DATABASE_URL_UNPOOLED\")\n  extensions = [vector(schema: \"public\")]\n}\n\nmodel User {\n  id                  String  @id\n  name                String?\n  email               String? @unique\n  phone               String?\n  plan                String  @default(\"basic\")\n  credits             Int     @default(3)\n  image               String?\n  language            String? @default(\"english\")\n  timezone            String  @default(\"Asia/Taipei\") // Store timezone in IANA format\n  onboardingEmailSent Boolean @default(false)\n\n  bankAccounts      BankAccount[]\n  budgets           Budget[]\n  transactions      Transaction[]\n  incomes           Income[]\n  expenses          Expense[]\n  budgetRollovers   BudgetRollover[]\n  budgetPreferences BudgetPreferences[]\n\n  createdAt DateTime @default(now()) @map(name: \"created_at\")\n  updatedAt DateTime @default(now()) @map(name: \"updated_at\")\n\n  stripeCustomerId       String?       @unique @map(name: \"stripe_customer_id\")\n  stripeSubscriptionId   String?       @unique @map(name: \"stripe_subscription_id\")\n  stripePriceId          String?       @map(name: \"stripe_price_id\")\n  stripeCurrentPeriodEnd DateTime?     @map(name: \"stripe_current_period_end\")\n  //workspaceId            String?\n  members                Member[]\n  projects               Project[]\n  tasks                  Task[]\n  Category               Category[]\n  assets                 Asset[]\n  investments            Investment[]\n  liabilities            Liability[]\n  savingsGoals           SavingsGoal[]\n  chats                  Chat[]\n  speeches               Speech[]\n  banners                Banner[]\n\n  @@map(name: \"users\")\n}\n\nmodel Workspace {\n  id         String    @id @default(cuid())\n  name       String\n  orgnr      Int?\n  address    String?\n  postalCode String?\n  city       String?\n  inviteCode String?\n  //bank_accounts BankAccount[]\n  members    Member[]\n  projects   Project[]\n  tasks      Task[]\n  speeches   Speech[]\n\n  createdAt DateTime @default(now()) @map(name: \"created_at\")\n  updatedAt DateTime @default(now()) @map(name: \"updated_at\")\n}\n\nmodel Member {\n  id            String  @id @default(cuid())\n  userId        String\n  bankAccountId String?\n  workspaceId   String\n  role          Role    @default(MEMBER) // Workspace-specific role\n\n  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)\n  assignees   Task[]\n  bankAccount BankAccount? @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)\n  workspace   Workspace    @relation(fields: [workspaceId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, workspaceId]) // Ensure a user can't have multiple roles for the same workspace\n}\n\nmodel Project {\n  id          String    @id @default(cuid())\n  name        String\n  userId      String\n  workspaceId String\n  imageUrl    String?\n  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)\n  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)\n  createdAt   DateTime  @default(now()) @map(name: \"created_at\")\n  updatedAt   DateTime  @default(now()) @map(name: \"updated_at\")\n  tasks       Task[]\n  speeches    Speech[]\n}\n\nmodel Task {\n  id          String     @id @default(cuid())\n  name        String\n  userId      String\n  workspaceId String\n  projectId   String\n  assigneeId  String\n  description String?\n  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)\n  workspace   Workspace  @relation(fields: [workspaceId], references: [id], onDelete: Cascade)\n  assignee    Member     @relation(fields: [assigneeId], references: [id])\n  startDate   DateTime?\n  dueDate     DateTime\n  status      TaskStatus\n  position    Int        @default(1000)\n\n  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)\n  createdAt DateTime @default(now()) @map(name: \"created_at\")\n  updatedAt DateTime @default(now()) @map(name: \"updated_at\")\n\n  reminders Reminder[] // One-to-many relationship with reminders\n}\n\nmodel Reminder {\n  id            String        @id @default(cuid())\n  taskId        String\n  task          Task          @relation(fields: [taskId], references: [id], onDelete: Cascade)\n  enabled       Boolean       @default(false) // Whether the reminder is active\n  basis         ReminderBasis // START_DATE or DUE_DATE\n  daysBefore    Int // Days before the basis date to send a reminder\n  customDate    DateTime? // Custom reminder date\n  workflowRunId String? // Track workflow runs for dynamic updates\n  workflowRuns  WorkflowRun[] // One-to-many relationship with workflow runs\n  createdAt     DateTime      @default(now()) @map(name: \"created_at\")\n  updatedAt     DateTime      @default(now()) @map(name: \"updated_at\")\n}\n\nenum ReminderBasis {\n  START_DATE\n  DUE_DATE\n}\n\nmodel WorkflowRun {\n  id            String   @id @default(cuid())\n  reminderId    String\n  reminder      Reminder @relation(fields: [reminderId], references: [id], onDelete: Cascade)\n  workflowRunId String   @unique // Workflow ID from Upstash\n  status        String // Workflow status (e.g., \"active\", \"completed\")\n  createdAt     DateTime @default(now()) @map(name: \"created_at\")\n  updatedAt     DateTime @default(now()) @map(name: \"updated_at\")\n}\n\nmodel BankAccount {\n  id              String           @id @default(cuid())\n  createdAt       DateTime         @default(now())\n  updatedAt       DateTime         @default(now())\n  resourceId      String?\n  originalId      String?\n  orgId           String?\n  userId          String?\n  name            String\n  originalPayload Json?\n  initialAmount   Int?             @default(0)\n  Balance         Balance[]\n  TimeDeposit     TimeDeposit[]\n  Transaction     Transaction[]\n  Income          Income[]\n  Expense         Expense[]\n  Budget          Budget[]\n  BudgetRollover  BudgetRollover[]\n  accountType     AccountType      @default(BANK)\n  resource        Resource?        @relation(fields: [resourceId], references: [id], onDelete: Cascade)\n  //workspaceId     String?          \n  //workspace       Workspace?       @relation(fields: [workspaceId], references: [id])\n  members         Member[]\n  user            User?            @relation(fields: [userId], references: [id])\n\n  @@map(name: \"bank_accounts\")\n}\n\nmodel Balance {\n  id              String       @id @default(cuid())\n  createdAt       DateTime     @default(now())\n  updatedAt       DateTime     @default(now())\n  accountId       String?\n  assetId         String?\n  currencyIso     String\n  amount          Int?\n  date            DateTime\n  description     String?\n  type            BalanceType  @default(AVAILABLE)\n  originalPayload Json?\n  bankAccount     BankAccount? @relation(fields: [accountId], references: [id], onDelete: Cascade)\n  asset           Asset?       @relation(fields: [assetId], references: [id], onDelete: Cascade)\n  currency        Currency     @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)\n}\n\nmodel TimeDeposit {\n  id                  String          @id @default(cuid())\n  createdAt           DateTime        @default(now())\n  updatedAt           DateTime        @default(now())\n  accountId           String?\n  assetId             String?\n  categoryId          String?\n  currencyIso         String\n  amount              Int?\n  date                DateTime\n  certificateNo       String?\n  period              String?\n  interestRate        Decimal?\n  description         String?\n  type                TimeDepositType @default(AVAILABLE)\n  originalPayload     Json?\n  bankAccount         BankAccount?    @relation(fields: [accountId], references: [id], onDelete: Cascade)\n  asset               Asset?          @relation(fields: [assetId], references: [id], onDelete: Cascade)\n  currency            Currency        @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)\n  category            Category?       @relation(fields: [categoryId], references: [id])\n  categoryValidated   Boolean         @default(false)\n  suggestedCategoryId String?\n}\n\nmodel Currency {\n  iso         String        @id\n  symbol      String        @default(\"$\")\n  name        String        @default(\"新台幣\")\n  numericCode Int?\n  Balance     Balance[]\n  Transaction Transaction[]\n  TimeDeposit TimeDeposit[]\n}\n\nmodel Asset {\n  id              String             @id @default(cuid())\n  name            String\n  originalPayload Json?\n  Balance         Balance[]\n  TimeDeposit     TimeDeposit[]\n  Transaction     Transaction[]\n  type            AssetType\n  value           Decimal\n  purchaseDate    DateTime?\n  description     String?\n  userId          String\n  user            User               @relation(fields: [userId], references: [id], onDelete: Cascade)\n  transactions    AssetTransaction[]\n  valuations      AssetValuation[]\n\n  @@index([id, userId])\n  @@map(name: \"assets\")\n}\n\nmodel AssetValuation {\n  id        String   @id @default(cuid())\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n  value     Decimal\n  date      DateTime\n  assetId   String\n  asset     Asset    @relation(fields: [assetId], references: [id], onDelete: Cascade)\n\n  @@map(name: \"asset_valuations\")\n}\n\nmodel AssetTransaction {\n  id          String          @id @default(cuid())\n  createdAt   DateTime        @default(now())\n  updatedAt   DateTime        @updatedAt\n  type        TransactionType\n  amount      Decimal\n  date        DateTime\n  description String?\n  assetId     String\n  asset       Asset           @relation(fields: [assetId], references: [id], onDelete: Cascade)\n\n  @@map(name: \"asset_transactions\")\n}\n\nmodel Transaction {\n  id                  String       @id @default(cuid())\n  createdAt           DateTime     @default(now())\n  updatedAt           DateTime     @default(now())\n  accountId           String?\n  assetId             String?\n  userId              String\n  user                User         @relation(fields: [userId], references: [id], onDelete: Cascade)\n  currencyIso         String\n  categoryId          String?\n  amount              Decimal\n  date                DateTime\n  description         String\n  originalPayload     Json?\n  review              Boolean      @default(false)\n  bankAccount         BankAccount? @relation(fields: [accountId], references: [id], onDelete: Cascade)\n  asset               Asset?       @relation(fields: [assetId], references: [id], onDelete: Cascade)\n  category            Category?    @relation(fields: [categoryId], references: [id])\n  currency            Currency     @relation(fields: [currencyIso], references: [iso], onDelete: Cascade)\n  type                String\n  categoryValidated   Boolean      @default(false)\n  suggestedCategoryId String?\n\n  @@index([id, userId, accountId, categoryId, type])\n}\n\nmodel Income {\n  id          String   @id @default(cuid())\n  createdAt   DateTime @default(now())\n  updatedAt   DateTime @updatedAt\n  amount      Decimal\n  description String?\n  date        DateTime\n  categoryId  String\n  category    Category @relation(fields: [categoryId], references: [id])\n\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  accountId           String?\n  bankAccount         BankAccount? @relation(fields: [accountId], references: [id])\n  type                String\n  categoryValidated   Boolean      @default(false)\n  suggestedCategoryId String?\n  importHash          String?      @unique\n\n  @@unique([userId, accountId, categoryId, date, amount, description])\n  @@index([id, userId, accountId, categoryId, importHash])\n}\n\nmodel Expense {\n  id          String   @id @default(cuid())\n  createdAt   DateTime @default(now())\n  updatedAt   DateTime @updatedAt\n  amount      Decimal\n  description String?\n  date        DateTime\n  categoryId  String\n  category    Category @relation(fields: [categoryId], references: [id])\n\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  accountId           String?\n  bankAccount         BankAccount? @relation(fields: [accountId], references: [id])\n  type                String\n  categoryValidated   Boolean      @default(false)\n  suggestedCategoryId String?\n  importHash          String?      @unique\n\n  @@unique([userId, accountId, categoryId, date, amount, description])\n  @@index([id, userId, accountId, categoryId, importHash])\n}\n\nmodel SpreadsheetImport {\n  id         String   @id @default(cuid())\n  userId     String\n  importedAt DateTime @default(now())\n  fileName   String?\n  sheetName  String\n  rowCount   Int\n  type       String // \"Income\", \"Expense\", or \"Both\"\n  importHash String\n\n  @@unique([userId, sheetName, importHash])\n}\n\nenum CategoryType {\n  CREDIT\n  DEBIT\n}\n\nmodel Category {\n  id              String           @id @default(cuid())\n  createdAt       DateTime         @default(now())\n  updatedAt       DateTime         @updatedAt\n  name            String\n  icon            String\n  color           String           @default(\"hsl(var(--chart-1))\")\n  keywords        String[]         @default([])\n  type            CategoryType     @default(DEBIT)\n  userId          String\n  description     String?\n  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)\n  transactions    Transaction[]\n  budgets         CategoryBudget[]\n  incomes         Income[]\n  expenses        Expense[]\n  budgetRollovers BudgetRollover[]\n  TimeDeposit     TimeDeposit[]\n\n  @@unique([name, userId])\n  @@index([id, userId])\n  @@map(name: \"categories\")\n}\n\nmodel BudgetPreferences {\n  id                   String  @id @default(cuid())\n  userId               String  @unique\n  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)\n  autoCreateNextBudget Boolean @default(true)\n  autoApplyRollovers   Boolean @default(true)\n  rolloverThreshold    Decimal @default(0)\n}\n\nmodel Budget {\n  id                String           @id @default(cuid())\n  createdAt         DateTime         @default(now())\n  updatedAt         DateTime         @updatedAt\n  name              String?\n  startDate         DateTime?\n  endDate           DateTime?\n  amount            Decimal?\n  userId            String\n  user              User             @relation(fields: [userId], references: [id], onDelete: Cascade)\n  rolloversPrevious BudgetRollover[] @relation(\"PreviousBudget\")\n  rolloversNext     BudgetRollover[] @relation(\"NextBudget\")\n  categories        CategoryBudget[]\n  accountId         String?\n  account           BankAccount?     @relation(fields: [accountId], references: [id])\n\n  @@index([id, userId, accountId])\n  @@map(name: \"budgets\")\n}\n\nmodel CategoryBudget {\n  id         String   @id @default(cuid())\n  createdAt  DateTime @default(now())\n  updatedAt  DateTime @updatedAt\n  amount     Decimal\n  budgetId   String\n  categoryId String\n  budget     Budget   @relation(fields: [budgetId], references: [id], onDelete: Cascade)\n  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)\n\n  @@unique([budgetId, categoryId])\n  @@index([id, budgetId])\n  @@map(name: \"category_budgets\")\n}\n\nmodel BudgetRollover {\n  id     String @id @default(cuid())\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  categoryId String\n  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)\n\n  previousBudgetId String?\n  previousBudget   Budget? @relation(\"PreviousBudget\", fields: [previousBudgetId], references: [id], onDelete: SetNull)\n\n  nextBudgetId String?\n  nextBudget   Budget? @relation(\"NextBudget\", fields: [nextBudgetId], references: [id], onDelete: SetNull)\n\n  amount             Decimal // Rolled over amount\n  createdAt          DateTime     @default(now())\n  periodStart        DateTime\n  periodEnd          DateTime\n  rolloverPercentage Decimal?\n  accountId          String?\n  account            BankAccount? @relation(fields: [accountId], references: [id])\n\n  @@index([id, userId, accountId, categoryId])\n}\n\nmodel ConnectorConfig {\n  id          String       @id @default(cuid())\n  createdAt   DateTime     @default(now())\n  updatedAt   DateTime     @updatedAt\n  orgId       String\n  secret      Json?\n  env         ConnectorEnv\n  connectorId String\n  connector   Connector    @relation(fields: [connectorId], references: [id], onDelete: Cascade)\n}\n\nmodel Connector {\n  id              String            @id @default(cuid())\n  createdAt       DateTime          @default(now())\n  updatedAt       DateTime          @updatedAt\n  name            String\n  logoUrl         String?\n  status          ConnectorStatus\n  type            ConnectorType\n  connectorConfig ConnectorConfig[]\n  Integration     Integration[]\n}\n\nmodel Integration {\n  id                  String     @id @default(cuid())\n  createdAt           DateTime   @default(now())\n  updatedAt           DateTime   @updatedAt\n  name                String\n  logoUrl             String?\n  connectorProviderId String?    @unique\n  connectorId         String\n  connector           Connector  @relation(fields: [connectorId], references: [id], onDelete: Cascade)\n  Resource            Resource[]\n}\n\nmodel Resource {\n  id            String        @id @default(cuid())\n  createdAt     DateTime      @default(now())\n  updatedAt     DateTime      @updatedAt\n  integrationId String\n  originalId    String\n  userId        String\n  integration   Integration   @relation(fields: [integrationId], references: [id], onDelete: Cascade)\n  bankAccounts  BankAccount[]\n\n  @@map(name: \"resources\")\n}\n\nmodel Stats {\n  id                     String   @id @default(cuid())\n  createdAt              DateTime @default(now())\n  updatedAt              DateTime @updatedAt\n  totalUsers             Int      @default(0)\n  activeUsers            Int      @default(0)\n  totalAccounts          Int      @default(0)\n  totalTransactions      Int      @default(0)\n  totalAssets            Int      @default(0)\n  totalLiabilities       Int      @default(0)\n  totalInvestments       Int      @default(0)\n  avgAccountsPerUser     Decimal  @default(0)\n  avgTransactionsPerUser Decimal  @default(0)\n  dailyActiveUsers       Int      @default(0)\n  weeklyActiveUsers      Int      @default(0)\n  monthlyActiveUsers     Int      @default(0)\n  operatingSystem        Json? // Store counts for different OS\n  browser                Json? // Store counts for different browsers\n  country                Json? // Store counts for different countries\n  lastUpdated            DateTime @default(now())\n\n  @@map(name: \"stats\")\n}\n\nmodel StatsHistory {\n  id                 String   @id @default(cuid())\n  createdAt          DateTime @default(now())\n  totalUsers         Int\n  activeUsers        Int\n  totalAccounts      Int\n  totalTransactions  Int\n  totalAssets        Int\n  totalLiabilities   Int\n  totalInvestments   Int\n  dailyActiveUsers   Int\n  weeklyActiveUsers  Int\n  monthlyActiveUsers Int\n  snapshot           Json // Store full stats snapshot\n\n  @@map(name: \"stats_history\")\n}\n\nmodel Investment {\n  id            String                  @id @default(cuid())\n  createdAt     DateTime                @default(now())\n  updatedAt     DateTime                @updatedAt\n  name          String\n  type          InvestmentType\n  amount        Decimal\n  shares        Decimal?\n  purchasePrice Decimal?\n  currentPrice  Decimal?\n  description   String?\n  userId        String\n  user          User                    @relation(fields: [userId], references: [id], onDelete: Cascade)\n  transactions  InvestmentTransaction[]\n  valuations    InvestmentValuation[]\n\n  @@index([id, userId])\n  @@map(name: \"investments\")\n}\n\nmodel InvestmentValuation {\n  id           String     @id @default(cuid())\n  createdAt    DateTime   @default(now())\n  updatedAt    DateTime   @updatedAt\n  value        Decimal\n  date         DateTime\n  investmentId String\n  investment   Investment @relation(fields: [investmentId], references: [id], onDelete: Cascade)\n\n  @@index([id, investmentId])\n  @@map(name: \"investment_valuations\")\n}\n\nmodel InvestmentTransaction {\n  id           String                    @id @default(cuid())\n  createdAt    DateTime                  @default(now())\n  updatedAt    DateTime                  @updatedAt\n  type         InvestmentTransactionType\n  amount       Decimal\n  shares       Decimal?\n  price        Decimal\n  date         DateTime\n  description  String?\n  investmentId String\n  investment   Investment                @relation(fields: [investmentId], references: [id], onDelete: Cascade)\n\n  @@index([id, investmentId])\n  @@map(name: \"investment_transactions\")\n}\n\nmodel Liability {\n  id             String             @id @default(cuid())\n  createdAt      DateTime           @default(now())\n  updatedAt      DateTime           @updatedAt\n  name           String\n  type           LiabilityType\n  amount         Decimal\n  interestRate   Decimal\n  monthlyPayment Decimal\n  startDate      DateTime?\n  endDate        DateTime?\n  description    String?\n  userId         String\n  user           User               @relation(fields: [userId], references: [id], onDelete: Cascade)\n  payments       LiabilityPayment[]\n\n  @@index([id, userId])\n  @@map(name: \"liabilities\")\n}\n\nmodel LiabilityPayment {\n  id          String      @id @default(cuid())\n  createdAt   DateTime    @default(now())\n  updatedAt   DateTime    @updatedAt\n  amount      Decimal\n  date        DateTime\n  type        PaymentType\n  description String?\n  liabilityId String\n  liability   Liability   @relation(fields: [liabilityId], references: [id], onDelete: Cascade)\n\n  @@map(name: \"liability_payments\")\n}\n\nmodel SavingsGoal {\n  id          String            @id @default(cuid())\n  createdAt   DateTime          @default(now())\n  updatedAt   DateTime          @updatedAt\n  name        String\n  target      Decimal\n  current     Decimal           @default(0)\n  deadline    DateTime?\n  description String?\n  isDefault   Boolean           @default(false) // To distinguish between system defaults and user goals\n  type        GoalType          @default(CUSTOM)\n  priority    Int               @default(0) // For ordering goals\n  userId      String\n  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)\n  progress    SavingsProgress[]\n\n  @@index([id, userId])\n  @@map(name: \"savings_goals\")\n}\n\nmodel SavingsProgress {\n  id        String      @id @default(cuid())\n  createdAt DateTime    @default(now())\n  amount    Decimal\n  date      DateTime\n  goalId    String\n  goal      SavingsGoal @relation(fields: [goalId], references: [id], onDelete: Cascade)\n\n  @@index([id, goalId])\n  @@map(name: \"savings_progress\")\n}\n\nmodel ContractAnalysis {\n  id                          String                 @id @default(cuid())\n  userId                      String\n  contractText                String\n  summary                     String\n  recommendations             String[]\n  keyClauses                  String[]\n  legalCompliance             String\n  negotiationPoints           String[]\n  contractDuration            String\n  terminationConditions       String\n  overallScore                Float\n  performanceMetrics          String[]\n  intellectualPropertyClauses Json?\n  createdAt                   DateTime               @default(now())\n  version                     Int                    @default(1)\n  userFeedback                Json?\n  customFields                Json?\n  expirationDate              DateTime?\n  language                    String                 @default(\"en\")\n  filePath                    String?\n  contractType                String\n  financialTerms              Json?\n  specificClauses             String\n  compensationStructure       CompensationStructure?\n  opportunities               Opportunity[]\n  risks                       Risk[]\n  chats                       Chat[]\n\n  @@index([userId])\n}\n\nmodel Risk {\n  id          String           @id @default(cuid())\n  contractId  String\n  risk        String\n  explanation String\n  severity    String\n  contract    ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)\n}\n\nmodel Opportunity {\n  id          String           @id @default(cuid())\n  contractId  String\n  opportunity String\n  explanation String\n  impact      String\n  contract    ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)\n}\n\nmodel CompensationStructure {\n  id            String           @id @default(cuid())\n  contractId    String           @unique\n  baseSalary    String\n  bonuses       String\n  equity        String\n  otherBenefits String\n  contract      ContractAnalysis @relation(fields: [contractId], references: [id], onDelete: Cascade)\n}\n\nmodel Speech {\n  id             String          @id @default(cuid())\n  userId         String\n  workspaceId    String\n  projectId      String\n  title          String\n  description    String?\n  createdAt      DateTime        @default(now())\n  updatedAt      DateTime        @updatedAt\n  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade)\n  workspace      Workspace       @relation(fields: [workspaceId], references: [id], onDelete: Cascade)\n  project        Project         @relation(fields: [projectId], references: [id], onDelete: Cascade)\n  recordings     Recording[]\n  transcriptions Transcription[]\n  analyses       Analysis[]\n}\n\nmodel Recording {\n  id        String   @id @default(cuid())\n  speechId  String\n  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)\n  url       String // URL to the stored audio file\n  duration  Int // Duration in seconds\n  createdAt DateTime @default(now())\n}\n\nmodel Transcription {\n  id        String   @id @default(cuid())\n  speechId  String\n  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)\n  text      String // Full transcribed text\n  language  String // Language code (e.g., \"en\", \"zh-TW\")\n  createdAt DateTime @default(now())\n}\n\nmodel Analysis {\n  id        String   @id @default(cuid())\n  speechId  String\n  speech    Speech   @relation(fields: [speechId], references: [id], onDelete: Cascade)\n  summary   String? // AI-generated summary\n  keyPoints Json? // Key points or action items extracted by AI\n  sentiment String? // Sentiment analysis (e.g., \"Positive\", \"Neutral\", \"Negative\")\n  createdAt DateTime @default(now())\n}\n\nmodel VectorStore {\n  id           String                      @id @default(uuid())\n  documentId   String\n  documentType String\n  content      String\n  embedding    Unsupported(\"vector(384)\")?\n  metadata     Json?\n  filePath     String\n  createdAt    DateTime                    @default(now())\n\n  @@index([documentId])\n  @@index([filePath])\n  @@index([documentType])\n}\n\nmodel Assistant {\n  assistant_id String   @id @default(cuid())\n  graph_id     String\n  config       Json\n  created_at   DateTime @default(now())\n  updated_at   DateTime @updatedAt\n  metadata     Json?\n  version      Int?\n  name         String\n  description  String?\n  runs         Run[]    @relation(\"AssistantRuns\")\n  chats        Chat[]   @relation(\"AssistantChats\")\n\n  @@unique([graph_id, name]) // Ensure uniqueness of graph_id + name\n}\n\nmodel Thread {\n  thread_id         String             @id @default(cuid())\n  created_at        DateTime           @default(now())\n  updated_at        DateTime           @updatedAt\n  metadata          Json?\n  status            ThreadStatus\n  values            Json\n  checkpoints       Checkpoint[]\n  checkpoint_blobs  CheckpointBlobs[]\n  checkpoint_writes CheckpointWrites[]\n  runs              Run[]              @relation(\"ThreadRuns\")\n}\n\nmodel Run {\n  run_id             String             @id @default(cuid())\n  thread             Thread             @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade, name: \"ThreadRuns\")\n  thread_id          String\n  assistant          Assistant          @relation(fields: [assistant_id], references: [assistant_id], onDelete: Cascade, name: \"AssistantRuns\")\n  assistant_id       String\n  created_at         DateTime           @default(now())\n  updated_at         DateTime           @updatedAt\n  status             RunStatus\n  metadata           Json?\n  multitask_strategy MultitaskStrategy?\n}\n\nmodel Checkpoint {\n  thread               Thread  @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)\n  thread_id            String\n  checkpoint_ns        String\n  checkpoint_id        String\n  parent_checkpoint_id String?\n  type                 String?\n  checkpoint           Json\n  metadata             Json?   @default(\"{}\")\n\n  @@id([thread_id, checkpoint_ns, checkpoint_id])\n}\n\nmodel CheckpointBlobs {\n  thread        Thread @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)\n  thread_id     String\n  checkpoint_ns String @default(\"\")\n  channel       String\n  version       String\n  type          String\n  blob          Bytes?\n\n  @@id([thread_id, checkpoint_ns, channel, version])\n  @@map(\"checkpoint_blobs\")\n}\n\nmodel CheckpointWrites {\n  thread        Thread  @relation(fields: [thread_id], references: [thread_id], onDelete: Cascade)\n  thread_id     String\n  checkpoint_ns String  @default(\"\")\n  checkpoint_id String\n  task_id       String\n  idx           Int\n  channel       String\n  type          String?\n  blob          Bytes\n\n  @@id([thread_id, checkpoint_ns, checkpoint_id, task_id, idx])\n  @@map(\"checkpoint_writes\")\n}\n\nmodel CheckpointMigrations {\n  v Int @id\n\n  @@map(\"checkpoint_migrations\")\n}\n\nmodel Chat {\n  id          String     @id @default(cuid())\n  assistantId String\n  contractId  String?\n  assistant   Assistant  @relation(fields: [assistantId], references: [assistant_id], onDelete: Cascade, name: \"AssistantChats\")\n  createdAt   DateTime   @default(now())\n  title       String\n  userId      String\n  visibility  Visibility @default(PRIVATE) // Equivalent to varchar('visibility', { enum: ['public', 'private'] }).notNull().default('private')\n\n  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)\n  contracts ContractAnalysis? @relation(fields: [contractId], references: [id], onDelete: Cascade)\n  messages  Message[]\n\n  @@map(\"Chat\")\n}\n\nmodel Message {\n  id        String   @id @default(cuid())\n  chatId    String\n  role      String\n  content   Json\n  createdAt DateTime @default(now())\n\n  chat Chat @relation(fields: [chatId], references: [id], onDelete: Cascade)\n\n  @@map(\"Message\")\n}\n\nmodel MemoryStore {\n  id         String   @id @default(cuid())\n  namespace  String[]\n  key        String\n  value      Json\n  created_at DateTime @default(now())\n  updated_at DateTime @updatedAt\n\n  @@unique([namespace, key])\n}\n\nmodel Banner {\n  id          String   @id @default(cuid())\n  userId      String?\n  user        User?    @relation(fields: [userId], references: [id], onDelete: Cascade)\n  title       String\n  type        String?  @default(\"title\")\n  size        String?  @default(\"text-base\")\n  fontWeight  String?  @default(\"font-normal\")\n  color       String\n  description String?\n  shared      Boolean  @default(false)\n  createdAt   DateTime @default(now())\n  updatedAt   DateTime @updatedAt\n\n  @@index([userId])\n}\n\nenum Role {\n  ADMIN\n  MEMBER\n}\n\nenum AccountType {\n  BANK\n  SAVINGS\n  INVESTMENT\n  CRYPTO\n  VIRTUAL\n}\n\nenum BalanceType {\n  AVAILABLE\n  BOOKED\n  EXPECTED\n}\n\nenum TimeDepositType {\n  AVAILABLE\n  WITHDRAWN\n}\n\nenum ConnectorEnv {\n  DEVELOPMENT\n  SANDBOX\n  PRODUCTION\n}\n\nenum ConnectorStatus {\n  ACTIVE\n  BETA\n  DEV\n  INACTIVE\n}\n\nenum ConnectorType {\n  DIRECT\n  AGGREGATED\n}\n\nenum AssetType {\n  REAL_ESTATE\n  VEHICLE\n  PRECIOUS_METALS\n  OTHER\n}\n\nenum TransactionType {\n  PURCHASE\n  SALE\n  MAINTENANCE\n  IMPROVEMENT\n  DEPRECIATION\n  APPRECIATION\n}\n\nenum InvestmentType {\n  STOCKS\n  CRYPTO\n  ETF\n  OTHER\n}\n\nenum InvestmentTransactionType {\n  BUY\n  SELL\n  DIVIDEND\n  SPLIT\n  MERGE\n}\n\nenum LiabilityType {\n  MORTGAGE\n  CREDIT_CARD\n  CAR_LOAN\n  STUDENT_LOAN\n}\n\nenum PaymentType {\n  REGULAR\n  EXTRA\n  INTEREST\n  PRINCIPAL\n}\n\nenum GoalType {\n  EMERGENCY_FUND\n  RETIREMENT\n  DOWN_PAYMENT\n  CUSTOM\n}\n\nenum TaskStatus {\n  BACKLOG\n  TODO\n  IN_PROGRESS\n  IN_REVIEW\n  DONE\n}\n\nenum RunStatus {\n  pending\n  running\n  error\n  success\n  timeout\n  interrupted\n}\n\nenum ThreadStatus {\n  idle\n  busy\n  interrupted\n}\n\nenum MultitaskStrategy {\n  reject\n  interrupt\n  rollback\n  enqueue\n}\n\nenum Visibility {\n  PUBLIC\n  PRIVATE\n}\n\n// if we need full reset : npx prisma db push --force-reset\n",
  "inlineSchemaHash": "a31793cb9d8e592c7df89a41a98c26a38589c738cb0a44906a892a769f6d4708",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"phone\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"plan\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"credits\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"image\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"language\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"timezone\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"onboardingEmailSent\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"bankAccounts\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToUser\"},{\"name\":\"budgets\",\"kind\":\"object\",\"type\":\"Budget\",\"relationName\":\"BudgetToUser\"},{\"name\":\"transactions\",\"kind\":\"object\",\"type\":\"Transaction\",\"relationName\":\"TransactionToUser\"},{\"name\":\"incomes\",\"kind\":\"object\",\"type\":\"Income\",\"relationName\":\"IncomeToUser\"},{\"name\":\"expenses\",\"kind\":\"object\",\"type\":\"Expense\",\"relationName\":\"ExpenseToUser\"},{\"name\":\"budgetRollovers\",\"kind\":\"object\",\"type\":\"BudgetRollover\",\"relationName\":\"BudgetRolloverToUser\"},{\"name\":\"budgetPreferences\",\"kind\":\"object\",\"type\":\"BudgetPreferences\",\"relationName\":\"BudgetPreferencesToUser\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"created_at\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"updated_at\"},{\"name\":\"stripeCustomerId\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"stripe_customer_id\"},{\"name\":\"stripeSubscriptionId\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"stripe_subscription_id\"},{\"name\":\"stripePriceId\",\"kind\":\"scalar\",\"type\":\"String\",\"dbName\":\"stripe_price_id\"},{\"name\":\"stripeCurrentPeriodEnd\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"stripe_current_period_end\"},{\"name\":\"members\",\"kind\":\"object\",\"type\":\"Member\",\"relationName\":\"MemberToUser\"},{\"name\":\"projects\",\"kind\":\"object\",\"type\":\"Project\",\"relationName\":\"ProjectToUser\"},{\"name\":\"tasks\",\"kind\":\"object\",\"type\":\"Task\",\"relationName\":\"TaskToUser\"},{\"name\":\"Category\",\"kind\":\"object\",\"type\":\"Category\",\"relationName\":\"CategoryToUser\"},{\"name\":\"assets\",\"kind\":\"object\",\"type\":\"Asset\",\"relationName\":\"AssetToUser\"},{\"name\":\"investments\",\"kind\":\"object\",\"type\":\"Investment\",\"relationName\":\"InvestmentToUser\"},{\"name\":\"liabilities\",\"kind\":\"object\",\"type\":\"Liability\",\"relationName\":\"LiabilityToUser\"},{\"name\":\"savingsGoals\",\"kind\":\"object\",\"type\":\"SavingsGoal\",\"relationName\":\"SavingsGoalToUser\"},{\"name\":\"chats\",\"kind\":\"object\",\"type\":\"Chat\",\"relationName\":\"ChatToUser\"},{\"name\":\"speeches\",\"kind\":\"object\",\"type\":\"Speech\",\"relationName\":\"SpeechToUser\"},{\"name\":\"banners\",\"kind\":\"object\",\"type\":\"Banner\",\"relationName\":\"BannerToUser\"}],\"dbName\":\"users\"},\"Workspace\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"orgnr\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"address\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"postalCode\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"city\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"inviteCode\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"members\",\"kind\":\"object\",\"type\":\"Member\",\"relationName\":\"MemberToWorkspace\"},{\"name\":\"projects\",\"kind\":\"object\",\"type\":\"Project\",\"relationName\":\"ProjectToWorkspace\"},{\"name\":\"tasks\",\"kind\":\"object\",\"type\":\"Task\",\"relationName\":\"TaskToWorkspace\"},{\"name\":\"speeches\",\"kind\":\"object\",\"type\":\"Speech\",\"relationName\":\"SpeechToWorkspace\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"created_at\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"updated_at\"}],\"dbName\":null},\"Member\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"bankAccountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workspaceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"role\",\"kind\":\"enum\",\"type\":\"Role\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"MemberToUser\"},{\"name\":\"assignees\",\"kind\":\"object\",\"type\":\"Task\",\"relationName\":\"MemberToTask\"},{\"name\":\"bankAccount\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToMember\"},{\"name\":\"workspace\",\"kind\":\"object\",\"type\":\"Workspace\",\"relationName\":\"MemberToWorkspace\"}],\"dbName\":null},\"Project\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workspaceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"ProjectToUser\"},{\"name\":\"workspace\",\"kind\":\"object\",\"type\":\"Workspace\",\"relationName\":\"ProjectToWorkspace\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"created_at\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"updated_at\"},{\"name\":\"tasks\",\"kind\":\"object\",\"type\":\"Task\",\"relationName\":\"ProjectToTask\"},{\"name\":\"speeches\",\"kind\":\"object\",\"type\":\"Speech\",\"relationName\":\"ProjectToSpeech\"}],\"dbName\":null},\"Task\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workspaceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"projectId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"assigneeId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"TaskToUser\"},{\"name\":\"workspace\",\"kind\":\"object\",\"type\":\"Workspace\",\"relationName\":\"TaskToWorkspace\"},{\"name\":\"assignee\",\"kind\":\"object\",\"type\":\"Member\",\"relationName\":\"MemberToTask\"},{\"name\":\"startDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"dueDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"TaskStatus\"},{\"name\":\"position\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"project\",\"kind\":\"object\",\"type\":\"Project\",\"relationName\":\"ProjectToTask\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"created_at\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"updated_at\"},{\"name\":\"reminders\",\"kind\":\"object\",\"type\":\"Reminder\",\"relationName\":\"ReminderToTask\"}],\"dbName\":null},\"Reminder\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"taskId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"task\",\"kind\":\"object\",\"type\":\"Task\",\"relationName\":\"ReminderToTask\"},{\"name\":\"enabled\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"basis\",\"kind\":\"enum\",\"type\":\"ReminderBasis\"},{\"name\":\"daysBefore\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"customDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"workflowRunId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workflowRuns\",\"kind\":\"object\",\"type\":\"WorkflowRun\",\"relationName\":\"ReminderToWorkflowRun\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"created_at\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"updated_at\"}],\"dbName\":null},\"WorkflowRun\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"reminderId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"reminder\",\"kind\":\"object\",\"type\":\"Reminder\",\"relationName\":\"ReminderToWorkflowRun\"},{\"name\":\"workflowRunId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"status\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"created_at\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\",\"dbName\":\"updated_at\"}],\"dbName\":null},\"BankAccount\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"resourceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"originalId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"orgId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"originalPayload\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"initialAmount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"Balance\",\"kind\":\"object\",\"type\":\"Balance\",\"relationName\":\"BalanceToBankAccount\"},{\"name\":\"TimeDeposit\",\"kind\":\"object\",\"type\":\"TimeDeposit\",\"relationName\":\"BankAccountToTimeDeposit\"},{\"name\":\"Transaction\",\"kind\":\"object\",\"type\":\"Transaction\",\"relationName\":\"BankAccountToTransaction\"},{\"name\":\"Income\",\"kind\":\"object\",\"type\":\"Income\",\"relationName\":\"BankAccountToIncome\"},{\"name\":\"Expense\",\"kind\":\"object\",\"type\":\"Expense\",\"relationName\":\"BankAccountToExpense\"},{\"name\":\"Budget\",\"kind\":\"object\",\"type\":\"Budget\",\"relationName\":\"BankAccountToBudget\"},{\"name\":\"BudgetRollover\",\"kind\":\"object\",\"type\":\"BudgetRollover\",\"relationName\":\"BankAccountToBudgetRollover\"},{\"name\":\"accountType\",\"kind\":\"enum\",\"type\":\"AccountType\"},{\"name\":\"resource\",\"kind\":\"object\",\"type\":\"Resource\",\"relationName\":\"BankAccountToResource\"},{\"name\":\"members\",\"kind\":\"object\",\"type\":\"Member\",\"relationName\":\"BankAccountToMember\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"BankAccountToUser\"}],\"dbName\":\"bank_accounts\"},\"Balance\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"accountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"assetId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"currencyIso\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"BalanceType\"},{\"name\":\"originalPayload\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"bankAccount\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BalanceToBankAccount\"},{\"name\":\"asset\",\"kind\":\"object\",\"type\":\"Asset\",\"relationName\":\"AssetToBalance\"},{\"name\":\"currency\",\"kind\":\"object\",\"type\":\"Currency\",\"relationName\":\"BalanceToCurrency\"}],\"dbName\":null},\"TimeDeposit\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"accountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"assetId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"categoryId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"currencyIso\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"certificateNo\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"period\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"interestRate\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"TimeDepositType\"},{\"name\":\"originalPayload\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"bankAccount\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToTimeDeposit\"},{\"name\":\"asset\",\"kind\":\"object\",\"type\":\"Asset\",\"relationName\":\"AssetToTimeDeposit\"},{\"name\":\"currency\",\"kind\":\"object\",\"type\":\"Currency\",\"relationName\":\"CurrencyToTimeDeposit\"},{\"name\":\"category\",\"kind\":\"object\",\"type\":\"Category\",\"relationName\":\"CategoryToTimeDeposit\"},{\"name\":\"categoryValidated\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"suggestedCategoryId\",\"kind\":\"scalar\",\"type\":\"String\"}],\"dbName\":null},\"Currency\":{\"fields\":[{\"name\":\"iso\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"symbol\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"numericCode\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"Balance\",\"kind\":\"object\",\"type\":\"Balance\",\"relationName\":\"BalanceToCurrency\"},{\"name\":\"Transaction\",\"kind\":\"object\",\"type\":\"Transaction\",\"relationName\":\"CurrencyToTransaction\"},{\"name\":\"TimeDeposit\",\"kind\":\"object\",\"type\":\"TimeDeposit\",\"relationName\":\"CurrencyToTimeDeposit\"}],\"dbName\":null},\"Asset\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"originalPayload\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"Balance\",\"kind\":\"object\",\"type\":\"Balance\",\"relationName\":\"AssetToBalance\"},{\"name\":\"TimeDeposit\",\"kind\":\"object\",\"type\":\"TimeDeposit\",\"relationName\":\"AssetToTimeDeposit\"},{\"name\":\"Transaction\",\"kind\":\"object\",\"type\":\"Transaction\",\"relationName\":\"AssetToTransaction\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"AssetType\"},{\"name\":\"value\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"purchaseDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"AssetToUser\"},{\"name\":\"transactions\",\"kind\":\"object\",\"type\":\"AssetTransaction\",\"relationName\":\"AssetToAssetTransaction\"},{\"name\":\"valuations\",\"kind\":\"object\",\"type\":\"AssetValuation\",\"relationName\":\"AssetToAssetValuation\"}],\"dbName\":\"assets\"},\"AssetValuation\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"value\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"assetId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"asset\",\"kind\":\"object\",\"type\":\"Asset\",\"relationName\":\"AssetToAssetValuation\"}],\"dbName\":\"asset_valuations\"},\"AssetTransaction\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"TransactionType\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"assetId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"asset\",\"kind\":\"object\",\"type\":\"Asset\",\"relationName\":\"AssetToAssetTransaction\"}],\"dbName\":\"asset_transactions\"},\"Transaction\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"accountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"assetId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"TransactionToUser\"},{\"name\":\"currencyIso\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"categoryId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"originalPayload\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"review\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"bankAccount\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToTransaction\"},{\"name\":\"asset\",\"kind\":\"object\",\"type\":\"Asset\",\"relationName\":\"AssetToTransaction\"},{\"name\":\"category\",\"kind\":\"object\",\"type\":\"Category\",\"relationName\":\"CategoryToTransaction\"},{\"name\":\"currency\",\"kind\":\"object\",\"type\":\"Currency\",\"relationName\":\"CurrencyToTransaction\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"categoryValidated\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"suggestedCategoryId\",\"kind\":\"scalar\",\"type\":\"String\"}],\"dbName\":null},\"Income\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"categoryId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"category\",\"kind\":\"object\",\"type\":\"Category\",\"relationName\":\"CategoryToIncome\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"IncomeToUser\"},{\"name\":\"accountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"bankAccount\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToIncome\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"categoryValidated\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"suggestedCategoryId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"importHash\",\"kind\":\"scalar\",\"type\":\"String\"}],\"dbName\":null},\"Expense\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"categoryId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"category\",\"kind\":\"object\",\"type\":\"Category\",\"relationName\":\"CategoryToExpense\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"ExpenseToUser\"},{\"name\":\"accountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"bankAccount\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToExpense\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"categoryValidated\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"suggestedCategoryId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"importHash\",\"kind\":\"scalar\",\"type\":\"String\"}],\"dbName\":null},\"SpreadsheetImport\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"importedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"fileName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"sheetName\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"rowCount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"importHash\",\"kind\":\"scalar\",\"type\":\"String\"}],\"dbName\":null},\"Category\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"icon\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"color\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"keywords\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"CategoryType\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"CategoryToUser\"},{\"name\":\"transactions\",\"kind\":\"object\",\"type\":\"Transaction\",\"relationName\":\"CategoryToTransaction\"},{\"name\":\"budgets\",\"kind\":\"object\",\"type\":\"CategoryBudget\",\"relationName\":\"CategoryToCategoryBudget\"},{\"name\":\"incomes\",\"kind\":\"object\",\"type\":\"Income\",\"relationName\":\"CategoryToIncome\"},{\"name\":\"expenses\",\"kind\":\"object\",\"type\":\"Expense\",\"relationName\":\"CategoryToExpense\"},{\"name\":\"budgetRollovers\",\"kind\":\"object\",\"type\":\"BudgetRollover\",\"relationName\":\"BudgetRolloverToCategory\"},{\"name\":\"TimeDeposit\",\"kind\":\"object\",\"type\":\"TimeDeposit\",\"relationName\":\"CategoryToTimeDeposit\"}],\"dbName\":\"categories\"},\"BudgetPreferences\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"BudgetPreferencesToUser\"},{\"name\":\"autoCreateNextBudget\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"autoApplyRollovers\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"rolloverThreshold\",\"kind\":\"scalar\",\"type\":\"Decimal\"}],\"dbName\":null},\"Budget\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"startDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"endDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"BudgetToUser\"},{\"name\":\"rolloversPrevious\",\"kind\":\"object\",\"type\":\"BudgetRollover\",\"relationName\":\"PreviousBudget\"},{\"name\":\"rolloversNext\",\"kind\":\"object\",\"type\":\"BudgetRollover\",\"relationName\":\"NextBudget\"},{\"name\":\"categories\",\"kind\":\"object\",\"type\":\"CategoryBudget\",\"relationName\":\"BudgetToCategoryBudget\"},{\"name\":\"accountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"account\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToBudget\"}],\"dbName\":\"budgets\"},\"CategoryBudget\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"budgetId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"categoryId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"budget\",\"kind\":\"object\",\"type\":\"Budget\",\"relationName\":\"BudgetToCategoryBudget\"},{\"name\":\"category\",\"kind\":\"object\",\"type\":\"Category\",\"relationName\":\"CategoryToCategoryBudget\"}],\"dbName\":\"category_budgets\"},\"BudgetRollover\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"BudgetRolloverToUser\"},{\"name\":\"categoryId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"category\",\"kind\":\"object\",\"type\":\"Category\",\"relationName\":\"BudgetRolloverToCategory\"},{\"name\":\"previousBudgetId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"previousBudget\",\"kind\":\"object\",\"type\":\"Budget\",\"relationName\":\"PreviousBudget\"},{\"name\":\"nextBudgetId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"nextBudget\",\"kind\":\"object\",\"type\":\"Budget\",\"relationName\":\"NextBudget\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"periodStart\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"periodEnd\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"rolloverPercentage\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"accountId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"account\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToBudgetRollover\"}],\"dbName\":null},\"ConnectorConfig\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"orgId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"secret\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"env\",\"kind\":\"enum\",\"type\":\"ConnectorEnv\"},{\"name\":\"connectorId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"connector\",\"kind\":\"object\",\"type\":\"Connector\",\"relationName\":\"ConnectorToConnectorConfig\"}],\"dbName\":null},\"Connector\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"logoUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"ConnectorStatus\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"ConnectorType\"},{\"name\":\"connectorConfig\",\"kind\":\"object\",\"type\":\"ConnectorConfig\",\"relationName\":\"ConnectorToConnectorConfig\"},{\"name\":\"Integration\",\"kind\":\"object\",\"type\":\"Integration\",\"relationName\":\"ConnectorToIntegration\"}],\"dbName\":null},\"Integration\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"logoUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"connectorProviderId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"connectorId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"connector\",\"kind\":\"object\",\"type\":\"Connector\",\"relationName\":\"ConnectorToIntegration\"},{\"name\":\"Resource\",\"kind\":\"object\",\"type\":\"Resource\",\"relationName\":\"IntegrationToResource\"}],\"dbName\":null},\"Resource\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"integrationId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"originalId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"integration\",\"kind\":\"object\",\"type\":\"Integration\",\"relationName\":\"IntegrationToResource\"},{\"name\":\"bankAccounts\",\"kind\":\"object\",\"type\":\"BankAccount\",\"relationName\":\"BankAccountToResource\"}],\"dbName\":\"resources\"},\"Stats\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"totalUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"activeUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalAccounts\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalTransactions\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalAssets\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalLiabilities\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalInvestments\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"avgAccountsPerUser\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"avgTransactionsPerUser\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"dailyActiveUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"weeklyActiveUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"monthlyActiveUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"operatingSystem\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"browser\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"country\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"lastUpdated\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":\"stats\"},\"StatsHistory\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"totalUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"activeUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalAccounts\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalTransactions\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalAssets\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalLiabilities\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"totalInvestments\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"dailyActiveUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"weeklyActiveUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"monthlyActiveUsers\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"snapshot\",\"kind\":\"scalar\",\"type\":\"Json\"}],\"dbName\":\"stats_history\"},\"Investment\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"InvestmentType\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"shares\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"purchasePrice\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"currentPrice\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"InvestmentToUser\"},{\"name\":\"transactions\",\"kind\":\"object\",\"type\":\"InvestmentTransaction\",\"relationName\":\"InvestmentToInvestmentTransaction\"},{\"name\":\"valuations\",\"kind\":\"object\",\"type\":\"InvestmentValuation\",\"relationName\":\"InvestmentToInvestmentValuation\"}],\"dbName\":\"investments\"},\"InvestmentValuation\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"value\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"investmentId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"investment\",\"kind\":\"object\",\"type\":\"Investment\",\"relationName\":\"InvestmentToInvestmentValuation\"}],\"dbName\":\"investment_valuations\"},\"InvestmentTransaction\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"InvestmentTransactionType\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"shares\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"price\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"investmentId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"investment\",\"kind\":\"object\",\"type\":\"Investment\",\"relationName\":\"InvestmentToInvestmentTransaction\"}],\"dbName\":\"investment_transactions\"},\"Liability\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"LiabilityType\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"interestRate\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"monthlyPayment\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"startDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"endDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"LiabilityToUser\"},{\"name\":\"payments\",\"kind\":\"object\",\"type\":\"LiabilityPayment\",\"relationName\":\"LiabilityToLiabilityPayment\"}],\"dbName\":\"liabilities\"},\"LiabilityPayment\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"PaymentType\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"liabilityId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"liability\",\"kind\":\"object\",\"type\":\"Liability\",\"relationName\":\"LiabilityToLiabilityPayment\"}],\"dbName\":\"liability_payments\"},\"SavingsGoal\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"target\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"current\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"deadline\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isDefault\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"type\",\"kind\":\"enum\",\"type\":\"GoalType\"},{\"name\":\"priority\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"SavingsGoalToUser\"},{\"name\":\"progress\",\"kind\":\"object\",\"type\":\"SavingsProgress\",\"relationName\":\"SavingsGoalToSavingsProgress\"}],\"dbName\":\"savings_goals\"},\"SavingsProgress\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"amount\",\"kind\":\"scalar\",\"type\":\"Decimal\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"goalId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"goal\",\"kind\":\"object\",\"type\":\"SavingsGoal\",\"relationName\":\"SavingsGoalToSavingsProgress\"}],\"dbName\":\"savings_progress\"},\"ContractAnalysis\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contractText\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"summary\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"recommendations\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"keyClauses\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"legalCompliance\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"negotiationPoints\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contractDuration\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"terminationConditions\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"overallScore\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"performanceMetrics\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"intellectualPropertyClauses\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"version\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"userFeedback\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"customFields\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"expirationDate\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"language\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"filePath\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contractType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"financialTerms\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"specificClauses\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"compensationStructure\",\"kind\":\"object\",\"type\":\"CompensationStructure\",\"relationName\":\"CompensationStructureToContractAnalysis\"},{\"name\":\"opportunities\",\"kind\":\"object\",\"type\":\"Opportunity\",\"relationName\":\"ContractAnalysisToOpportunity\"},{\"name\":\"risks\",\"kind\":\"object\",\"type\":\"Risk\",\"relationName\":\"ContractAnalysisToRisk\"},{\"name\":\"chats\",\"kind\":\"object\",\"type\":\"Chat\",\"relationName\":\"ChatToContractAnalysis\"}],\"dbName\":null},\"Risk\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contractId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"risk\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"explanation\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"severity\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contract\",\"kind\":\"object\",\"type\":\"ContractAnalysis\",\"relationName\":\"ContractAnalysisToRisk\"}],\"dbName\":null},\"Opportunity\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contractId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"opportunity\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"explanation\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"impact\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contract\",\"kind\":\"object\",\"type\":\"ContractAnalysis\",\"relationName\":\"ContractAnalysisToOpportunity\"}],\"dbName\":null},\"CompensationStructure\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contractId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"baseSalary\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"bonuses\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"equity\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"otherBenefits\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contract\",\"kind\":\"object\",\"type\":\"ContractAnalysis\",\"relationName\":\"CompensationStructureToContractAnalysis\"}],\"dbName\":null},\"Speech\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"workspaceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"projectId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"SpeechToUser\"},{\"name\":\"workspace\",\"kind\":\"object\",\"type\":\"Workspace\",\"relationName\":\"SpeechToWorkspace\"},{\"name\":\"project\",\"kind\":\"object\",\"type\":\"Project\",\"relationName\":\"ProjectToSpeech\"},{\"name\":\"recordings\",\"kind\":\"object\",\"type\":\"Recording\",\"relationName\":\"RecordingToSpeech\"},{\"name\":\"transcriptions\",\"kind\":\"object\",\"type\":\"Transcription\",\"relationName\":\"SpeechToTranscription\"},{\"name\":\"analyses\",\"kind\":\"object\",\"type\":\"Analysis\",\"relationName\":\"AnalysisToSpeech\"}],\"dbName\":null},\"Recording\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"speechId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"speech\",\"kind\":\"object\",\"type\":\"Speech\",\"relationName\":\"RecordingToSpeech\"},{\"name\":\"url\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"duration\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Transcription\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"speechId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"speech\",\"kind\":\"object\",\"type\":\"Speech\",\"relationName\":\"SpeechToTranscription\"},{\"name\":\"text\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"language\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Analysis\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"speechId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"speech\",\"kind\":\"object\",\"type\":\"Speech\",\"relationName\":\"AnalysisToSpeech\"},{\"name\":\"summary\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"keyPoints\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"sentiment\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"VectorStore\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"documentId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"documentType\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"content\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"filePath\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Assistant\":{\"fields\":[{\"name\":\"assistant_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"graph_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"config\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"created_at\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updated_at\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"version\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"runs\",\"kind\":\"object\",\"type\":\"Run\",\"relationName\":\"AssistantRuns\"},{\"name\":\"chats\",\"kind\":\"object\",\"type\":\"Chat\",\"relationName\":\"AssistantChats\"}],\"dbName\":null},\"Thread\":{\"fields\":[{\"name\":\"thread_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"created_at\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updated_at\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"ThreadStatus\"},{\"name\":\"values\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"checkpoints\",\"kind\":\"object\",\"type\":\"Checkpoint\",\"relationName\":\"CheckpointToThread\"},{\"name\":\"checkpoint_blobs\",\"kind\":\"object\",\"type\":\"CheckpointBlobs\",\"relationName\":\"CheckpointBlobsToThread\"},{\"name\":\"checkpoint_writes\",\"kind\":\"object\",\"type\":\"CheckpointWrites\",\"relationName\":\"CheckpointWritesToThread\"},{\"name\":\"runs\",\"kind\":\"object\",\"type\":\"Run\",\"relationName\":\"ThreadRuns\"}],\"dbName\":null},\"Run\":{\"fields\":[{\"name\":\"run_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"thread\",\"kind\":\"object\",\"type\":\"Thread\",\"relationName\":\"ThreadRuns\"},{\"name\":\"thread_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"assistant\",\"kind\":\"object\",\"type\":\"Assistant\",\"relationName\":\"AssistantRuns\"},{\"name\":\"assistant_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"created_at\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updated_at\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"status\",\"kind\":\"enum\",\"type\":\"RunStatus\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"multitask_strategy\",\"kind\":\"enum\",\"type\":\"MultitaskStrategy\"}],\"dbName\":null},\"Checkpoint\":{\"fields\":[{\"name\":\"thread\",\"kind\":\"object\",\"type\":\"Thread\",\"relationName\":\"CheckpointToThread\"},{\"name\":\"thread_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"checkpoint_ns\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"checkpoint_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"parent_checkpoint_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"checkpoint\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"}],\"dbName\":null},\"CheckpointBlobs\":{\"fields\":[{\"name\":\"thread\",\"kind\":\"object\",\"type\":\"Thread\",\"relationName\":\"CheckpointBlobsToThread\"},{\"name\":\"thread_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"checkpoint_ns\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"channel\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"version\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"blob\",\"kind\":\"scalar\",\"type\":\"Bytes\"}],\"dbName\":\"checkpoint_blobs\"},\"CheckpointWrites\":{\"fields\":[{\"name\":\"thread\",\"kind\":\"object\",\"type\":\"Thread\",\"relationName\":\"CheckpointWritesToThread\"},{\"name\":\"thread_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"checkpoint_ns\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"checkpoint_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"task_id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"idx\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"channel\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"blob\",\"kind\":\"scalar\",\"type\":\"Bytes\"}],\"dbName\":\"checkpoint_writes\"},\"CheckpointMigrations\":{\"fields\":[{\"name\":\"v\",\"kind\":\"scalar\",\"type\":\"Int\"}],\"dbName\":\"checkpoint_migrations\"},\"Chat\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"assistantId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"contractId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"assistant\",\"kind\":\"object\",\"type\":\"Assistant\",\"relationName\":\"AssistantChats\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"visibility\",\"kind\":\"enum\",\"type\":\"Visibility\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"ChatToUser\"},{\"name\":\"contracts\",\"kind\":\"object\",\"type\":\"ContractAnalysis\",\"relationName\":\"ChatToContractAnalysis\"},{\"name\":\"messages\",\"kind\":\"object\",\"type\":\"Message\",\"relationName\":\"ChatToMessage\"}],\"dbName\":\"Chat\"},\"Message\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"chatId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"role\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"content\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"chat\",\"kind\":\"object\",\"type\":\"Chat\",\"relationName\":\"ChatToMessage\"}],\"dbName\":\"Message\"},\"MemoryStore\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"namespace\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"key\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"value\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"created_at\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updated_at\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Banner\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"BannerToUser\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"type\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"size\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"fontWeight\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"color\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"shared\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = {
  getRuntime: async () => require('./query_engine_bg.js'),
  getQueryEngineWasmModule: async () => {
    const loader = (await import('#wasm-engine-loader')).default
    const engine = (await loader).default
    return engine
  }
}
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)


import { useQuery } from "@tanstack/react-query";
import { getChatsByAssistantId } from "@/actions/chats/actions";

type UseChatProps = {
  assistantId: string
}

export const useGetChats = ({ assistantId }: UseChatProps) => {
  const { data, isLoading, refetch, error } = useQuery({
    queryKey: [
      "chat", assistantId,
    ],
    queryFn: async () => {
      const data = await getChatsByAssistantId({id: assistantId});

      console.log("chat by assistant", data)

      if (!data) {
        throw new Error(`Failed to get chats.`);
      }
      
      return data;
    },
    enabled: <PERSON><PERSON><PERSON>(assistantId),
  });

  return {
    data,
    isLoading,
    refetch,
    error,
  };
};
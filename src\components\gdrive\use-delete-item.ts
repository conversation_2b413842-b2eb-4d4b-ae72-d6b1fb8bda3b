import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';


export function useDeleteItem() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (fileId: string) => {
      const response = await fetch(`/api/gdrive/delete-item`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ fileId }),
      });

      const responseData = await response.json();
      console.log("delete responseData", responseData)

      if (!response.ok || !responseData.success) {
        throw new Error(`Failed to delete item.`);
      }
      
      return responseData;
    },
    onSuccess: ({ data }) => {
      toast.success('Item deleted!');
      queryClient.invalidateQueries({ queryKey: ['googledrive'] });
      queryClient.invalidateQueries({ queryKey: ['googledrive', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete item.');
    },
  });

  return mutation;
}

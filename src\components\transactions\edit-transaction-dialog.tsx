"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { format } from "date-fns";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Loader2 } from "lucide-react";
import { updateTransaction } from "@/actions/transactions/update-transaction";
import { CategorySelect } from "@/components/categories/category-select";

const editTransactionSchema = z.object({
  description: z.string().optional(),
  amount: z.number().min(0, "金額必須大於 0"),
  date: z.date(),
  type: z.string().min(1, "請選擇交易類型"), //z.enum(["CREDIT", "DEBIT"]),
  categoryId: z.string().min(1, "請選擇類別"),
});

type EditTransactionFormValues = z.infer<typeof editTransactionSchema>;

interface EditTransactionDialogProps {
  transaction: {
    id: string;
    description?: string;
    amount: number;
    date: Date;
    type: string; //"CREDIT" | "DEBIT";
    categoryId: string;
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTransactionUpdate: () => Promise<void>;
}

export function EditTransactionDialog({
  transaction,
  open,
  onOpenChange,
  onTransactionUpdate,
}: EditTransactionDialogProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<EditTransactionFormValues>({
    resolver: zodResolver(editTransactionSchema),
    defaultValues: {
      description: transaction.description || "",
      amount: Number(transaction.amount),
      date: new Date(transaction.date),
      type: transaction.type,
      categoryId: transaction.categoryId,
    },
  });

  async function onSubmit(data: EditTransactionFormValues) {
    try {
      setIsLoading(true);
      const result = await updateTransaction({
        id: transaction.id,
        ...data,
      });

      if (!result.success) {
        throw new Error(result.error);
      }

      toast.success("交易更新成功！");
      onOpenChange(false);
      form.reset();
      await onTransactionUpdate();
    } catch (error) {
      toast.error("更新交易失敗");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>修改交易</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>金額</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="輸入金額"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>交易類型</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="選擇類型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="CREDIT">收入</SelectItem>
                          <SelectItem value="DEBIT">支出</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>交易日期</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      value={format(field.value, "yyyy-MM-dd")}
                      onChange={(e) => {
                        const date = new Date(e.target.value);
                        field.onChange(date);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述（選填）</FormLabel>
                  <FormControl>
                    <Input placeholder="輸入交易描述" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>類別</FormLabel>
                  <FormControl>
                    <CategorySelect
                      onSelect={(categoryId) => field.onChange(categoryId)}
                      currentCategoryId={field.value}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    處理中...
                  </>
                ) : (
                  "儲存變更"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
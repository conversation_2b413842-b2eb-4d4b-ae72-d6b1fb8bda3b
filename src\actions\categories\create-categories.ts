"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";

const categorySchema = 
z.object({
  name: z.string().min(2, "Category name must be at least 2 characters"),
  icon: z.string().min(1, "Icon is required"),
  color: z.string().min(1, "Color is required"),
});

const createCategoriesSchema = z.array(categorySchema);

export async function createCategories(
  input: z.infer<typeof createCategoriesSchema>
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized: No user found");
    }

    const validatedFields = createCategoriesSchema.parse(input);
    const results = [];

    for (const category of validatedFields) {
      try {
        // Check for duplicate category
        const existingCategory = await prisma.category.findFirst({
          where: {
            name: category.name,
            userId,
          },
        });

        if (existingCategory) {
          results.push({
            category: category.name,
            success: false,
            error: "Category already exists",
          });
          continue;
        }

        // Create the category
        const createdCategory = await prisma.category.create({
          data: {
            name: category.name,
            icon: category.icon,
            color: category.color,
            userId,
          },
        });

        results.push({
          category: createdCategory.name,
          success: true,
        });
      } catch (error) {
        console.error(`Error creating category ${category.name}:`, error);
        results.push({
          category: category.name,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    revalidatePath("/categories");

    return {
      success: true,
      results,
    };
  } catch (error) {
    console.error("Error in createCategories:", error);
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors };
    }
    if (error instanceof Error) {
      return { success: false, error: error.message };
    }
    return {
      success: false,
      error: "An unexpected error occurred while creating the categories",
    };
  }
}

import { useQueryState, ParserBuilder } from "nuqs";
import { GoogleDriveItem } from "@/components/gdrive/columns";

const parseGdriveItem = {
  parse: (value: string | null): GoogleDriveItem | null => {
    if (!value) return null;
    try {
      return JSON.parse(value) as GoogleDriveItem;
    } catch {
      return null;
    }
  },
  serialize: (value: GoogleDriveItem | null): string | null => {
    if (!value) return null;
    return JSON.stringify(value);
  }
};

export const useGdriveItems = () => {
  const [rawItems, setItems] = useQueryState<GoogleDriveItem[] | null>(
    "gdrive-items",
    {
      parse: (value: string | null) => {
        if (!value) return null;
        try {
          return JSON.parse(value) as GoogleDriveItem[];
        } catch {
          return null;
        }
      },
      serialize: (value: GoogleDriveItem[] | null) => {
        return value ? JSON.stringify(value) : null;
      }
    } as ParserBuilder<GoogleDriveItem[] | null>
  );

  const items = rawItems || [];
  return [items, setItems] as const;
};

export const useGdriveCurrentFolder = () => {
  return useQueryState<GoogleDriveItem | null>(
    "gdrive-current-folder",  // Changed from rename-gdrive-current-folder
    {
      parse: parseGdriveItem.parse,
      serialize: parseGdriveItem.serialize,
    } as ParserBuilder<GoogleDriveItem | null>
  );
};
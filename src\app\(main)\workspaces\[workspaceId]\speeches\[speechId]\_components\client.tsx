"use client"

import { SpeechData } from "@/components/speeches/types";
import { SpeechBreadcrumbs } from "@/components/speeches/speech-breadcrumbs";
import { SpeechOverview } from "@/components/speeches/speech-overview";
import { SpeechDescription } from "@/components/speeches/speech-description";
import { SpeechRecognition } from "@/components/speeches/auto-stop"
import { Separator } from "@/components/ui/separator";
import { useSpeechId } from "@/components/speeches/use-speech-id";

interface SpeechIdClientProps {
  initialValues: SpeechData;
}

export const SpeechIdClient = ({
    initialValues,
}: SpeechIdClientProps) => {
  const speechId = useSpeechId()
  return (
    <div className="flex flex-col">
      <SpeechBreadcrumbs project={initialValues?.project!} speech={initialValues.speech} />
      <Separator className="my-4" />
      <div className="grid grid-col-1 lg:grid-cols-2 gap-4">
        <SpeechRecognition speechId={speechId} />
        <SpeechOverview data={initialValues} />
        {/*<SpeechDescription data={initialValues} />*/}
      </div>
    </div>
  )
}
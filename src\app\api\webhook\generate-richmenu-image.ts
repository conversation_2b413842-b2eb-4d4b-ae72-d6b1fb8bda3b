import { RichMenuType } from './richMenu';
import { createCanvas, GlobalFonts, SKRSContext2D } from '@napi-rs/canvas';
import path from 'path';
import fs from 'fs';

// 字型設定
//GlobalFonts.registerFromPath(path.join(process.cwd(), 'fonts/noto-sans-tc-chinese-traditional-500-normal.woff'), 'Noto Sans TC');

const drawText = (ctx: SKRSContext2D, text: string, x: number, y: number, options?: {
  fontSize?: number;
  color?: string;
  align?: 'left' | 'center' | 'right';
}) => {
  const { fontSize = 36, color = '#000000', align = 'center' } = options || {};
  
  ctx.font = `${fontSize}px "Noto Sans TC"`;
  ctx.fillStyle = color;
  ctx.textAlign = align;
  ctx.fillText(text, x, y);
};

const drawButton = (ctx: SKRSContext2D, text: string, x: number, y: number, width: number, height: number, selected = false) => {
  // 背景
  ctx.fillStyle = selected ? '#6D7278' : '#F8F9FA';
  ctx.fillRect(x, y, width, height);
  
  // 文字
  const textX = x + width / 2;
  const textY = y + height / 2 + 12;
  drawText(ctx, text, textX, textY, {
    color: selected ? '#FFFFFF' : '#000000',
    fontSize: 32
  });
};

const drawMenuTypeButtons = (ctx: SKRSContext2D, selectedType: RichMenuType) => {
  // 上方選單按鈕
  drawButton(ctx, '文字訊息', 11, 17, 820, 280, selectedType === 'message');
  drawButton(ctx, '彈性訊息', 860, 17, 820, 280, selectedType === 'flex');
  drawButton(ctx, '輪播訊息', 1686, 17, 820, 280, selectedType === 'carousel');
};

const drawMainActionButton = (ctx: SKRSContext2D, menuType: RichMenuType) => {
  // 下方主要動作按鈕
  const actionText = menuType === 'message' ? '傳送文字訊息' :
                    menuType === 'flex' ? '傳送彈性訊息' : '傳送輪播訊息';
                    
  drawButton(ctx, actionText, 11, 390, 2453, 563);
};

const drawSeparators = (ctx: SKRSContext2D) => {
  ctx.strokeStyle = '#E9ECEF';
  ctx.lineWidth = 2;
  
  // 垂直分隔線
  ctx.beginPath();
  ctx.moveTo(842, 17);
  ctx.lineTo(842, 297);
  ctx.stroke();
  
  ctx.beginPath();
  ctx.moveTo(1675, 17);
  ctx.lineTo(1675, 297);
  ctx.stroke();
  
  // 水平分隔線
  ctx.beginPath();
  ctx.moveTo(11, 330);
  ctx.lineTo(2464, 330);
  ctx.stroke();
};

export const generateRichMenuImage = async (menuType: RichMenuType = 'message') => {
  // 建立畫布
  const canvas = createCanvas(2500, 843);
  const ctx = canvas.getContext('2d');
  
  // 設定背景色
  ctx.fillStyle = '#FFFFFF';
  ctx.fillRect(0, 0, 2500, 843);
  
  // 繪製選單按鈕
  drawMenuTypeButtons(ctx, menuType);
  
  // 繪製分隔線
  drawSeparators(ctx);
  
  // 繪製主要動作按鈕
  drawMainActionButton(ctx, menuType);
  
  // 確保目錄存在
  const outputDir = path.join(process.cwd(), 'public/images');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // 儲存圖片
  const outputPath = path.join(outputDir, `richmenu-${menuType}.png`);
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(outputPath, buffer);
  
  return outputPath;
};

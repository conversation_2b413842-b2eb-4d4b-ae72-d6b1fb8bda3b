"use client";

import { BannerC<PERSON>, type Banner } from "@/components/banner/banner-creator";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  type UniqueIdentifier,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { ReactNode, useState, useEffect } from "react";
import AccountBalances from "@/components/reports/account-balances";
import AccountTimeDeposits from "./account-time-deposits";
import AccountTransactionsMatrix from "@/components/reports/account-transactions-matrix";
import AccountStatements from "@/components/reports/account-satements";
import { getAccountDetails } from "./get-account-details";
import { getVirtualAccountDetails } from "@/actions/banking/get-virtual-account-details";
import { getAccountBalances } from "@/actions/banking/get-account-balances";
import { getAccountTimeDeposits } from "@/actions/banking/get-account-time-deposits";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  startOfYear,
  endOfYear,
  subYears,
  startOfMonth,
  endOfMonth,
  subMonths,
} from "date-fns";
import { notFound } from "next/navigation";
import { GripVertical, Trash, Pencil, RefreshCw, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";

import { Draggable } from "@/components/draggable";
import { Droppable } from "@/components/droppable";
import { DateRange } from "react-day-picker";
import { startOfDay, endOfDay } from "date-fns";
import { Calendar } from "@/components/ui/calendar";

type SortableItemProps = {
  id: UniqueIdentifier;
  isBanner?: boolean;
  children: ReactNode;
};
const SortableAccountReport = ({
  id,
  children,
  isBanner,
}: SortableItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} className="relative">
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-4 cursor-grab active:cursor-grabbing"
      >
        <GripVertical id="no-print" className="h-4 w-4" />
      </div>
      <div className={`px-2 ${isBanner ? "py-1" : ""}`}>{children}</div>
    </div>
  );
};

// Main Component
type BankAccount = { id: string; name: string; accountType: string }
type AccountReports = {
  reportType: string;
  bankAccounts: { id: string; name: string; accountType: string }[];
  initialBanners?: Banner[];
};

type DroppableAccount = {
  id: UniqueIdentifier;
  name: string;
  accountType: string;
};

type TimeRangeKey = keyof ReturnType<typeof getTimeRanges>;

export function getTimeRanges(reportType: string) {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth();

  const timeRanges = {
    // Year-level ranges
    last_3_year: {
      label: "2 Year before",
      from: startOfYear(subYears(new Date(), 3)),
      to: endOfYear(subYears(new Date(), 3)),
    },
    last_2_year: {
      label: "Year before",
      from: startOfYear(subYears(new Date(), 2)),
      to: endOfYear(subYears(new Date(), 2)),
    },
    last_year: {
      label: "Last Year",
      from: startOfYear(subYears(new Date(), 1)),
      to: endOfYear(subYears(new Date(), 1)),
    },
    this_year: {
      label: "This Year",
      from: startOfYear(new Date()),
      to: endOfMonth(new Date()),
    },

    // Month-level ranges
    last_6_months: {
      label: "6個月前",
      from: startOfMonth(subMonths(new Date(), 6)),
      to: endOfMonth(new Date()),
    },
    two_months_before: {
      label: `上上個月 (${currentYear})`,
      from: startOfMonth(subMonths(new Date(), 2)),
      to: endOfMonth(subMonths(new Date(), 2)),
    },
    last_month: {
      label: `上個月 (${currentYear})`,
      from: startOfMonth(subMonths(new Date(), 1)),
      to: endOfMonth(subMonths(new Date(), 1)),
    },
    this_month: {
      label: `本月 (${currentYear})`,
      from: startOfMonth(new Date()),
      to: endOfMonth(new Date()),
    },

    // Monthly ranges with year context
    last_month_last_year: {
      label: `上個月 (${currentYear - 1})`,
      from: startOfMonth(new Date(currentYear - 1, currentMonth - 1, 1)),
      to: endOfMonth(new Date(currentYear - 1, currentMonth - 1, 1)),
    },
    this_month_last_year: {
      label: `本月 (${currentYear - 1})`,
      from: startOfMonth(new Date(currentYear - 1, currentMonth, 1)),
      to: endOfMonth(new Date(currentYear - 1, currentMonth, 1)),
    },
    last_month_last_2_year: {
      label: `上個月 (${currentYear - 2})`,
      from: startOfMonth(new Date(currentYear - 2, currentMonth - 1, 1)),
      to: endOfMonth(new Date(currentYear - 2, currentMonth - 1, 1)),
    },
    this_month_last_2_year: {
      label: `本月 (${currentYear - 2})`,
      from: startOfMonth(new Date(currentYear - 2, currentMonth, 1)),
      to: endOfMonth(new Date(currentYear - 2, currentMonth, 1)),
    },
  };

  // Filter time ranges based on the report type
  if (reportType === "MONTHLY") {
    return {
      two_months_before: timeRanges.two_months_before,
      last_month: timeRanges.last_month,
      this_month: timeRanges.this_month,
      last_month_last_year: timeRanges.last_month_last_year,
      this_month_last_year: timeRanges.this_month_last_year,
      last_month_last_2_year: timeRanges.last_month_last_2_year,
      this_month_last_2_year: timeRanges.this_month_last_2_year,
    };
  } else if (reportType === "ANNUAL") {
    return {
      last_2_year: timeRanges.last_2_year,
      last_year: timeRanges.last_year,
      this_year: timeRanges.this_year,
      last_6_months: timeRanges.last_6_months,
    };
  }

  return {};
}

export const AccountReports = ({
  reportType,
  bankAccounts,
  initialBanners = [],
}: AccountReports) => {
  const timeRanges = getTimeRanges(reportType!);
  type TimeRangeKey = keyof typeof timeRanges;
  const [timeRange, setTimeRange] = useState<TimeRangeKey>(
    reportType === "ANNUAL" ? "this_year" : "last_month"
  );
  const [customRange, setCustomRange] = useState<DateRange | undefined>();
  const [useCustomRange, setUseCustomRange] = useState(false);
  const [loading, setLoading] = useState(true);
  const [accountData, setAccountData] = useState<Record<string, any>>({});
  const [loadingAccounts, setLoadingAccounts] = useState<Set<string>>(
    new Set()
  );
  const [droppedItems, setDroppedItems] = useState<(Banner | DroppableAccount)[]>(
    () => {
      const accountsForReport = getAccountsForReport(bankAccounts, reportType);
      const initialItems: (Banner | DroppableAccount)[] = [
        //...initialBanners,
        ...accountsForReport
      ];
      return initialItems;
    }
  );

  const [accounts, setAccounts] = useState<typeof bankAccounts>(
    () => getAvailableAccounts(bankAccounts, reportType)
  );
  //const [banners, setBanners] = useState<Banner[]>([]);  
  const [banners, setBanners] = useState<Banner[]>(initialBanners);
  const [editingBanner, setEditingBanner] = useState<Banner | undefined>(undefined);
  const [lastUpdated, setLastUpdated] = useState<Record<string, Date>>({});

  const handleBannerCreate = (banner: Banner) => {
    if (editingBanner) {
      // Replace the edited banner
      setBanners((prev) =>
        prev.map((b) => (b.id === editingBanner.id ? banner : b))
      );
      setEditingBanner(undefined);
    } else {
      setBanners((prev) => [...prev, banner]);
    }
  };

  const handleEditComplete = () => {
    setEditingBanner(undefined);
  };

  // Modified fetchData to use custom range if selected
  const fetchData = async (account: DroppableAccount) => {
    const accountId = account?.id!;
    try {
      setLoadingAccounts((prev) => new Set(prev).add(String(accountId)));
      let startDate: Date, endDate: Date;
      if (useCustomRange && customRange?.from && customRange?.to) {
        startDate = startOfDay(customRange.from);
        endDate = endOfDay(customRange.to);
      } else {
        startDate = timeRanges[timeRange]?.from!;
        endDate = timeRanges[timeRange]?.to!;
      }
      let result: any;
      if (account?.accountType === "VIRTUAL") {
        if (reportType === "ANNUAL") {
          result = await getAccountDetails(
            String(accountId),
            startDate,
            endDate
          );
        } else {
          result = await getVirtualAccountDetails(
            String(accountId),
            startDate,
            endDate
          );
        }
      } else if (account?.accountType === "SAVINGS") {
        result = await getAccountTimeDeposits(
          String(accountId),
          startDate,
          endDate
        );
      } else {
        result = await getAccountBalances(
          String(accountId),
          startDate,
          endDate
        );
      }
      if (!result.success || !result.data) {
        notFound();
        return;
      }
      setAccountData((prevData) => ({
        ...prevData,
        [accountId]: result.data,
      }));
    } catch (error) {
      console.error(`Failed to fetch account details for ${accountId}`, error);
    } finally {
      setLoadingAccounts((prev) => {
        const updated = new Set(prev);
        updated.delete(String(accountId));
        return updated;
      });
      setLastUpdated(prev => ({
        ...prev,
        [accountId]: new Date()
      }));
    }
  };

  const refreshAllAccountData = async () => {
    const accountsInDroppable = droppedItems.filter(
      (item): item is DroppableAccount => !("title" in item)
    );

    if (accountsInDroppable.length === 0) return;

    const accountsToRefresh = getAccountsForReport(
      accountsInDroppable,
      reportType
    );

    if (accountsToRefresh.length === 0) return;

    setAccountData({});
    await Promise.all(accountsToRefresh.map((account) => fetchData(account)));
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const activeId = String(active.id); // Ensure we convert to string
    const overId = String(over.id);

    if (overId === "droppable") {
      const draggedBanner = banners.find((b) => b.id === activeId);
      const draggedAccount = accounts.find((acc) => acc.id === activeId);

      if (draggedBanner) {
        setDroppedItems((prev) => [
          ...prev,
          { ...draggedBanner, id: String(draggedBanner.id) },
        ]);
        setBanners((prev) => prev.filter((b) => b.id !== activeId));
      } else if (draggedAccount) {
        setDroppedItems((prev) => [
          ...prev,
          { ...draggedAccount, id: String(draggedAccount.id) },
        ]);
        setAccounts((prev) => prev.filter((acc) => acc.id !== activeId));
      }
    } else if (overId === "available-accounts") {
      const item = droppedItems.find((item) => item.id === activeId);
      if (!item) return;

      if ("title" in item) {
        setBanners((prev) => [...prev, { ...item, id: String(item.id) }]);
      } else {
        setAccounts((prev) => [...prev, { ...item, id: String(item.id) }]);
      }
      setDroppedItems((prev) => prev.filter((item) => item.id !== activeId));
    } else {
      const oldIndex = droppedItems.findIndex((item) => item.id === activeId);
      const newIndex = droppedItems.findIndex((item) => item.id === overId);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newOrder = [...droppedItems];
        const [movedItem] = newOrder.splice(oldIndex, 1);
        newOrder.splice(newIndex, 0, {
          ...movedItem,
          id: String(movedItem.id),
        });
        setDroppedItems(newOrder);
      }
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  useEffect(() => {
    const startDate = timeRanges[timeRange]?.to || subYears(new Date(), 1);

    // Type guard function to identify accounts
    const isAccount = (
      item: Banner | DroppableAccount
    ): item is DroppableAccount => {
      return !("title" in item);
    };

    // Get all accounts regardless of whether they've been fetched before
    const accounts = droppedItems.filter(isAccount);

    // Fetch data for all accounts when timeRange changes
    if (accounts.length > 0) {
      accounts.forEach((account) => fetchData(account));
    }
  }, [droppedItems, timeRange]);

  useEffect(() => {
    // Update loading state based on `loadingAccounts`
    setLoading(loadingAccounts.size > 0);
  }, [loadingAccounts]);

  useEffect(() => {
    console.log("accountData==>", accountData);
  }, [accountData]);

  if (loading && !droppedItems.length) {
    return <div className="flex justify-center items-center">Loading...</div>;
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <div className="flex flex-col gap-y-4">
        {/* Banner Creation Section */}
        <div id="no-print">
          <div className="p-4 border rounded">
            <h2 className="text-2xl font-bold tracking-tight mb-4">
              Banner Management
            </h2>
            <BannerCreator
              onBannerCreate={handleBannerCreate}
              editBanner={editingBanner}
              onEditComplete={handleEditComplete}
            />

            <h3 className="text-lg font-bold mt-4 mb-2">Available Banners</h3>
            <div className="flex text-sm gap-2 p-2 rounded-md">
              {banners.map((banner) => (
                <Draggable key={banner.id} id={banner.id}>
                  <div
                    style={{ color: banner.color }}
                    className={`group relative rounded px-2 py-1 dark:bg-sky-200/80`}
                  >
                    <div className="absolute top-[-0.5rem] right-0 opacity-0 group-hover:opacity-100 flex gap-1">
                      <Button
                        size={"xs"}
                        variant={"secondary"}
                        onClick={() => setEditingBanner(banner)}
                        className="size-9 rounded-full p-1"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size={"xs"}
                        variant={"secondary"}
                        onClick={async () => {
                          try {
                            const response = await fetch(
                              `/api/banners?id=${banner.id}`,
                              {
                                method: "DELETE",
                              }
                            );

                            if (!response.ok) {
                              throw new Error("Failed to delete banner");
                            }

                            setBanners((prev) =>
                              prev.filter((i) => i.id !== banner.id)
                            );
                          } catch (error) {
                            console.error("Error deleting banner:", error);
                          }
                        }}
                        className="size-9 rounded-full p-1"
                      >
                        <Trash color={"red"} className="h-4 w-4" />
                      </Button>
                    </div>
                    {banner.title}
                  </div>
                </Draggable>
              ))}
            </div>
          </div>
        </div>

        {/* Available Accounts */}
        <div id="no-print">
          <Droppable id="available-accounts">
            <div className="p-4 border rounded">
              <div className="flex flex-row items-center gap-4">
                <h2 className="text-2xl font-bold tracking-tight mr-4 mb-4">
                  Available Accounts
                </h2>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={refreshAllAccountData}
                  disabled={loadingAccounts.size > 0}
                  className="flex items-center gap-2"
                >
                  {loadingAccounts.size > 0 ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                  Refresh All Data
                </Button>
                <Select
                  value={timeRange}
                  onValueChange={(value: TimeRangeKey) => {
                    setTimeRange(value);
                    setUseCustomRange(false);
                  }}
                  disabled={useCustomRange}
                >
                  <SelectTrigger
                    className="h-9 w-[160px] rounded-lg"
                    aria-label="Select a value"
                  >
                    <SelectValue placeholder="Last 1 year" />
                  </SelectTrigger>
                  <SelectContent className="rounded-xl">
                    {Object.entries(timeRanges).map(([key, range]) => (
                      <SelectItem key={key} value={key} className="rounded-lg">
                        {range.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="custom-range-toggle"
                    checked={useCustomRange}
                    onChange={(e) => setUseCustomRange(e.target.checked)}
                    className="mr-1"
                  />
                  <label htmlFor="custom-range-toggle" className="text-sm">
                    Custom Range
                  </label>
                </div>
              </div>
              {useCustomRange && (
                <div className="flex flex-col gap-2 mt-2">
                  <Calendar
                    mode="range"
                    selected={customRange}
                    onSelect={(range) => {
                      setCustomRange(range);
                      // Trigger immediate refresh if range is complete
                      if (range?.from && range?.to) {
                        const accounts = droppedItems.filter(
                          (item) => !("title" in item)
                        );
                        accounts.forEach((account) =>
                          fetchData(account as DroppableAccount)
                        );
                      }
                    }}
                    numberOfMonths={2}
                  />
                  <div className="text-xs text-muted-foreground">
                    {customRange?.from && customRange?.to
                      ? `From: ${customRange.from.toLocaleDateString()} To: ${customRange.to.toLocaleDateString()}`
                      : "Select a start and end date (max 1 year duration)"}
                  </div>
                </div>
              )}
              <div className="flex text-sm gap-2 mt-2">
                {accounts.map((account) => (
                  <Draggable key={account.id} id={account.id}>
                    {account.name}
                  </Draggable>
                ))}
              </div>
            </div>
          </Droppable>
        </div>
        {/* Droppable Area */}
        <Droppable id="droppable">
          <div className="min-h-[200px] border-2 border-black rounded">
            {droppedItems.length > 0 ? (
              <SortableContext
                items={droppedItems.map((item) => item.id)}
                strategy={verticalListSortingStrategy}
              >
                {droppedItems.map((item) => {
                  if ("title" in item) {
                    // Render banner
                    {
                      console.log("item==>", item);
                    }
                    {
                      console.log("banners==>", banners);
                    }
                    return (
                      <SortableAccountReport
                        key={item.id}
                        id={item.id}
                        isBanner
                      >
                        <div className="rounded">
                          <div
                            className={`text-center px-2 py-1 dark:bg-sky-200/80 rounded-md group 
                              ${(item as Banner).size} ${(item as Banner).fontWeight} 
                              ${
                                (item as Banner).type === "title"
                                  ? "font-bold"
                                  : (item as Banner).type === "assignee"
                                    ? "mt-16"
                                    : ""
                              } `}
                            style={{
                              color: (item as Banner).color,
                            }}
                          >
                            <Button
                              variant={"ghost"}
                              onClick={() => {
                                setBanners((prev) => [...prev, item as Banner]);
                                setDroppedItems((prev) =>
                                  prev.filter((i) => i.id !== item.id)
                                );
                              }}
                              className="absolute top-0.5 right-3 opacity-0 group-hover:opacity-100 p-1"
                            >
                              <Trash color={"red"} className="sieze-4" />
                            </Button>
                            {(item as Banner).title}
                          </div>
                        </div>
                      </SortableAccountReport>
                    );
                  } else {
                    // Render account
                    return (
                      <SortableAccountReport key={item.id} id={item.id}>
                        <div id="print-area" className="border-1">
                          <div className="flex flex-row items-center justify-between gap-2">
                            <h4 id="no-print" className="font-bold px-8 py-3">
                              {(item as DroppableAccount).name}
                            </h4>
                            <div
                              id="no-print"
                              className="text-xs text-gray-500 px-1"
                            >
                              資料更新日期:{" "}
                              {lastUpdated[item.id]?.toLocaleString() ||
                                "Never"}
                            </div>
                          </div>

                          <div className="space-y-4">
                            {reportType === "ANNUAL" &&
                              accountData[item.id]?.transactions && (
                                <AccountTransactionsMatrix
                                  dataRange={
                                    useCustomRange &&
                                    customRange?.from &&
                                    customRange?.to
                                      ? {
                                          from: startOfDay(customRange.from),
                                          to: endOfDay(customRange.to),
                                        }
                                      : timeRanges[timeRange]
                                  }
                                  transactions={
                                    accountData[item.id].transactions
                                  }
                                  onTransactionUpdate={async () =>
                                    await fetchData(item as DroppableAccount)
                                  }
                                />
                              )}
                            {reportType === "MONTHLY" &&
                              accountData[item.id]?.transactions && (
                                <AccountStatements
                                  dataRange={
                                    useCustomRange &&
                                    customRange?.from &&
                                    customRange?.to
                                      ? {
                                          from: startOfDay(customRange.from),
                                          to: endOfDay(customRange.to),
                                        }
                                      : timeRanges[timeRange]
                                  }
                                  account={accountData[item.id].account}
                                  transactions={
                                    accountData[item.id].transactions
                                  }
                                  onTransactionUpdate={async () =>
                                    await fetchData(item as DroppableAccount)
                                  }
                                />
                              )}
                            {accountData[item.id]?.timeDeposits && (
                              <AccountTimeDeposits
                                name={(item as DroppableAccount).name}
                                dataRange={
                                  useCustomRange &&
                                  customRange?.from &&
                                  customRange?.to
                                    ? {
                                        from: startOfDay(customRange.from),
                                        to: endOfDay(customRange.to),
                                      }
                                    : timeRanges[timeRange]
                                }
                                timeDeposits={accountData[item.id].timeDeposits}
                              />
                            )}
                            {accountData[item.id]?.balances && (
                              <AccountBalances
                                name={(item as DroppableAccount).name}
                                dataRange={
                                  useCustomRange &&
                                  customRange?.from &&
                                  customRange?.to
                                    ? {
                                        from: startOfDay(customRange.from),
                                        to: endOfDay(customRange.to),
                                      }
                                    : timeRanges[timeRange]
                                }
                                transactions={accountData[item.id].balances}
                              />
                            )}
                          </div>
                        </div>
                      </SortableAccountReport>
                    );
                  }
                })}
              </SortableContext>
            ) : (
              <div className="text-center py-6">
                Drop accounts or banners here
              </div>
            )}
          </div>
        </Droppable>
        <div id="no-print" className="flex flex-rows justify-center gap-2">
          <Button
            size="sm"
            variant="datePicker"
            onClick={refreshAllAccountData}
            disabled={loadingAccounts.size > 0}
            className="flex items-center gap-2"
          >
            {loadingAccounts.size > 0 ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh All Data
          </Button>
          <Button
            size={"sm"}
            variant={"datePicker"}
            onClick={() => {
              const accountsForReport = getAccountsForReport(
                bankAccounts,
                reportType
              );
              const availableAccounts = getAvailableAccounts(
                bankAccounts,
                reportType
              );

              setDroppedItems([...initialBanners, ...accountsForReport]);
              setAccounts(availableAccounts);
              // Move dropped banners back to available banners
              const droppedBanners = droppedItems.filter(
                (item): item is Banner => "title" in item
              );
              setBanners((prev) => [...prev, ...droppedBanners]);
            }}
          >
            Reset
          </Button>
          <Button
            size={"sm"}
            variant={"datePicker"}
            onClick={() => {
              window.print();
            }}
            className="btn btn-primary text-sm"
          >
            Print Report
          </Button>
        </div>
      </div>
    </DndContext>
  );
};


const getAccountsForReport = (accounts: DroppableAccount[], reportType: string) => {
  if (reportType === "ANNUAL") {
    return accounts.filter(account => 
      account.accountType === "VIRTUAL" //|| account.accountType === "SAVINGS"
    );
  }
  return accounts;
};

const getAvailableAccounts = (accounts: BankAccount[], reportType: string) => {
  if (reportType === "ANNUAL") {
    return accounts.filter(account => 
      account.accountType !== "VIRTUAL"// && account.accountType !== "SAVINGS"
    );
  }
  return [];
};
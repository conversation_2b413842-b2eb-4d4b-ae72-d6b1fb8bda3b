'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

export async function getWorkspaces() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }
     
    // Get all workspaces for a user
    const userWorkspaces = await prisma.workspace.findMany({
      where: {
        members: {
          some: {
            userId: userId
          }
        }
      },
      select: {
        id: true,
        name: true,
        members: true
      }
    })

   
    return {
      data: userWorkspaces,
      success: true
    }

  } catch (error) {
    console.error('Error getting workspace:', error)
    return {
      error: "Failed to get workspace.",
      success: false
    }
  }
}
import { useState } from "react";
import { GoogleDriveItem } from "@/components/gdrive/columns";

export const useFileUpload = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

  const handleLocalFileSelect = (file: File) => {
    setSelectedFile(file);
    setIsUploadModalOpen(true);
  };

  const handleGoogleDriveFileSelect = async (item: GoogleDriveItem) => {
    try {
      const response = await fetch(`/api/gdrive/${item.id}/download-item`);
      const blob = await response.blob();
      const convertedFile = new File([blob], item.name, { type: blob.type });
      
      setSelectedFile(convertedFile);
      setIsUploadModalOpen(true);
    } catch (error) {
      console.error('File download error:', error);
    }
  };

  const closeUploadModal = () => {
    setIsUploadModalOpen(false);
    setSelectedFile(null);
  };

  return {
    selectedFile,
    isUploadModalOpen,
    setIsUploadModalOpen,
    handleLocalFileSelect,
    handleGoogleDriveFileSelect,
    closeUploadModal
  };
};
"use client"

import { useEffect } from 'react';
import { useSpeechRecognition } from '@lobehub/tts/react';
import { useSaveSpeechResult } from './use-save-speech-result';
import { Icon, StoryBook, useControls, useCreateStore } from '@lobehub/ui';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Mic, StopCircle } from 'lucide-react';
import { Flexbox } from 'react-layout-kit';

export const SpeechRecognition = ({ speechId }: { speechId: string }) => {
  const store = useCreateStore();
  const { locale }: any = useControls(
    {
      locale: 'zh-TW',
    },
    { store },
  );

  const { text, start, stop, isLoading, formattedTime, url } = useSpeechRecognition(locale, {
    autoStop: false,
  });

  const { mutate, isPending: isSaving } = useSaveSpeechResult();

  useEffect(() => {
    if (!isLoading && text) {
      mutate({ speechId, text, language: 'zh-TW' });
    }
  }, [isLoading, text, speechId, mutate]);

  return (
    <StoryBook levaStore={store}>
      <Flexbox gap={8}>
        {isLoading ? (
          <Button className="w-full flex items-center gap-2" variant="destructive" onClick={stop}>
            <Icon icon={StopCircle} />
            Stop {formattedTime}
          </Button>
        ) : (
          <Button className="w-full flex items-center gap-2" onClick={start}>
            <Icon icon={Mic} />
            Recognition
          </Button>
        )}
        <Textarea placeholder="Recognition result..." value={text} readOnly />
        {url && <audio controls src={url} />}
      </Flexbox>
    </StoryBook>
  );
};
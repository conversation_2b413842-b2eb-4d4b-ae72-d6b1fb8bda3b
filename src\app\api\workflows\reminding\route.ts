import { prisma } from "@/lib/db"; 
import { serve } from "@upstash/workflow/nextjs";
import { sendEmail } from "@/lib/workflow";
import { createOrUpdateWorkflowRun } from "@/actions/reminders/create-or-update-workflowrun";
import { Task, Member, Reminder, TaskStatus } from "@prisma/client";
import { DateTime } from "luxon";

type InitialData = {
  task: Task & {
    assignee: Member & {
      user: {
        id: string; name: string | null; email: string | null; image: string | null; timezone: string;
      };
    };
    reminders: Reminder[];
  };
  assignerName: string;
  assignerEmail: string;
  timezone: string;
}

export const { POST } = serve<InitialData>(async (context) => {
  const { task, assignerName, assignerEmail, timezone } = context.requestPayload;
  
  // Schedule reminders
  const scheduledTimes = new Set<number>();

  for (const reminder of task.reminders) {
    if (!reminder.enabled) continue;
  
    const adjustedReminderDate = calculateReminderDate(reminder, task, timezone);

    const reminderDate = adjustedReminderDate;
    if (!reminderDate) continue;
  
    // Skip if we already scheduled a reminder for this exact time
    if (scheduledTimes.has(reminderDate)) {
      console.log(`Skipping duplicate reminder for ${new Date(reminderDate * 1000).toISOString()}`);
      continue;
    }
  
    scheduledTimes.add(reminderDate);
    console.log(`Scheduling reminder for ${new Date(reminderDate * 1000).toISOString()}`);
    
    // Handle maxDelay exceeded error by breaking down the sleep duration
    const maxDelay = 604800; // Maximum allowed delay (7 days in seconds)
    const currentTime = Math.floor(Date.now() / 1000);
    let delay = reminderDate - currentTime;
    
    if (delay > maxDelay) {
      // If delay exceeds the max allowed, break it down into multiple chunks of maxDelay
      const numberOfDelays = Math.ceil(delay / maxDelay);
      for (let i = 0; i < numberOfDelays; i++) {
        const chunkDelay = Math.min(maxDelay, delay - i * maxDelay);
        const waitUntil = currentTime + i * maxDelay + chunkDelay;
        console.log(`Sleeping until: ${new Date(waitUntil * 1000).toISOString()}`);
        
        await context.sleepUntil(`reminder-${task.id}-${reminder.id}-${i}`, waitUntil);
      }
    } else {
      await context.sleepUntil(`reminder-${task.id}-${reminder.id}`, reminderDate);
    }
    
    await context.run("send-reminder", async () => {
      try {
        const updatedTask = await prisma.task.findUnique({
          where: { id: task.id },
          include: {
            assignee: { include: { user: true } },
            reminders: { where: { id: reminder.id }}
          },
        });

        if (updatedTask && !updatedTask.status.includes(TaskStatus.DONE)) {
          await sendEmail({
            subject: `Task Assigned: ${task.name}`,
            assignerName,
            assignerEmail: assignerEmail ?? "",
            assigneeName: task.assignee.user.name ?? "",
            assigneeEmail: task.assignee.user.email ?? "",
            taskName: task.name,
            startDate: task.startDate ? task.startDate : task.dueDate,
            dueDate: task.dueDate,
            taskDescription: task.description ?? "No description provided",
            timezone: task.assignee.user.timezone ?? "UTC"
          });
        }
      } catch (error) {
        console.error("Failed to send reminder email:", error);
      }

      // Create or update the WorkflowRun record
      if (reminder.id && reminder.workflowRunId) {
        await createOrUpdateWorkflowRun(reminder.id, reminder.workflowRunId, "delivered");
      }
    });
  }
});

export function calculateReminderDate(
  reminder: Reminder,
  task: Task,
  userTimezone: string
): number | undefined {
  let basisDate;

  if (reminder.customDate) {
    basisDate = DateTime.fromISO(reminder.customDate.toISOString(), { zone: "UTC" });
  } else {
    basisDate = DateTime.fromISO(
      reminder.basis === "START_DATE" ? task?.startDate!.toISOString() : task.dueDate.toISOString(),
      { zone: "UTC" }
    ).minus({ days: reminder.daysBefore });
  }

  if (!basisDate.isValid) {
    console.error("Invalid basis date for reminder");
    return undefined;
  }

  // Convert to user's timezone and back to UTC for scheduling
  const adjustedDate = basisDate.setZone(userTimezone).toUTC();

  const now = DateTime.utc().toSeconds();
  const reminderTime = adjustedDate.toSeconds();

  console.log(`Reminder calculation:
    Type: ${reminder.customDate ? "Custom Date" : "Days Before"}
    Target Time: ${adjustedDate.toISO()}
  `);

  // Only schedule reminders in the future
  if (reminderTime <= now) {
    console.log("Reminder date is in the past, skipping");
    return undefined;
  }

  return Math.floor(reminderTime);
}


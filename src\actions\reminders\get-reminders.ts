'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";


export async function getUpcomingReminders(taskId: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return { error: "Unauthorized", success: false };
    }

    const upcomingReminders = await prisma.reminder.findMany({
      where: {
        enabled: true,
        OR: [
          { customDate: { lte: new Date() } },
          {
            basis: "DUE_DATE",
            daysBefore: { gte: 3 }, // Example filter
          },
        ],
      },
      include: {
        task: true,
      },
    });
      

    return {
      data: {
        upcomingReminders
      },
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to get task.",
      success: false,
    };
  }
}

import { currentUser, clerkClient } from "@clerk/nextjs/server";
import { google } from "googleapis";
import { cookies } from 'next/headers';
import config from "@/lib/config/env";

const specialUserId = process.env.QIAOXING_FUHUA_USER_ID!!;
const privateKey = process.env.NEXT_PUBLIC_PRIVATE_KEY;
const clientEmail = process.env.NEXT_PUBLIC_CLIENT_EMAIL;
const serviceAccountClientId =
  process.env.NEXT_PUBLIC_SERVICE_ACCOUNT_CLIENT_ID;

export const authenticateGoogle = () => {
  const auth = new google.auth.GoogleAuth({
    credentials: {
      type: "service_account",
      private_key: privateKey,
      client_email: clientEmail,
      client_id: serviceAccountClientId,
    },
    scopes: "https://www.googleapis.com/auth/drive",
  });

  return auth;
};

export const authenticateGoogleOAuth2 = async () => {
  try {
    const user = await currentUser();
    const userId = user?.id;
    if (!userId) {
      return undefined
    }

    if (userId) {
      const clerkauth = await clerkClient();
      const tokenResponse = await clerkauth.users.getUserOauthAccessToken(
        specialUserId,
        "oauth_google"
      );

      // Extract the access token from the response
      const accessToken = tokenResponse.data[0]?.token;
      if (accessToken) {
        const { clientId, clientSecret } = config.env.google;
        const auth = new google.auth.OAuth2(clientId, clientSecret);
        auth.setCredentials({ access_token: accessToken });
        return auth;
      }
    }


    // Fallback to cookies if Clerk auth fails or user isn't using Google provider
    const cookieStore = await cookies();
    const accessToken = cookieStore.get('access_token')?.value;
    
    if (accessToken) {
      const { clientId, clientSecret } = config.env.google;
      const auth = new google.auth.OAuth2(clientId, clientSecret);
      auth.setCredentials({ access_token: accessToken });
      return auth;
    }

    return null;
  } catch (error) {
    console.error('Error getting Google auth:', error);
    return null;
  }
};

export const authenticateGoogleDrive = async () => {
  try {
    // First try to get token from cookies
    const cookieStore = await cookies();
    const accessToken = cookieStore.get('access_token')?.value;
    
    if (accessToken) {
      const { clientId, clientSecret } = config.env.google;
      const auth = new google.auth.OAuth2(clientId, clientSecret);
      auth.setCredentials({ access_token: accessToken });
      return auth;
    }
    
    // If no token in cookies, try service account
    return authenticateGoogle();
  } catch (error) {
    console.error('Google Drive authentication failed:', error);
    return undefined;
  }
};
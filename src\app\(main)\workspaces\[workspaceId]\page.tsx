import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { getWorkspaceDetails } from "@/actions/workspaces/get-workspace-details";
import { WorkspaceIdClient } from "./_components/client";
import { ProjectAvatar } from "@/components/projects/project-avatar";

interface WorkspaceIdPageProps {
  params: Promise<{
    workspaceId: string;
  }>;
}

const WorkspaceIdPage = async (props: WorkspaceIdPageProps) => {
  const params = await props.params;
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const response = await getWorkspaceDetails(params.workspaceId);
  if (!response.success || !response.data) redirect("/dashboard");
  const workspace = response.data


  return (
    <div className="w-full mx-auto">
      <div className="flex items-center gap-x-1 text-sm mb-4">
        <ProjectAvatar
          name={workspace?.name}
          className="size-4"
          fallbackClassName="text-xs"
        />
        {workspace?.name}          
      </div>
      <WorkspaceIdClient initialValues={workspace}/>
    </div>
  )
}

export default WorkspaceIdPage
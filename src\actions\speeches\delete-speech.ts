'use server';

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { z } from "zod";
import { revalidatePath } from "next/cache";
const deleteSchema = z.object({
  speechId: z.string().min(1),
});

export async function deleteSpeech(input: z.infer<typeof deleteSchema>) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false,
      };
    }

    const validatedFields = deleteSchema.safeParse(input);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false,
      };
    }

    const { speechId } = validatedFields.data;

    // Fetch the speech to ensure it exists and check workspace permissions
    const speech = await prisma.speech.findUnique({
      where: { id: speechId },
      include: {
        workspace: true,
      },
    });

    if (!speech) {
      return {
        error: "Speech not found.",
        success: false,
      };
    }



    // Delete the speech in a transaction
    const deletedSpeech = await prisma.$transaction(async (tx) => {
      return tx.speech.delete({
        where: { id: speechId },
      });
    });

    revalidatePath(`/workspaces`);

    return {
      success: true,
      data: deletedSpeech,
      message: "Speech deleted successfully.",
    };
  } catch (error) {
    console.error("Error deleting speech:", error);
    return {
      error: "Failed to delete speech.",
      success: false,
    };
  }
}

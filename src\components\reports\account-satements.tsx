import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  flexRender,
  CellContext,
  createColumnHelper,
  ColumnFiltersState,
  VisibilityState,
} from "@tanstack/react-table";
import { ReactNode, Fragment, useMemo, useState } from "react";
import { MAX_DATE_RANGE_DAYS } from "@/constants";
import {
  differenceInDays,
  subYears,
  startOfYear,
  endOfYear,
  format,
} from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

type Account = {
  id: string;
  name: string;
  type: string;
  balance: number;
}
type Transaction = {
  id: string;
  date: Date;
  description: string;
  incomeAmount: number;
  expenseAmount: number;
  count: number;
  type: string; // CREDIT or DEBIT
}


interface AccountStatementProps {
  dataRange?: { label?: string; from: Date; to: Date };
  account: Account;
  transactions: Transaction[];
  onTransactionUpdate: () => Promise<void>;
}

// Extend the Balance type to include calculated fields
interface ExtendedBalance extends Transaction {
  previousAmount: number;
  netAmount: number;
}

const columnHelper = createColumnHelper<ExtendedBalance>();

export default function AccountStatements({
  dataRange,
  account,
  transactions,
  onTransactionUpdate,
}: AccountStatementProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from:  dataRange?.from!, //|| startOfYear(subYears(new Date(), 10))
    to: dataRange?.to! //|| endOfYear(new Date()),
  });
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    date: false,
  });
  const [headerHidden, setHeaderHidden] = useState<boolean>(false);
  const [unifiedFilter, setUnifiedFilter] = useState<string>("");
  const [amountFilter, setAmountFilter] = useState<{
    field: string;
    operator: string;
    value: number | null;
  }>({
    field: "netAmount",
    operator: ">=",
    value: null,
  });

  const processedTransactions = useMemo(() => {
    const filteredData = transactions.filter((transaction) => {
      const transactionDate = new Date(transaction.date);
      const withinDateRange =
        transactionDate >=
          (dateRange?.from ?? startOfYear(subYears(new Date(), 1))) &&
        transactionDate <= (dateRange?.to ?? endOfYear(new Date()));

      const matchesUnifiedFilter =
        unifiedFilter === "" ||
        Object.entries(transaction).some(([key, value]) =>
          value?.toString().toLowerCase().includes(unifiedFilter.toLowerCase())
        );

      const matchesAmountFilter = amountFilter.value
        ? (amountFilter.field === "incomeAmount" &&
            (amountFilter.operator === ">="
              ? transaction.incomeAmount >= amountFilter.value
              : amountFilter.operator === "<="
                ? transaction.incomeAmount <= amountFilter.value
                : transaction.incomeAmount === amountFilter.value)) ||
          (amountFilter.field === "expenseAmount" &&
            (amountFilter.operator === ">="
              ? transaction.expenseAmount >= amountFilter.value
              : amountFilter.operator === "<="
                ? transaction.expenseAmount <= amountFilter.value
                : transaction.expenseAmount === amountFilter.value)) ||
          (amountFilter.field === "netAmount" &&
            (amountFilter.operator === ">="
              ? transaction.incomeAmount - transaction.expenseAmount >=
                amountFilter.value
              : amountFilter.operator === "<="
                ? transaction.incomeAmount - transaction.expenseAmount <=
                  amountFilter.value
                : transaction.incomeAmount - transaction.expenseAmount ===
                  amountFilter.value))
        : true;

      return withinDateRange && matchesUnifiedFilter && matchesAmountFilter;
    });

    const incomes = filteredData.filter((t) => t.type === "CREDIT");
    const expenses = filteredData.filter((t) => t.type === "DEBIT");
    const totalIncome = incomes.reduce((sum, t) => sum + t.incomeAmount, 0);
    const totalExpense = expenses.reduce((sum, t) => sum + t.expenseAmount, 0);

    // Create a total row
    const totalRow = {
      id: "total",
      date: new Date(),
      description: "合計金額",
      incomeAmount: totalIncome,
      expenseAmount: totalExpense,
      type: "TOTAL",
      netAmount: totalIncome - totalExpense
    };

    return [...incomes, ...expenses, totalRow];
  }, [transactions, dateRange, unifiedFilter, amountFilter]);

  const columns = useMemo(() => {
    return [
      columnHelper.accessor("date", {
        header: "日期",
        cell: (info) => (
          <TableCell className="min-w-[6rem] text-left px-2 py-0">
            <span className={""}>
              {info.getValue()
                ? format(
                    new Date(info.getValue() as string | Date),
                    "MMM dd, yyyy"
                  )
                : ""}
            </span>
          </TableCell>
        ),
      }),
      columnHelper.accessor("description", {
        header: `[${account?.name}明細]` ,
        cell: (info) =>
          info.row.original.type === "TOTAL" ? (
            <TableCell className="pl-1 py-0 font-bold border-t border-b">{info.getValue()}</TableCell>
          ) : (
            <TableCell className=" line-clamp-1 pl-1 py-0">{info.getValue()}</TableCell>
          ),
      }),
      columnHelper.accessor("incomeAmount", {
        header: "收入",
        cell: (info) => {
          if (info.row.original.type === "DEBIT") {
            return <TableCell className="text-right px-1 py-0" />;
          }
          if (info.row.original.type === "TOTAL") {
            return (
              <TableCell className="text-right px-1 py-0 font-bold border-t border-b">
                <span>
                  {new Intl.NumberFormat("zh-TW", {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                    style: "currency",
                    currency: "TWD",
                  }).format(Number(info.getValue()) || 0)}
                </span>
              </TableCell>
            );
          }
          return (
            <TableCell className="text-right px-1 py-0">
              <span>
                {new Intl.NumberFormat("zh-TW", {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
                  style: "currency",
                  currency: "TWD",
                }).format(Number(info.getValue()) || 0)}
              </span>
            </TableCell>
          );
        },
      }),
      columnHelper.accessor("expenseAmount", {
        header: "支出",
        cell: (info) => {
          if (info.row.original.type === "CREDIT") {
            return <TableCell className="text-right px-1 py-0" />;
          }
          if (info.row.original.type === "TOTAL") {
            return (
              <TableCell className="text-right px-1 py-0 font-bold border-t border-b">
                <span>
                  {new Intl.NumberFormat("zh-TW", {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                    style: "currency",
                    currency: "TWD",
                  }).format(Number(info.getValue()) || 0)}
                </span>
              </TableCell>
            );
          }
          return (
            <TableCell className="text-right px-1 py-0">
              <span>
                {new Intl.NumberFormat("zh-TW", {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
                  style: "currency",
                  currency: "TWD",
                }).format(Number(info.getValue()) || 0)}
              </span>
            </TableCell>
          );
        },
      }),
      columnHelper.accessor("netAmount", {
        header: "本期結餘",
        cell: (info) => {
          const value = Number(info.getValue()) || 0;
          const isNegative = value < 0;
          if (info.row.original.type === "TOTAL") {
            return (
              <TableCell className="text-right pr-2 py-[0.2rem] font-bold border-t border-b">
                 <span className={isNegative ? "text-red-500" : ""}>
                  {new Intl.NumberFormat("zh-TW", {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                    style: "currency",
                    currency: "TWD",
                  }).format(value)}
                </span>
              </TableCell>
            );
          }
          return (
            <TableCell className="text-right px-2 py-0">
              {value !== 0 && (
                <span>
                  {new Intl.NumberFormat("zh-TW", {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                    style: "currency",
                    currency: "TWD",
                  }).format(value)}
                </span>
              )}
            </TableCell>
          );
        },
      }),
    ];
  }, []);

  const table = useReactTable({
    data: processedTransactions as ExtendedBalance[],
    columns,
    state: {
      columnVisibility,
    },
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const toggleHeaderVisibility = () => {
    setHeaderHidden(!headerHidden); // Toggle header visibility
  };

  return (
    <div className="space-y-1">
      <div id="no-print" className="flex gap-2">
        <DateRangePicker
          key={`${dataRange?.from}-${dataRange?.to}`}
          initialDateFrom={dataRange?.from}
          initialDateTo={dataRange?.to}
          showCompare={false}
          onUpdate={(values) => {
            const { from, to } = values.range;
            if (!from || !to) return;
            if (differenceInDays(to, from) > MAX_DATE_RANGE_DAYS) {
              toast.error(`Max range is ${MAX_DATE_RANGE_DAYS} days!`);
              return;
            }
            setDateRange({ from, to });
          }}
        />
        <Input
          placeholder="Search..."
          value={unifiedFilter}
          onChange={(e) => setUnifiedFilter(e.target.value)}
          className="h-8"
        />
        <Select
          value={amountFilter.field}
          onValueChange={(value) =>
            setAmountFilter((prev) => ({ ...prev, field: value }))
          }
        >
          <SelectTrigger className="h-8 w-fit">
            <SelectValue placeholder="Field" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="netAmount">Net</SelectItem>
            <SelectItem value="incomeAmount">Income</SelectItem>
            <SelectItem value="expenseAmount">Expense</SelectItem>
          </SelectContent>
        </Select>
        <Select
          value={amountFilter.operator}
          onValueChange={(value) =>
            setAmountFilter((prev) => ({ ...prev, operator: value }))
          }
        >
          <SelectTrigger className="h-8 w-fit">
            <SelectValue placeholder="Operator" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="=">=</SelectItem>
            <SelectItem value=">=">{`>=`}</SelectItem>
            <SelectItem value="<=">{`<=`}</SelectItem>
          </SelectContent>
        </Select>
        <Input
          type="number"
          placeholder="Amount"
          value={amountFilter.value ?? ""}
          onChange={(e) =>
            setAmountFilter((prev) => ({
              ...prev,
              value: e.target.value ? parseFloat(e.target.value) : null,
            }))
          }
          className="h-8 w-32"
        />
        <DropdownMenu>
          <DropdownMenuTrigger className="h-8" asChild>
            <Button variant="outline">Columns</Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table.getAllLeafColumns().map((column) => (
              <DropdownMenuCheckboxItem
                key={column.id}
                className="capitalize"
                checked={column.getIsVisible()}
                onCheckedChange={(value) => column.toggleVisibility(!!value)}
              >
                {column.id}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        {/* Toggle Header Visibility Button */}
        <Button
          variant={"outline"}
          className="h-8"
          type="button"
          onClick={toggleHeaderVisibility}
        >
          {headerHidden ? "Show Header" : "Hide Header"}
        </Button>
      </div>
      <div className="border-none space-y-2">
        <Table className='text-[18px]'>
          <TableHeader
            style={{ display: headerHidden ? "none" : "table-header-group" }}
          >
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className={cn(
                      "h-8 p-1 border-t border-b text-black bg-green-100",
                        (header.id === "date" && "pl-1") ||
                        (header.id === "description" && "w-[20rem] text-left pl-1") ||
                        (header.id === "incomeAmount" && "w-[16rem] text-right") ||
                        (header.id === "expenseAmount" && "w-[16rem] text-right") ||
                        (header.id === "netAmount" && "w-[16rem] text-right pr-2")

                    )}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                    {header.column.getIsSorted() && (
                      <span>
                        {header.column.getIsSorted() === "asc" ? " ↑" : " ↓"}
                      </span>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="text-black bg-white">
            {table.getFilteredRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Fragment key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell as (
                        info: CellContext<ExtendedBalance, any>
                      ) => ReactNode,
                      cell.getContext()
                    )}
                  </Fragment>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

"use server"
import { auth } from "@clerk/nextjs/server"
import { prisma } from "@/lib/db";
import { ArtifactV3 } from "@badget/shared/types";
import { ALL_MODEL_NAMES } from "@badget/shared/models";
import { CustomModelConfig } from "@badget/shared/types";
import { cookies } from "next/headers";
import { HAS_EMPTY_THREADS_CLEARED_COOKIE } from "@/constants";
import { HumanMessage, AIMessageChunk, BaseMessage } from "@langchain/core/messages";


export async function findThreads(take?: number, skip?: number) {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  try {
    const results = await prisma.thread.findMany({
      where: {
        metadata: {
            path: ["clerk_user_id"], // Path to the JSON key
            equals: userId, // Value to match
        },
      },
      take,
      skip, // ✅ This acts as the "offset"
    })

    console.log("results....", results)

    return {
      success: true,
      results,
    };
  } catch (error) {
    console.error(`Error finding thread:`, error);
  }
}

export async function findThread(threadId: string) {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  try {
    const userThread = await prisma.thread.findUnique({
      where: { thread_id: threadId },
    })
    console.log("userThread....", userThread)
    return {
      success: true,
      userThread,
    };
  } catch (error) {
    console.error(`Error finding thread:`, error);
  }
}

export async function createNewThread(modelName: ALL_MODEL_NAMES, modelConfig: CustomModelConfig) {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  try {
    console.log("modelName", modelName)
    console.log("modelConfig", modelConfig)
    const thread = await prisma.thread.create({
      data: {
        status: "idle", // Provide a valid ThreadStatus value
        values: {},
        metadata: {
          clerk_user_id: userId,
          customModelName: modelName,
          modelConfig: {
            ...modelConfig,
            ...(modelConfig.provider === "azure_openai" && {
              azureConfig: modelConfig.azureConfig,
            }),
          },
        },
      },
    });

    return {
      success: true,
      thread,
    };
  } catch (error) {
    console.error(`Error creating thread:`, error);
  }
}

type MessageValue = {id: string, type: string, content:string}
export async function updateThread(
  threadId: string,
  artifactToUpdate?: ArtifactV3, // Now optional
  messages?: MessageValue[]
) {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }

  console.log("messages >>>====mmmmm=====>>", messages)
  try {
    // Fetch the existing thread's values
    const existingThread = await prisma.thread.findUnique({
      where: { thread_id: threadId },
      select: { values: true },
    });

    // Extract existing artifact and messages
    const existingValues = existingThread?.values as unknown as { 
      artifact?: ArtifactV3; 
      messages?: MessageValue[] 
    } || {};
    const existingArtifact: ArtifactV3 = existingValues.artifact ?? { currentIndex: 0, contents: [] };
    const existingMessages: MessageValue[] = existingValues?.messages ?? [];

    // Merge artifact only if artifactToUpdate is provided
    let updatedArtifact: ArtifactV3 = existingArtifact;
    if (artifactToUpdate) {
      const mergedContents = [...existingArtifact.contents];

      for (const newItem of artifactToUpdate.contents) {
        const existingIndex = mergedContents.findIndex((item) => item.index === newItem.index);
        if (existingIndex !== -1) {
          mergedContents[existingIndex] = newItem; // Update existing item
        } else {
          mergedContents.push(newItem); // Append new item
        }
      }

      updatedArtifact = {
        currentIndex: Math.max(existingArtifact.currentIndex, artifactToUpdate.currentIndex),
        contents: mergedContents,
      };
    }

    // Merge messages only if messages are provided
    const updatedMessages = messages ? [...existingMessages, ...messages] : existingMessages;

    // Prepare final values
    const updatedValues = {
      artifact: JSON.parse(JSON.stringify(updatedArtifact)), // Prevent potential Prisma JSON issues
      messages: JSON.parse(JSON.stringify(updatedMessages)),
    };

    // Update the thread in the database
    const updatedThread = await prisma.thread.update({
      where: { thread_id: threadId },
      data: { values: updatedValues },
    });

    return { success: true, updatedThread };
  } catch (error) {
    console.error("Error updating thread:", error);
    throw new Error("Failed to update thread.");
  }
}


export async function removeThread(threadId: string) {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  if (!threadId) {
    throw new Error("Missing required field");
  }

  try {
    await prisma.thread.delete({ where: { thread_id: threadId}})
} catch (error) {
    console.error(`Error deleting assistant:`, error);
  }
}

export async function fetchAndDeleteThreads(currentThreadId: string | undefined) {
  const { userId } = await auth();
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }

  if (!currentThreadId) {
    throw new Error("Missing required field");
  }

  const cookieStore = await cookies();
  const hasBeenClearedCookie = cookieStore.get(HAS_EMPTY_THREADS_CLEARED_COOKIE)?.value;
  if (hasBeenClearedCookie === "true") {
    return { success: true, message: "Already cleared" };
  }

  const processedThreadIds = new Set<string>();

  const processThreads = async (offset = 0) => {
    const userThreads = await prisma.thread.findMany({
      where: {
        metadata: {
          path: ["clerk_user_id"],
          equals: userId,
        },
      },
      take: 100,
      skip: offset,
    });

    const threadsToDelete = userThreads.filter(
      (thread) =>
        !thread.values &&
        thread.thread_id !== currentThreadId &&
        !processedThreadIds.has(thread.thread_id)
    );

    if (threadsToDelete.length > 0) {
      // Create an array of unique thread IDs
      const uniqueThreadIds = Array.from(
        new Set(threadsToDelete.map((thread) => thread.thread_id))
      );

      // Process unique thread IDs in batches of 10
      for (let i = 0; i < uniqueThreadIds.length; i += 10) {
        try {
          await Promise.all(
            uniqueThreadIds.slice(i, i + 10).map(async (threadId) => {
              await prisma.thread.delete({
                where: { thread_id: threadId }
              });
              processedThreadIds.add(threadId);
            })
          );
        } catch (e) {
          console.error("Error deleting threads", e);
        }
      }
    }

    if (userThreads.length === 100) {
      // If we got 100 threads, there might be more, so continue fetching
      await processThreads(offset + 100);
    }
  };

  try {
    await processThreads();
    cookieStore.set(HAS_EMPTY_THREADS_CLEARED_COOKIE, "true");
    return { success: true, count: processedThreadIds.size };
  } catch (e) {
    console.error("Error fetching & deleting threads", e);
    return { success: false, error: e instanceof Error ? e.message : String(e) };
  }
}
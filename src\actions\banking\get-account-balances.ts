"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

interface AccountDetails {
  account: {
    id: string;
    name: string;
    type: string;
    balance: number;
    accountNumber?: string;
    lastUpdated: Date;
  };
  balances: {
    id: string;
    date: Date;
    description: string | null;
    amount: number;
    type: string;
  }[];
}

export async function getAccountBalances(
  accountId: string,
  startDate: Date,
  endDate: Date
): Promise<{ success: boolean; data?: AccountDetails; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const oneMonthBefore = new Date(startDate);
    oneMonthBefore.setDate(1); // Set to first day of month
    oneMonthBefore.setMonth(oneMonthBefore.getMonth() - 2);
    oneMonthBefore.setHours(oneMonthBefore.getHours() + 8);

    // Get account with latest balance
    const account = await prisma.bankAccount.findFirst({
      where: {
        id: accountId,
        userId,
      },
      include: {
        Balance: {
          where: {
            date: {
              gte: new Date(oneMonthBefore),
              lte: endDate
            },
          },
          orderBy: { date: "desc" },
        },
      },
    });

    if (!account) {
      throw new Error("Account not found");
    }

    return {
      success: true,
      data: {
        account: {
          id: account.id,
          name: account.name,
          type: account.accountType,
          balance: account.Balance[0]?.amount || 0,
          accountNumber: account.originalId || undefined,
          lastUpdated: account.Balance[0]?.date || account.updatedAt,
        },
        balances: account.Balance.map((tx) => ({
          id: tx.id,
          description: tx.description,
          date: tx.date,
          amount: Number(tx.amount),
          type: tx.type,
        })),
      },
    };
  } catch (error) {
    console.error("Error in getAccountDetails:", error);
    return {
      success: false,
      error: "Failed to fetch account details",
    };
  }
}

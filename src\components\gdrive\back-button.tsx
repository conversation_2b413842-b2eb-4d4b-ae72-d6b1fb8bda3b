import Link from "next/link";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import styles from "./home.module.css";
import axios from "axios";
import IconUpLevel from "./icon-up-level";
import config from "@/store/config.json";
import { getGoogleToken } from "@/actions/get-google-token";


const BackButton = () => {
  const router = useRouter();
  const fid =
    typeof router.query.fid != "undefined"
      ? router.query.fid
      : config.directory.target_folder;
  const [fparent, setFParent] = useState("");
  // Using the custom hook to get the access token
  
  useEffect(() => {
    const fetchData = async () => {
      const { accessToken } = await getGoogleToken();
      if (!accessToken) {
        throw new Error("Unable to get a valid access token.");
      }
      const response = await axios.get(
        `https://www.googleapis.com/drive/v2/files/${fid}/parents`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      const data = response.data;
      setFParent(data.items[0].id);
    };
    fetchData();
  }, [fid]);

  return (
    <Link
      href={{
        pathname: `/list/[fid]`,
        query: {
          fid: fparent,
          fname: "get me",
        },
      }}
      as={`/list/${fparent}`}
      key={fparent}
    >
      <button
        className={styles.BackButton}
        onClick={() => {
          const container = document.querySelector(".searchContainer");
          if (typeof container != "undefined" && container) {
            container.innerHTML = "";
          }
        }}
      >
        <IconUpLevel />
      </button>
    </Link>
  );
};

export default BackButton;

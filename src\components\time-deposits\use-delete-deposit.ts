import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteTimeDeposit } from "@/actions/time-deposits/delete-deposit";
import { toast } from 'sonner';


export function useDeleteDeposit() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (timeDepositId: string) => {
      const response = await deleteTimeDeposit({ timeDepositId });
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to delete deposit.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Deposit deleted!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ["banking"] });
      queryClient.invalidateQueries({ queryKey: ['deposites'] });
      queryClient.invalidateQueries({ queryKey: ['deposit', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete deposit.');
    },
  });

  return mutation;
}

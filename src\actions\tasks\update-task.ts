'use server'
import { after } from 'next/server'
import { currentUser, auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { revalidatePath } from "next/cache"
import { z } from "zod"
import { updateTaskSchema } from "@/components/tasks/schemas"
import { <PERSON>mind<PERSON>, Reminder<PERSON><PERSON> } from "@prisma/client";
import { updateRemindersWithWorkflows } from "@/actions/reminders/update-reminders-with-workflows"; // Import your helper function

export async function updateTask(values: z.infer<typeof updateTaskSchema>) {
  try {
    const user = await currentUser();
    const userId = user?.id
    const assignerName = user?.firstName + " " + user?.lastName
    const assignerEmail = user?.emailAddresses[0].emailAddress
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false,
      };
    }

    const validatedFields = updateTaskSchema.safeParse(values);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false,
      };
    }

    const { id, name, status, projectId, assigneeId, startDate, dueDate, description, reminders } =
      validatedFields.data;

    const existingTask = await prisma.task.findUnique({
      where: { id },
    });

    // Check if creator is a member of the workspace
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId: existingTask?.workspaceId },
      select: { user: { select: { timezone: true } }}
    });

    if (!member) {
      return {
        error: "Unauthorized",
        success: false,
      };
    }

    const timezone = member.user.timezone ?? "UTC";

    const task = await prisma.$transaction(async (tx) => {
      // Only update reminders if they are provided in the update
      let updatedReminders: Reminder[] = [];
      if (reminders !== undefined) {
        const existingReminders = await tx.reminder.findMany({
          where: { taskId: id },
        });

        // Delete reminders that are no longer present
        const remindersToDelete = existingReminders.filter((existing) =>
          !reminders.some(
            (newReminder) =>
              newReminder.id && newReminder.id === existing.id
          )
        );

        if (remindersToDelete.length > 0) {
          await tx.reminder.deleteMany({
            where: {
              id: {
                in: remindersToDelete.map((r) => r.id),
              },
            },
          });
        }

        // Update or create reminders
        updatedReminders = [];
        for (const reminder of reminders) {
          const reminderData = {
            enabled: reminder.enabled,
            basis: reminder.basis as ReminderBasis,
            daysBefore: reminder.daysBefore ?? 1,
            customDate: reminder.customDate
              ? new Date(reminder.customDate).toISOString()
              : null,
          };

          if (reminder.id) {
            const updatedReminder = await tx.reminder.update({
              where: { id: reminder.id },
              data: reminderData,
            });
            updatedReminders.push(updatedReminder);
          } else {
            const newReminder = await tx.reminder.create({
              data: {
                taskId: id,
                ...reminderData,
              },
            });
            updatedReminders.push(newReminder);
          }
        }
      }

      // Update the task
      const updatedTask = await tx.task.update({
        where: { id },
        data: {
          name,
          status,
          projectId,
          startDate: startDate ? new Date(startDate).toISOString() : null,
          dueDate: new Date(dueDate).toISOString(),
          assigneeId,
          description,
        },
        include: {
          reminders: true,
        },
      });

      return { updatedTask, updatedReminders };
    });

    if (!task) {
      return {
        error: "Failed to update task.",
        success: false,
      };
    }

    //after(async () => {
      // Update workflows after the transaction to prevent cascading failures
      if (reminders !== undefined) {
        await updateRemindersWithWorkflows(id, task.updatedTask, task.updatedReminders, assignerName, assignerEmail!, timezone);
      }
    //});

    revalidatePath("/workspaces");

    return {
      data: task.updatedTask,
      success: true,
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to update task.",
      success: false,
      details: error instanceof Error ? error.stack : undefined,
    };
  }
}




export async function updateTaskDescription(id: string, data: Partial<z.infer<typeof updateTaskSchema>>) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    const existingTask = await prisma.task.findUnique({
      where: {
        id,
      },
    })
    

    // Check if creator is a member of the workspace
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId: existingTask?.workspaceId },
    });

    if (!member) {
      return {
        error: "Unauthorized",
        success: false
      }
    }

    const task = await prisma.task.update({
      where: {
        id,
      },
      data: {
        description: data.description,
      }
    })

    revalidatePath('/workspaces')

    return {
      data: task,
      success: true
    }
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to update task description.",
      success: false,
      details: error instanceof Error ? error.stack : undefined
    }
  }
}
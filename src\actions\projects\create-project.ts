'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache"
import { z } from "zod"
import { createProjectSchema } from "@/components/projects/schemas";


export async function createProject(values: z.infer<typeof createProjectSchema>) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }
    const validatedFields = createProjectSchema.safeParse(values)
    
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        success: false
      }
    }
console.log("values=========>", values)
    const { name, image, workspaceId } = validatedFields.data
    
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId },
    });
  
    if (!member) {
      return {
        error: "Unauthorized",
        success: false
      }
    }

    const project = await prisma.project.create({
      data: {
        name,
        userId,
        workspaceId,
      }
    })

    revalidatePath('/projects')
    
    return {
      data: project,
      success: true
    }

  } catch (error) {
    console.error('Error creating project:', error)
    return {
      error: "Failed to create project.",
      success: false
    }
  }
}
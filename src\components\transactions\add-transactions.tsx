"use client";

import { BankAccount, Category } from "@prisma/client";
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray, SubmitHandler } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { format, parse, isValid, startOfDay } from "date-fns";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Loader2, Plus } from "lucide-react";
import { createTransactions } from "@/actions/transactions/create-transaction";
import { CreateCategoryDialog } from "@/components/categories/create-categories";


const transactionSchema = z.object({
  type: z.enum(["CREDIT", "DEBIT"], { message: "Please select a valid type" }),
  amount: z.number().min(1, "Amount is required"),
  categoryId: z.string().min(3, "Please select a category"),
  accountId: z.string().min(1, "Please select an account"),
  description: z.string().optional(),
  transactionDate: z
    .union([
      z.date(),
      z.string().transform((val) => {
        if (val === '') return startOfDay(new Date()); // Default to today
        const parsedDate = parse(val, 'yyyy-MM-dd', new Date());
        if (isValid(parsedDate) && parsedDate <= new Date()) {
          return startOfDay(parsedDate);
        }
        throw new Error("Invalid date format");
      })
    ])
    .optional()
    .default(startOfDay(new Date())), // Set default to today
});

type TransactionFormValues = z.infer<typeof transactionSchema>;
type AccountData = Pick<BankAccount, 'id' | 'name' | 'accountType'>
type CategoryData = Pick<Category, 'id' | 'name'>

export function AddTransactionComponent({ 
  categories, 
  accounts 
}: { 
  categories: CategoryData[], 
  accounts: AccountData[] 
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm<{ transactions: TransactionFormValues[] }>({
    resolver: zodResolver(z.object({
      transactions: z.array(transactionSchema)
    })),
    defaultValues: {
      transactions: [{ 
        type: "DEBIT", 
        amount: 0, 
        categoryId: "", 
        accountId: "",
        description: "",
        transactionDate: startOfDay(new Date()),
      }],
    }
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "transactions",
  });

  const onSubmit: SubmitHandler<{ transactions: TransactionFormValues[] }> = async (formData) => {
    try {
      setIsLoading(true);
  
      // Call the server-side function to create multiple transactions
      const result = await createTransactions({
        transactions: formData.transactions.map((transaction) => ({
          accountId: transaction.accountId,
          description: transaction?.description ?? "",
          amount: transaction.amount,
          type: transaction.type,
          categoryId: transaction.categoryId,
          currencyIso: "TWD",
          date: transaction.transactionDate || startOfDay(new Date()),
        })),
      });
  
      if (!result.success) {
        toast.error(result.error);
        return;
      }
  
      toast.success("Transactions created successfully!");
      setIsDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error in onSubmit:", error);
      toast.error("Failed to create transactions");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Plus className="h-4 w-4" /> Add Transaction
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-screen sm:max-w-[800px] overflow-auto">
        <DialogHeader>
          <DialogTitle className="my-4">Add New Transaction</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="space-y-4 border-b pb-4">
                {/* Transaction Fields */}
                <div className="grid grid-cols-3 gap-4">
                  {/* Amount Field */}
                  <FormField 
                    control={form.control} 
                    name={`transactions.${index}.amount`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter amount"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* Transaction Type Dropdown */}
                  <FormField 
                    control={form.control} 
                    name={`transactions.${index}.type`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Transaction Type</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="CREDIT">Credit</SelectItem>
                              <SelectItem value="DEBIT">Debit</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                  {/* Transaction Date */}
                  <FormField
                    control={form.control}
                    name={`transactions.${index}.transactionDate`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Transaction Date</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            {...field}
                            value={
                              field.value 
                                ? format(field.value, 'yyyy-MM-dd') 
                                : format(new Date(), 'yyyy-MM-dd') // Fallback to today
                            }
                            onChange={(e) => {
                              const inputDate = e.target.value;
                              
                              const parsedDate = parse(inputDate, 'yyyy-MM-dd', new Date());
                              
                              if (isValid(parsedDate) && parsedDate <= new Date()) {
                                field.onChange(startOfDay(parsedDate));
                              } else {
                                // If invalid, reset to today
                                field.onChange(startOfDay(new Date()));
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Category Dropdown */}
                  <FormField
                    control={form.control} 
                    name={`transactions.${index}.categoryId`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel><div className="h-8">Category <CreateCategoryDialog /></div></FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((category) => (
                                <SelectItem key={category.id} value={category.id}>
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* Account Dropdown */}
                  <FormField 
                    control={form.control} 
                    name={`transactions.${index}.accountId`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel><div className="h-8 flex items-end">Bank Account</div></FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="Select account" />
                            </SelectTrigger>
                            <SelectContent>
                              {accounts.map((account) => (
                                <SelectItem key={account.id} value={account.id}>
                                  {account.name} ({account.accountType || "Unknown"})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                </div>
                {/* Description Field */}
                <FormField 
                  control={form.control} 
                  name={`transactions.${index}.description`} 
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Add details about the transaction"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} 
                />

                {/* Remove Transaction Button */}
                {fields.length > 1 && (
                  <Button 
                    type="button" 
                    variant="destructive" 
                    size="sm"
                    onClick={() => remove(index)}
                  >
                    Remove Transaction
                  </Button>
                )}
              </div>
            ))}
            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => append({ type: "CREDIT", amount: 0, categoryId: "", accountId: "" })}
              >
                <Plus className="mr-2 h-4 w-4" /> Add More Transaction
              </Button>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  "Save Transactions"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
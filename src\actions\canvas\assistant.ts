"use server"
import { auth } from "@clerk/nextjs/server"
import { prisma } from "@/lib/db";
import { AssistantTool } from "@/contexts/AssistantContext";
import { ContextDocument } from "@badget/shared/types";

export async function findAssistants() {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  try {
    const results = await prisma.assistant.findMany({
      where: {
        metadata: {
            path: ["user_id"], // Path to the JSON key
            equals: userId, // Value to match
        },
      }
    })

    return {
      success: true,
      results,
    };
  } catch (error) {
    console.error(`Error finding assistant:`, error);
  }
}

export async function findAssistant() {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  try {
    const userAssistants = await prisma.assistant.findMany({
      where: {
        graph_id: "agent",
        metadata: {
          path: ["user_id"], // Path to the JSON key
          equals: userId, // Value to match
        },
      },
      take: 100
    })

    return {
      success: true,
      userAssistants,
    };
  } catch (error) {
    console.error(`Error finding assistant:`, error);
  }
}

export async function createAssistant(
  newAssistant: {
    graphId?: string,
    tools: AssistantTool[] | undefined,
    systemPrompt: string | undefined,
    name: string,
    documents: ContextDocument[] | undefined ,
    metadata: Record<string, any>,    
  }

) {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }

  const { graphId, tools, systemPrompt, name, documents, metadata } =
 newAssistant;

  try {
    const createdAssistant = await prisma.assistant.create({
      data: {
        graph_id: graphId || "agent",
        name,
        metadata: {
            user_id: userId,
            ...metadata,
        },
        config: {
          configurable: {
          tools,
            systemPrompt,
            documents,
          },
        },
      },
    });

    return {
      success: true,
      createdAssistant,
    };
  } catch (error) {
    console.error(`Error creating assistant:`, error);
  }
}

export async function updateAssistant(
  assistantId: string,
  editAssistant: {
    tools: AssistantTool[] | undefined,
    systemPrompt: string | undefined,
    name: string,
    documents: ContextDocument[] | undefined ,
    metadata: Record<string, any>,
  }
) {
  const { userId } = await auth()
  if (!userId) {
    throw new Error("Unauthorized: No user found");
  }
  const { tools, systemPrompt, name, documents, metadata } =
    editAssistant;

  try {
    const updatedAssistant = await prisma.assistant.update({
      where: {
        assistant_id: assistantId
      },
      data: {
        name,
        graph_id: "agent",
        metadata: {
          user_id: userId,
          ...metadata,
        },
        config: {
          configurable: {
            tools,
            systemPrompt,
            documents,
          },
        },
      }
    })

    return {
      success: true,
      updatedAssistant,
    };
  } catch (error) {
    console.error(`Error creating assistant:`, error);
  }
}

export async function removeAssistant(assistantId: string) {
  const { userId } = await auth()
    if (!userId) {
      throw new Error("Unauthorized: No user found");
  }
  try {
    await prisma.assistant.delete({ where: { assistant_id: assistantId}})
} catch (error) {
    console.error(`Error deleting assistant:`, error);
  }
}
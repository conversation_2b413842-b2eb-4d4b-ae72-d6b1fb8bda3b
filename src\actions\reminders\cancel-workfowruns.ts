import { workflowClient } from "@/lib/workflow";

export async function cancelWorkflowRuns(workflowRunIds: string[]) {
  try {
    await workflowClient.cancel({ ids: workflowRunIds });
    console.log("Successfully canceled workflow runs:", workflowRunIds);
  } catch (error) {
    console.error("Failed to cancel workflow runs:", workflowRunIds, error);
    throw new Error("Failed to cancel associated workflows.");
  }
}

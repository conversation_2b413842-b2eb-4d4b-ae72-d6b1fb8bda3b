import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { getSpeech } from "@/actions/speeches/get-speech";
import { SpeechIdClient } from "./_components/client";


interface SpeechIdPageProps {
  params: Promise<{
    workspaceId: string;
    speechId: string;
  }>;
}

export default async function SpeechIdPage(props: SpeechIdPageProps) {
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const params = await props.params;
  const response = await getSpeech(params.speechId);

  if ('error' in response) {
    redirect(`/workspaces/${params.workspaceId}/speeches`);
  }
  
  return (
    <div className="flex flex-col gap-y-2">
      <SpeechIdClient initialValues={response.data} />
    </div>
  )
}
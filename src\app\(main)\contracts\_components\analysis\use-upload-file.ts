import { useState } from "react"
import { useMutation } from "@tanstack/react-query"

interface UploadFileOptions {
  onUploadComplete?: () => void
  onError?: (error: Error) => void
}

export type UploadStep = "upload" | "detecting" | "confirm" | "processing" | "done"

export function useUploadFile(options: UploadFileOptions = {}) {
  const [file, setFile] = useState<File | null>(null)
  const [detectedType, setDetectedType] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [step, setStep] = useState<UploadStep>("upload")

  const { mutate: detectContractType, isPending: isDetecting } = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData()
      formData.append("contract", file)

      const response = await fetch(`/api/contracts/detect-type`, {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Failed to detect contract type")
      }

      const data = await response.json()
      return data.detectedType
    },
    onSuccess: (data: string) => {
      setDetectedType(data)
      setStep("confirm")
    },
    onError: (error) => {
      console.error(error)
      setError("Failed to detect contract type")
      setStep("upload")
      options.onError?.(error as Error)
    },
  })

  const { mutate: uploadFile, isPending: isProcessing } = useMutation({
    mutationFn: async ({
      file,
      contractType,
    }: {
      file: File
      contractType: string
    }) => {
      const formData = new FormData()
      formData.append("contract", file)
      formData.append("contractType", contractType)

      const response = await fetch(`/api/contracts/analyze`, {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Failed to upload contract")
      }

      return response.json()
    },
    onSuccess: (data) => {
      setStep("done")
      options.onUploadComplete?.()
    },
    onError: (error) => {
      console.error(error)
      setError("Failed to upload contract")
      setStep("upload")
      options.onError?.(error as Error)
    },
  })

  const handleFileSelect = (selectedFile: File | null) => {
    setFile(selectedFile)
    setError(null)
    setStep("upload")
  }

  const handleFileUpload = () => {
    if (file) {
      setStep("detecting")
      detectContractType(file)
    }
  }

  const handleAnalyzeContract = () => {
    if (file && detectedType) {
      setStep("processing")
      uploadFile({ file, contractType: detectedType })
    }
  }

  const reset = () => {
    setFile(null)
    setDetectedType(null)
    setError(null)
    setStep("upload")
  }

  return {
    file,
    detectedType,
    error,
    step,
    isDetecting,
    isProcessing,
    handleFileSelect,
    handleFileUpload,
    handleAnalyzeContract,
    reset,
  }
}


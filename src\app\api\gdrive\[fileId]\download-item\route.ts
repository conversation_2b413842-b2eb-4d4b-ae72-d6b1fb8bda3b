import { google } from "googleapis";
import { NextRequest, NextResponse } from "next/server";
import { authenticateGoogleOAuth2 } from "../../authenticate";

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ fileId: string }> }
  ) {
    try {
      const params = await props.params;
      const { fileId } = params;
    if (!fileId) {
      return NextResponse.json(
        { error: "File ID is required" },
        { status: 400 }
      );
    }

    const auth = await authenticateGoogleOAuth2();
    if (!auth) {
      throw new Error("Authentication failed");
    }
    const drive = google.drive({ version: "v3", auth });

    // Get file metadata to check if it exists and get its MIME type
    const file = await drive.files.get({
      fileId: fileId,
      fields: "name, mimeType",
    });

    // Get the file content
    const response = await drive.files.get(
      {
        fileId: fileId,
        alt: "media",
      },
      { responseType: "stream" }
    );

    // Create response headers
    const headers = new Headers();
    headers.set("Content-Type", file.data.mimeType || "application/octet-stream");
    headers.set(
      "Content-Disposition",
      `attachment; filename="${encodeURIComponent(file.data.name || "download")}`
    );

    // Stream the file content
    // @ts-ignore - TypeScript doesn't recognize Response.body as a valid stream
    return new Response(response.data, {
      headers,
      status: 200,
    });

  } catch (error: any) {
    console.error("Error downloading file:", error);

    const errorMessage = 
      error.response?.data?.error?.message || 
      error.message || 
      "Failed to download file";

    return NextResponse.json(
      { error: errorMessage },
      { status: error.response?.status || 500 }
    );
  }
}
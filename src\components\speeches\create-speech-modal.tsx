"use client"

import { ResponsiveModal } from "@/components/responsive-modal";
import { CreateSpeechFormWrapper } from "./create-speech-form-wrapper";
import { useCreateSpeechModal } from "@/hooks/use-create-speech-modal";

export const CreateSpeechModal = () => {
  const { isOpen, setIsOpen, close } = useCreateSpeechModal()
  return (
    <ResponsiveModal open={isOpen} onOpenChange={setIsOpen}>
      <CreateSpeechFormWrapper onCancel={close} />
    </ResponsiveModal>
  )
}
"use server";

import { currentUser } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { google } from "googleapis";
import { calendarEventSchema } from '@/components/tasks/schemas';
import { z } from "zod";
import { NextResponse } from 'next/server';

type CreateMeetingResponse = {
  success?: boolean;
  data?: any;
  error?: string;
  message?: string;
};

const SCOPES = [
  "https://www.googleapis.com/auth/calendar",
  "https://www.googleapis.com/auth/calendar.events",
];

const calendarId = process.env.QF_GOOGLE_CALENDAR_ID;
const initGoogleCalendar = async () => {
  try {
    // Replace escaped newlines with actual newlines
    const privateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n');

    const credentials = {
      client_id: process.env.GOOGLE_SERVICE_CLIENT_ID,
      client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      project_id: process.env.QF_GOOGLE_PROJECT_ID,
      private_key: privateKey
    }

    const auth = new google.auth.GoogleAuth({
      credentials,
      scopes: SCOPES,
    });

    const calendar = google.calendar({ version: "v3", auth });
    if (!calendar) {
      throw new Error("Failed to initialize Google Calendar");
    }
    return calendar;
  } catch (error) {
    console.error("Error initializing Google Calendar API:", error);
    throw error;
  }
};

export async function POST(req: Request) {
  try {

    const user = await currentUser();
    const userId = user?.id;
  
    if (!userId) {
        return NextResponse.json({
        error: "Unauthorized.",
        success: false
      });
    }

    const { values } = await req.json();
    
    if (!values) {
      return NextResponse.json({
        error: "No event data provided.",
        success: false
      })
    }

    const validatedFields = calendarEventSchema.safeParse(values)
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false
      }
    }

    const newEvent = validatedFields.data

    const calendar = await initGoogleCalendar();

    const event = {
      ...newEvent,
      conferenceData: {
        createRequest: {
          requestId: Math.random().toString(36).substring(7),
          conferenceSolutionKey: { type: "hangoutsMeet" },
        },
      },
      reminders: {
        useDefault: false,
        overrides: [{ method: "email", minutes: 30 }],
      },
    };
    
    const createdMeeting = await calendar?.events.insert({
      calendarId,
      requestBody: event,
    });

    if (!createdMeeting.data) {
      return NextResponse.json({ 
        success: false,
        error: "Failed to create meeting"
      });
    }

    return NextResponse.json({ 
      data: createdMeeting, 
      success: true,
      message: "Meeting has been created" 
    });

  } catch (error) {
    console.error('Error creating meeting:', error);
    return NextResponse.json({ 
      success: false,
      error: error instanceof Error ? error.message : "Failed to create meeting"
    });
  } finally {
    revalidatePath("/");
  }
}

'use client';
import config from "@/lib/config/env"

export const generateOAuthState = () => {
  const state = Math.random().toString(36).substring(2);
  console.log('Generated state:', state); // For debugging
  return state;
};
  
export const getGoogleOAuthURL = (state: string) => {
  const { publicClientId, redirectUri } = config.env.google
  const rootUrl = 'https://accounts.google.com/o/oauth2/v2/auth';
  
  if (!redirectUri || !publicClientId) {
    throw new Error('Missing required environment variables for Google OAuth');
  }

  const options = {
    redirect_uri: redirectUri,
    client_id: publicClientId,
    access_type: 'offline',
    response_type: 'code',
    prompt: 'consent',
    scope: [
      'https://www.googleapis.com/auth/drive.metadata.readonly',
      "https://www.googleapis.com/auth/drive.file",
      "https://www.googleapis.com/auth/drive"
    ].join(' '),
    state
  };

  const qs = new URLSearchParams(options);
  return `${rootUrl}?${qs.toString()}`;
};
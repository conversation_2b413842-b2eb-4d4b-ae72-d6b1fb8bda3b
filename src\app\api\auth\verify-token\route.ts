import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { rateLimit } from "@/lib/rate-limit";
import { validateCsrfToken } from "@/lib/csrf";
import axios from "axios";
import config from "@/lib/config/env";


const limiter = rateLimit({
  interval: 60 * 1000,
  uniqueTokenPerInterval: 500
});

export async function GET(req: Request) {
  try {
    console.log("Verification attempt - Headers:", req.headers);
    console.log("Cookies:", req.headers.get("cookie"));

    const { userId } = await auth();
    if (!userId) return NextResponse.json({ error: "Unauthorized", success: false }, { status: 401 });

    // Rate limiting
    const clientIp = req.headers.get("x-forwarded-for") || "0.0.0.0";
    try {
      await limiter.check(10, clientIp); // 10 verifications per minute
    } catch {
      return NextResponse.json(
        { error: "Too many verification attempts" },
        { status: 429 }
      );
    }

    // CSRF protection
    const csrfToken = req.headers.get("x-csrf-token");
    if (!csrfToken) {
      return NextResponse.json(
        { error: "Missing CSRF token" },
        { status: 403 }
      );
    }
    if (!validateCsrfToken(csrfToken)) {
      return NextResponse.json(
        { error: "Invalid request" },
        { status: 403 }
      );
    }

    // Get access token from cookies
    const cookies = req.headers.get("cookie") || "";
    console.log("Cookies_verify-token:", cookies);

    const accessToken = cookies.match(/access_token=([^;]*)/)?.[1];
    console.log("Access token present:", !!accessToken);
    
    if (!accessToken) {
      console.log("No access token found in cookies");
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Verify token with Google
    const response = await axios.get(
      "https://www.googleapis.com/oauth2/v3/tokeninfo",
      {
        params: { access_token: accessToken },
        validateStatus: status => status < 500
      }
    );

    // Check if token is valid and not expired
    if (response.status !== 200) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    // Verify expected audience (client ID)
    const { clientId } = config.env.google;
    const { aud, exp } = response.data;
    if (aud !== clientId) {
      return NextResponse.json(
        { error: "Invalid token audience" },
        { status: 401 }
      );
    }

    // Set security headers
    const responseHeaders = new Headers({
      "X-Content-Type-Options": "nosniff",
      "X-Frame-Options": "DENY",
      "Content-Security-Policy": "default-src 'self'",
      "Cache-Control": "no-store, no-cache, must-revalidate",
    });

    // Return minimal necessary information
    /*return NextResponse.json(
      { 
        valid: true,
        expires_at: exp
      },
      { 
        headers: responseHeaders,
        status: 200 
      }
    );*/
    return NextResponse.json(
      { valid: true },
      { status: 200 }
    );

  } catch (error: any) {
    console.error("Token Verification Error:", error.response?.data || error);
    console.error("Detailed verification error:", error);

    return NextResponse.json(
      { 
        error: "Verification failed",
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Only allow GET requests
export async function POST() {
  return NextResponse.json(
    { error: "Method not allowed" },
    { status: 405 }
  );
}
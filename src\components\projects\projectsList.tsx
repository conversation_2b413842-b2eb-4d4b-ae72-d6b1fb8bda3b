
"use client"

import Link from "next/link";
import { usePathname } from "next/navigation";
import { RiAddCircleFill } from "react-icons/ri";
import { useGetProjects } from "./use-get-projects";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { cn } from "@/lib/utils";
import { useCreateProjectModal } from "@/hooks/use-create-project-modal";
import { ProjectAvatar } from "./project-avatar";
import { Frame } from "lucide-react";


export const Projects = () => {
  const pathname = usePathname();
  const workspaceId = useWorkspaceId();
  const { open } = useCreateProjectModal();
  const { projects } = useGetProjects({workspaceId})
  return (
    <div className="flex flex-col items-start gap-x-2 ml-4">
      <div className="flex flex-row items-center gap-x-2">
        <Frame size={"16"} className="text-neutral-800 dark: black mb-[0.1rem] rounded-md" />
        <p className="text-xs uppercase text-primary font-semibold">Projects</p>
        <RiAddCircleFill
          className="size-5 text-neutral-500 cursor-pointer hover:text-neutral-400"
          onClick={open}
        />
      </div>
      <div className="flex flex-col pt-2 px-4 gap-y-2">
        {projects && projects.map((project) => {
          const href = `/workspaces/${workspaceId}/projects/${project.id}`
          const isActive = pathname === href
          return (
            <Link href={href} key={project.id}>
              <div className={cn(
                "flex items-center gap-1 rounded-md hover:opcity-75 transition cursor-pointer text-neutral-500",
                isActive && "bg-white shadow-sm hover:opacity-100 text-primary"
                )}
              >
                <ProjectAvatar image={project.imageUrl!!} name={project.name} />
               <span className="truncate text-xs">{project.name}</span>
              </div>
            </Link>
          )
      })}        
      </div>

    </div>
  )
}
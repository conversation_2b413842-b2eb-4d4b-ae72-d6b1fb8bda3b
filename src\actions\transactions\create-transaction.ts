"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { suggestCategory } from "@/lib/utils/transaction-categorization";
import { Prisma } from "@prisma/client";


export async function createTransactions({
  transactions, // Array of transaction data
}: {
  transactions: {
    accountId: string;
    description: string;
    amount: number;
    type: "CREDIT" | "DEBIT";
    categoryId?: string;
    currencyIso?: string;
    date?: Date;
  }[];
}) {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    const createdTransactions = [];
    // Group transactions by account to update balances efficiently
    const accountBalanceChanges: Record<string, number> = {};
    
    for (const transactionData of transactions) {
      const {
        accountId,
        description,
        amount,
        type,
        categoryId,
        currencyIso = "TWD",
        date = new Date(),
      } = transactionData;

      // Determine the suggested category
      const { categoryId: suggestedCategoryId, confidence } = await suggestCategory(
        description,
        amount,
        type
      );

      // Use the user-provided categoryId, if available
      const category = categoryId
        ? await prisma.category.findUnique({
            where: { id: categoryId, userId },
          })
        : null;

      // Check if the category exists or is valid
      if (categoryId && !category) {
        throw new Error("Category not found or unauthorized");
      }

      // Create the new transaction
      const transaction = await prisma.transaction.create({
        data: {
          accountId,
          description,
          amount: new Prisma.Decimal(amount),
          type,
          userId,
          currencyIso,
          date,
          categoryId: category ? category.id : undefined,
          suggestedCategoryId,
          categoryValidated: !!categoryId, // If user provided a category, mark as validated
        },
      });

      createdTransactions.push(transaction);
      
      // Track balance changes by account
      // CREDIT adds to the balance, DEBIT subtracts from the balance
      const balanceChange = type === "CREDIT" ? amount : -amount;
      accountBalanceChanges[accountId] = (accountBalanceChanges[accountId] || 0) + balanceChange;
    }
    
    // Update balances for affected accounts
    for (const accountId in accountBalanceChanges) {
      // Get the current latest balance for this account
      const latestBalance = await prisma.balance.findFirst({
        where: { accountId: accountId },
        orderBy: { date: 'desc' },
      });
      
      // Get the account to check currency
      const account = await prisma.bankAccount.findUnique({
        where: { id: accountId },
      });
      
      if (!account) continue;
      
      // Determine currency to use
      const currencyIso = createdTransactions.find(tx => tx.accountId === accountId)?.currencyIso || 
                          (latestBalance?.currencyIso) || 
                          "TWD";
      
      // Calculate new balance amount
      const currentAmount = latestBalance?.amount ?? 0;
      const newAmount = currentAmount + accountBalanceChanges[accountId];
      
      // Create a new balance record
      await prisma.balance.create({
        data: {
          amount: newAmount,
          date: new Date(),
          bankAccount: {
            connect: {
              id: accountId,
            },
          },
          currency: {
            connect: {
              iso: currencyIso,
            },
          },
        },
      });
    }

    revalidatePath("/banking")
    return { success: true, transactions: createdTransactions };
  } catch (error) {
    console.error("Error creating transactions:", error);
    return { success: false, error: "Failed to create transactions" };
  }
}
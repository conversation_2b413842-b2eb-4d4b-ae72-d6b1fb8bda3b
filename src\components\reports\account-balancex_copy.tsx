import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  flexRender,
  CellContext,
  createColumnHelper,
  ColumnFiltersState,
  VisibilityState 
} from '@tanstack/react-table'
import { ReactNode, Fragment ,useMemo, useState } from "react";
import { MAX_DATE_RANGE_DAYS } from "@/constants";
import { 
	differenceInDays, 
	subYears, 
	startOfYear, 
	endOfYear, 
	format 
} from "date-fns";
import { Balance } from "@prisma/client";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface AccountBalanceProps {
  name?: string;
	transactions: Balance[];
}

// Extend the Balance type to include calculated fields
interface ExtendedBalance extends Balance {
  name: string;
	previousAmount: number;
	netAmount: number;
}

const columnHelper = createColumnHelper<ExtendedBalance>();


export default function AccountTransactions({ name, transactions }: AccountBalanceProps) {
	const [dateRange, setDateRange] = useState<DateRange | undefined>({
		from: startOfYear(subYears(new Date(), 1)), 
		to: endOfYear(new Date()),
	});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({date: false})
	const [amountFilter, setAmountFilter] = useState<{ operator: string; value: number | null }>({
	  operator: ">=", // Default operator
	  value: null,   // Default value
	});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [unifiedFilter, setUnifiedFilter] = useState<string>("");
  const [headerHidden, setHeaderHidden] = useState<boolean>(true);

	const calculateBalanceData = useMemo(() => {
		let previousAmount = 0;
		return transactions.map((transaction) => {
			const currentAmount = transaction.amount || 0;
			const netAmount = currentAmount - previousAmount;
			const result = {
				...transaction,
				previousAmount,
				netAmount,
			};
			previousAmount = currentAmount;
			return result;
		});
	}, [transactions]);

	const filteredTransactions = useMemo(() => {
		return calculateBalanceData.filter((transaction) => {
			const transactionDate = new Date(transaction.date);

		// Date filter
		const withinDateRange =
			transactionDate >= (dateRange?.from ?? startOfYear(subYears(new Date(), 1))) &&
			transactionDate <= (dateRange?.to ?? endOfYear(new Date()));


			const matchesUnifiedFilter =
				unifiedFilter === "" ||
				Object.entries(transaction).some(([key, value]) => {
					if (key === "date" && value) {
						try {
							
							const formattedDate = format(new Date(value as Date), "MMM d, yyyy").toLowerCase();
							return formattedDate.includes(unifiedFilter.toLowerCase());
						} catch {
							return false;
						}
					}
					return value?.toString().toLowerCase().includes(unifiedFilter.toLowerCase());
				});

				// Amount filter
				const matchesAmountFilter = amountFilter.value
					? amountFilter.operator === ">="
					? transaction?.amount! >= amountFilter.value
					: amountFilter.operator === "<="
					? transaction?.amount! <= amountFilter.value
					: transaction.amount === amountFilter.value
					: true;

			return withinDateRange && matchesUnifiedFilter && matchesAmountFilter;
		});
	}, [calculateBalanceData, dateRange, unifiedFilter, amountFilter]);
  
  const columns = [
    columnHelper.accessor('name', {
      header: 'Account',
      cell: (info) => (
        <TableCell className="w-[16rem] text-left text-xs p-1 pl-2">
          <span className={""}>
            {name}
          </span>
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
    columnHelper.accessor('date', {
      header: 'Date',
      cell: (info) => (
        <TableCell className="w-[7rem] text-xs p-1">
          <span className={""}>
            {format(info.getValue(), "MMM d, yyyy")}
          </span>
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
    columnHelper.accessor('previousAmount', {
      header: "Previous Amount",
      cell: (info) => (
        <TableCell className="w-[8rem] text-right text-xs p-1">
          <span className="">
            {new Intl.NumberFormat("zh-TW", {
              style: "currency",
              currency: "TWD",
              minimumFractionDigits: 0, // No decimal places
              maximumFractionDigits: 0, // No decimal places
            }).format(info?.getValue()!)}
          </span>
        </TableCell>
      ),
    }),
    columnHelper.accessor('amount', {
      header: () => 'Amount',
      cell: (info) => (
        <TableCell className="w-[8rem] text-right text-xs p-1">
          <span className="">
            {new Intl.NumberFormat("zh-TW", {
              style: "currency",
              currency: "TWD",
              minimumFractionDigits: 0, // No decimal places
              maximumFractionDigits: 0, // No decimal places
            }).format(info?.getValue()!)}
          </span>
        </TableCell>
      ),
      filterFn: (row, columnId, filterValue) => {
        if (!filterValue?.value) return true; // Show all rows if no filter value
    
        const amount = row.getValue(columnId) as number;
        switch (filterValue.operator) {
          case "=":
            return amount === filterValue.value;
          case ">=":
            return amount >= filterValue.value;
          case "<=":
            return amount <= filterValue.value;
          default:
            return true;
        }
      },
    }),
    columnHelper.accessor('netAmount', {
      header: "Net",
      cell: (info) => (
        <TableCell className="w-[8rem] text-right text-xs p-1">
          <span className="">
            {new Intl.NumberFormat("zh-TW", {
              style: "currency",
              currency: "TWD",
              minimumFractionDigits: 0, // No decimal places
              maximumFractionDigits: 0, // No decimal places
            }).format(info?.getValue()!)}
          </span>
        </TableCell>
      ),
    }),
    columnHelper.accessor('description', {
      header: 'Description',
      cell: (info) => (
        <TableCell className="p-1 pr-2">
          {info.getValue()}
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
  ];
  
  const table = useReactTable({
    data: filteredTransactions as ExtendedBalance[],
    columns,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnFiltersChange: setColumnFilters,
    state: {
      columnVisibility,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: 30,
      },
    },
  });
  
  const toggleHeaderVisibility = () => {
    setHeaderHidden(!headerHidden); // Toggle header visibility
  };

  return (
    <div className="space-y-1">
      <div id="no-print" className="flex gap-2">
        <DateRangePicker
          initialDateFrom={dateRange?.from}
          initialDateTo={dateRange?.to}
          showCompare={false}
          onUpdate={(values) => {
            const { from, to } = values.range;
              // We update the date range only if both dates are set

              if (!from || !to) return;
              if (differenceInDays(to, from) > MAX_DATE_RANGE_DAYS) {
              toast.error(
                  `The selected date range is too big. Max allowed range is ${MAX_DATE_RANGE_DAYS} days!`
              );
              return;
              }

              setDateRange({ from, to });
          }}
        /> 
        <Input
          placeholder="Search..."
          value={unifiedFilter}
          onChange={(e) => setUnifiedFilter(e.target.value)}
          className="h-8"
        />
        <div className="flex items-center gap-2">
          <Select
            value={amountFilter.operator}
            onValueChange={(value) => setAmountFilter((prev) => ({ ...prev, operator: value }))}
          >
            <SelectTrigger className="h-8 w-fit gap-x-1">
              <SelectValue placeholder="Operator" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="=">=</SelectItem>
              <SelectItem value=">=">{`>=`}</SelectItem>
              <SelectItem value="<=">{`<=`}</SelectItem>
            </SelectContent>
          </Select>
          <Input
            type="number"
            placeholder="Amount"
            value={amountFilter.value ?? ""}
            onChange={(e) => setAmountFilter((prev) => ({
              ...prev,
              value: e.target.value ? parseFloat(e.target.value) : null,
            }))}
            className="h-8 min-w-[10rem] max-w-[150px]"
          />
          <DropdownMenu>
            <DropdownMenuTrigger className="h-8" asChild>
              <Button variant="outline">Columns</Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table.getAllLeafColumns().map(column => (
                <DropdownMenuCheckboxItem
                  key={column.id}
                  className="capitalize"
                  checked={column.getIsVisible()}
                  onCheckedChange={value => column.toggleVisibility(!!value)}
                >
                  {column.id}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          {/* Toggle Header Visibility Button */}
          <Button variant={"outline"} className="h-8" type="button" onClick={toggleHeaderVisibility}>
            {headerHidden ? "Show Header" : "Hide Header"}
          </Button>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader style={{ display: headerHidden ? "none" : "table-header-group" }}>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead 
                    key={header.id}
                    className={cn(
                      "h-8 text-xs p-1",
                      header.id === 'account' && "w-[16rem] text-left pl-2" ||
                      header.id === 'date' && "w-[7rem] text-left" ||
                      header.id === 'previousAmount' && "w-[8rem] text-right" ||
                      header.id === 'amount' && "w-[8rem] text-right" ||
                      header.id === 'netAmount' && "w-[8rem] text-right" ||
                      header.id === 'description' && "text-center pr-2"
                    )}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                    {header.column.getIsSorted() && (
                      <span>{header.column.getIsSorted() === "asc" ? " ↑" : " ↓"}</span>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getFilteredRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Fragment key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell as (info: CellContext<ExtendedBalance, any>) => ReactNode,
                      cell.getContext()
                    )}
                  </Fragment>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

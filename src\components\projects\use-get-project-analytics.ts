import { Project } from "@prisma/client";
import { useQuery } from "@tanstack/react-query";
import { getProjectAnalytics } from "@/actions/projects/analytics";


interface UseGetProjectAnalyticsProps {
  projectId: string;
}

export const useGetProjectAnalytics = ({ projectId }: UseGetProjectAnalyticsProps) => {
    const { data, isLoading, error } = useQuery({
    queryKey: ["project-analytics", projectId],
    queryFn: async () => {
      const response = await getProjectAnalytics(projectId);
      if (!response.success || !response.data) throw new Error("Failed to fetch projects");
      return response?.data;
    },
    enabled: !!projectId,
  })

  return { data, isLoading, error };
}
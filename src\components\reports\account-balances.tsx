import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  flexRender,
  CellContext,
  createColumnHelper,
  ColumnFiltersState,
  VisibilityState 
} from '@tanstack/react-table'
import { ReactNode, Fragment ,useMemo, useState } from "react";
import { MAX_DATE_RANGE_DAYS } from "@/constants";
import { 
	differenceInDays, 
	subYears, 
	startOfYear, 
	endOfYear, 
	format 
} from "date-fns";
import { Balance } from "@prisma/client";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface AccountBalanceProps {
  name?: string;
  dataRange?: { label?: string; from: Date; to: Date };
  transactions: Balance[];
}
// Extend the Balance type to include calculated fields
interface ExtendedBalance extends Balance {
  name: string;
	previousAmount: number;
	netAmount: number;
}

const columnHelper = createColumnHelper<ExtendedBalance>();


export default function AccountBalances({ name, dataRange, transactions }: AccountBalanceProps) {
	const [dateRange, setDateRange] = useState<DateRange | undefined>({
		from: startOfYear(subYears(new Date(), 1)), 
		to: endOfYear(new Date()),
	});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({date: false, description: false})
	const [amountFilter, setAmountFilter] = useState<{ operator: string; value: number | null }>({
	  operator: ">=", // Default operator
	  value: null,   // Default value
	});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [unifiedFilter, setUnifiedFilter] = useState<string>("");
  const [headerHidden, setHeaderHidden] = useState<boolean>(false);
  const calculateBalanceData = useMemo(() => {
		// Sort transactions by date in descending order (newest first)
		const sortedTransactions = [...transactions].sort((a, b) => 
			new Date(b.date).getTime() - new Date(a.date).getTime()
		);

		// Get the latest record
		const latestRecord = sortedTransactions[0];
		if (!latestRecord) return [];

		const currentAmount = latestRecord.amount || 0;
		const previousAmount = sortedTransactions[1]?.amount || 0;

		// Create the initial record with the latest amount
		const result = [{
			...latestRecord,
			previousAmount,
			netAmount: currentAmount - previousAmount,
		}];

		return result;
	}, [transactions]);

	const filteredTransactions = useMemo(() => {
		return calculateBalanceData.filter((transaction) => {
			const transactionDate = new Date(transaction.date);

		// Date filter
		const withinDateRange =
			transactionDate >= (dateRange?.from ?? startOfYear(subYears(new Date(), 1))) &&
			transactionDate <= (dateRange?.to ?? endOfYear(new Date()));


			const matchesUnifiedFilter =
				unifiedFilter === "" ||
				Object.entries(transaction).some(([key, value]) => {
					if (key === "date" && value) {
						try {
							
							const formattedDate = format(new Date(value as Date), "MMM d, yyyy").toLowerCase();
							return formattedDate.includes(unifiedFilter.toLowerCase());
						} catch {
							return false;
						}
					}
					return value?.toString().toLowerCase().includes(unifiedFilter.toLowerCase());
				});

				// Amount filter
				const matchesAmountFilter = amountFilter.value
					? amountFilter.operator === ">="
					? transaction?.amount! >= amountFilter.value
					: amountFilter.operator === "<="
					? transaction?.amount! <= amountFilter.value
					: transaction.amount === amountFilter.value
					: true;

			return withinDateRange && matchesUnifiedFilter && matchesAmountFilter;
		});
	}, [calculateBalanceData, dateRange, unifiedFilter, amountFilter]);
  
  const columns = [
    columnHelper.accessor('name', {
      header: '帳戶名稱',
      cell: (info) => (
        <TableCell className="w-[20rem] text-left pl-1 py-0">
          <span className={""}>
            {name}
          </span>
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
    columnHelper.accessor('date', {
      header: '結算日',
      cell: (info) => (
        <TableCell className="w-[7rem]  px-1 py-0">
          <span className={""}>
            {format(info.getValue(), "MMM d, yyyy")}
          </span>
        </TableCell>
      ),
      filterFn: 'includesString'
    }),    
    columnHelper.accessor('previousAmount', {
      header: "上期累積結餘",
      cell: (info) => (
        <TableCell className="w-[16rem] text-right p-0">
          <span className="">
            {info.getValue() === 0 ? "" : new Intl.NumberFormat("zh-TW", {
              style: "currency",
              currency: "TWD",
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(info.getValue()!)}
          </span>
        </TableCell>
      ),
    }),
    columnHelper.accessor('netAmount', {
      header: "本期結餘",
      cell: (info) => {
        const value = info?.getValue()!;
        const isNegative = value < 0;
        
        return (
          <TableCell className="w-[16rem] text-right px-1 py-0">
            <span className={isNegative ? "text-red-500" : ""}>
              {new Intl.NumberFormat("zh-TW", {
                style: "currency",
                currency: "TWD",
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
              }).format(value)}
            </span>
          </TableCell>
        );
      },
    }),
    columnHelper.accessor('amount', {
      header: () => '本期累積結餘',
      cell: (info) => (
        <TableCell className="w-[16rem] text-right font-bold px-2 py-0">
          <span className="">
            {new Intl.NumberFormat("zh-TW", {
              style: "currency",
              currency: "TWD",
              minimumFractionDigits: 0, // No decimal places
              maximumFractionDigits: 0, // No decimal places
            }).format(info?.getValue()!)}
          </span>
        </TableCell>
      ),
      filterFn: (row, columnId, filterValue) => {
        if (!filterValue?.value) return true; // Show all rows if no filter value
    
        const amount = row.getValue(columnId) as number;
        switch (filterValue.operator) {
          case "=":
            return amount === filterValue.value;
          case ">=":
            return amount >= filterValue.value;
          case "<=":
            return amount <= filterValue.value;
          default:
            return true;
        }
      },
    }),
    columnHelper.accessor('description', {
      header: '備註',
      cell: (info) => (
        <TableCell className="p-1 pr-2 text-right">
          {info.getValue()}
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
  ];
  
  const table = useReactTable({
    data: filteredTransactions as ExtendedBalance[],
    columns,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnFiltersChange: setColumnFilters,
    state: {
      columnVisibility,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: 30,
      },
    },
  });
  
  const toggleHeaderVisibility = () => {
    setHeaderHidden(!headerHidden); // Toggle header visibility
  };

  return (
    <div className="space-y-1">
      <div id="no-print" className="flex gap-2">
        <DateRangePicker
          key={`${dataRange?.from}-${dataRange?.to}`}
          initialDateFrom={dataRange?.from}
          initialDateTo={dataRange?.to}
          showCompare={false}
          onUpdate={(values) => {
            const { from, to } = values.range;
              // We update the date range only if both dates are set

              if (!from || !to) return;
              if (differenceInDays(to, from) > MAX_DATE_RANGE_DAYS) {
              toast.error(
                  `The selected date range is too big. Max allowed range is ${MAX_DATE_RANGE_DAYS} days!`
              );
              return;
              }

              setDateRange({ from, to });
          }}
        /> 
        <Input
          placeholder="Search..."
          value={unifiedFilter}
          onChange={(e) => setUnifiedFilter(e.target.value)}
          className="h-8"
        />
        <div className="flex items-center gap-2">
          <Select
            value={amountFilter.operator}
            onValueChange={(value) => setAmountFilter((prev) => ({ ...prev, operator: value }))}
          >
            <SelectTrigger className="h-8 w-fit gap-x-1">
              <SelectValue placeholder="Operator" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="=">=</SelectItem>
              <SelectItem value=">=">{`>=`}</SelectItem>
              <SelectItem value="<=">{`<=`}</SelectItem>
            </SelectContent>
          </Select>
          <Input
            type="number"
            placeholder="Amount"
            value={amountFilter.value ?? ""}
            onChange={(e) => setAmountFilter((prev) => ({
              ...prev,
              value: e.target.value ? parseFloat(e.target.value) : null,
            }))}
            className="h-8 min-w-[10rem] max-w-[150px]"
          />
          <DropdownMenu>
            <DropdownMenuTrigger className="h-8" asChild>
              <Button variant="outline">Columns</Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table.getAllLeafColumns().map(column => (
                <DropdownMenuCheckboxItem
                  key={column.id}
                  className="capitalize"
                  checked={column.getIsVisible()}
                  onCheckedChange={value => column.toggleVisibility(!!value)}
                >
                  {column.id}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          {/* Toggle Header Visibility Button */}
          <Button variant={"outline"} className="h-8" type="button" onClick={toggleHeaderVisibility}>
            {headerHidden ? "Show Header" : "Hide Header"}
          </Button>
        </div>
      </div>
      <div className="border-none space-y-2">
        <Table className='text-[18px]'>
          <TableHeader style={{ display: headerHidden ? "none" : "table-header-group" }}>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead 
                    key={header.id}
                    className={cn(
                      "h-8 p-1 border-t border-b text-black bg-green-100",
                      header.id === 'name' && "w-[20rem] text-left pl-1" ||
                      header.id === 'date' && "w-[7rem] text-left" ||
                      header.id === 'previousAmount' && "w-[16rem] px-0 text-right" ||
                      header.id === 'netAmount' && "w-[16rem] text-right" ||
                      header.id === 'amount' && "w-[16rem] text-right" ||
                      header.id === 'description' && "w-[10rem] text-right pr-2 whitespace-nowrap"
                    )}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                    {header.column.getIsSorted() && (
                      <span>{header.column.getIsSorted() === "asc" ? " ↑" : " ↓"}</span>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="text-black bg-white">
            {table.getFilteredRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Fragment key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell as (info: CellContext<ExtendedBalance, any>) => ReactNode,
                      cell.getContext()
                    )}
                  </Fragment>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

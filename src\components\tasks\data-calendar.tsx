import { TaskWithRelations } from "@/actions/tasks/get-tasks";
import {
  format,
  getDay,
  parse,
  startOfWeek,
  addMonths,
  subMonths,  
} from "date-fns";
import { enUS } from "date-fns/locale";
import { useState } from "react";
import { Calendar, dateFnsLocalizer } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import "./data-calendar.css";
import { EventCard } from "./event-card";
import { CalendarIcon, ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import { Button } from "../ui/button";

const locales  = {
  "en-US": enUS,
}

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  addMonths,
  subMonths,
  locales,
});


interface DataCalendarProps {
  data: TaskWithRelations[];
}

export const DataCalendar = ({ data }: DataCalendarProps) => {
  const [value, setValue] = useState(
    data.length > 0 ? new Date(data[0].dueDate) : new Date()
  );

  const events = data.map((task) => ({
    start: new Date(task?.startDate!),
    end: new Date(task.dueDate),
    title: task.name,
    project: task.project,
    assignee: task.assignee,
    status: task.status,
    id: task.id,
    workspaceId: task.workspaceId,
  }))

  const handleNavigate = (action: "PREV" | "NEXT" | "TODAY") => {
    switch (action) {
      case "PREV":
        setValue(subMonths(value, 1));
        break;
      case "NEXT":
        setValue(addMonths(value, 1));
        break;
      case "TODAY":
        setValue(new Date());
        break;
      default:
        break;
    }
  }

  return (
    <Calendar
      localizer={localizer}
      date={value}
      events={events}
      views={["month"]}
      defaultView="month"
      toolbar
      showAllEvents={true}
      className="h-full"
      max={new Date(new Date().setFullYear(new Date().getFullYear() + 1))}
      formats={{
        weekdayFormat: (date, culture, localizer) => localizer?.format(date, "EEE", culture) ?? ""
      }}
      components={{
        eventWrapper: ({ event }) => (
          <EventCard
            id={event.id}
            title={event.title}
            project={event.project}
            assignee={event.assignee}
            status={event.status}
            workspaceId={event.workspaceId}
          />
        ),
        toolbar: () => (
          <CustomToolbar date={value} onNavigate={handleNavigate} />
        )
      }}
    >
       
    </Calendar>
  )
}

type CustomToolbarProps = {
  date: Date;
  onNavigate: (action: "PREV" | "NEXT" | "TODAY") => void;
}
  
const CustomToolbar = ({
  date,
  onNavigate,
}: CustomToolbarProps) => {
  return (
    <div className="flex mb-4 items-center w-full lg:w-auto justify-center lg:justify-start">
      <Button
        onClick={() => onNavigate("PREV")}
        variant="ghost"
        size="icon"
        className="flex items-center"
      >
        <ChevronLeftIcon className="size-4 mr-2" />
      </Button>
      <div className="flex items-center border border-input rounded-md px-3 py-2 h-8 justify-center w-full lg:w-auto">
        <CalendarIcon className="size-4 mr-2" />
        <p className="text-sm">{format(date, "MMMM yyyy")}</p>
      </div>
      <Button
        onClick={() => onNavigate("NEXT")}
        variant="ghost"
        size="icon"
        className="flex items-center"
      >
        <ChevronRightIcon className="size-4 ml-2" />
      </Button>
    </div>    
  )
}
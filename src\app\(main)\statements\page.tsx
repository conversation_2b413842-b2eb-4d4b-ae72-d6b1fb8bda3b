import { getCurrentUser } from "@/actions/user/get-current-user";
import { getBankingOverview } from "@/actions/statements/get-banking-overview";
import BankAccountOverview from "@/components/transactions/bank-account-overview";
import BankAccountSelector from "@/components/transactions/bank-account-selector";
import { redirect } from "next/navigation";
import { EmptyPlaceholder } from "@/components/empty-placeholder";
import { Wallet } from "lucide-react";
import { getSelectCategories } from "@/actions/categories/get-select-categories";
import { getSelectAccounts } from "@/actions/account/get-select-accounts";
import { AddBankAccountComponent } from "@/components/account-connection";
import { AddStatementComponent } from "@/components/statements/add-statements";
import { SpreadsheetDialog } from "@/components/copilot/spreadsheet/SpreadsheetDialog";

export default async function BankingPage() {
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const { success, data, error } = await getBankingOverview("VIRTUAL");
  const categoryResponse = await getSelectCategories();
  const accountResponse = await getSelectAccounts();

  const categories = categoryResponse?.categories ?? [];
  const accounts = accountResponse?.accounts ?? [];

  if (!success || !data || data.bankAccounts.length === 0) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Management Accounts</h2>
          <div className="flex flex-rows items-center space-x-2">
            <AddBankAccountComponent />
          </div>
        </div>
        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon icon={Wallet} />
          <EmptyPlaceholder.Title>No management accounts yet</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            Connect your management accounts to start tracking your finances.
          </EmptyPlaceholder.Description>
        </EmptyPlaceholder>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Management Accounts</h2>
        <div className="flex items-center space-x-2">
          <AddStatementComponent 
             categories={categories} 
             accounts={accounts} 
          />
          <SpreadsheetDialog
             categories={categories} 
             accounts={accounts} 
          />
          <AddBankAccountComponent />
        </div>
      </div>
      {/*<BankAccountOverview data={data} />*/}
      <BankAccountSelector data={data} />
    </div>
  );
}

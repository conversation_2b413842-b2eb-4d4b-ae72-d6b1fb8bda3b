import { Message, FlexMessage, TextMessage, TemplateMessage } from '@line/bot-sdk';

export const createTextMessage = (userId: string): TextMessage => ({
  type: 'text',
  text: `您好！這是一個示範文字訊息。\n用戶ID: ${userId}`
});

export const createFlexMessage = (): FlexMessage => ({
  type: 'flex',
  altText: '彈性訊息示範',
  contents: {
    type: 'bubble',
    header: {
      type: 'box',
      layout: 'vertical',
      contents: [
        {
          type: 'text',
          text: '彈性訊息示範',
          weight: 'bold',
          size: 'xl'
        }
      ]
    },
    hero: {
      type: 'image',
      url: 'https://via.placeholder.com/1024x512',
      size: 'full',
      aspectRatio: '2:1'
    },
    body: {
      type: 'box',
      layout: 'vertical',
      contents: [
        {
          type: 'text',
          text: '這是一個彈性訊息的示範',
          wrap: true
        },
        {
          type: 'button',
          style: 'primary',
          action: {
            type: 'uri',
            label: '了解更多',
            uri: 'https://developers.line.biz/en/docs/messaging-api/message-types/#flex-messages'
          },
          margin: 'md'
        }
      ]
    }
  }
});

export const createCarouselMessage = (): TemplateMessage => ({
  type: 'template',
  altText: '輪播訊息示範',
  template: {
    type: 'carousel',
    columns: [
      {
        thumbnailImageUrl: 'https://via.placeholder.com/512',
        title: '輪播項目 1',
        text: '這是第一個輪播項目的說明文字',
        actions: [
          {
            type: 'uri',
            label: '查看詳情',
            uri: 'https://line.me'
          }
        ]
      },
      {
        thumbnailImageUrl: 'https://via.placeholder.com/512',
        title: '輪播項目 2',
        text: '這是第二個輪播項目的說明文字',
        actions: [
          {
            type: 'uri',
            label: '查看詳情',
            uri: 'https://line.me'
          }
        ]
      },
      {
        thumbnailImageUrl: 'https://via.placeholder.com/512',
        title: '輪播項目 3',
        text: '這是第三個輪播項目的說明文字',
        actions: [
          {
            type: 'uri',
            label: '查看詳情',
            uri: 'https://line.me'
          }
        ]
      }
    ]
  }
});

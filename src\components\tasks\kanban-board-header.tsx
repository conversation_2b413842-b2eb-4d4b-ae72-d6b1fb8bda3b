"use client"

import {
  <PERSON><PERSON>heckIcon,
  CircleDashedIcon,
  CircleDotDashedIcon,
  CircleDotIcon,
  CircleIcon,
  PlusIcon,
} from "lucide-react";
import { snakeCaseToTitleCase } from "@/lib/utils";
import { TaskStatus } from "@prisma/client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useCreateTaskModal } from "@/hooks/use-create-task-modal";

interface KanbanColumHeaderProps {
  board: TaskStatus;
  taskCount: number;
}

const statusIconMap: Record<TaskStatus, React.ReactNode> = {
  [TaskStatus.BACKLOG]: (
    <CircleDashedIcon className="size-[18px] text-pink-400" />
  ),
  [TaskStatus.TODO]: (
    <CircleIcon className="size-[18px] text-red-400" />
  ),
  [TaskStatus.IN_PROGRESS]: (
    <CircleDotDashedIcon className="size-[18px] text-yellow-400" />
  ),
  [TaskStatus.IN_REVIEW]: (
    <CircleDotIcon className="size-[18px] text-blue-400" />
  ),
  [TaskStatus.DONE]: (
    <CircleCheckIcon className="size-[18px] text-emerald-400" />
  ),
}

export const KanbanColumHeader = ({ 
  board, taskCount 
}: KanbanColumHeaderProps) => {
  const {open} = useCreateTaskModal();
  const icon = statusIconMap[board];
  return (
    <div className="px-2 py-1 flex items-center justify-between">
      <div className="flex items-center gap-x-2">
        {icon}
        <h2 className="font-medium text-sm">
          {snakeCaseToTitleCase(board)}
        </h2>
        <div className="size-5 flex items-center justify-center rounded-md bg-neutral-200 text-neutral-700 text-xs font-medium">
          {taskCount}
        </div>
      </div>
      <Button onClick={open} variant="ghost" size="icon" className="size-5">
        <PlusIcon className="size-4" />
      </Button>
    </div>
  );
}
import { google } from "googleapis";
import { productsSchema } from "@/app/api/webhook/schemas";

export async function fetchProductsFromSheets() {
  try {
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
    });

    const sheets = google.sheets({ version: 'v4', auth });
    
    // First check if we can access the spreadsheet
    const spreadsheet = await sheets.spreadsheets.get({
      spreadsheetId: process.env.GOOGLE_PRODUCTS_SHEETS_ID,
    });

    // Find the zhData sheet
    const productsSheet = spreadsheet.data.sheets?.find(
      sheet => sheet.properties?.title === 'zhData'
    );

    if (!productsSheet) {
      throw new Error("zhData sheet not found");
    }

    // Get the values from the sheet
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: process.env.GOOGLE_PRODUCTS_SHEETS_ID,
      range: 'zhData!A2:S', // Assuming headers are in row 1, data starts from row 2
      valueRenderOption: 'UNFORMATTED_VALUE',
    });

    const rows = response.data.values || [];

    // Map the sheet rows to match the productsSchema fields
    const products = rows.map((row, index) => {
      // Example mapping, adjust field indices and types as needed to match your productsSchema
      return {
        onSale: row[0] === true || row[0] === 'TRUE' || row[0] === 1,
        id: Number(row[1]) || index + 1,
        title: String(row[2] || ''),
        slug: String(row[3] || ''),
        price: typeof row[4] === 'number' ? row[4] : Number(row[4]) || 0,
        description: String(row[5] || ''),
        category_id: typeof row[6] === 'number' ? row[6] : Number(row[6]) || 0,
        category_name: String(row[7] || ''),
        "category.slug": String(row[8] || ''),
        "category.image": String(row[9] || ''),
        "images.0": String(row[10] || ''),
        "images.1": row[11] ? String(row[11]) : undefined,
        "images.2": row[12] ? String(row[12]) : undefined,
        inStock: typeof row[13] === 'number' ? row[13] : Number(row[13]) || 0,
        rating: typeof row[14] === 'number' ? row[14] : Number(row[14]) || 0,
        ratingEmoji: String(row[15] || ''),
        ratingEmoji2: row[16] ? String(row[16]) : undefined,
        "Number of Reviews": typeof row[17] === 'number' ? row[17] : Number(row[17]) || 0,
        specs: String(row[18] || ''),
      };
    });

    // Validate the data against our schema
    const validatedProducts = productsSchema.parse(products);

    // Only return enabled products, sorted by date
    return validatedProducts
      .filter(products => products.onSale)

  } catch (error) {
    console.error('Error fetching products data:', error);
    throw new Error('Failed to fetch products data');
  }
}

import ConvexClientProvider from "@/components/convex-client-provider";
import { <PERSON>Provider } from "@clerk/nextjs";
import { TailwindIndicator } from "@/components/tailwind-indicator";
import { ThemeProvider } from "@/components/theme-provider";
import { ThemeToggle } from "@/components/theme-toggle";
import { NuqsAdapter } from 'nuqs/adapters/react';
import { cn, constructMetadata } from "@/lib/utils";
import type { Metadata, Viewport } from "next";
import "./globals.css";
import "./scrollbar-hidden.css"
import { Toaster } from "sonner";
import "@fontsource/noto-sans-tc";

export const metadata: Metadata = constructMetadata({});

export const viewport: Viewport = {
  colorScheme: "light",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
        <script src="https://accounts.google.com/gsi/client" async defer></script>
      </head>
      <body
        className={cn(
          "min-h-screen bg-background antialiased w-full mx-auto scroll-smooth"
        )}
      >
        <ClerkProvider dynamic>
          <ConvexClientProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="light"
              enableSystem={false}
            >
              <NuqsAdapter>{children}</NuqsAdapter>
              <ThemeToggle />
              <Toaster richColors />
              <TailwindIndicator />
            </ThemeProvider>
          </ConvexClientProvider>
        </ClerkProvider>  
      </body>
    </html>
  );
}

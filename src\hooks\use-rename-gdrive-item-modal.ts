import { useQueryState, parseAsString, ParserBuilder, } from "nuqs";
import { GoogleDriveItem } from "@/components/gdrive/columns";
import { useGdriveItems, useGdriveCurrentFolder } from "@/hooks/use-gdrive-state";

// Create a custom parser for GoogleDriveItem
const parseGdriveItem = {
  parse: (value: string | null): GoogleDriveItem | null => {
    if (!value) return null;
    try {
      return JSON.parse(value) as GoogleDriveItem;
    } catch {
      return null;
    }
  },
  serialize: (value: GoogleDriveItem | null): string | null => {
    if (!value) return null;
    return JSON.stringify(value);
  }
};

export const useRenameGdriveItemModal = () => {
  const [item, setItem] = useQueryState<GoogleDriveItem | null>(
    "rename-gdrive-item",
    {
      parse: parseGdriveItem.parse,
      serialize: parseGdriveItem.serialize,
    } as ParserBuilder<GoogleDriveItem | null>
  );

  const [currentFolder, setCurrentFolder] = useGdriveCurrentFolder();
  const [items, setItems] = useGdriveItems();

  const [isOpen, setIsOpen] = useQueryState(
    "rename-gdrive-modal",
    parseAsString.withDefault("false")
  );

  const open = (item: GoogleDriveItem, items: GoogleDriveItem[], folder: GoogleDriveItem | null) => {
    setItem(item);
    setCurrentFolder(folder);
    setIsOpen("true");
  };

  const close = () => {
    //setItem(null);
    //setCurrentFolder(null);
    setIsOpen("false");
  };
  console.log("items======: ", items)
  console.log("currentFolders=====: ", currentFolder)

  return {
    item,
    items,
    setItems,
    currentFolder,
    setCurrentFolder,
    isOpen: isOpen === "true",
    open,
    close,
    setItem,
    setIsOpen: (open: boolean) => setIsOpen(open ? "true" : "false"),
  };
};
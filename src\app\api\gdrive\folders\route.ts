import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';
import { authenticateGoogleOAuth2 } from "../authenticate";

type Item = {
  id: string;
  name: string;
  type: string;
  extension: string;
  size: number;
  icon: string;
  owner?: string;
  modifiedTime?: string;
  shared?: boolean;
  permissions?: {
    id: string;
    displayName: string;
    type: string;
    kind: string;
    photoLink: string;
    emailAddress: string;
    role: string;
    deleted: boolean;
    pendingOwner: boolean;
  }[];
  webViewLink: string;
  
  children?: React.ReactNode[];
}

type File = {
  id: string;
  name: string;
  mimeType: string;
  fileExtension: string;
  size: number;
  iconLink: string;
  shared?: boolean;
  permissions?: {
    id: string;
    displayName: string;
    type: string;
    kind: string;
    photoLink: string;
    emailAddress: string;
    role: string;
    deleted: boolean;
    pendingOwner: boolean;
  }[];
  webViewLink: string;
}

async function fetchFolderContents(
  drive: { files: { list: (arg0: { q: string; fields: string; }) => any; }; },
  folderId: string = 'root'
): Promise<any[]> {
  const response = await drive.files.list({
    q: `'${folderId}' in parents and trashed=false`,
    fields: 'files(id, name, mimeType, fileExtension, size, iconLink, owners, modifiedTime, shared, permissions, webViewLink)',
  });

  const items = await Promise.all(
    response.data.files.map(async (file: File & { owners: { displayName: string }[]; modifiedTime: string }) => {
      const item: Item = {
        id: file.id,
        name: file.name,
        type:
          file.mimeType === 'application/vnd.google-apps.folder'
            ? 'folder'
            : 'file',
        extension: file.fileExtension,
        size: file.size,
        icon: file.iconLink,
        owner: file.owners?.[0]?.displayName || 'Unknown',
        modifiedTime: file.modifiedTime,
        shared: file.shared,
        permissions: file.permissions,
        webViewLink: file.webViewLink,
      };

      if (item.type === 'folder') {
        item.children = await fetchFolderContents(drive, file.id);
      }

      return item;
    })
  );

  return items;
}
export async function GET(req: NextRequest) {
  try {
    const auth = await authenticateGoogleOAuth2();
    if (!auth) {
      throw new Error('Authentication failed');
    }

    const drive = google.drive({ version: 'v3', auth });
    const structure = await fetchFolderContents(drive);
    //console.log("structure", structure)

    return NextResponse.json({ structure });
  } catch (error) {
    console.error('Error fetching folder structure:', error);
    return NextResponse.json(
      { error: 'Failed to fetch folder structure' },
      { status: 500 }
    );
  }
}

import crypto from 'crypto';

interface TransactionData {
  date: Date | string;
  type: 'CREDIT' | 'DEBIT';
  categoryId: string;
  accountId: string;
  amount: number;
  description: string;
}

export function calculateTransactionHash(item: TransactionData): string {
  const dateObj = item.date instanceof Date ? item.date : new Date(item.date);
  const rocYear = dateObj.getFullYear() - 1911;
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const date = `${rocYear}/${month}`;
  const hashString = `${date}|${item.type}|${item.categoryId}|${item.accountId}|${item.amount}|${item.description}`;
  return crypto.createHash('md5').update(hashString).digest('hex');
}

export function calculateTransactionFullHash(item: TransactionData): string {
  const date = item.date;
  const hashString = `${date}|${item.type}|${item.categoryId}|${item.accountId}|${item.amount}|${item.description}`;
  return crypto.createHash('md5').update(hashString).digest('hex');
}

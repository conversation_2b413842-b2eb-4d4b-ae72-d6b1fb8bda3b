'use server'

import { prisma } from "@/lib/db";
import { Project, Task, Reminder } from "@prisma/client";
import { auth } from "@clerk/nextjs/server";

export type TaskData = {
  task: Task;
  project: Project;
  assignee: {
    id: string;
    workspaceId: string;
    name?: string;
    email?: string;
    image?: string;
  };
  reminders: <PERSON>minder[];
};
export type GetTaskResponse = 
  | { 
      data: TaskData
    }
  | { 
      error: string; 
      success: false; 
    };


export async function getTask(taskId: string): Promise<GetTaskResponse> {
  try {
    const { userId } = await auth();
    if (!userId) {
      return { error: "Unauthorized", success: false };
    }

    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        project: true,
        assignee: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
        reminders: true,
      },
    });

    if (!task) {
      throw new Error("Not authorized");
    }

    const member = await prisma.member.findFirst({
      where: { userId, workspaceId: task?.workspaceId },
    });

    if (!member) {
      return { error: "Unauthorized", success: false };
    }

    const basicTask = { 
      id: task.id, 
      name: task.name, 
      status: task.status, 
      description: task.description,
      userId: task.userId, 
      workspaceId: task.workspaceId, 
      projectId: task.projectId,
      assigneeId: task.assigneeId,
      startDate: task.startDate,
      dueDate: task.dueDate,
      position: task.position,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    } as Task;

    const assignee = {
      ...member,
      name: task.assignee?.user.name ?? undefined,
      email: task.assignee?.user.email ?? undefined,
      image: task.assignee?.user.image ?? undefined,
    };

    return {
      data: {
        task: basicTask,
        project: task.project,
        assignee,
        reminders: task.reminders,
      },
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to get task.",
      success: false,
    };
  }
}

export function csvMetadataExtraction(csvContent: string): [string, string] | never {
  if (!csvContent) {
    throw new Error('CSV content is empty');
  }
  const lines = csvContent.split('\n');
  
  if (lines.length < 3) {
    throw new Error('CSV must have at least 3 lines');
  }
  
  // Extract first line - your exact logic
  const firstLine = csvContent.split("\n")[0].trim().replace(/^"|"$/g, "");
  const [accountText] = firstLine.split(/[,\n]/); // Handle both CSV and plain text formats
  const accountId = accountText.match(/^(\d+)\s+(.+)/)?.[0] || accountText; // Match dynamic account name pattern
  
  // Extract date from third line
  const thirdLine = lines[2];
  const dateMatch = thirdLine.match(/(\d{4}\/\d{2}\/\d{2})\s*\)/);
  const endDate = dateMatch ? dateMatch[1].replace(/\//g, '') : null;
  
  if (!endDate) {
    throw new Error('Could not extract date from third line');
  }
  
  return [accountId, endDate];
}

// Output: { accountId: "************ 綜合活期存款", endDate: "********" }
import React, { useState } from "react";
import { TableCell } from "@/components/ui/table"; // adjust import as needed
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { useCreateDepositModal } from "@/hooks/use-create-deposit-modal";
import { useUpdateDepositModal } from "@/hooks/use-update-deposit-modal";
import { DeleteDepositButton } from "./delete-deposit-button";
import { NotebookTabs, Plus, Pencil, Trash2Icon } from "lucide-react";

interface DepositActionsProps {
  id: string;
  children: React.ReactNode;
}

export function DepositAction({ id, children }: DepositActionsProps) {
  const {open: create} = useCreateDepositModal()
  const {open} = useUpdateDepositModal()
  const [isAlertOpen, setIsAlertOpen] = useState<boolean>(false);
  return (
    <div className="flex justify-end">
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          {children}
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {/*<DropdownMenuItem>
            <NotebookTabs className="h-4 w-4" />
            View Details
          </DropdownMenuItem>*/}
          <DropdownMenuItem onClick={() => { create() }}>
            <Plus className="h-4 w-4" />
            Create Time Deposit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => { open(id) }}>
            <Pencil className="h-4 w-4" />
            Edit Time Deposit
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Trash2Icon className="h-4 w-4" />
            <DeleteDepositButton
              depositId={id}
              isAlertOpen={isAlertOpen}
              setIsAlertOpen={setIsAlertOpen}
            />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

import { ZepClient } from "@getzep/zep-cloud";
import { Message } from "@getzep/zep-cloud/api"
import { Document } from "@langchain/core/documents";

const API_KEY = process.env.ZEP_QSTAR_API_KEY;

export const zepClient = new ZepClient({
  apiKey: API_KEY,
});

export async function combineDocuments(docs: Document[], documentSeparator = "\n\n") {
  const docStrings: string[] = await Promise.all(
    docs.map((doc) => doc.pageContent)
  );
  return docStrings.join(documentSeparator);
}

export async function zepMemory(
  companionId: string, 
  sessionID: string, 
  userId: string,
  prompt: string,
) {

  let session
  let prevMemoryMessages: any[] = [];
  let prevMemorySummaries: any[] = [];
  let prevMemoryFacts: any[] = [];
  // Get session
    try {

      /*const user = await zepClient.user.get(userId);
      console.log("-------------------------------------->User: ", JSON.stringify(user));
      if (user) {
        await zepClient.user.add({
          userId,
          email: "<EMAIL>",
          firstName: "僑星福華",
          lastName: "",
        });
      }*/
      session = await zepClient.memory.getSession(sessionID);
      console.debug("Retrieved session ", session);
      if (!session) {
        // Add session associated with user
        const newSesstion = await zepClient.memory.addSession({
          sessionId: sessionID,
          metadata: { companionId },
          userId,
        });
        session = newSesstion;
        console.debug("Adding new session ", sessionID);
      }

      // Add memory. We could do this in a batch, but we'll do it one by one rather to
      // ensure that summaries and other artifacts are generated correctly.
      /*if (session) {
        await zepClient.memory.add(sessionID, {
          messages: [
            { role: "user", roleType: "user", content: prompt }
          ]
        });
        console.debug("Added new memory for session ", sessionID);
      }*/
    } catch (error) {
      console.debug("Got error:", error);
    }

  // Get newly added memory
    /*try {
      console.debug("Getting memory for newly added memory with sessionid ", sessionID);
      const memory = await zepClient.memory.get(sessionID);
      console.log("Memory: ", JSON.stringify(memory));
      if (memory?.messages) {
        memory.messages.forEach((message) => {
          console.debug(JSON.stringify(message));
        });
      }
    } catch (error) {
      console.error("Got error:", error);
    }*/

  // get session messages
  let sessionMessages: Message[] = [];
  let firstSessionsMessageId: string = ''
    try {
      //const sessionMessagesResult = await zepClient.memory.getSessionMessages(sessionID, { limit: 10, cursor: 1 });
      // To fetch the latest 5 messages, set cursor to 0 (most recent) and reverse the result if needed
      const sessionMessagesResult = await zepClient.memory.get(sessionID, { lastn: 3 });
      console.debug("Session messages: ", JSON.stringify(sessionMessagesResult));

      // Extract rowCount
      //const rowCount = sessionMessagesResult?.rowCount || 0;

      if (sessionMessagesResult?.messages) {
        sessionMessages = sessionMessagesResult.messages;
        firstSessionsMessageId = sessionMessages[0]?.uuid!;

          /*if (rowCount > 6) {
            // Synthesize a question from most recent messages.
            // Useful for RAG apps.
            // This is faster than using an LLM chain.
            console.debug("\n---Synthesize a question from most recent messages");
            const { question } = await zepClient.memory.synthesizeQuestion(sessionID, { lastNMessages: 3 });
            console.debug(`Question: ${question}`);
            
            // Search messages in memory with MMR and lambda=0.6
            try {
              prevMemoryMessages = await zepClient.memory.searchSessions({
                sessionIds: [sessionID],
                text: question,
                searchType: "mmr",
                mmrLambda: 0.6,
                limit: 3,
                searchScope: "messages",
              });


              if (prevMemoryMessages) {
                prevMemoryMessages.forEach((searchMessageResult) => {
                console.debug("Search Message Result: ", JSON.stringify(searchMessageResult.message));
                console.debug("Search Message Result Score: ", JSON.stringify(searchMessageResult.score));
                });        
                console.log("prevMemoryMessages", prevMemoryMessages)
              }
                
            } catch (error) {
              console.error("Got error:", error);
            }

            try {
              // Run both search operations in parallel
              const [prevMemorySummaries, memoryFacts] = await Promise.all([
                // Search summaries in memory with MMR and lambda=0.9
                zepClient.memory.search(sessionID, {
                  text: question ?? prompt,
                  searchScope: "summary",
                  searchType: "mmr",
                  mmrLambda: 0.8,
                  limit: 3,
                }),
                
                // Search facts in memory with MMR and lambda=0.9
                zepClient.memory.searchSessions({
                  sessionIds: [sessionID],
                  text: question ?? prompt,
                  searchScope: "facts",
                  searchType: "mmr",
                  mmrLambda: 0.8,
                  limit: 3,
                })
              ]);
            
              // Handle summaries
              if (prevMemorySummaries) {
                console.log("prevMemorySummaries", prevMemorySummaries);
                prevMemorySummaries.forEach((searchSummaryResult) => {
                  console.debug("Search Summary Result: ", JSON.stringify(searchSummaryResult.summary));
                  console.debug("Search Summary Result Score: ", JSON.stringify(searchSummaryResult.score));
                });
              }
            
              // Handle facts
              if (memoryFacts && memoryFacts?.results) {
                prevMemoryFacts = memoryFacts.results;
                console.log("memoryFacts", memoryFacts);
                memoryFacts.results.forEach((searchFactsResult) => {
                  console.debug("Search Facts Result: ", JSON.stringify(searchFactsResult.fact));
                  console.debug("Search Facts Result Score: ", JSON.stringify(searchFactsResult.score));
                });
              }
            
            } catch (error) {
              console.error("Got error:", error);
            }
         }*/
      }

      //return { prevMemoryMessages: sessionMessages, prevMemorySummaries, prevMemoryFacts};
      //return { prevMemoryFacts, prevMemorySummaries};
      return { prevMemoryMessages: sessionMessages};
    } catch (error) {
      console.error("Got error:", error);
    }

}

export const formatChatSummaries = (summaries: { summary: { content: string } }[]) => {
  const summaryContents = summaries.map(result => result.summary.content);
  return summaryContents.join('\n');
};

export const formatChatFacts = (facts: { fact: { fact: string } }[]) => {
  const factContents = facts.map(result => result.fact.fact);
  return factContents.join('\n');
};
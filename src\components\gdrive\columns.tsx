"use client"

import type { ColumnDef, Table, Row } from "@tanstack/react-table"
import { ItemAction } from "./item-action"
import { MdFolder } from "react-icons/md";
import { ArrowUpDown, MoreHorizontal, FileIcon, UsersRound } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { format, parseISO } from "date-fns"
import { toZonedTime } from "date-fns-tz"

export type GoogleDriveItem = {
  id: string
  name: string
  owner: string
  modifiedTime: string
  size: string
  type: "file" | "folder"
  icon: string
  extension?: string
  shared?: boolean;
  permissions?: {
    id: string;
    displayName: string;
    type: string;
    kind: string;
    photoLink: string;
    emailAddress: string;
    role: string;
    deleted: boolean;
    pendingOwner: boolean;
  }[];
  webViewLink?: string;
  children?: GoogleDriveItem[]
}

interface ColumnsProps {
  onDrillDown: (item: GoogleDriveItem) => void
  currentFolder: GoogleDriveItem | null
  setCurrentFolder: (item: GoogleDriveItem | null) => void
  selectedFiles?: GoogleDriveItem[];
  onFileSelect?: (item: GoogleDriveItem) => void;
}

export const createColumns = ({
  onDrillDown,
  currentFolder,
  setCurrentFolder,
  selectedFiles,
  onFileSelect,
}: ColumnsProps): ColumnDef<GoogleDriveItem>[] => [
  // Optional selection column
  ...(onFileSelect ? [{
    id: "select",
    header: () => null,
    cell: ({ row }: { row: Row<GoogleDriveItem> }) => (
      row.original.type === 'file' ? (
        <Checkbox
          checked={selectedFiles?.some(file => file.id === row.original.id)}
          onCheckedChange={(value) => {
            if (onFileSelect && row.original.type === 'file') {
              onFileSelect(row.original);
            }
          }}
          aria-label="Select file"
        />
      ) : null
    ),
  }] : []),
  {
    accessorKey: "name",
    sortingFn: (rowA, rowB) => {
      // Prioritize folders at the top
      const isRowAFolder = rowA.original.type === "folder";
      const isRowBFolder = rowB.original.type === "folder";

      if (isRowAFolder && !isRowBFolder) return -1;
      if (!isRowAFolder && isRowBFolder) return 1;

      // Alphabetical sorting for items of the same type
      return rowA.original.name.localeCompare(rowB.original.name);
    },
    header: ({ column }) => {
      return (
        <Button
          className="font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const item = row.original;
      const isShortcut = item?.icon && item?.icon.includes(
        "application/vnd.google-apps.shortcut"
      );

      const ShareIcon = () => (
        item?.shared && <UsersRound className="ml-2 h-3 w-3 text-muted-foreground" />
      );

      return (
        <div className="flex items-center">
          {item.type === "folder" ? (
            <Button
              variant="ghost"
              className="h-8 font-semibold p-0 hover:bg-transparent"
              onClick={() => onDrillDown(item)}
            >
              <MdFolder className="size-5 text-orange-400" />
              {item.name}
              <ShareIcon />
            </Button>
          ) : isShortcut ? (
            <div className="flex items-center">
              <FileIcon className="mr-2 h-4 w-4 text-blue-500" />
              <span className="italic">{item.name} (Shortcut)</span>
              <ShareIcon />
            </div>
          ) : (
            <div className="flex items-center">
              <FileIcon className="mr-2 h-4 w-4" />
              {item.webViewLink ? (
                <a 
                  href={item.webViewLink} 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="max-w-[20rem] truncate hover:text-clip"
                >
                  {item.name}
                </a>
              ) : (
                <p className="max-w-[20rem] truncate hover:text-clip">{item.name}</p>
              )}
              <ShareIcon />
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "type",
    id: "type",
    enableHiding: true, // Include in the table model but don't display it
    header: () => null, // Prevent it from showing in the table header
    cell: () => null, // Prevent it from rendering in rows
  },
  {
    accessorKey: "extension",
    id: "extension",
    enableHiding: false,
    filterFn: (row, id, value) => {
      if (!value || value === "all") return true;
      return row.getValue(id) === value.toLowerCase();
    },
    header: () => null, // Hide the header but keep the column for filtering
    cell: () => null, // Hide the cell but keep the column for filtering
  },
  {
    accessorKey: "owner",
    header: "Owner",
  },
  {
    accessorKey: "modifiedTime",
    header: ({ column }) => {
      return (
        <Button
          className="font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Modified Time
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const modifiedTimeValue: string | undefined = row.getValue("modifiedTime");
      
      if (!modifiedTimeValue) {
        return "N/A";
      }
  
      try {
        const modifiedTime = parseISO(modifiedTimeValue);
        const zonedTime = toZonedTime(
          modifiedTime,
          Intl.DateTimeFormat().resolvedOptions().timeZone
        );
        return format(zonedTime, "yyyy-MM-dd hh:mm a");
      } catch (error) {
        console.error("Error parsing modifiedTime:", error);
        return "Invalid Date"; // Fallback for invalid dates
      }
    },
    sortingFn: (rowA, rowB, columnId) => {
      const dateA = parseISO(rowA.getValue(columnId) || "1970-01-01T00:00:00Z");
      const dateB = parseISO(rowB.getValue(columnId) || "1970-01-01T00:00:00Z");
      return dateA.getTime() - dateB.getTime();
    },
  },
  
  {
    accessorKey: "size",
    header: ({ column }) => {
      return (
        <Button
          className="font-semibold"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Size
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const rawSize = row.getValue("size") as string;
      const size = Number.parseFloat(rawSize);

      if (row.original.type === "folder") {
        return "-"; // No size for folders
      }

      if (isNaN(size) || size <= 0) {
        // Handle invalid or missing size
        return "";
      }

      if (size >= 1024 * 1024 * 1024) {
        // Convert to GB
        return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
      } else if (size >= 1024 * 1024) {
        // Convert to MB
        return `${(size / (1024 * 1024)).toFixed(2)} MB`;
      } else if (size >= 1024) {
        // Convert to KB
        return `${(size / 1024).toFixed(2)} KB`;
      } else {
        // Display size in bytes
        return `${size} B`;
      }
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const item = row.original;
  
      return (
        <ItemAction 
          item={item}
          currentFolder={currentFolder}
          setCurrentFolder={setCurrentFolder}
        >
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </ItemAction>
      );
    },
  }
];
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { getTask } from "@/actions/tasks/get-task";
import { TaskIdClient } from "./_components/client";


interface TaskIdPageProps {
  params: Promise<{
    workspaceId: string;
    taskId: string;
  }>;
}

export default async function TaskIdPage(props: TaskIdPageProps) {
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const params = await props.params;
  const response = await getTask(params.taskId);

  if ('error' in response) {
    redirect(`/workspaces/${params.workspaceId}/tasks`);
  }
  
  return (
    <div className="flex flex-col gap-y-2">
      <TaskIdClient initialValues={response.data} />
    </div>
  )
}
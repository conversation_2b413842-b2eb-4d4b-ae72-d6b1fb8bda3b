import React, { useState, useEffect } from "react";
import type { FC } from "react";
import useDrivePicker from "react-google-drive-picker";
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card";
import { Loading } from "@/components/ui/loader";


interface FileData {
  id: string;
  name: string;
  mimeType: string;
  content?: string; // Optional, for text-based files
  webViewLink: string;
}

declare global {
  interface Window {
    google: any;
  }
}

const GooglePicker: FC = () => {
    const [openPicker] = useDrivePicker();
    const [files, setFiles] = useState<FileData[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
  
    const scope = [
      //"https://www.googleapis.com/auth/drive.file",
      //"https://www.googleapis.com/auth/drive"
      "https://www.googleapis.com/auth/drive.readonly",
      "https://www.googleapis.com/auth/drive.photos.readonly",
    ];
  
    const handleOpenPicker = async () => {
      setIsLoading(true);
      
      try {
        // Initialize Google Identity Services client
        const client = window.google.accounts.oauth2.initTokenClient({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
          scope: scope.join(' '),
          callback: async (tokenResponse: any) => {
            if (tokenResponse && tokenResponse.access_token) {
              const pickerConfig: any = { //Type 'PickerConfiguration' is missing the property 'callbackFunction'.
                clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
                developerKey: process.env.NEXT_PUBLIC_GOOGLE_DRIVE_PICKER!,
                viewId: "DOCS" as const,
                token: tokenResponse.access_token,
                showUploadView: true,
                showUploadFolders: true,
                supportDrives: true,
                multiselect: true,
                locale: "zh-TW",
                origin: window.location.origin,
                parentWindow: window,
                viewMimeTypes: "image/jpeg,image/png,image/gif",
                callbackFunction: async (data: { action: string; docs: any[]; }) => {
                  if (data.action === "picked") {
                    const accessToken = tokenResponse.access_token;
                    const driveFileUrl = "https://www.googleapis.com/drive/v3/files";
                    
                    const fetchedFiles = await Promise.all(
                      data.docs.map(async (item) => {
                        try {
                          const metadataResponse = await fetch(
                            `${driveFileUrl}/${item.id}?fields=id,name,mimeType,webViewLink`,
                            {
                              headers: {
                                Authorization: `Bearer ${accessToken}`,
                              },
                            }
                          );
                          
                          if (!metadataResponse.ok) {
                            console.error('Metadata fetch failed:', await metadataResponse.text());
                            return null;
                          }
  
                          const metadata = await metadataResponse.json();
                          return {
                            id: metadata.id,
                            name: metadata.name,
                            mimeType: metadata.mimeType,
                            webViewLink: metadata.webViewLink,
                          };
                        } catch (error) {
                          console.error('Error fetching file metadata:', error);
                          return null;
                        }
                      })
                    ).then(files => files.filter(file => file !== null));
  
                    setFiles((prevFiles) => [...prevFiles, ...fetchedFiles]);
                  }
                }
              };
              
              openPicker(pickerConfig);
            }
          },
        });
  
        // Request the token
        client.requestAccessToken();
  
      } catch (error) {
        console.error('Google Picker Error:', error);
      } finally {
        setIsLoading(false);
      }
    };
  
    const handleProcessFiles = () => {
      console.log("Files to process:", files);
    };
  
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-row intes-center gap-x-1">
            {isLoading && <Loading className="size-4" />}
            <Button
              className="h-8"
              onClick={handleOpenPicker}
              variant="outline"
              size={"sm"}
              disabled={isLoading}
            >
              {isLoading ? 'Loading...' : 'Open Google Picker'}
            </Button>
          </div>
          <div className="p-3">
            <p className="text-sm">Selected Files:</p>
            <div className="p-4">
              {files.length > 0 ? (
                files.map((file) => (
                  <div key={file.id}>
                    <p className="text-sm">
                      <strong>{file.name}</strong> ({file.mimeType})
                    </p>
                    <p className="text-sm">
                      <a
                        href={file.webViewLink}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        View File
                      </a>
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-sm">No files selected</p>
              )}
            </div>
          </div>
          <Button 
            onClick={handleProcessFiles} 
            size={"xs"} 
            className="h-8 mt-4"
            disabled={files.length === 0}
          >
            Process Files
          </Button>
        </CardContent>
      </Card>
    );
  };
  
  export default GooglePicker;
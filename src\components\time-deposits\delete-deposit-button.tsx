'use client'

import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useDeleteDeposit } from "./use-delete-deposit"
import { Loader2 } from "lucide-react"

interface DeleteDepositButtonProps {
  depositId: string
  isAlertOpen: boolean;
  setIsAlertOpen: (open: boolean) => void;
}

export function DeleteDepositButton({ 
  depositId, 
  isAlertOpen,
  setIsAlertOpen,
}: DeleteDepositButtonProps) {
  const { mutate, isPending } = useDeleteDeposit();
  const onDelete = async () => {
    mutate(depositId)
  }

  return (
    <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
      <AlertDialogTrigger asChild onClick={(e) => {
        e.preventDefault(); // Prevent dropdown from closing
        setIsAlertOpen(true);
      }}>
        <button disabled={isPending}>
          {isPending ? (
            <><Loader2 className="h-4 w-4 animate-spin" />Delete Deposit</>
          ) : (
            "Delete Deposit"
          )}
        </button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the deposit
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={() => setIsAlertOpen(false)}>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onDelete}>
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
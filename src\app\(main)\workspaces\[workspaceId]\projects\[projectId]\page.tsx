import Link from "next/link";
import { PencilIcon } from "lucide-react";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/actions/user/get-current-user";
import { getProject } from "@/actions/projects/get-project";

import { ProjectAvatar } from "@/components/projects/project-avatar";
import { TaskViewSwitcher } from "@/components/tasks/task-view-switcher";
import { ProjectIdClient } from "./client";
import { Button } from "@/components/ui/button";

interface ProjectIdPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default async function ProjectIdPage(props: ProjectIdPageProps) {
  const user = await getCurrentUser();
  if (!user) redirect("/");

  const params = await props.params;
  const initialValues = await getProject(params.projectId);

  if (!initialValues) {
    throw new Error("Project not found");
  }

  return (
    <div className="flex flex-col gap-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-1 text-sm">
          <ProjectAvatar
            name={initialValues?.name}
            image={initialValues?.imageUrl!}
            className="size-4"
            fallbackClassName="text-xs"
          />
          {initialValues?.name}          
        </div>

        <Button variant="outline" size="sm" asChild>
          <Link href={`/workspaces/${initialValues?.workspaceId}/projects/${initialValues?.id}/settings`}>
            <PencilIcon className="size-4" />
          </Link>
        </Button>
      </div>

      <ProjectIdClient projectId={params.projectId} />
      <TaskViewSwitcher initialProjectId={params.projectId} hideProjectFilter />
    </div>
  )
}
import { WebhookEvent, WebhookRequestBody } from '@line/bot-sdk';
import { NextRequest, NextResponse } from 'next/server';
import { handleMenuChange, handleMessageSend } from './handle-menu';

export async function POST(req: NextRequest) {
  if (!process.env.CHANNEL_ACCESS_TOKEN || !process.env.CHANNEL_SECRET) {
    return NextResponse.json({ error: 'LINE Bot 憑證未設定' }, { status: 500 });
  }

  try {
    const body = await req.json() as WebhookRequestBody;
    const events: WebhookEvent[] = body.events;

    for (const event of events) {
      if (event.type === 'postback') {
        const params = new URLSearchParams(event.postback.data);
        const action = params.get('action');

        switch (action) {
          case 'change_menu':
            await handleMenuChange(event);
            break;
          case 'send_message':
            await handleMessageSend(event);
            break;
          default:
            console.warn('未處理的 postback action:', action);
        }
      }
    }

    return NextResponse.json({ message: '處理成功' });
  } catch (error) {
    console.error('Webhook 處理失敗:', error);
    return NextResponse.json({ error: 'Webhook 處理失敗' }, { status: 500 });
  }
}

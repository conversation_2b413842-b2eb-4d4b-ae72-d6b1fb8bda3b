"use client"

import { useQuery } from "@tanstack/react-query";
import { getAccountDetails } from "@/actions/banking/get-account-details";

interface UseAccountsProps {
  accountId: string;
  startDate: Date;
  endDate?: Date;
  shouldFetch?: boolean | Date;
}

export const useGetAccounts = ({
  accountId,
  startDate,
  endDate,
  shouldFetch
}: UseAccountsProps) => {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: [
      "banking", 
      accountId, 
      endDate?.toISOString()
    ],
    queryFn: async () => {
      const response = await getAccountDetails(
        accountId,
        startDate,
        endDate
      );

      //console.log("Raw response:", response);

      if (!response.success) {
        throw new Error(response.error || "Failed to fetch accountes");
      }

      if (response.data) {
        return response.data;
      }
    },
    enabled: !!accountId && !!startDate && !!endDate && !!shouldFetch,
  });

  return {
    data,
    isLoading,
    error,
    refetch
  };
};

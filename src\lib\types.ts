export type ServerActionResult<Result> = Promise<
  | Result
  | {
      error: string
    }
>

export interface FileWithPreview extends File {
  preview: string
}

export interface OutputImgSlider {
  previewURL: string
  outputURL: string
}

// Copilot
export interface SlideModel {
  content: string;
  backgroundImageUrl: string;
  backgroundImageDescription: string;
  spokenNarration: string;
}
export interface Cell {
  value: string;
}

export type SpreadsheetRow = Cell[];

export interface SpreadsheetData {
  id: string;
  title: string;
  rows: SpreadsheetRow[];
}

export type Resource = {
  url: string;
  title: string;
  description: string;
};

export type AgentState = {
  model: string;
  research_question: string;
  report: string;
  resources: any[];
  logs: any[];
}

export type Timeframe = "month" | "year";
export type Period = { year: number; month: number };
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createProject } from '@/actions/projects/create-project';
import { z } from 'zod';
import { createProjectSchema } from '@/components/projects/schemas';
import { toast } from 'sonner';


export function useCreateProject() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (values: z.infer<typeof createProjectSchema>) => {
      console.log("values@useCreateProject", values)
      const response = await createProject(values);
      
      if (!response?.success || !response?.data) {
        throw new Error(response.error || 'Failed to create project.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Project created!');
      // Refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['project', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create project.');
    },
  });

  return mutation;
}

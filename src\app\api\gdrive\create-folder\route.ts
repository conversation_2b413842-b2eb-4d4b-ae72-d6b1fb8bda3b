import { google } from "googleapis";
import { NextResponse } from "next/server";
import { authenticateGoogleOAuth2 } from "../authenticate";


const uploadFolderToDrive = async (folderId: string | null, folderName: string): Promise<{ folder: { data: any }, existingFolder: boolean } | { error: string }> => {
  // Validate input
  if (!folderName || folderName.trim() === '') {
    throw new Error('Folder name cannot be empty');
  }

  // Sanitize folder name (remove invalid characters)
  const sanitizedFolderName = folderName.replace(/[/\\?*:|"<>]/g, '').trim();

  const auth = await authenticateGoogleOAuth2();
  if (!auth) {
    throw new Error("Authentication failed");
  }

  const drive = google.drive({ version: "v3", auth });
  
  try {
    // Check if folder with same name already exists in the parent folder
    const existingFolderQuery = `name='${sanitizedFolderName}' and mimeType='application/vnd.google-apps.folder' and trashed=false`;
    const existingFolderParams: any = {
      q: existingFolderQuery,
      fields: 'files(id, name)'
    };

    // Add parent folder condition if not root
    if (folderId && folderId !== 'root') {
      existingFolderParams.q += ` and '${folderId}' in parents`;
    }

    const existingFolderResponse = await drive.files.list(existingFolderParams);

    // If folder already exists, return existing folder
    if (existingFolderResponse.data.files && existingFolderResponse.data.files.length > 0) {
      const existingFolder = existingFolderResponse.data.files[0];
      return {
        folder: {
          data: {
            id: existingFolder.id,
            name: existingFolder.name,
            type: 'folder'
          }
        },
        existingFolder: true
      };
    }

    // Create new folder
    const requestBody: any = {
      name: sanitizedFolderName,
      mimeType: "application/vnd.google-apps.folder",
    };

    // Add parents if folderId is provided and not 'root'
    if (folderId && folderId !== 'root') {
      requestBody.parents = [folderId];
    }

    const response = await drive.files.create({
      requestBody,
      fields: 'id, name, mimeType, fileExtension, size, iconLink, owners, modifiedTime, shared, permissions, webViewLink',
      supportsAllDrives: true,
    });

    const folder = response.data;
    const item = {
      id: folder.id,
      name: folder.name,
      type: folder.mimeType === 'application/vnd.google-apps.folder' ? 'folder' : 'file',
      extension: folder.fileExtension,
      size: folder.size,
      icon: folder.iconLink,
      owner: folder.owners?.[0]?.displayName || 'Unknown',
      modifiedTime: folder.modifiedTime,
      shared: folder.shared,
      permissions: folder.permissions,
      webViewLink: folder.webViewLink,
    };

    return {
      folder: { data: item },
      existingFolder: false
    };
  } catch (error) {
    console.error('Error creating folder:', error);
    return { error: 'Failed to create folder' };
  }
};

export async function POST(req: Request) {
  try {
    const res = await req.json();
    const { folderId, folderName } = res;

    if (!folderName) {
      return NextResponse.json(
        { error: 'Folder name is required' }, { status: 400 });
    }

    // Pass null or 'root' for root folder creation
    const result = await uploadFolderToDrive(folderId ?? 'root', folderName);
    
    if ('error' in result) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json(
      { 
        folder: result.folder,
        existingFolder: result.existingFolder 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Failed to create folder' }, { status: 500 });
  }
}


import { useQuery } from "@tanstack/react-query";
import { getTask } from "@/actions/tasks/get-task";

interface UseTaskProps {
  taskId: string
}

export const useGetTask = ({ 
  taskId, 
}: UseTaskProps) => {
  const { data, isLoading, error } = useQuery({
    queryKey: [
      "task", 
      taskId
    ],
    queryFn: async () => {
      const response = await getTask(taskId);
      
      if ('error' in response) {
        throw new Error(response.error);
      }
      return response.data;
    },
    enabled: !!taskId,
  });

  return {
    data,
    isLoading,
    error,
  };
};
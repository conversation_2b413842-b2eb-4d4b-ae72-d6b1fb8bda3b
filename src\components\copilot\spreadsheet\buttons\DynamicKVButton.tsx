"use client"

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { uploadIncomeExpense } from '../actions/upload-unique';
import { AlertCircle, Loader2 } from 'lucide-react'
import { toast } from 'sonner';

type KVData = {
  date: string;
  item: string;
  value: string;
  sheetName: string;
  type?: string | null;
  categoryValidation: boolean;
};

type UploadData = {
  value: number;
  sheetName: string;
  item: string;
  type: 'CREDIT' | 'DEBIT';
  year: string;
  month: string;
  day?: string;
}

type LastImportData = {
  lastUpdated: string;
  rowCount: number;
  type: string;
};

export const DynamicKeyValueButton = ({
  open,
  onClose,
  normalizedData,
  mappingType,
  bankAccounts,
  categories,
  spreadsheets,
}: {
  open: boolean;
  onClose: () => void;
  normalizedData: KVData[];
  mappingType: "single" | "multi" | null;
  bankAccounts: { id: string; name: string; accountType: string }[];
  categories:  { id: string; name: string; }[];
  spreadsheets: { title: string }[];
}) => {
  const [types, setTypes] = useState<{ [sheetName: string]: string }>({});
  const [sheetToCategory, setSheetToCategory] = useState<{ [sheetName: string]: string | null }>({});
  const [sheetToBankAccount, setSheetToBankAccount] = useState<{ [sheetName: string]: string | null }>({});
  const [enrichedData, setEnrichedData] = useState<KVData[]>([]);
  const [lastImports, setLastImports] = useState<{ [sheetName: string]: LastImportData | null }>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleTypeChange = (sheetName: string, type: string) => {
    setTypes((prev) => ({ ...prev, [sheetName]: type }));
    setEnrichedData(
      normalizedData.map((row) => ({
        ...row,
        type: row.sheetName === sheetName ? type : types[row.sheetName] || null,
      }))
    );
  };

  const handleCategoryChange = (sheetName: string, categoryId: string) => {
    setSheetToCategory((prev) => ({ ...prev, [sheetName]: categoryId }));
  };

  const handleBankAccountChange = (sheetName: string, bankAccountId: string) => {
    setSheetToBankAccount((prev) => ({ ...prev, [sheetName]: bankAccountId }));
  };

  async function fetchLastImports() {
    try {
      console.log("spreadsheets: ", spreadsheets)
      const imports = await Promise.all(
        spreadsheets.map(async (sheet) => {
          const response = await fetch(`/api/spreadsheet/last-updated?sheetName=${encodeURIComponent(sheet.title)}`);
          if (!response.ok) {
            throw new Error(`Failed to fetch last import information for ${sheet.title}`);
          }
          const data = await response.json();
          return { [sheet.title]: data };
        })
      );
      setLastImports(Object.assign({}, ...imports));
    } catch (error) {
      console.error('Error fetching last import information:', error);
      toast.error('Failed to fetch last import information');
    }
  }

  useEffect(() => {
    fetchLastImports();
  }, [spreadsheets]);

  const handleSaveToDatabase = async () => {
    const missingSelections = spreadsheets.filter(
      (sheet) => !sheetToBankAccount[sheet.title] || !sheetToCategory[sheet.title]
    );
    if (missingSelections.length > 0) {
      toast.error(
        `Please select a bank account and category for the following sheets: ${missingSelections
          .map((sheet) => sheet.title)
          .join(", ")}`
      );
      //return;
    }
  
    try {
      setIsLoading(true);
      const formattedData = enrichedData
        .filter((row) => row.type && sheetToBankAccount[row.sheetName] && sheetToCategory[row.sheetName])
        .map((row) => {
          const cleanValue = parseFloat(row.value.replace(/,/g, ""));
          const [yearStr, monthStr] = row.date.split("/");
  
          let type: "CREDIT" | "DEBIT";
          switch (row.type) {
            case "income":
              type = "CREDIT";
              break;
            case "expense":
              type = "DEBIT";
              break;
            default:
              throw new Error(`Unexpected category for row: ${row.item}`);
          }
  
          return {
            //sheetName: row.sheetName,
            amount: cleanValue,
            description: row.item,
            date: row.date,
            year: yearStr,
            month: monthStr,
            type,
            bankAccountId: sheetToBankAccount[row.sheetName]!,
            categoryId: sheetToCategory[row.sheetName]!,
            categoryValidated: true
          };
        });
  
      console.log("formattedData: ", formattedData, );
  
      if (formattedData.length === 0) {
        toast.error("No data selected");
        onClose();
        return;
      }
  
      const results = await Promise.all(
        spreadsheets.map(async (sheet) => {
          //const sheetData = formattedData.filter((row) => row.sheetName === sheet.title);
          if (formattedData.length > 0) {
            return await uploadIncomeExpense(formattedData, sheet.title);
          }
          return null;
        })
      );
  
      const errors = results.filter((result) => result && !result.success);
      if (errors.length > 0) {
        toast.error("Some sheets failed to upload. Check the console for details.");
        console.error("Upload errors:", errors);
      } else {
        toast.success("Data successfully saved to database");
      }
      onClose();
    } catch (error) {
      console.error("Error saving data:", error);
      toast.error("Failed to upload data to the database");
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mappingType === "single" ? "Single Key-Value Mapping" : "Multi Key-Value Mapping"}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-row text-sm">      
          {Object.entries(lastImports).map(([sheetName, importData]) => (
            importData && (
              <Alert key={sheetName}>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>{sheetName}</AlertTitle>
                <AlertDescription className="text-xs">
                  Last imported on: {new Date(importData.lastUpdated).toLocaleString()}
                  <br />
                  Rows imported: {importData.rowCount}
                  <br />
                  Type: {importData.type}
                </AlertDescription>
              </Alert>
            )
          ))}
        </div>
  
        <div className="text-sm">
          {spreadsheets.map((sheet) => (
            <div key={sheet.title} className="grid grid-cols-3 gap-6 my-1">
             {/* Type Selection */}
              <div className="flex items-center gap-1">
                <span className="min-w-[10rem] font-semibold">{sheet.title}</span>
                <Select
                  value={types[sheet.title] || ""}
                  onValueChange={(value) => handleTypeChange(sheet.title, value)}
                >
                  <SelectTrigger id={`type-${sheet.title}`} className="h-8 p-1">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="income">收入</SelectItem>
                    <SelectItem value="expense">支出</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Categories Selection */}
              <div className="flex items-center gap-1">
                <label className="block mb-1 text-sm font-medium">Category</label>
                <Select
                  value={sheetToCategory[sheet.title] || ""}
                  onValueChange={(value) => handleCategoryChange(sheet.title, value)}
                >
                  <SelectTrigger id={`category-${sheet.title}`} className="h-8 p-1">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent className="p-1">
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Bank Account Selection */}
              <div className="flex items-center gap-1">
                <label className="block mb-1 text-sm font-medium">Account</label>
                <Select
                  value={sheetToBankAccount[sheet.title] || ""}
                  onValueChange={(value) => handleBankAccountChange(sheet.title, value)}
                >
                  <SelectTrigger id={`account-${sheet.title}`} className="h-8 p-1">
                    <SelectValue placeholder="Bank Account" />
                  </SelectTrigger>
                  <SelectContent className="p-1">
                    {bankAccounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        {account.name} ({account.accountType})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          ))}
        </div>
  
        {/* Data Preview */}
        <div className="overflow-auto max-h-100 text-sm">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="p-1">Month</TableHead>
                <TableHead className="p-1">Item</TableHead>
                <TableHead className="text-right p-1">Value</TableHead>
                <TableHead className="text-center p-1">Sheet</TableHead>
                <TableHead className="p-1">Type</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {normalizedData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell className="p-1">{row.date}</TableCell>
                  <TableCell className="p-1">{row.item}</TableCell>
                  <TableCell className="text-right p-1">{row.value}</TableCell>
                  <TableCell className="text-center p-1">{row.sheetName}</TableCell>
                  <TableCell className="p-1">{types[row.sheetName] || ""}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
          
        <Button
          className="w-full"
          disabled={isLoading}
          onClick={handleSaveToDatabase}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Upload to Database...
            </>
          ) : (
            "Upload to Database"
          )}
        </Button>
      </DialogContent>
    </Dialog>
  );
};
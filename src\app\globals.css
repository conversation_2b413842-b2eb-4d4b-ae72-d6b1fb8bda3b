@tailwind base;
@tailwind components;
@tailwind utilities;

@media print{
  #no-print{
    display: none;
  }

  #print-area{
    display: block;
    margin-top: 0mm;
    margin-bottom: 0mm;
  }

  body{
    margin: 0;
    padding: 0;
    box-shadow: none;
  }
}

@layer base {
  :root {
    --base-palette-gradients-purple: linear-gradient(97deg, rgba(163, 75, 233, 1), rgba(82, 43, 92, 1));
    --base-palette-gradients-green: linear-gradient(97deg, rgba(0, 168, 57, 1), rgba(0, 201, 189, 1));
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-0: 215 50% 23%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --chart-6: 300 65% 55%;
    --chart-7: 152 45% 50%;
    --chart-8: 190 95% 39%;
    --chart-9: 350 60% 55%;
    --chart-10: 240 20% 60%;
    --chart-11: 220 80% 60%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-0: 215 50% 50%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --chart-6: 300 60% 60%;
    --chart-7: 152 50% 55%;
    --chart-8: 190 85% 45%;
    --chart-9: 350 55% 60%;
    --chart-10: 240 25% 65%;
    --chart-11: 220 80% 65%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  :root {
    --chart-0: 215 50% 23%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --chart-6: 300 65% 55%;
    --chart-7: 152 45% 50%;
    --chart-8: 190 95% 39%;
    --chart-9: 350 60% 55%;
    --chart-10: 240 20% 60%;
    font-family: Inter, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "salt";
  }

  .dark {
    --chart-0: 215 50% 50%;
    --chart-1: 12 70% 50%;
    --chart-2: 173 60% 45%;
    --chart-3: 197 45% 40%;
    --chart-4: 43 70% 55%;
    --chart-5: 27 80% 55%;
    --chart-6: 300 60% 60%;
    --chart-7: 152 50% 55%;
    --chart-8: 190 85% 45%;
    --chart-9: 350 55% 60%;
    --chart-10: 240 25% 65%;
  }

  * {
    @apply border-border;
  }

  @supports (font-variation-settings: normal) {
    :root {
      font-family: InterVariable, sans-serif;
      font-feature-settings: "cv02", "cv03", "cv04", "cv11", "salt";
    }
  }
  

  body {
    @apply bg-background text-foreground;
  }
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::--webkit-scrollbar {
  display: none;
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
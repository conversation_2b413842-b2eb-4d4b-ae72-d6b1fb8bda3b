import { NextRequest, NextResponse } from 'next/server';
import { uploadImageToMaintenanceFeeDrive } from '@/lib/google/upload-image-to-maintenance-fee-drive';
import { appendAutoPayResidentToSheet } from '@/lib/google/append-autopay-resident-to-sheet';
import { fillAutoPayAmountForResident } from '@/lib/google/upload-deposit-to-sheets';
import { analyzeAutoPayResidentImage } from '@/app/api/webhook/functions';

export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const file = formData.get('file') as File;
  const buffer = Buffer.from(await file.arrayBuffer());
  const base64Image = buffer.toString('base64');
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `auto_pay_resident_${timestamp}.jpg`;

  const [driveUploadResult, analyzedAutoPayResident] = await Promise.all([
    uploadImageToMaintenanceFeeDrive(buffer, filename),
    analyzeAutoPayResidentImage(base64Image),
  ]);
  const autoPayResidentData = {
    ...analyzedAutoPayResident,
    googleDriveLink: driveUploadResult.webViewLink || undefined,
  };
  const [appendResult, fillResultsArr] = await Promise.all([
    appendAutoPayResidentToSheet(autoPayResidentData),
    fillAutoPayAmountForResident(autoPayResidentData),
  ]);
  return NextResponse.json({
    appendResult,
    fillResultsArr,
  });
}
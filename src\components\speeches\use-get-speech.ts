import { useQuery } from "@tanstack/react-query";
import { getSpeech } from "@/actions/speeches/get-speech";

interface UseSpeechProps {
  speechId: string
}

export const useGetSpeech = ({ 
  speechId, 
}: UseSpeechProps) => {
  const { data, isLoading, error } = useQuery({
    queryKey: [
      "speech", 
      speechId
    ],
    queryFn: async () => {
      const response = await getSpeech(speechId);
      
      if ('error' in response) {
        throw new Error(response.error);
      }
      return response.data;
    },
    enabled: !!speechId,
  });

  return {
    data,
    isLoading,
    error,
  };
};
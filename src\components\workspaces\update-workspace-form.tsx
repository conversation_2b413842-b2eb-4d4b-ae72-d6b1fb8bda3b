"use client"

import Link from "next/link";
import { useState, useTransition } from "react";
import { Workspace } from "@prisma/client";
import { useUpdateWorkspace } from "./use-update-workspace";
import { updateWorkspaceSchema } from "./schemas";
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner"
import { ChevronsLeft, CopyIcon, Loader2 } from "lucide-react";
import { DeleteWorkspaceButton } from "./delete-workspace-button";
import { ResetInviteCodeButton } from "./reset-invite-code-button";


interface UpdateWorkspaceFormProps {
  workspace: Workspace;
  onCancel?: () => void;
}

type FormValues = z.infer<typeof updateWorkspaceSchema>;

export const UpdateWorkspaceForm = ({ workspace, onCancel }: UpdateWorkspaceFormProps) => {
  const [inviteCode, setInviteCode] = useState(workspace.inviteCode); 
  const [isPending, startTransition] = useTransition();

  const { mutate, isPending: isUpdating } = useUpdateWorkspace(workspace.id);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(updateWorkspaceSchema),
    defaultValues: {
      name: workspace.name,
      orgnr: workspace.orgnr || undefined,
      address: workspace.address || '',
      postalCode: workspace.postalCode || '',
      city: workspace.city || '',
    },
  });

  const onSubmit = async (values: FormValues, e: React.BaseSyntheticEvent | undefined) => {
    e?.preventDefault();

    startTransition(async () => {
      mutate(values);    
    })
  }

  let fullInviteLink = "";
  if (typeof window !== undefined) {
    fullInviteLink = `${window.location.origin}/workspaces/${workspace.id}/join/${inviteCode}`;
  }
  

  const handleCopyInviteLink = () => {
    navigator.clipboard
      .writeText(fullInviteLink)
      .then(() => toast.success("Invite link copied to clipboard"))
      .catch(() => toast.error("Failed to copy invite link"));
  };

  const handleInviteCodeReset = (newInviteCode: string) => {
    setInviteCode(newInviteCode); 
  };

  return (
    <div className="flex flex-col gap-y-2">
      <Card className="w-full h-full border-none shadow-none">
        <CardHeader className="flex p-7 pt-2">
          <CardTitle className="flex items-center gap-x-2 text-xl font-bold">
            <Button variant="outline" size="sm" className="p-2">
              <Link href={`/workspaces/${workspace.id}`}>
                <ChevronsLeft className="size-8" />
              </Link>
            </Button>
            Update Workspace
          </CardTitle>
        </CardHeader>
        <div className="px-7">
          <Separator />
        </div>
        <CardContent className="p-7">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Workspace Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter workspace name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="orgnr"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Organization Number</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="Enter organization number" 
                          onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="postalCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Postal Code</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter postal code" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter city" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />      

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <Separator className="px-7" />
              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={onCancel}>
                  <Link href={`/workspaces/${workspace.id}`}>
                    Cancel
                  </Link>
                </Button>
                <Button type="submit" disabled={isPending}>
                  {isPending ? (
                    <><Loader2 className="h-4 w-4 animate-spin" />Update Workspace</>
                  ) : (
                    "Update Workspace"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
      <Card className="w-full h-full border-none shadow-none">
        <CardContent className="px-7">
          <div className="flex flex-col mb-4">
            <h3 className="font-bold">Invite Members</h3>
            <p className="text-sm text-foreground/60">
              Use the invite link to add members to your workspace.
            </p>
            <div className="mt-4">
              <div className="flex items-center gap-x-2">
                <Input disabled value={fullInviteLink} />
                <Button onClick={handleCopyInviteLink} variant={"secondary"} >
                  <CopyIcon className="size-5" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card className="w-full h-full border-none shadow-none">
        <CardContent className="px-7">
          <div className="flex flex-col mb-4">
            <h3 className="font-bold">Danger Zone</h3>
            <p className="text-sm text-foreground/60">
              Reset invide code.The previous code will no longer be valid
            </p>
          </div>
          <div className="flex justify-end space-x-4">
            <ResetInviteCodeButton
              workspaceId={workspace.id}
              workspaceName={workspace.name}
              isPending={isPending}
              startTransition={(callback) => {
                startTransition(callback);
              }}
              onInviteCodeReset={handleInviteCodeReset}
            />
          </div>
        </CardContent>
      </Card>
      <Card className="w-full h-full border-none shadow-none">
        <CardContent className="px-7">
          <div className="flex flex-col mb-4">
            <h3 className="font-bold">Danger Zone</h3>
            <p className="text-sm text-foreground/60">
              Deleting a workspace will remove all associated data
            </p>
          </div>
          <div className="flex justify-end space-x-4">
            <DeleteWorkspaceButton
              workspaceId={workspace.id}
              workspaceName={workspace.name}
              isPending={isPending}
              startTransition={(callback) => {
                startTransition(callback);
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>

  );
};
// src/hooks/use-get-accounts.ts
"use client";

import { useQuery } from "@tanstack/react-query";
import { getSelectAccounts } from "@/actions/account/get-select-accounts"; // Ensure this action exists
import { BankAccount, AccountType } from "@prisma/client";

// Define and export the structure of the account data needed
export type SelectAccountData = Pick<BankAccount, 'id' | 'name' | 'accountType'>;

// Define the expected return structure from the server action
interface GetSelectAccountsResponse {
  success: boolean;
  accounts?: SelectAccountData[];
  error?: string;
}

/**
 * Fetches accounts suitable for select dropdowns using React Query.
 * @returns {object} The query result containing accounts data, loading state, and error state.
 */
export function useGetAccounts() {
  const query = useQuery<SelectAccountData[], Error>({
    queryKey: ["accounts", "select"],
    queryFn: async (): Promise<SelectAccountData[]> => {
      const response: GetSelectAccountsResponse = await getSelectAccounts(); // Ensure this action exists
      if (!response.success || !response.accounts) {
        throw new Error(response.error || "Failed to fetch accounts");
      }
      // Filter for VIRTUAL accounts if needed, otherwise return all
      return response.accounts.filter(acc => acc.accountType === AccountType.VIRTUAL);
      //return response.accounts;
    },
    staleTime: 300000, // 5 minutes stale time
  });

  return {
    accounts: query.data ?? [],
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch
  };
}
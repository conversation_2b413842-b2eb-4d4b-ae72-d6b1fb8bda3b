import { after } from 'next/server'
import { currentUser } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { revalidatePath } from "next/cache";
import { NextRequest, NextResponse } from "next/server";
import { serve } from "@upstash/workflow/nextjs";
import { sendEmail } from "@/lib/workflow";
import { createTaskSchema } from "@/components/tasks/schemas";
import { Task ,Reminder, ReminderBasis, TaskStatus } from "@prisma/client";


export const POST = async (req: NextRequest) => {
  try {
    const user = await currentUser();
    const userId = user?.id
    const assignerName = user?.firstName + " " + user?.lastName
    const assignerEmail = user?.emailAddresses[0].emailAddress
    if (!userId) return NextResponse.json({ error: "Unauthorized", success: false }, { status: 401 });

    const body = await req.json();
    const validated = createTaskSchema.safeParse(body);
    if (!validated.success) {
      return NextResponse.json({ error: "Invalid fields", errors: validated.error.format(), success: false }, { status: 400 });
    }

    const { name, workspaceId, projectId, startDate, dueDate, assigneeId, status, reminders } = validated.data;

    // Verify user is a member of the workspace
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId },
    });
    if (!member) return NextResponse.json({ error: "Unauthorized", success: false }, { status: 403 });

    // Verify assignee is a workspace member
    const assigneeMember = await prisma.member.findFirst({
      where: { id: assigneeId, workspaceId },
    });
    if (!assigneeMember) return NextResponse.json({ error: "Invalid assignee", success: false }, { status: 404 });

    // Get highest position task for proper sorting
    const highestPositionTask = await prisma.task.findFirst({
      where: { workspaceId, status },
      orderBy: { position: "desc" },
    });
    const newPosition = highestPositionTask ? highestPositionTask.position + 1000 : 1000;

    // Create task with reminders
    const task = await prisma.task.create({
      data: {
        name,
        status,
        userId,
        workspaceId,
        projectId,
        assigneeId,
        startDate: startDate ?? null,
        dueDate,
        position: newPosition,
        reminders: {
          create: reminders?.map((reminder) => ({
            enabled: reminder.enabled,
            basis: reminder.basis as ReminderBasis,
            daysBefore: reminder.daysBefore ?? 1,
            customDate: reminder.customDate ?? null,
          })) ?? [],
        },
      },
      include: { reminders: true, assignee: { include: { user: true } } },
    });

    console.log("task", task)

    after(async () => {
      await sendEmail({
        subject: `Task Assigned: ${task.name}`,
        assignerName,
        assignerEmail: assignerEmail ?? "",
        assigneeName: task.assignee?.user.name ?? "",
        assigneeEmail: task.assignee?.user.email ?? "",
        taskName: task.name,
        startDate: task.startDate?.toISOString() ?? task?.dueDate!.toISOString(),
        dueDate: task?.dueDate!?.toISOString(),
        taskDescription: task.description ?? "No description provided",
        timezone: task.assignee?.user.timezone ?? "UTC"
      })
      const { POST: handler } = serve(async (context) => {
        console.log("after")
        // Immediate notification for task creation
        await context.run("task-assignment", async () => {
          await sendEmail({
            subject: `Task Assigned: ${task.name}`,
            assignerName,
            assignerEmail: assignerEmail ?? "",
            assigneeName: task.assignee?.user.name ?? "",
            assigneeEmail: task.assignee?.user.email ?? "",
            taskName: task.name,
            startDate: task.startDate?.toISOString() ?? task?.dueDate!.toISOString(),
            dueDate: task?.dueDate!?.toISOString(),
            taskDescription: task.description ?? "No description provided",
            timezone: task.assignee?.user.timezone ?? "UTC"
          });
        });

        // Schedule reminders
        for (const reminder of task.reminders) {
          if (!reminder.enabled) continue;

          const reminderDate = calculateReminderDate(reminder, task);
          if (!reminderDate) continue;

          await context.sleepUntil(`reminder-${task.id}-${reminder.id}`, reminderDate);
          await context.run("send-reminder", async () => {
            const updatedTask = await prisma.task.findUnique({
              where: { id: task.id },
              include: {
                assignee: { include: { user: true } },
              },
            });

            if (updatedTask && !updatedTask.status.includes(TaskStatus.DONE)) {
              await sendEmail({
                subject: `Task Assigned: ${task.name}`,
                assignerName,
                assignerEmail: assignerEmail ?? "",
                assigneeName: task.assignee?.user.name ?? "",
                assigneeEmail: task.assignee?.user.email ?? "",
                taskName: task.name,
                startDate: task.startDate?.toISOString() ?? task?.dueDate!.toISOString(),
                dueDate: task?.dueDate!?.toISOString(),
                taskDescription: task.description ?? "No description provided",
                timezone: task.assignee.user.timezone ?? "UTC"
              });
            }
          });
        }
      });
      return await handler(req);
    });

    // Revalidate paths
    revalidatePath(`/workspaces/${workspaceId}`);
    return NextResponse.json({ data: task, success: true });
  } catch (error) {
    console.log("error: ", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to create task", success: false },
      { status: 500 }
    );
  }
}

function calculateReminderDate(reminder: Reminder, task: Task) {
  if (reminder.customDate) return reminder.customDate;

  const basisDate = reminder?.basis === ReminderBasis.START_DATE ? task?.startDate : task.dueDate;
  if (!basisDate) return null;

  const reminderDate = new Date(basisDate);
  reminderDate.setDate(reminderDate.getDate() - (reminder.daysBefore ?? 1));
  return reminderDate;
}

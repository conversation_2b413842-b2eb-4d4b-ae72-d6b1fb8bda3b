import { messagingApi } from '@line/bot-sdk';
import { format, parse } from 'date-fns';

type Bulletin = {
  id: number;
  enable: boolean;
  title: string;
  topic: string;
  content: string;
  date: string;
  severity: string;
  image?: string;
}

function formatBulletinDate(dateStr: string): string {
  try {
    // Parse the ISO date string (YYYY-MM-DD)
    const date = parse(dateStr, 'yyyy-MM-dd', new Date());
    // Convert to Taiwan calendar year (minus 1911)
    const twYear = date.getFullYear() - 1911;
    // Format month and day with leading zeros
    const month = format(date, 'MM');
    const day = format(date, 'dd');
    // Return formatted string
    return `中華民國 ${twYear} 年 ${month} 月 ${day} 日`;
  } catch (error) {
    // Return original string if parsing fails
    console.error('Date parsing error:', error);
    return dateStr;
  }
}

export function createAnnounceFlexMessage(bulletins: Bulletin[]): messagingApi.FlexContainer {
  const bubbles: messagingApi.FlexBubble[] = bulletins.map((bulletin) => {
    // Build the body contents array properly
    const bodyContents: messagingApi.FlexComponent[] = [
      {
        type: "box",
        layout: "vertical",
        contents: [
          {
            type: "text",
            text: bulletin.topic,
            align: "center",
            weight: "bold",
            size: "lg",
          },
        ],
      },
    ];

    // Conditionally add image if it exists
    if (bulletin.image) {
      bodyContents.push({
        type: "image",
        url: bulletin.image,
        size: "full",
        aspectRatio: "24:18",
        aspectMode: "cover",
      } as messagingApi.FlexImage);
    }

    // Add the rest of the content
    bodyContents.push(
      {
        type: "box",
        layout: "vertical",
        contents: [
          {
            type: "box",
            layout: "vertical",
            contents: [
              {
                type: "filler",
              },
            ],
            width: "0%",
            backgroundColor: "#DE5658",
            height: "6px",
          },
        ],
        backgroundColor: "#FAD2A76E",
        height: "6px",
        margin: "sm",
      },
      {
        type: "box",
        layout: "horizontal",
        contents: [
          {
            type: "text",
            text: bulletin.content,
            size: "md",
            wrap: true,
          },
        ],
        flex: 1,
        margin: "lg",
        borderWidth: "medium",
      }
    );

    return {
      type: "bubble",
      size: "mega",
      header: {
        type: "box",
        layout: "vertical",
        contents: [
          {
            type: "text",
            text: bulletin.title,
            color: "#ffffff",
            align: "center",
            size: "xl",
            gravity: "center",
            weight: "bold",
          },
        ],
        backgroundColor: bulletin.severity,
        paddingTop: "19px",
        paddingAll: "12px",
        paddingBottom: "16px",
      },
      body: {
        type: "box",
        layout: "vertical",
        contents: bodyContents,
        spacing: "md",
        paddingAll: "12px",
      },
      footer: {
        type: "box",
        layout: "vertical",
        contents: [
          {
            type: "text",
            text: "僑星福華管理委員會 ",
            weight: "bold",
            align: "center",
          },
          {
            type: "text",
            text: formatBulletinDate(bulletin.date),
            weight: "bold",
            align: "center",
            size: "sm",
          },
        ],
        backgroundColor: "#d8edb2",
      },
      styles: {
        footer: {
          separator: false,
        },
      },
    } as messagingApi.FlexBubble;
  });

  return {
    type: 'carousel',
    contents: bubbles
  };
}

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { removeChatById } from "@/actions/chats/actions";

export const useDeleteContractChat = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, contractId }: { id: string; contractId: string }) => {
      const response =  await removeChatById({ id, contractId });
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to delete project.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      // Invalidate cache to refetch updated chats after deletion
      queryClient.invalidateQueries({ queryKey: ['contracts'] });
      queryClient.invalidateQueries({ queryKey: ['contracts', data.id] });

    },
  });
};

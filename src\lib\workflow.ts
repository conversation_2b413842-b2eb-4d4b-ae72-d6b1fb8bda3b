import { Client as WorkflowClient } from "@upstash/workflow";
import { Client as QStashClient, resend as QstashRsend } from "@upstash/qstash";
import { Resend } from 'resend';
import config from "@/lib/config/env";
import React, { ReactElement } from 'react';
import { TaskAssignmentEmail } from '@/components/reminders/email-emplate';

const QSTASH_URL = config.env.upstash.qstashUrl;
const QSTASH_TOKEN = config.env.upstash.qstashToken;
const RESEND_TOKEN = config.env.resendToken;
const resend = new Resend(RESEND_TOKEN);

if (!QSTASH_URL || !QSTASH_TOKEN || !RESEND_TOKEN) {
  throw new Error("Required environment variables (QSTASH_URL, QSTASH_TOKEN, RESEND_TOKEN) are not set.");
}

export const workflowClient = new WorkflowClient({
  baseUrl: QSTASH_URL,
  token: QSTASH_TOKEN,
});

const qstashClient = new QStashClient({
  token: QSTASH_TOKEN,
});

export const sendEmail = async ({
  subject,
  assigneeName,
  assigneeEmail,
  assignerName,
  assignerEmail,
  taskName,
  taskDescription,
  startDate,
  dueDate,
  timezone
}: {
  subject: string;
  assigneeName: string;
  assigneeEmail: string;
  assignerName: string;
  assignerEmail: string;
  taskName: string;
  taskDescription: string;
  startDate: Date | string;
  dueDate: Date | string;
  timezone: string;
}) => {
  console.log("Sending email to", assigneeEmail);

  const emailTemplateElement: ReactElement = React.createElement(TaskAssignmentEmail, {
    assigneeName,
    assignerName,
    assignerEmail,
    taskName,
    taskDescription,
    startDate,
    dueDate,
    timezone
  });

  /*await qstashClient.publishJSON({
    api: {
      name: "email",
      provider: QstashRsend({ token: config.env.resendToken }),
    },
    body: {
      from: 'ComfyMinds Lab <<EMAIL>>',
      to: [assigneeEmail],
      subject,
      html: `
      Hi ${assigneeName},

      You have been assigned a new task by ${assignerName} (${assignerEmail}).

      Task: ${taskName}
      Description: ${taskDescription}
      Start Date: ${startDate.toLocaleString()}
      Due Date: ${dueDate.toLocaleString()}

      Best regards,
      ComfyMinds Lab
    `,
    },
  });*/
  const { data, error } = await resend.emails.send({
    from: 'ComfyMinds Lab <<EMAIL>>',
    to: [assigneeEmail],
    subject,
    react: emailTemplateElement,
  });
};

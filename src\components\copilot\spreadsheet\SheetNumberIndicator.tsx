interface SheetNumberIndicatorProps {
  currentSheetIndex: number;
  totalSheets: number;
}

export function SheetNumberIndicator({
  currentSheetIndex,
  totalSheets,
}: SheetNumberIndicatorProps) {
  return (
    <div className="flex- items-center justify-center flex uppercase text-xs font-bold tracking-widest mx-4">
      <span className="mr-3">{SHEETS_ICON}</span>
      Sheet {currentSheetIndex + 1} of {totalSheets}
    </div>
  );
}

const SHEETS_ICON = (
  <svg
    width="10px"
    height="10px"
    viewBox="0 0 42 42"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
  >
    <rect x="0" y="0" width="10" height="10" />
    <rect x="16" y="0" width="10" height="10" />
    <rect x="32" y="0" width="10" height="10" />
    <rect x="0" y="16" width="10" height="10" />
    <rect x="16" y="16" width="10" height="10" />
    <rect x="32" y="16" width="10" height="10" />
    <rect x="0" y="32" width="10" height="10" />
    <rect x="16" y="32" width="10" height="10" />
    <rect x="32" y="32" width="10" height="10" />
  </svg>
);
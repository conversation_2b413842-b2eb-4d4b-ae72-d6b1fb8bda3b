'use server';

import { Role } from "@prisma/client";
import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { updateWorkspaceSchema } from "@/components/workspaces/schemas";

export async function updateWorkspace(workspaceId: string, values: z.infer<typeof updateWorkspaceSchema>) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    // Check if user exists and has ADMIN role
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId },
      select: { role: true }
    });
    console.log("member", member)

    if (!member || member.role !== Role.ADMIN) {
      return {
        error: "Only administrators can update workspaces.",
        success: false
      }
    }
    
    // Validate input data
    const validatedFields = updateWorkspaceSchema.safeParse(values);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        success: false
      }
    }

    const { name, orgnr, address, postalCode, city } = validatedFields.data;

    // Update workspace
    const workspace = await prisma.workspace.update({
      where: { id: workspaceId },
      data: {
        name,
        orgnr,
        address,
        postalCode,
        city,
        updatedAt: new Date(),
      }
    });

    revalidatePath('/workspaces');
    
    return {
      data: workspace,
      success: true
    }

  } catch (error) {
    console.error('Error updating workspace:', error);
    return {
      error: "Failed to update workspace.",
      success: false
    }
  }
}

"use client"

import { 
  Card, 
  CardContent, 
} from "@/components/ui/card";

import { Loader } from "lucide-react";
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { useGetProjects } from "@/components/projects/use-get-projects";
import { useGetMembers } from "@/components/workspaces/use-get-members";
import { CreateTaskForm } from "./create-task-form";


interface CreateTaskFormWrapperProps {
  onCancel?: () => void;
}

export const CreateTaskFormWrapper = ({ onCancel }: CreateTaskFormWrapperProps) => {
  const workspaceId = useWorkspaceId();
  
  const { projects, isLoading: isLoadingProjects } = useGetProjects({
    workspaceId,
  });
  const { members, isLoading: isLoadingMembers } = useGetMembers({
    workspaceId,
  });

  const projectOptions = projects?.map((project) => ({
    id: project.id,
    name: project.name,
    imageUrl: project.imageUrl
  }));

  const memberOptions = members?.map((member) => ({
    id: member.id,
    name: member.user.name,
  }));

  const isLoading = isLoadingProjects || isLoadingMembers || !workspaceId;

  if (isLoading) {
    <Card className="w-full h-[714px] border-none shadow-none">
      <CardContent className="flex items-center jistify-center h-full">
        <Loader className="size-5 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>;
  }

  return (
    <CreateTaskForm 
      onCancel={onCancel}
      projectOption={projectOptions ?? []}
      memberOptions={memberOptions ?? []}
    />
  );
};
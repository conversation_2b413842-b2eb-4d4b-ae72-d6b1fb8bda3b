import { useQuery } from "@tanstack/react-query";

interface UseMeetingProps {
  eventId: string
}

export const useGetMeeting = ({ 
  eventId, 
}: UseMeetingProps) => {
  const { data, isLoading, error } = useQuery({
    queryKey: [
      "calendar-event",
    ],
    queryFn: async () => {
      const url = `/api/calendar/${eventId}/fetch`;
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      });

      const result = await response.json();
      console.log("result", result)

      if (!response.ok || !result.success || !result.data) {
        throw new Error(`Failed to get schedule.`);
      }
      
      return result.data;
    },
  });

  return {
    data,
    isLoading,
    error,
  };
};
"use server";

import { prisma } from "@/lib/db";
import { CATEGORIES } from "@/lib/config/categories";
import * as LucideIcons from 'lucide-react';
import { type LucideIcon } from 'lucide-react';

type Transaction = {
  id: string;
  name: string;
  category: string;
  amount: number;
  date: Date;
  icon: string;
  color: string;
}
interface DashboardOverview {
  totalBalance: number;
  monthlyChange: number;
  monthlyChangePercentage: number;
  monthlyIncome: {
    current: number;
    previous: number;
    change: number;
    changePercentage: number;
  };
  monthlyExpenses: {
    current: number;
    previous: number;
    change: number;
    changePercentage: number;
  };
  topExpenseCategories: {
    name: string;
    amount: number;
    color: string;
    percentage: number;
  }[];
  topIncomeCategories: {
    name: string;
    amount: number;
    color: string;
    percentage: number;
  }[];
  recentIncomeTransactions: Transaction[];
  recentExpenseTransactions: Transaction[];
  monthlyTrend: {
    date: string;
    income: number;
    expenses: number;
    balance: number;
  }[];
  accounts: {
    id: string;
    name: string;
    type: string;
    balance: number;
    lastTransaction?: {
      amount: number;
      date: Date;
      description: string;
    };
  }[];
  savingsGoals: {
    current: number;
    previous: number;
    change: number;
    changePercentage: number;
    recentSavingsTransactions: Transaction[];
  }
}

function getIconName(icon: LucideIcon): string {
  return Object.keys(LucideIcons).find(
    key => LucideIcons[key as keyof typeof LucideIcons] === icon
  ) || "HelpCircle";
}

export async function getDashboardOverview(userId: string): Promise<{
  success: boolean;
  data?: DashboardOverview;
  error?: string;
}> {
  try {
    // Fetch user accounts with balances and recent transactions
    const accountsPromise = prisma.bankAccount.findMany({
      where: { userId },
      include: {
        Balance: { orderBy: { date: "desc" }, take: 1 },
        Transaction: { orderBy: { date: "desc" }, take: 1 },
      },
    });

    // Fetch transactions for the last month
    const monthlyTransactionsPromise = prisma.transaction.findMany({
      where: {
        userId,
        date: { gte: new Date(new Date().setMonth(new Date().getMonth() - 1)) },
      },
      include: {
        category: true,
      },
      orderBy: { date: "desc" },
    });

    // Get previous month's transactions
    const previousMonthTransactionsPromise = prisma.transaction.findMany({
      where: {
        userId,
        date: {
          gte: new Date(new Date().setMonth(new Date().getMonth() - 2)),
          lt: new Date(new Date().setMonth(new Date().getMonth() - 1))
        },
      },
      include: {
        category: true,
      },
    });

    // Get all savings goals with their progress
    const savingsGoalsPromise = prisma.savingsGoal.findMany({
      where: { userId },
      include: {
        progress: {
          orderBy: { date: "desc" },
          take: 1,
        },
      },
      orderBy: { priority: "asc" },
    });

    const now = new Date();
    const startOfCurrentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(startOfCurrentMonth.getTime() - 1); // Last day of the previous month
    
    const currSavingsProgressPromise = prisma.savingsProgress.findMany({
      where: {
        goal: { userId },
        date: { gte: startOfCurrentMonth }, // From the start of the current month
      },
      orderBy: { date: "desc" },
      include: { goal: true },
    });
    
    const prevMonthSavingsProgressPromise = prisma.savingsProgress.findMany({
      where: {
        goal: { userId },
        date: { gte: startOfLastMonth, lte: endOfLastMonth }, // From the start to the end of the previous month
      },
      orderBy: { date: "desc" },
      include: { goal: true },
    });

    // Resolve all promises concurrently
    const [
      accountsResult,
      monthlyTransactionsResult,
      previousMonthTransactionsResult,
      SavingsResult,
      prevMonthSavingsProgressResult,
    ] = await Promise.allSettled([
      accountsPromise,
      monthlyTransactionsPromise,
      previousMonthTransactionsPromise,
      savingsGoalsPromise,
      prevMonthSavingsProgressPromise,
    ]);

    // Extract resolved data
    if (accountsResult.status !== "fulfilled") throw new Error("Failed to fetch accounts");
    const accounts = accountsResult.value;

    if (monthlyTransactionsResult.status !== "fulfilled") throw new Error("Failed to fetch transactions");
    const monthlyTransactions = monthlyTransactionsResult.value;

    if (previousMonthTransactionsResult.status !== "fulfilled") throw new Error("Failed to fetch transactions");
    const previousMonthTransactions = previousMonthTransactionsResult.value;

    if (SavingsResult.status !== "fulfilled") throw new Error("Failed to fetch saving goals");
    const savingsGoals = SavingsResult.value;

    if (prevMonthSavingsProgressResult.status !== "fulfilled") throw new Error("Failed to fetch saving goals progress");
    const prevMonthSavingsProgress = prevMonthSavingsProgressResult.value;


    // Calculate total balance
    const totalBalance = accounts.reduce(
      (sum, account) => sum + (account.Balance[0]?.amount || 0),
      0
    );

    // Calculate total income and expenses
    const monthlyIncome = monthlyTransactions
      .filter(tx => tx.type === "CREDIT")
      .reduce((sum, tx) => sum + tx.amount.toNumber(), 0);

    const monthlyExpenses = monthlyTransactions
      .filter(tx => tx.type === "DEBIT")
      .reduce((sum, tx) => sum + Math.abs(tx.amount.toNumber()), 0);

    // Get expense categories
    const expenseCategoryTotals = monthlyTransactions
      .filter(tx => tx.type === "DEBIT" && tx.categoryId)
      .reduce((acc, tx) => {
        if (!tx.categoryId || !tx.category) return acc;
        
        if (!acc[tx.categoryId]) {
          acc[tx.categoryId] = {
            amount: 0,
            name: tx.category.name,
            icon: tx.category.icon,
          };
        }
      
      acc[tx.categoryId].amount += Math.abs(tx.amount.toNumber());
      return acc;
    }, {} as Record<string, { amount: number; name: string; icon: string }>);

    // Get income categories
    const incomeCategoryTotals = monthlyTransactions
      .filter(tx => tx.type === "CREDIT" && tx.categoryId)
      .reduce((acc, tx) => {
        if (!tx.categoryId || !tx.category) return acc;
        
        if (!acc[tx.categoryId]) {
          acc[tx.categoryId] = {
            amount: 0,
            name: tx.category.name,
            icon: tx.category.icon,
          };
        }
        
      acc[tx.categoryId].amount += tx.amount.toNumber();
      return acc;
    }, {} as Record<string, { amount: number; name: string; icon: string }>);

    // Top expense categories
    const topExpenseCategories = Object.entries(expenseCategoryTotals)
      .map(([categoryId, data]) => {
        const configCategory = CATEGORIES.find(
          c => c.name.toLowerCase() === data.name.toLowerCase()
        );
        
        return {
          name: data.name,
          amount: data.amount,
          color: configCategory?.color || "hsl(var(--chart-10))",
          percentage: (data.amount / monthlyExpenses) * 100,
        };
      })
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5);

    // Top income categories
    const topIncomeCategories = Object.entries(incomeCategoryTotals)
      .map(([categoryId, data]) => {
        const configCategory = CATEGORIES.find(
          c => c.name.toLowerCase() === data.name.toLowerCase()
        );
        
        return {
          name: data.name,
          amount: data.amount,
          color: configCategory?.color || "hsl(var(--chart-10))",
          percentage: (data.amount / monthlyIncome) * 100,
        };
      })
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5);

    // Recent transactions formatting function
    const formatTransaction = (tx: typeof monthlyTransactions[0]) => {
    const configCategory = tx.category 
      ? CATEGORIES.find(c => c.name.toLowerCase() === tx.category?.name.toLowerCase())
      : null;

      return {
        id: tx.id,
        name: tx.category?.name!,
        category: tx.category?.name || "Other",
        amount: Math.abs(tx.amount.toNumber()),
        date: tx.date,
        icon: configCategory ? getIconName(configCategory.icon) : "HelpCircle",
        color: configCategory?.color || "hsl(var(--chart-10))",
      };
    };

    // Recent income and expense transactions
    const recentIncomeTransactions = monthlyTransactions
      .filter(tx => tx.type === "CREDIT")
      .slice(0, 5)
      .map(formatTransaction);

    const recentExpenseTransactions = monthlyTransactions
      .filter(tx => tx.type === "DEBIT")
      .slice(0, 5)
      .map(formatTransaction);

    // Format accounts data
    const formattedAccounts = accounts.map((account) => ({
      id: account.id,
      name: account.name,
      type: account.accountType,
      balance: account.Balance[0]?.amount || 0,
      lastTransaction: account.Transaction[0]
        ? {
            amount: account.Transaction[0].amount.toNumber(),
            date: account.Transaction[0].date,
            description: account.Transaction[0].description,
          }
        : undefined,
    }));

    // Previous month calculations
    const previousMonthIncome = previousMonthTransactions
      .filter(tx => tx.type === "CREDIT")
      .reduce((sum, tx) => sum + tx.amount.toNumber(), 0);

    const previousMonthExpenses = previousMonthTransactions
      .filter(tx => tx.type === "DEBIT")
      .reduce((sum, tx) => sum + Math.abs(tx.amount.toNumber()), 0);

    // Calculate metrics
    const incomeMetrics = {
      currentValue: monthlyIncome,
      previousValue: previousMonthIncome,
      change: monthlyIncome - previousMonthIncome,
      changePercentage: previousMonthIncome > 0
        ? ((monthlyIncome - previousMonthIncome) / previousMonthIncome) * 100
        : (monthlyIncome - previousMonthIncome)
    };

    const expenseMetrics = {
      currentValue: monthlyExpenses,
      previousValue: previousMonthExpenses,
      change: monthlyExpenses - previousMonthExpenses,
      changePercentage: previousMonthExpenses > 0
        ? ((monthlyExpenses - previousMonthExpenses) / previousMonthExpenses) * 100
        : (monthlyExpenses - previousMonthExpenses)
    };

    const totalSavings = savingsGoals.reduce(
      (sum, goal) => sum + goal.current.toNumber(),
      0
    );
    console.log("savingsGoals", JSON.stringify(savingsGoals, null, 2))

    const prevMonthSavingsTotal = prevMonthSavingsProgress.reduce(
      (sum, progress) => sum + progress.amount.toNumber(),
      0
    );

    const monthlyChangeTotalSavings = totalSavings - prevMonthSavingsTotal;

    const recentSavingsTransactions = savingsGoals
    .slice(0, 5)
    .map(formatSavings);


    // Format recent transactions
    /*const formattedTransactions = recentTransactions.map((tx) => {
      const configCategory = CATEGORIES.find(
        (c) => c.name.toLowerCase() === tx.category?.name.toLowerCase()
      );
      return {
        id: tx.id,
        name: tx.category?.name!,
        category: tx.category?.name || "Other",
        amount: tx.amount.toNumber(),
        date: tx.date,
        icon: configCategory ? getIconName(configCategory.icon) : "HelpCircle",
        color: configCategory?.color || "hsl(var(--chart-10))",
      };
    });*/

    return {
      success: true,
      data: {
        totalBalance,
        monthlyIncome: {
          current: incomeMetrics.currentValue,
          previous: incomeMetrics.previousValue,
          change: incomeMetrics.change,
          changePercentage: incomeMetrics.changePercentage,
        },
        monthlyExpenses: {
          current: expenseMetrics.currentValue,
          previous: expenseMetrics.previousValue,
          change: expenseMetrics.change,
          changePercentage: expenseMetrics.changePercentage,
        },
        monthlyChange: monthlyIncome - monthlyExpenses,
        monthlyChangePercentage:
          monthlyIncome > 0
          ? ((monthlyIncome - monthlyExpenses) / monthlyIncome) * 100
          : 0,
        topIncomeCategories,
        topExpenseCategories,
        recentIncomeTransactions,
        recentExpenseTransactions,
        monthlyTrend: [], // TODO: Implement monthly trend calculation
        accounts: formattedAccounts,
        savingsGoals: {
          current: totalSavings,
          previous: prevMonthSavingsTotal,
          change: monthlyChangeTotalSavings,
          changePercentage: prevMonthSavingsTotal > 0 
            ? (monthlyChangeTotalSavings / prevMonthSavingsTotal) * 100 : monthlyChangeTotalSavings,
          recentSavingsTransactions,
        }
      },
    };
  } catch (error) {
    console.error("Error in getDashboardOverview:", error);
    return {
      success: false,
      error: "Failed to fetch dashboard overview",
    };
  }
}

// Recent transactions formatting function
const formatSavings = (tx: any) => {
  const configCategory = tx.type 
    ? CATEGORIES.find(c => c.name.toLowerCase() === tx.type.toLowerCase())
    : null;

    return {
      id: tx.id,
      name: tx.name!,
      category: tx.name || "Other",
      amount: Math.abs(tx.current.toNumber()),
      date: tx.updatedAt,
      icon: configCategory ? getIconName(configCategory.icon) : "HelpCircle",
      color: configCategory?.color || "hsl(var(--chart-10))",
    };
};
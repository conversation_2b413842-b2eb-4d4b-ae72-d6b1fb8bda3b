{
  /** 
  掃描社區每月管理費自動轉帳清單圖檔，
  並上傳掃描圓檔及辨識結果至Google Drive/Sheets。
*/
}
import { NextRequest, NextResponse } from 'next/server';
import { webhook, messagingApi, HTTPFetchError, validateSignature } from '@line/bot-sdk';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { uploadImageToMaintenanceFeeDrive } from '@/lib/google/upload-image-to-maintenance-fee-drive';
import { appendAutoPayResidentToSheet, AutoPayResidentData } from '@/lib/google/append-autopay-resident-to-sheet';
import { fillAutoPayAmountForResident } from '@/lib/google/upload-deposit-to-sheets';
import { 
  getDateRanges, 
  streamToBuffer,
  getMessageContentWithRetry,
  initializeAutoPayResidentSheet, 
} from '../../functions';
import { analyzeAutoPayResidentImage } from "@/lib/google/analyze-autopay-resident-image";


export const maxDuration = 300;

let systemPrompt = `
1.Do not convert user datetime, just add user timezone (+08:00) for tool input, such as 2025-05-16T20:00:00+08:00.
2.When user specify date without time, it means start_time: 23:59, end_time: 00:00.
3.Today is ${getDateRanges().today}
4.This week: ${getDateRanges().thisWeek.start} to ${getDateRanges().thisWeek.end}
5.This month: ${getDateRanges().thisMonth.start} to ${getDateRanges().thisMonth.end}
6.使用繁體中文回答
`

const client = new messagingApi.MessagingApiClient({
  channelAccessToken: process.env.AUTO_PAY_RESIDENT_CHANNEL_ACCESS_TOKEN!,
});
const blobClient = new messagingApi.MessagingApiBlobClient({ 
  channelAccessToken: process.env.AUTO_PAY_RESIDENT_CHANNEL_ACCESS_TOKEN!,
});

const middlewareConfig = {
  channelSecret: process.env.AUTO_PAY_RESIDENT_CHANNEL_SECRET!,
};

const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
});

// Utility function to start loading status
async function startLoadingStatus(chatId: string, loadingSeconds: number = 5) {
  try {
    const response = await fetch('https://api.line.me/v2/bot/chat/loading/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`,
      },
      body: JSON.stringify({
        chatId,
        loadingSeconds,
      }),
    });
    
    if (!response.ok) {
      console.error('Failed to start loading status:', await response.text());
    }
  } catch (error) {
    console.error('Error starting loading status:', error);
  }
}

const eventHandler = async (event: webhook.Event) => {
  console.log("===========================event================================", JSON.stringify(event, null, 2))
  if (event.type !== 'message' || !event.replyToken) return;

  // Type guard for group messages
  if (event?.source?.type === "group" && 
    event.message.type === 'text' && 
    !event.message.text?.includes("@AI巧管家")
  ) {
    return;
  }

  // User ID check
  const allowedUserIds = ["U7d91a648c3b6a05921fc48017ff09581", "U01c0b00ebe5ca71d16613571a14fa4d4"];
  if (!allowedUserIds.includes(event?.source?.userId ?? "")) {
    return;
  }

  if (event.message.type === 'image') {
    try {
      // Get the image content using the messaging API
      const imageStream = await getMessageContentWithRetry(blobClient, event.message.id);
      const buffer = await streamToBuffer(imageStream);
      
      // Generate a filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `auto_pay_resident_${timestamp}.jpg`;
        const base64Image = buffer.toString('base64');

      // Run multiple operations in parallel
      const [driveUploadResult, analyzedAutoPayResident] = await Promise.all([
        // Upload to Drive
        uploadImageToMaintenanceFeeDrive(buffer, filename).then(result => {
          console.log("Drive upload result:", result);
          return result;
        }),
        // Analyze auto-pay resident statement
        analyzeAutoPayResidentImage(base64Image).then(result => {
          console.log("analyzedAutoPayResident: ", JSON.stringify(result, null, 2));
          return result;
        }),
        // Initialize sheet (we don't need its result)
        initializeAutoPayResidentSheet().catch((error: any) => {
          console.error('Error initializing sheet:', error);
          // Don't let sheet initialization failure stop the whole process
          return null;
        })
      ]);
      
      // Prepare a response message that includes both the analysis and the Google Drive link
      const autoPayResidentData: AutoPayResidentData = {
        ...analyzedAutoPayResident,
        googleDriveLink: driveUploadResult.webViewLink || undefined
      };
     
      // Upload to Google Sheets and fill in parallel
      const [appendResult, fillResultsArr] = await Promise.all([
        appendAutoPayResidentToSheet(autoPayResidentData),
        fillAutoPayAmountForResident(autoPayResidentData)
      ]);
      
      // Count successful/unsuccessful fill results
      const fillArr = Array.isArray(fillResultsArr) ? fillResultsArr : [fillResultsArr];
      const successful = fillArr.filter((r: any) => r.success).length;
      const unsuccessful = fillArr.filter((r: any) => !r.success);
      const unsuccessfulCount = unsuccessful.length;
      if (unsuccessfulCount > 0) {
        console.error('Some records failed to fill in Google Sheet:', unsuccessful);
      }
      // Send the analysis result back
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: 'text',
            text: `資料處理完成！成功填入：${successful} 筆，失敗：${unsuccessfulCount} 筆。\n\n預覽試算表：${appendResult.spreadsheetUrl}\n\nDrive連結：${appendResult.googleDriveLink}`,
          },
        ],
      });


      return;
    } catch (error) {
      console.error('Error processing image:', error);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: 'text', text: '圖片處理失敗，請稍後再試。' }],
      });
      return;
    }
  }
};

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = req.headers.get('x-line-signature') || '';

  const isValid = validateSignature(body, middlewareConfig.channelSecret, signature);

  if (!isValid) {
    return new Response('Invalid signature', { status: 401 });
  }

  const events: webhook.Event[] = JSON.parse(body).events;
  console.log("//////////////////events: ////////////////////", events)

  try {
    await eventHandler(events[0]);
    return NextResponse.json({ status: 'success' });
  } catch (err: unknown) {
    if (err instanceof HTTPFetchError) {
      console.error(err.status);
      console.error(err.headers.get('x-line-request-id'));
      console.error(err.body);
    } else if (err instanceof Error) {
      console.error(err);
    }

    return NextResponse.json({ status: 'error' }, { status: 500 });
  }
}
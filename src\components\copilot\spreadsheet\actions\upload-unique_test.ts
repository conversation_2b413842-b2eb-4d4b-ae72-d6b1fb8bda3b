'use server'

import { auth } from "@clerk/nextjs/server";
import { prisma } from "@/lib/db";
import { neon } from '@neondatabase/serverless';
import { Decimal } from "@prisma/client/runtime/library";
import cuid from 'cuid';
import crypto from 'crypto';

interface BulkUploadItem {
  bankAccountId: string;
  amount: number;
  description: string;
  date: string | Date;
  year: string;
  month: string;
  day?: string;
  type: 'CREDIT' | 'DEBIT';
  categoryId: string; 
  categoryValidated: boolean;
}

interface DataItem { 
  accountId: string;
  amount: Decimal; 
  description: string; 
  date: Date; 
  userId: string; 
  categoryId: string; 
  type: 'CREDIT' | 'DEBIT';
  categoryValidated: boolean;
  importHash: string;
}

interface ValidationError {
  index: number;
  item: BulkUploadItem;
  error: string;
}

interface UploadResult {
  success: boolean;
  data?: any;
  validationErrors?: ValidationError[];
  error?: string;
}

const queryFunction = neon(`${process.env.DATABASE_URL!!}`);

function calculateImportHash(item: BulkUploadItem, sheetName: string): string {
  const hashString = `${sheetName}|${item.date}|${item.type}|${item.categoryId}|${item.bankAccountId}|${item.amount}|${item.description}`;
  console.log("hashString: ", hashString)
  return crypto.createHash('md5').update(hashString).digest('hex');
}

export async function uploadIncomeExpense(items: BulkUploadItem[], sheetName: string): Promise<UploadResult> {
  console.log("items: ", items)
  if (items.length === 0) {
    return { success: true };
  }
  try {
    const { userId } = await auth();
    if (!userId) return { success: false, error: "Unauthorized" };

    // Validate all items first
    const validationErrors: ValidationError[] = [];
    const validItems = items.map((item, index) => {
      try {
        //validateAmount(item.amount);
        const date = getDateFromFields(item.year, item.month, item.day);
        return { ...item, date, isValid: true };
      } catch (error) {
        validationErrors.push({
          index,
          item,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        return { ...item, isValid: false };
      }
    });

    console.log("validationErrors", validationErrors)
    if (validationErrors.length > 0) {
      return { success: false, validationErrors };
    }

    // Separate income and expense items
    const incomeItems: DataItem[] = [];
    const expenseItems: DataItem[] = [];
    
    validItems.forEach(item => {
      const date = getDateFromFields(item.year, item.month, item.day);
      const importHash = calculateImportHash(item, sheetName);
      const processedItem = {
        amount: new Decimal(item.amount),
        description: item.description || '',
        date: date,
        userId,
        accountId: item.bankAccountId,
        categoryId: item.categoryId,
        type: item.type,
        categoryValidated: true,
        importHash
      };

      if (item.type === 'CREDIT') {
        incomeItems.push(processedItem);
      } else {
        expenseItems.push(processedItem);
      }
    });

    // --- NEW: Check for existing data in the same month and update instead of insert ---
    // Helper to get YYYY-MM string from Date
    function getYearMonth(date: Date) {
      return date.toISOString().slice(0, 7); // 'YYYY-MM'
    }

    // Group items by accountId, categoryId, and year-month
    function groupByMonth(items: DataItem[]) {
      const map = new Map<string, DataItem[]>();
      for (const item of items) {
        const key = `${item.accountId}|${item.categoryId}|${getYearMonth(item.date)}`;
        if (!map.has(key)) map.set(key, []);
        map.get(key)!.push(item);
      }
      return map;
    }

    const incomeGroups = groupByMonth(incomeItems);
    const expenseGroups = groupByMonth(expenseItems);

    // Process in transaction with separate batches for income and expense
    const result = await prisma.$transaction(async (tx) => {
      // --- INCOME: For each group, check if a record exists for that month, accountId, categoryId, userId ---
      for (const [key, group] of incomeGroups.entries()) {
        const [accountId, categoryId, yearMonth] = key.split('|');
        // Find existing record for this user, account, category, and month
        const existing = await tx.income.findFirst({
          where: {
            userId,
            accountId,
            categoryId,
            date: {
              gte: new Date(`${yearMonth}-01T00:00:00.000Z`),
              lt: new Date(`${yearMonth}-31T23:59:59.999Z`),
            },
          },
        });
        const item = group[0]; // Only one per group expected
        if (existing) {
          // Update existing
          await tx.income.update({
            where: { id: existing.id },
            data: {
              amount: item.amount,
              description: item.description,
              date: item.date,
              categoryValidated: item.categoryValidated,
              importHash: item.importHash,
              updatedAt: new Date(),
            },
          });
        } else {
          // Insert new
          await tx.income.create({
            data: {
              id: cuid(),
              createdAt: new Date(),
              updatedAt: new Date(),
              amount: item.amount,
              description: item.description,
              date: item.date,
              userId,
              accountId,
              categoryId,
              type: item.type,
              categoryValidated: item.categoryValidated,
              importHash: item.importHash,
            },
          });
        }
      }

      // --- EXPENSE: For each group, check if a record exists for that month, accountId, categoryId, userId ---
      for (const [key, group] of expenseGroups.entries()) {
        const [accountId, categoryId, yearMonth] = key.split('|');
        const existing = await tx.expense.findFirst({
          where: {
            userId,
            accountId,
            categoryId,
            date: {
              gte: new Date(`${yearMonth}-01T00:00:00.000Z`),
              lt: new Date(`${yearMonth}-31T23:59:59.999Z`),
            },
          },
        });
        const item = group[0];
        if (existing) {
          await tx.expense.update({
            where: { id: existing.id },
            data: {
              amount: item.amount,
              description: item.description,
              date: item.date,
              categoryValidated: item.categoryValidated,
              importHash: item.importHash,
              updatedAt: new Date(),
            },
          });
        } else {
          await tx.expense.create({
            data: {
              id: cuid(),
              createdAt: new Date(),
              updatedAt: new Date(),
              amount: item.amount,
              description: item.description,
              date: item.date,
              userId,
              accountId,
              categoryId,
              type: item.type,
              categoryValidated: item.categoryValidated,
              importHash: item.importHash,
            },
          });
        }
      }

      // Log the spreadsheet import
      await tx.spreadsheetImport.create({
        data: {
          userId,
          sheetName,
          rowCount: items.length,
          type: incomeItems.length > 0 && expenseItems.length > 0 ? "Both" : 
                 incomeItems.length > 0 ? "Income" : "Expense",
          importHash: crypto.createHash('md5').update(`${userId}|${sheetName}|${Date.now()}`).digest('hex'),
          fileName: "Spreadsheet Import", 
        }
      });

      return { success: true };
    });

    return {
      success: true,
      data: result
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

function processYear(yearStr: string): number {
  const yearNum = parseInt(yearStr);
  if (yearNum < 1000) {
    return yearNum + 1911;
  }
  return yearNum;
}

function getDateFromFields(year: string, month: string, day?: string): Date {
  const processedYear = processYear(year);
  const processedMonth = parseInt(month) - 1; // JS months are 0-based
  
  // Validate month
  if (processedMonth < 0 || processedMonth > 11) {
    throw new Error('Invalid month');
  }
  
  // If no day provided, use last day of month
  return day 
    ? new Date(processedYear, processedMonth, parseInt(day))
    : new Date(processedYear, processedMonth + 1, 0);
}

function validateAmount(amount: number): boolean {
  // Amount validations:
  // 1. Must be a number
  if (typeof amount !== 'number' || isNaN(amount)) {
    throw new Error('Amount must be a valid number');
  }
  
  // 2. Must be positive
  if (amount <= 0) {
    throw new Error('Amount must be greater than 0');
  }
  
  // 3. Check for reasonable limits (e.g., prevent unreasonably large numbers)
  if (amount > 999999999) {
    throw new Error('Amount exceeds maximum limit');
  }
  
  // 4. Check decimal places (max 2)
  if (amount.toString().split('.')[1]?.length > 2) {
    throw new Error('Amount cannot have more than 2 decimal places');
  }

  return true;
}

import React from "react";
import { format, setHours } from "date-fns";
import { X, CalendarIcon, ClockIcon } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";


interface DatePickerProps {
  id?: string;
  label?: string;
  placeholder?: string;
  date: Date | undefined;
  onDateChange: (date: Date | undefined) => void;
  className?: string;
}

export function DateTimePicker({
  id,
  label,
  placeholder = "Pick a date",
  date,
  onDateChange,
  className,
}: DatePickerProps) {
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(date);
  const [selectedHour, setSelectedHour] = React.useState(date ? format(date, "hh") : "");
  const [selectedMinute, setSelectedMinute] = React.useState(date ? format(date, "mm") : "");
  const [selectedAMPM, setSelectedAMPM] = React.useState(date ? format(date, "a") : "AM");

  const handleDateSelect = (newDate: Date | undefined) => {
    setSelectedDate(newDate);
    updateDateTime(newDate, selectedHour, selectedMinute, selectedAMPM);
  };

  const handleHourChange = (hour: string) => {
    setSelectedHour(hour);
    updateDateTime(selectedDate, hour, selectedMinute, selectedAMPM);
  };

  const handleMinuteChange = (minute: string) => {
    setSelectedMinute(minute);
    updateDateTime(selectedDate, selectedHour, minute, selectedAMPM);
  };

  const handleAMPMChange = (ampm: string) => {
    setSelectedAMPM(ampm);
    updateDateTime(selectedDate, selectedHour, selectedMinute, ampm);
  };

  const updateDateTime = (date: Date | undefined, hour: string, minute: string, ampm: string) => {
    if (date && hour && minute) {
      let hours = parseInt(hour);
      if (ampm === "PM" && hours !== 12) {
        hours += 12;
      } else if (ampm === "AM" && hours === 12) {
        hours = 0;
      }
      const newDate = setHours(date, hours);
      newDate.setMinutes(parseInt(minute));
      onDateChange(newDate);
    } else {
      onDateChange(undefined);
    }
  };

  const resetDateTime = () => {
    setSelectedDate(undefined);
    setSelectedHour("");
    setSelectedMinute("");
    setSelectedAMPM("AM");
    onDateChange(undefined);
  };

  return (
    <div className={cn("space-y-1", className)}>
      {label && <Label htmlFor={id}>{label}</Label>}
      <div className="flex flex-row items-center">
          {selectedDate && 
            <Button
              type="button"
              onClick={resetDateTime}
              variant="ghost"
              size="sm"
              className="h-8 p-1 z-60"
            >
              <X color= "red" className="h-3 w-3" />
            </Button>
          }      
        <Popover>
            <PopoverTrigger className="px-1" asChild>
            <Button
                id={id}
                variant="outline"
                size="sm"
                className={cn(
                "w-full justify-start text-left font-normal",
                !date && "text-muted-foreground/80"
                )}
            >
                <CalendarIcon color="orange" className="h-2 w-2" />
                {date ? format(date, "PPp") : <span>{placeholder}</span>}
            </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              initialFocus
            />
            <div className="p-3 border-t border-border">
                <div className="flex items-center">
                <ClockIcon className="mr-2 h-4 w-4" />
                <Label>Time</Label>
                </div>
                <div className="flex space-x-2 mt-1">
                <Select value={selectedHour} onValueChange={handleHourChange}>
                    <SelectTrigger className="h-8 w-[70px]">
                    <SelectValue placeholder="HH" />
                    </SelectTrigger>
                    <SelectContent>
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((hour) => (
                        <SelectItem key={hour} value={hour.toString().padStart(2, '0')}>
                        {hour.toString().padStart(2, '0')}
                        </SelectItem>
                    ))}
                    </SelectContent>
                </Select>
                <Select value={selectedMinute} onValueChange={handleMinuteChange}>
                  <SelectTrigger className="h-8 w-[70px]">
                    <SelectValue placeholder="MM" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 60 }, (_, i) => i).map((minute) => (
                      <SelectItem key={minute} value={minute.toString().padStart(2, '0')}>
                        {minute.toString().padStart(2, '0')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedAMPM} onValueChange={handleAMPMChange}>
                  <SelectTrigger className="h-8 w-[70px]">
                    <SelectValue placeholder="AM/PM" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AM">AM</SelectItem>
                    <SelectItem value="PM">PM</SelectItem>
                  </SelectContent>
                </Select>
                </div>
            </div>
            </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}


import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(req: NextRequest) {
  try {
    const { state } = await req.json();
    
    if (!state) {
      return NextResponse.json(
        { error: 'Missing state parameter' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();
    cookieStore.set('oauth_state', state, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      // Set a reasonable expiry time
      maxAge: 60 * 5 // 5 minutes
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error setting state:', error);
    return NextResponse.json(
      { error: 'Failed to set state' },
      { status: 500 }
    );
  }
}
"use client"

import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { useGetTask } from "./use-get-task";
import { useGetProjects } from "@/components/projects/use-get-projects";
import { useGetMembers } from "@/components/workspaces/use-get-members";
import { UpdateTaskForm } from "./update-task-form";
import { 
  Card, 
  CardContent, 
} from "@/components/ui/card";

import { Loader } from "lucide-react";

interface UpdateTaskFormWrapperProps {
  onCancel?: () => void;
  id: string;
}

export const UpdateTaskFormWrapper = ({ onCancel, id }: UpdateTaskFormWrapperProps) => {
  const workspaceId = useWorkspaceId();
  
  const { data: initialValues, isLoading: isLoadingTask } = useGetTask({ 
    taskId: id 
  })
  const { projects, isLoading: isLoadingProjects } = useGetProjects({
    workspaceId,
  });
  const { members, isLoading: isLoadingMembers } = useGetMembers({
    workspaceId,
  });

  const projectOptions = projects?.map((project) => ({
    id: project.id,
    name: project.name,
    imageUrl: project.imageUrl
  }));

  const memberOptions = members?.map((member) => ({
    id: member.id,
    name: member.user.name,
  }));

  const isLoading = isLoadingTask || isLoadingProjects || isLoadingMembers || !workspaceId;

  if (isLoading) {
    <Card className="w-full h-[714px] border-none shadow-none">
      <CardContent className="flex items-center jistify-center h-full">
        <Loader className="size-5 animate-spin text-muted-foreground" />
      </CardContent>
    </Card>;
  }

  if (!initialValues) return null;

  return (
    <UpdateTaskForm 
      onCancel={onCancel}
      projectOptions={projectOptions ?? []}
      memberOptions={memberOptions ?? []}
      initialValues={initialValues}
    />
  );
};

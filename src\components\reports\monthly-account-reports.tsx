"use client"

import { Banner<PERSON><PERSON>, type Banner } from "@/components/banner/banner-creator"
import { 
  DndContext, 
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  type UniqueIdentifier
} from '@dnd-kit/core';
import { 
  SortableContext, 
  verticalListSortingStrategy,
  useSortable 
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ReactNode, useState, useEffect } from 'react';
import AccountBalances from "@/components/reports/account-balances"
import AccountStatements from "@/components/reports/account-satements";
import { getAccountBalances } from '@/actions/banking/get-account-balances';
import { getVirtualAccountDetails } from '@/actions/banking/get-virtual-account-details';
import { subYears, endOfYear } from "date-fns";
import { notFound } from "next/navigation";
import { GripVertical } from "lucide-react";
import { Button } from "@/components/ui/button";

import { Draggable } from "@/components/draggable";
import { Droppable } from "@/components/droppable"; 
import { XCircle } from "lucide-react";

type SortableAccountProps = {
  id: string
  isBanner?: boolean
  children: ReactNode
}
const SortableAccountReport = ({ id, isBanner, children }: SortableAccountProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition
  };

  return (
    <div ref={setNodeRef} style={style} className="relative">
      <div 
        {...attributes} 
        {...listeners}
        className="absolute left-2 top-4 cursor-grab active:cursor-grabbing"
      >
        <GripVertical id="no-print" className="h-4 w-4" />
      </div>
      <div className={`pl-0 ${isBanner ? 'py-2' : ''}`}>
        {children}
      </div>
    </div>
  );
};

// Main Component
type BankAccount  = {
  bankAccounts: {id: string; name: string; accountType: string }[]
}
type DroppableAccount  = { id: UniqueIdentifier; name: string; accountType: string }

type TimeRangeKey = keyof typeof timeRanges;
const timeRanges = {
  "2y": { label: "Last 2 years", duration: subYears(new Date(), 2) },
  "1y": { label: "This year", duration: subYears(new Date(), 1) },
  "6m": { label: "Last 6 months", duration: subYears(new Date(), 0.5) },
} as const;

export const MonthlyAccountReports = ({ bankAccounts }: BankAccount) => {
  const [accounts, setAccounts] = useState(bankAccounts);
  const [timeRange, setTimeRange] = useState<TimeRangeKey>("2y");
  const [loading, setLoading] = useState(true);
  const [accountData, setAccountData] = useState<Record<string, any>>({});
  const [droppedAccounts, setDroppedAccounts] = useState<any[]>([]);
  const [loadingAccounts, setLoadingAccounts] = useState<Set<string>>(new Set());
  const [banners, setBanners] = useState<Banner[]>([]);
  const [droppedBanners, setDroppedBanners] = useState<Banner[]>([]);

  const handleBannerCreate = (banner: Banner) => {
    setBanners(prev => [...prev, banner]);
  };

  const fetchData = async (account: DroppableAccount) => {
    console.log("accounts", account)
    const accountId = account?.id!
    try {
      // Mark account as loading
      setLoadingAccounts((prev) => new Set(prev).add(String(accountId)));

      const startDate = timeRanges[timeRange]?.duration || subYears(new Date(), 1);
      const endDate = endOfYear(startDate)
      //const account = accounts.find((acc) => acc.id === account.id);
      let result: any
      if (account?.accountType === "VIRTUAL") {
        //result = await getVirtualAccountDetails(String(accountId), startDate);
        result = await getVirtualAccountDetails(String(accountId), startDate, endDate)
        console.log("MONTHLY VIRTUAL accounts result", result)
      } else {
        result = await getAccountBalances(String(accountId), startDate, endDate);
      }
      
      if (!result.success || !result.data) {
        notFound();
        return;
      }

      // Update account data
      setAccountData((prevData) => ({
        ...prevData,
        [accountId]: result.data,
      }));
    } catch (error) {
      console.error(`Failed to fetch account details for ${accountId}`, error);
    } finally {
      // Remove account from loading state
      setLoadingAccounts((prev) => {
        const updated = new Set(prev);
        updated.delete(String(accountId));
        return updated;
      });
      console.log("accountData", accountData)
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const activeId = String(active.id);
    const overId = String(over.id);
    
    // Check if the dragged item is a banner
    const draggedBanner = banners.find(b => b.id === activeId);
    if (draggedBanner) {
      if (overId === "droppable") {
        // Add banner to dropped zone
        setDroppedBanners(prev => [...prev, draggedBanner]);
        setBanners(prev => prev.filter(b => b.id !== activeId));
      } else if (overId === "available-accounts") {
        // Move banner back to available section
        setBanners(prev => [...prev, draggedBanner]);
        setDroppedBanners(prev => prev.filter(b => b.id !== activeId));
      }
      return;
    }

    // Handle account drops (existing logic)
    const draggedAccount = accounts.find(acc => acc.id === activeId) || 
        droppedAccounts.find(acc => acc.id === activeId);
    
    if (!draggedAccount) return;

    if (overId === "droppable" && !droppedAccounts.some(acc => acc.id === activeId)) {
      setDroppedAccounts(prev => [...prev, draggedAccount]);
      setAccounts(prev => prev.filter(acc => acc.id !== activeId));
    } else if (overId === "available-accounts" && droppedAccounts.some(acc => acc.id === activeId)) {
      setAccounts(prev => [...prev, draggedAccount]);
      setDroppedAccounts(prev => prev.filter(acc => acc.id !== activeId));
      setAccountData(prev => {
        const newData = { ...prev };
        delete newData[activeId];
        return newData;
      });
    } else if (overId !== activeId && droppedAccounts.some(acc => acc.id === activeId)) {
      const oldIndex = droppedAccounts.findIndex(acc => acc.id === activeId);
      const newIndex = droppedAccounts.findIndex(acc => acc.id === overId);
      
      const newOrder = [...droppedAccounts];
      newOrder.splice(oldIndex, 1);
      newOrder.splice(newIndex, 0, draggedAccount);
      
      setDroppedAccounts(newOrder);
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  useEffect(() => {
    // Fetch data for newly dropped accounts
    const newAccounts = droppedAccounts.filter(
      (accountId) => !accountData[accountId]
    );
    if (newAccounts.length > 0) {
      newAccounts.forEach((accountId) => fetchData(accountId));
    }
    console.log("droppedAccounts", droppedAccounts)
  }, [droppedAccounts]);

  useEffect(() => {
    // Update loading state based on `loadingAccounts`
    setLoading(loadingAccounts.size > 0);
  }, [loadingAccounts]);

  if (loading && !droppedAccounts.length) {
    return <div className="flex justify-center items-center">Loading...</div>;
  }

  return (
    <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
      <div className="flex flex-col gap-y-4">
        {/* Banner Creation Section */}
        <div id="no-print">
          <div className="p-4 border rounded">
            <h2 className="text-2xl font-bold tracking-tight mb-4">Banner Management</h2>
            <BannerCreator onBannerCreate={handleBannerCreate} />
            
            <h3 className="text-lg font-bold mt-4 mb-2">Available Banners</h3>
            <div className="flex text-sm gap-2">
              {banners.map(banner => (
                <Draggable key={banner.id} id={banner.id}>
                  <div style={{ backgroundColor: banner.color }}>
                    {banner.title}
                  </div>
                </Draggable>
              ))}
            </div>
          </div>
        </div>

        {/* Available Accounts */}
        <div id="no-print">
          <Droppable id="available-accounts">
            <div className="p-4 border rounded">
              <h2 className='text-2xl font-bold tracking-tight mb-4'>Available Accounts..</h2>
              <div className="flex text-sm gap-2">
                {accounts.map((account) => (
                  <Draggable key={account.id} id={account.id}>
                    {account.name}
                  </Draggable>
                ))}
              </div>
            </div>
          </Droppable>
        </div>
        {/* Droppable Area */}
        <Droppable id="droppable">
          <div className="min-h-[200px]">
            {droppedBanners.length > 0 || droppedAccounts.length > 0 ? (
              <SortableContext 
                items={[...droppedAccounts.map(acc => acc.id), ...droppedBanners.map(b => b.id)]} 
                strategy={verticalListSortingStrategy}
              >
                {/* Render Banners */}
                {droppedBanners.map((banner) => (
                  <SortableAccountReport key={banner.id} id={banner.id} isBanner>
                    <div className="rounded">
                      <div 
                        className="w-full text-center p-1 rounded-md group"
                        style={{ backgroundColor: banner.color }}
                      >
                        {/* Add remove button */}
                        <Button 
                          variant={"ghost"}
                          onClick={() => {
                            // Move banner back to available section
                            setBanners(prev => [...prev, banner]);
                            // Remove from dropped zone
                            setDroppedBanners(prev => prev.filter(b => b.id !== banner.id));
                          }}
                          style={{ backgroundColor: banner.color }}
                          className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1"
                        >
                          <XCircle color={"red"} className="h-4 w-4" />
                        </Button>
                        {banner.title}
                      </div>
                    </div>
                  </SortableAccountReport>
                ))}

                {/* Render Accounts */}
                {droppedAccounts.map((account) => (
                  <SortableAccountReport key={account.id} id={account.id}>
                    <div id="print-area" className="rounded">
                      <h4 id="no-print" className='font-bold px-8 py-3'>{account.name}</h4>
                      <div>
                        {accountData[account.id]?.transactions && (
                          <AccountStatements
                            transactions={accountData[account.id].transactions}
                            onTransactionUpdate={async () => await fetchData(account.id)}
                          />
                        )}
                        {accountData[account.id]?.balances && (
                          <AccountBalances
                            name={account.name}
                            transactions={accountData[account.id].balances}
                          />
                        )}
                      </div>
                    </div>
                  </SortableAccountReport>
                ))}
              </SortableContext>
            ) : (
              <div className="text-center py-6">Drop accounts here</div>
            )}
          </div>
        </Droppable>
        <div id="no-print" className='flex flex-rows justify-center'>
          <Button 
           
            size={"sm"}
            variant={"datePicker"}
            onClick={() => {
              setDroppedBanners([])
              setDroppedAccounts([]);
              setAccounts(bankAccounts);
            }}
          >
            Reset
          </Button>
          <Button
            size={"sm"}
            variant={"datePicker"}
            onClick={() => {
              window.print();
            }}
            className="btn btn-primary text-sm"
          >
            Print Report
          </Button>
        </div>
      </div>
    </DndContext>
  );
};
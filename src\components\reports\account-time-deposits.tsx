import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  flexRender,
  CellContext,
  createColumnHelper,
  ColumnFiltersState,
  VisibilityState 
} from '@tanstack/react-table'
import { ReactNode, Fragment ,useMemo, useState } from "react";
import { MAX_DATE_RANGE_DAYS } from "@/constants";
import { 
	differenceInDays, 
	subYears, 
	startOfYear, 
	endOfYear, 
	format 
} from "date-fns";
import { TimeDeposit } from "@prisma/client";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { DateRange } from "react-day-picker";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface AccountTimeDepositProps {
  name?: string;
  dataRange?: { label?: string; from: Date; to: Date };
	timeDeposits: TimeDeposit[];
}

// Extend the TimeDeposit type to include calculated fields
interface ExtendedTimeDeposit extends TimeDeposit {
  name: string;
	previousAmount: number;
	netAmount: number;
}

const columnHelper = createColumnHelper<ExtendedTimeDeposit>();


export default function AccountTimeDeposits({ name, dataRange, timeDeposits }: AccountTimeDepositProps) {
	const [dateRange, setDateRange] = useState<DateRange | undefined>({
		from: startOfYear(subYears(new Date(), 1)), 
		to: endOfYear(new Date()),
	});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({date: false})
	const [amountFilter, setAmountFilter] = useState<{ operator: string; value: number | null }>({
	  operator: ">=", // Default operator
	  value: null,   // Default value
	});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [unifiedFilter, setUnifiedFilter] = useState<string>("");
  const [headerHidden, setHeaderHidden] = useState<boolean>(false);
  const [showAllTypes, setShowAllTypes] = useState(false);
 
  // Memoized calculation of balance data with more robust error handling
  const calculateBalanceData = useMemo(() => {
    let previousAmount = 0;
    return timeDeposits.map((timeDeposit) => {
      try {
        const currentAmount = Number(timeDeposit.amount) || 0;
        const netAmount = currentAmount - previousAmount;
        previousAmount = currentAmount;

        return {
          ...timeDeposit,
          previousAmount,
          netAmount,
          // Add additional computed fields if needed
          formattedDate: format(new Date(timeDeposit.date), "MMM d, yyyy"),
        };
      } catch (error) {
        console.error("Error processing time deposit:", error);
        return {
          ...timeDeposit,
          previousAmount: 0,
          netAmount: 0,
          formattedDate: "Invalid Date",
        };
      }
    });
  }, [timeDeposits]);

  // Comprehensive filtered transactions with improved filtering logic
  const filteredTransactions = useMemo(() => {
    return calculateBalanceData.filter((timeDeposit) => {
      try {
        const transactionDate = new Date(timeDeposit.date);

        // Date range filter
        const withinDateRange =
          transactionDate >= (dateRange?.from ?? startOfYear(subYears(new Date(), 1))) &&
          transactionDate <= (dateRange?.to ?? endOfYear(new Date()));

        // Unified filter with more robust matching
        const matchesUnifiedFilter =
          unifiedFilter === "" ||
          Object.entries(timeDeposit).some(([key, value]) => {
            // Handle different types of values
            if (value === null || value === undefined) return false;

            // Special handling for date
            if (key === "date" || key === "formattedDate") {
              return value.toString().toLowerCase().includes(unifiedFilter.toLowerCase());
            }

            // Handle numeric values
            if (typeof value === 'number') {
              return value.toString().includes(unifiedFilter);
            }

            // Default string comparison
            return value.toString().toLowerCase().includes(unifiedFilter.toLowerCase());
          });

        // Amount filter with more flexible operators
        const matchesAmountFilter = amountFilter.value !== null
          ? (amountFilter.operator === ">=" 
              ? Number(timeDeposit.amount) >= amountFilter.value
              : amountFilter.operator === "<=" 
              ? Number(timeDeposit.amount) <= amountFilter.value
              : amountFilter.operator === "=="
              ? Number(timeDeposit.amount) === amountFilter.value
              : true)
          : true;

        // Type filter
        const matchesTypeFilter = showAllTypes || timeDeposit.type === 'AVAILABLE';

        return withinDateRange && matchesUnifiedFilter && matchesAmountFilter && matchesTypeFilter;
      } catch (error) {
        console.error("Error filtering time deposit:", error);
        return false;
      }
    });
  }, [
    calculateBalanceData, 
    dateRange, 
    unifiedFilter, 
    amountFilter, 
    showAllTypes
  ]);

  // Additional utility functions
  const totalAvailableAmount = useMemo(() => {
    return filteredTransactions
      .filter(deposit => deposit.type === 'AVAILABLE')
      .reduce((sum, deposit) => sum + (Number(deposit.amount) || 0), 0);
  }, [filteredTransactions]);

  const totalWithdrawnAmount = useMemo(() => {
    return filteredTransactions
      .filter(deposit => deposit.type === 'WITHDRAWN')
      .reduce((sum, deposit) => sum + (Number(deposit.amount) || 0), 0);
  }, [filteredTransactions]);
  
  const columns = [
    columnHelper.accessor('name', {
      header: '帳戶名稱',
      cell: (info) => (
        <TableCell className="w-[20rem] text-left pl-1 py-0">
          <span>
            {name}
          </span>
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
    columnHelper.accessor('certificateNo', {
      header: '定存單號',
      cell: (info) => (
        <TableCell className="w-[16rem] text-right px-1 py-0">
          <span className={""}>
            {info.getValue()}
          </span>
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
    columnHelper.accessor('date', {
      header: '日期',
      cell: (info) => (
        <TableCell className="w-[7rem] px-1 py-0">
          <span className={""}>
            {format(info.getValue(), "MMM d, yyyy")}
          </span>
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
    columnHelper.accessor('amount', {
      header: () => '金額',
      cell: (info) => (
        <TableCell className="w-[16rem] text-right px-1 py-0">
          <span className="">
            {new Intl.NumberFormat("zh-TW", {
              style: "currency",
              currency: "TWD",
              minimumFractionDigits: 0, // No decimal places
              maximumFractionDigits: 0, // No decimal places
            }).format(info?.getValue()!)}
          </span>
        </TableCell>
      ),
      filterFn: (row, columnId, filterValue) => {
        if (!filterValue?.value) return true; // Show all rows if no filter value
    
        const amount = row.getValue(columnId) as number;
        switch (filterValue.operator) {
          case "=":
            return amount === filterValue.value;
          case ">=":
            return amount >= filterValue.value;
          case "<=":
            return amount <= filterValue.value;
          default:
            return true;
        }
      },
    }),
    columnHelper.accessor('description', {
      header: '備註',
      cell: (info) => (
        <TableCell className="w-[16rem] text-right pr-2 py-0">
          {info.getValue()}
        </TableCell>
      ),
      filterFn: 'includesString'
    }),
  ];
  
  const table = useReactTable({
    data: filteredTransactions as unknown as ExtendedTimeDeposit[],
    columns,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnFiltersChange: setColumnFilters,
    state: {
      columnVisibility,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: 30,
      },
    },
  });
  
  const toggleHeaderVisibility = () => {
    setHeaderHidden(!headerHidden); // Toggle header visibility
  };

  return (
    <div className="space-y-0">
      <div id="no-print" className="flex gap-2">
        <DateRangePicker
          key={`${dataRange?.from}-${dataRange?.to}`}
          initialDateFrom={dataRange?.from}
          initialDateTo={dataRange?.to}
          showCompare={false}
          onUpdate={(values) => {
            const { from, to } = values.range;
              // We update the date range only if both dates are set

              if (!from || !to) return;
              if (differenceInDays(to, from) > MAX_DATE_RANGE_DAYS) {
              toast.error(
                  `The selected date range is too big. Max allowed range is ${MAX_DATE_RANGE_DAYS} days!`
              );
              return;
              }

              setDateRange({ from, to });
          }}
        /> 
        <Input
          placeholder="Search..."
          value={unifiedFilter}
          onChange={(e) => setUnifiedFilter(e.target.value)}
          className="h-8"
        />
        <div className="flex items-center gap-2">
          <Select
            value={amountFilter.operator}
            onValueChange={(value) => setAmountFilter((prev) => ({ ...prev, operator: value }))}
          >
            <SelectTrigger className="h-8 w-fit gap-x-1">
              <SelectValue placeholder="Operator" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="=">=</SelectItem>
              <SelectItem value=">=">{`>=`}</SelectItem>
              <SelectItem value="<=">{`<=`}</SelectItem>
            </SelectContent>
          </Select>
          <Input
            type="number"
            placeholder="Amount"
            value={amountFilter.value ?? ""}
            onChange={(e) => setAmountFilter((prev) => ({
              ...prev,
              value: e.target.value ? parseFloat(e.target.value) : null,
            }))}
            className="h-8 min-w-[8rem] max-w-[150px]"
          />
          <DropdownMenu>
            <DropdownMenuTrigger className="h-8" asChild>
              <Button variant="outline">Columns</Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table.getAllLeafColumns().map(column => (
                <DropdownMenuCheckboxItem
                  key={column.id}
                  className="capitalize"
                  checked={column.getIsVisible()}
                  onCheckedChange={value => column.toggleVisibility(!!value)}
                >
                  {column.id}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          {/* Toggle Header Visibility Button */}
          <Button variant={"outline"} className="h-8" type="button" onClick={toggleHeaderVisibility}>
            {headerHidden ? "Show Header" : "Hide Header"}
          </Button>

          {/* Type Filter Toggle */}
          <Button 
            variant={showAllTypes ? "default" : "outline"}
            onClick={() => setShowAllTypes(!showAllTypes)}
          >
            {showAllTypes ? "Showing All Types" : "Show Only Available"}
          </Button>
        </div>
      </div>
      <div className="border-none space-y-2">
        <Table className='text-[18px]'>
          <TableHeader style={{ display: headerHidden ? "none" : "table-header-group" }}>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead 
                    key={header.id}
                    className={cn(
                      "h-8 p-1 border-t border-b text-black bg-green-100",
                      header.id === 'name' && "w-[20rem] text-left pl-1" ||
                      header.id === 'certificateNo' && "w-[16rem] text-right" ||
                      header.id === 'date' && "w-[7rem] text-left" ||
                      header.id === 'amount' && "w-[16rem] text-right" ||
                      header.id === 'description' && "w-[16rem] text-right pr-2"
                    )}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                    {header.column.getIsSorted() && (
                      <span>{header.column.getIsSorted() === "asc" ? " ↑" : " ↓"}</span>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="text-black bg-white">
            {table.getFilteredRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <Fragment key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell as (info: CellContext<ExtendedTimeDeposit, any>) => ReactNode,
                      cell.getContext()
                    )}
                  </Fragment>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {/* Additional summary information */}
      <div className="flex justify-between border-t border-b-2 text-black bg-white">
        <div className="flex w-full justify-between text-sm px-[0.3rem] py-[0.4rem]">
          <span className='text-[18px] font-bold'>合計金額 </span>
          <strong className='text-[18px] font-bold'>${totalAvailableAmount.toLocaleString()}</strong>
        </div>
      </div>
    </div>
  );
}

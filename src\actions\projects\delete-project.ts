'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { z } from "zod";
import { revalidatePath } from "next/cache";


const deleteSchema = z.object({
  projectId: z.string().min(1),
});

export async function deleteProject(input: z.infer<typeof deleteSchema>) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    const validatedFields = deleteSchema.safeParse(input);
    if (!validatedFields.success) {
      return {
        error: "Invalid fields.",
        errors: validatedFields.error.format(),
        success: false
      }
    }

    const { projectId } = validatedFields.data;

    // Check if user belongs to the project
    const userProject = await prisma.project.findUnique({
      where: { id: projectId },
    })

    // Check if user exists
    const member = await prisma.member.findFirst({
      where: { userId, workspaceId: userProject?.workspaceId!, },
    });

    if (!member) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    if (!userProject) {
      return {
        error: "You don't have access to this project.",
        success: false
      }
    }

    // Delete project and all related data in a transaction
    const deletedProject = await prisma.$transaction(async (tx) => {
      return tx.project.delete({
        where: {
          id: userProject.id
        }
      });
    });

    revalidatePath('/projects')

    return {
      success: true,
      data: deletedProject,
      message: "Project deleted successfully"
    }

  } catch (error) {
    console.error('Error deleting project:', error)
    return {
      error: "Failed to delete project.",
      success: false
    }
  }
}

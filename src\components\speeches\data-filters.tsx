
import { useWorkspaceId } from "@/components/workspaces/use-workspace-id";
import { useGetProjects } from "@/components/projects/use-get-projects";
import { useSpeechFilters } from "@/components/speeches/use-speech-filters";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectSeparator,
    SelectTrigger,
    SelectValue,
  } from "@/components/ui/select"
import { Frame } from "lucide-react";


interface DataFiltersProps {
  hideProjectFilter?: boolean;
}

export const DataFilters = ({
  hideProjectFilter
}: DataFiltersProps) => {
  const workspaceId = useWorkspaceId();
  
  const { projects, isLoading: isLoadingProjects } = useGetProjects({
    workspaceId,
  });

  const isLoading = isLoadingProjects || !workspaceId;

  const projectOptions = projects?.map((project) => ({
    value: project.id,
    label: project.name,
  }));

  const [{
    projectId,
    search,
    startDate,
    endDate,
  }, setFilters] = useSpeechFilters();

  const onProjectChange = (value: string) => {
    setFilters({ projectId: value === "projectId" ? null : value as string });
  }

  if (isLoading) return null;


  return (
    <div className="flex flex-col lg:flex-row gap-2">
      {!hideProjectFilter && (
        <Select
          defaultValue={projectId ?? "projectId"}
          onValueChange={(value) => {onProjectChange(value)}}
        >
          <SelectTrigger className="w-full lg:w-auto h-8">
            <div className="flex items-center pr-2">
              <Frame className="size-3 mr-2" />
              <SelectValue placeholder="All projects" />
            </div>
          </SelectTrigger>
          <SelectContent className="max-w-[160px]">
            <SelectItem value="projectId">All projects</SelectItem>
            <SelectSeparator />
            {projectOptions?.map((project) => (
              <SelectItem key={project.value} value={project.value}>
                {project.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      <div className="flex flex-row items-center gap-x-2">
        <label className='text-sm'>CreateAt</label>
        <Input
          id="startDate"
          className="h-8"
          type="date"
          name="startDate"
          onChange={(e) => setFilters((prevFilters) => ({
            ...prevFilters,
            startDate: e.target.value || null
          }))}
          value={startDate || ''}
        />
      </div>
      <div className="flex flex-row items-center gap-x-2">
        <label className='text-sm'>UpdateAt</label>
        <Input
          id="dueDate"
          className="h-8"
          type="date"
          name="dueDate"
          onChange={(e) => setFilters((prevFilters) => ({
            ...prevFilters,
            dueDate: e.target.value || null
          }))}
          value={endDate || ''}
        />
      </div>
    </div>
  )
}
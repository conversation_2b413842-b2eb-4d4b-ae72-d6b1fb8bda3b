import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateTaskDescription } from '@/actions/tasks/update-task';
import { z } from 'zod';
import { updateTaskSchema } from '@/components/tasks/schemas';
import { toast } from 'sonner';

export function useUpdateTaskDescription() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      taskId, 
      data 
    }: { 
      taskId: string; 
      data: Partial<z.infer<typeof updateTaskSchema>> 
    }) => {
      const response = await updateTaskDescription(taskId, data);
      if (!response.success && response.data) {
        throw new Error(response.error || 'Failed to update task.');
      }
      return { data: response.data };
    },
    onSuccess: ({ data }) => {
      toast.success('Task updated!');
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['task', data?.id] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update task.');
    },
  });
}
'use server'

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";

export async function getProjects(workspaceId: string) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return {
        error: "Unauthorized.",
        success: false
      }
    }

    const member = await prisma.member.findFirst({
      where: { userId, workspaceId },
    });
  
    if (!member) {
      return {
        error: "Unauthorized",
        success: false
      }
    }
     
    // Get all projects from a workspaceId
    const projects = await prisma.project.findMany({
      where: { workspaceId },
      orderBy: {
        updatedAt: 'desc'
      }
    })
    
    return {
      data: projects,
      success: true
    }

  } catch (error) {
    console.error('Error getting projects:', error)
    return {
      error: "Failed to get projects.",
      success: false
    }
  }
}
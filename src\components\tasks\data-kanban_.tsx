import { useCallback, useEffect, useState } from "react";
import { TaskStatus } from "@prisma/client";
import { TaskWithRelations } from "@/actions/tasks/get-tasks";
import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult
} from "@hello-pangea/dnd";
import { KanbanColumHeader } from "./kanban-board-header";
import { KanbanCard } from "./kanban-card";


const boards: TaskStatus[] = [
  TaskStatus.BACKLOG,    
  TaskStatus.TODO,
  TaskStatus.IN_PROGRESS,
  TaskStatus.IN_REVIEW,
  TaskStatus.DONE,
];

type TaskState = {
  [key in TaskStatus]: TaskWithRelations[];
}

interface DataKanbanProps {
  data: TaskWithRelations[];
  onChange: (tasks: { id: string; status: TaskStatus; position: number; }[]) => void
}

export const DataKanban = ({ data, onChange }: DataKanbanProps) => {
  const [tasks, setTasks] = useState<TaskState>(() => {
    const initialTasks: TaskState = {
      [TaskStatus.BACKLOG]: [],
      [TaskStatus.TODO]: [],
      [TaskStatus.IN_PROGRESS]: [],
      [TaskStatus.IN_REVIEW]: [],
      [TaskStatus.DONE]: [],
    };
    data.forEach((task) => {
      initialTasks[task.status].push(task);
    });
    // Sort tasks by position
    Object.keys(initialTasks).forEach((status) => {
      initialTasks[status as TaskStatus].sort((a, b) => a.position - b.position);
    })
    return initialTasks;
  });

  useEffect(() => {
    const newTasks: TaskState = {
      [TaskStatus.BACKLOG]: [],
      [TaskStatus.TODO]: [],
      [TaskStatus.IN_PROGRESS]: [],
      [TaskStatus.IN_REVIEW]: [],
      [TaskStatus.DONE]: [],
    };

    data.forEach((task) => {
      newTasks[task.status].push(task);
    });
    Object.keys(newTasks).forEach((status) => {
      newTasks[status as TaskStatus].sort((a, b) => a.position - b.position);
    })

    setTasks(newTasks)
  } , [data]);


  const onDragEnd = useCallback((result: DropResult) => {
    if (!result.destination) return;
  
    const { source, destination } = result;
    const sourceStatus = source.droppableId as TaskStatus;
    const destStatus = destination.droppableId as TaskStatus;
    let updatedPayload: { id: string; status: TaskStatus; position: number; }[] = [];
  
    setTasks((prevTasks) => {
      const newTasks = { ...prevTasks };
      // Safely remove the task from the source column
      const sourceColumn = [...newTasks[sourceStatus]];
      const [movedTask] = sourceColumn.splice(source.index, 1);
  
      if (!movedTask) {
        console.error("No task to move");
        return prevTasks; // Return previous state if no task found
      }
  
      // Create a new task object with potentially updated status
      const updatedMovedTask = sourceStatus !== destStatus
        ? { ...movedTask, status: destStatus }
        : movedTask;
  
      // Add task to destination column
      const destColumn = [...newTasks[destStatus]];
      destColumn.splice(destination.index, 0, updatedMovedTask);
  
      // Update the columns in the new state
      newTasks[sourceStatus] = sourceColumn;
      newTasks[destStatus] = destColumn;
  
      // Update positions for affected tasks
      updatedPayload = [];
      updatedPayload.push({
        id: updatedMovedTask.id,
        status: destStatus,
        position: Math.min((destination.index + 1) * 1000, 1_000_000)
      })

      // Update position for affected tasks in the destination column
      newTasks[destStatus].forEach((task, index) => {
        if (task && task.id !== updatedMovedTask.id) {
          const newPosition = Math.min((index + 1) * 1000, 1_000_000)
          if (task.position !== newPosition) {
            updatedPayload.push({
              id: task.id,
              status: destStatus,
              position: newPosition
            })
          }
        }
      });

      // If task moved between columns, update positions in the source column
      if (sourceStatus !== destStatus) {
        newTasks[sourceStatus].forEach((task, index) => {
          if (task) {
            const newPosition = Math.min((index + 1) * 1000, 1_000_000)
            if (task.position !== newPosition) {
              updatedPayload.push({
                id: task.id,
                status: sourceStatus,
                position: newPosition
              })
            }
          }
        })
      }
  
      return newTasks;
    });
    onChange(updatedPayload)
  }, [onChange]);

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="flex overflow-x-auto">
        {boards.map((board) => {
          return (
            <div key={board} className="flex-1 mx-2 bg-muted p-1.5 rounded-md min-w-[200px]">
              <KanbanColumHeader 
                board={board}
                taskCount={tasks[board].length}
              />
              <Droppable droppableId={board} key={board}>
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="min-h-[200px] py-1"
                  >
                    {tasks[board].map((task, index) => (
                      <Draggable
                        key={task.id}
                        draggableId={task.id}
                        index={index}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="mb-2"
                          >
                            <KanbanCard task={task} />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          )
        })}
      </div>
    </DragDropContext>
  )
}
import React from "react";
import { useDroppable } from "@dnd-kit/core";

export const Droppable = ({ id, children }: { id: string; children: React.ReactNode }) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  return (
    <div 
      ref={setNodeRef} 
      className={`border-2 border-dashed rounded-lg transition-colors duration-200 ${
        isOver ? 'border-primary/50 bg-primary/5' : 'border-gray-200'
      }`}
    >
      {children}
    </div>
  );
};
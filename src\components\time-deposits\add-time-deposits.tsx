"use client";

import { BankAccount, Category } from "@prisma/client";
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray, SubmitHandler } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { format, parse, isValid, startOfDay } from "date-fns";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Loader2, Plus } from "lucide-react";
import { createTimeDeposits } from "@/actions/time-deposits/create-deposits";
import { CreateCategoryDialog } from "@/components/categories/create-categories";
import { Textarea } from "../ui/textarea";


const timeDepositSchema = z.object({
  type: z.enum(["AVAILABLE", "WITHDRAWN"], { message: "Please select a valid type" }),
  amount: z.number().min(1, "Amount is required"),
  certificateNo: z.string().min(3, "Certificate Number is required"),
  period: z.string().optional(),
  interestRate: z.number().optional(),
  categoryId: z.string().min(3, "Please select a category"),
  accountId: z.string().min(1, "Please select an account"),
  description: z.string().optional(),
  transactionDate: z
    .union([
      z.date(),
      z.string().transform((val) => {
        if (val === '') return startOfDay(new Date()); // Default to today
        const parsedDate = parse(val, 'yyyy-MM-dd', new Date());
        if (isValid(parsedDate) && parsedDate <= new Date()) {
          return startOfDay(parsedDate);
        }
        throw new Error("Invalid date format");
      })
    ])
    .optional()
    .default(startOfDay(new Date())), // Set default to today
});

type TimeDepositFormValues = z.infer<typeof timeDepositSchema>;
type AccountData = Pick<BankAccount, 'id' | 'name' | 'accountType'>
type CategoryData = Pick<Category, 'id' | 'name'>

export function AddTimeDepositComponent({ 
  categories, 
  accounts 
}: { 
  categories: CategoryData[], 
  accounts: AccountData[] 
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm<{ timeDeposits: TimeDepositFormValues[] }>({
    resolver: zodResolver(z.object({
      timeDeposits: z.array(timeDepositSchema)
    })),
    defaultValues: {
      timeDeposits: [{ 
        type: "AVAILABLE", 
        amount: 0,
        certificateNo: "",
        period: "1",
        interestRate: 1.665,
        categoryId: "cm53sxms00001jv036gtap8fq", 
        accountId: "",
        transactionDate: startOfDay(new Date()),
      }],
    }
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "timeDeposits",
  });

  const onSubmit: SubmitHandler<{ timeDeposits: TimeDepositFormValues[] }> = async (formData) => {
    try {
      setIsLoading(true);
  
      // Call the server-side function to create multiple timeDeposits
      const result = await createTimeDeposits({
        timeDeposits: formData.timeDeposits.map((timeDeposit) => ({
          accountId: timeDeposit.accountId,
          certificateNo: timeDeposit.certificateNo,
          period: timeDeposit?.period ?? "",
          interestRate: timeDeposit?.interestRate ?? 1.665,
          description: timeDeposit?.description ?? "",
          amount: timeDeposit.amount,
          type: timeDeposit.type,
          categoryId: timeDeposit.categoryId ?? "cm53sxms00001jv036gtap8fq",
          currencyIso: "TWD",
          date: timeDeposit.transactionDate || startOfDay(new Date()),
        })),
      });
  
      if (!result.success) {
        toast.error(result.error);
        return;
      }
  
      toast.success("Transactions created successfully!");
      setIsDialogOpen(false);
      form.reset();
    } catch (error) {
      console.error("Error in onSubmit:", error);
      toast.error("Failed to create timeDeposits");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Plus className="mr-2 h-4 w-4" /> Add Time Deposit
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-screen sm:max-w-[800px] overflow-auto">
        <DialogHeader>
          <DialogTitle className="my-4">Add New Time Deposit</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="space-y-4 border-b pb-4">
                {/* TimeDeposit Fields */}
                <div className="grid grid-cols-3 gap-4">
                  {/* Amount Field */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.amount`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter amount"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* TimeDeposit Type Dropdown */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.type`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Type</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="AVAILABLE">Available</SelectItem>
                              <SelectItem value="WITHDRAWN">Withdrawn</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                  {/* TimeDeposit Date */}
                  <FormField
                    control={form.control}
                    name={`timeDeposits.${index}.transactionDate`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            {...field}
                            value={
                              field.value 
                                ? format(field.value, 'yyyy-MM-dd') 
                                : format(new Date(), 'yyyy-MM-dd') // Fallback to today
                            }
                            onChange={(e) => {
                              const inputDate = e.target.value;
                              
                              const parsedDate = parse(inputDate, 'yyyy-MM-dd', new Date());
                              
                              if (isValid(parsedDate) && parsedDate <= new Date()) {
                                field.onChange(startOfDay(parsedDate));
                              } else {
                                // If invalid, reset to today
                                field.onChange(startOfDay(new Date()));
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  {/* CertificateNo Field */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.certificateNo`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Certificate Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Time Deposit Certificate No"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                  
                  {/* Interest Rate Field */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.interestRate`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Interest Rate %</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field} 
                            onChange={(e) => field.onChange(e.target.value === '' ? undefined : parseFloat(e.target.value))}
                            value={field.value === undefined || field.value === null ? '' : field.value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* TimeDeposit Period */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.period`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Period</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Time Deposit Period"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Category Dropdown */}
                  <FormField
                    control={form.control} 
                    name={`timeDeposits.${index}.categoryId`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel><div className="h-8">Category <CreateCategoryDialog /></div></FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((category) => (
                                <SelectItem key={category.id} value={category.id}>
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />

                  {/* Account Dropdown */}
                  <FormField 
                    control={form.control} 
                    name={`timeDeposits.${index}.accountId`} 
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel><div className="h-8 flex items-end">Bank Account</div></FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <SelectTrigger className="text-sm text-muted-foreground">
                              <SelectValue placeholder="Select account" />
                            </SelectTrigger>
                            <SelectContent>
                              {accounts.map((account) => (
                                <SelectItem key={account.id} value={account.id}>
                                  {account.name} ({account.accountType || "Unknown"})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} 
                  />
                </div>
                {/* Description Field */}
                <FormField 
                  control={form.control} 
                  name={`timeDeposits.${index}.description`} 
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Add details about the timeDeposit"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} 
                />

                {/* Remove TimeDeposit Button */}
                {fields.length > 1 && (
                  <Button 
                    type="button" 
                    variant="destructive" 
                    size="sm"
                    onClick={() => remove(index)}
                  >
                    Remove TimeDeposit
                  </Button>
                )}
              </div>
            ))}
            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => append({ 
                  type: "AVAILABLE", amount: 0, categoryId: "cm53sxms00001jv036gtap8fq", 
                  accountId: "", certificateNo: "", period: "1", interestRate: 1.665, 
                })}
              >
                <Plus className="mr-2 h-4 w-4" /> Add More TimeDeposit
              </Button>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  "Save TimeDeposits"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}